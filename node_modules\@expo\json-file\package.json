{"name": "@expo/json-file", "version": "9.1.5", "description": "A module for reading, writing, and manipulating JSON files", "main": "build/JsonFile.js", "scripts": {"build": "expo-module tsc", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && expo-module tsc", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck", "watch": "expo-module tsc --watch --preserveWatchOutput"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/json-file"}, "keywords": ["json"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/json-file#readme", "files": ["build"], "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}, "devDependencies": {"@types/babel__code-frame": "^7.0.1", "@types/json5": "^2.2.0", "expo-module-scripts": "^4.1.9", "memfs": "^3.2.0"}, "publishConfig": {"access": "public"}, "gitHead": "7980ffdc39bf0ca82e62b59148a43ed755991ecb"}