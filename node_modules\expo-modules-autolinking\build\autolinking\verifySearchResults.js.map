{"version": 3, "file": "verifySearchResults.js", "sourceRoot": "", "sources": ["../../src/autolinking/verifySearchResults.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,gDAAwB;AAIxB;;GAEG;AACH,SAAgB,mBAAmB,CAAC,aAA4B,EAAE,OAAsB;IACtF,MAAM,YAAY,GAAqC,CAAC,GAAG,EAAE,EAAE,CAC7D,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/C,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,KAAK,MAAM,UAAU,IAAI,aAAa,EAAE;QACtC,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;QAE3C,IAAI,QAAQ,CAAC,UAAU,EAAE,MAAM,EAAE;YAC/B,OAAO,CAAC,IAAI,CAAC,mCAAmC,eAAK,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,MAAM,eAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,KAAK,eAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAE7F,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C,OAAO,CAAC,GAAG,CAAC,MAAM,eAAK,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,KAAK,eAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;aAC7F;YACD,OAAO,EAAE,CAAC;SACX;KACF;IACD,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,OAAO,CAAC,IAAI,CACV,wGAAwG,CACzG,CAAC;KACH;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAxBD,kDAwBC", "sourcesContent": ["import chalk from 'chalk';\nimport path from 'path';\n\nimport { PackageRevision, SearchOptions, SearchResults } from '../types';\n\n/**\n * Verifies the search results by checking whether there are no duplicates.\n */\nexport function verifySearchResults(searchResults: SearchResults, options: SearchOptions): number {\n  const relativePath: (pkg: PackageRevision) => string = (pkg) =>\n    path.relative(options.projectRoot, pkg.path);\n  let counter = 0;\n\n  for (const moduleName in searchResults) {\n    const revision = searchResults[moduleName];\n\n    if (revision.duplicates?.length) {\n      console.warn(`⚠️  Found multiple revisions of ${chalk.green(moduleName)}`);\n      console.log(` - ${chalk.magenta(relativePath(revision))} (${chalk.cyan(revision.version)})`);\n\n      for (const duplicate of revision.duplicates) {\n        console.log(` - ${chalk.gray(relativePath(duplicate))} (${chalk.gray(duplicate.version)})`);\n      }\n      counter++;\n    }\n  }\n  if (counter > 0) {\n    console.warn(\n      '⚠️  Please get rid of multiple revisions as it may introduce some side effects or compatibility issues'\n    );\n  }\n  return counter;\n}\n"]}