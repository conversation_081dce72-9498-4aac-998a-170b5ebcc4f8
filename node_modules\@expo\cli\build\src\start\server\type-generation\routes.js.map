{"version": 3, "sources": ["../../../../../src/start/server/type-generation/routes.ts"], "sourcesContent": ["import fs from 'fs/promises';\nimport debounce from 'lodash.debounce';\nimport { Server } from 'metro';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { directoryExistsAsync } from '../../../utils/dir';\nimport { unsafeTemplate } from '../../../utils/template';\nimport { ServerLike } from '../BundlerDevServer';\nimport { metroWatchTypeScriptFiles } from '../metro/metroWatchTypeScriptFiles';\n\n// /test/[...param1]/[param2]/[param3] - captures [\"param1\", \"param2\", \"param3\"]\nexport const CAPTURE_DYNAMIC_PARAMS = /\\[(?:\\.{3})?(\\w*?)[\\]$]/g;\n// /[...param1]/ - Match [...param1]\nexport const CATCH_ALL = /\\[\\.\\.\\..+?\\]/g;\n// /[param1] - Match [param1]\nexport const SLUG = /\\[.+?\\]/g;\n// /(group1,group2,group3)/test - match (group1,group2,group3)\nexport const ARRAY_GROUP_REGEX = /\\(\\s*\\w[\\w\\s]*?,.*?\\)/g;\n// /(group1,group2,group3)/test - captures [\"group1\", \"group2\", \"group3\"]\nexport const CAPTURE_GROUP_REGEX = /[\\\\(,]\\s*(\\w[\\w\\s]*?)\\s*(?=[,\\\\)])/g;\n/**\n * Match:\n *  - _layout files, +html, +not-found, string+api, etc\n *  - Routes can still use `+`, but it cannot be in the last segment.\n */\nexport const TYPED_ROUTES_EXCLUSION_REGEX = /(_layout|[^/]*?\\+[^/]*?)\\.[tj]sx?$/;\n\nexport interface SetupTypedRoutesOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  typesDirectory: string;\n  projectRoot: string;\n  /** Absolute expo router routes directory. */\n  routerDirectory: string;\n  plugin?: Record<string, any>;\n}\n\nexport async function setupTypedRoutes(options: SetupTypedRoutesOptions) {\n  /*\n   * In SDK 51, TypedRoutes was moved out of cli and into expo-router. For now we need to support both\n   * the legacy and new versions of TypedRoutes.\n   *\n   * TODO (@marklawlor): Remove this check in SDK 53, only support Expo Router v4 and above.\n   */\n  const typedRoutesModule = resolveFrom.silent(\n    options.projectRoot,\n    'expo-router/build/typed-routes'\n  );\n  return typedRoutesModule ? typedRoutes(typedRoutesModule, options) : legacyTypedRoutes(options);\n}\n\nasync function typedRoutes(\n  typedRoutesModulePath: any,\n  { server, metro, typesDirectory, projectRoot, routerDirectory, plugin }: SetupTypedRoutesOptions\n) {\n  /*\n   * Expo Router uses EXPO_ROUTER_APP_ROOT in multiple places to determine the root of the project.\n   * In apps compiled by Metro, this code is compiled away. But Typed Routes run in NodeJS with no compilation\n   * so we need to explicitly set it.\n   */\n  process.env.EXPO_ROUTER_APP_ROOT = routerDirectory;\n\n  const typedRoutesModule = require(typedRoutesModulePath);\n\n  /*\n   * Typed Routes can be run with out Metro or a Server, e.g. `expo customize tsconfig.json`\n   */\n  if (metro && server) {\n    // Setup out watcher first\n    metroWatchTypeScriptFiles({\n      projectRoot,\n      server,\n      metro,\n      eventTypes: ['add', 'delete', 'change'],\n      callback: typedRoutesModule.getWatchHandler(typesDirectory),\n    });\n  }\n\n  /*\n   * In SDK 52, the `regenerateDeclarations` was changed to accept plugin options.\n   * This function has an optional parameter that we cannot override, so we need to ensure the user\n   * is using a compatible version of `expo-router`. Otherwise, we will fallback to the old method.\n   *\n   * TODO(@marklawlor): In SDK53+ we should remove this check and always use the new method.\n   */\n  if ('version' in typedRoutesModule && typedRoutesModule.version >= 52) {\n    typedRoutesModule.regenerateDeclarations(typesDirectory, plugin);\n  } else {\n    typedRoutesModule.regenerateDeclarations(typesDirectory);\n  }\n}\n\nasync function legacyTypedRoutes({\n  server,\n  metro,\n  typesDirectory,\n  projectRoot,\n  routerDirectory,\n}: SetupTypedRoutesOptions) {\n  const { filePathToRoute, staticRoutes, dynamicRoutes, addFilePath, isRouteFile } =\n    getTypedRoutesUtils(routerDirectory);\n\n  // Typed Routes can be run with out Metro or a Server, e.g. `expo customize tsconfig.json`\n  if (metro && server) {\n    metroWatchTypeScriptFiles({\n      projectRoot,\n      server,\n      metro,\n      eventTypes: ['add', 'delete', 'change'],\n      async callback({ filePath, type }) {\n        if (!isRouteFile(filePath)) {\n          return;\n        }\n\n        let shouldRegenerate = false;\n\n        if (type === 'delete') {\n          const route = filePathToRoute(filePath);\n          staticRoutes.delete(route);\n          dynamicRoutes.delete(route);\n          shouldRegenerate = true;\n        } else {\n          shouldRegenerate = addFilePath(filePath);\n        }\n\n        if (shouldRegenerate) {\n          regenerateRouterDotTS(\n            typesDirectory,\n            new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n            new Set(dynamicRoutes.keys())\n          );\n        }\n      },\n    });\n  }\n\n  if (await directoryExistsAsync(routerDirectory)) {\n    // Do we need to walk the entire tree on startup?\n    // Idea: Store the list of files in the last write, then simply check Git for what files have changed\n    await walk(routerDirectory, addFilePath);\n  }\n\n  regenerateRouterDotTS(\n    typesDirectory,\n    new Set([...staticRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set([...dynamicRoutes.values()].flatMap((v) => Array.from(v))),\n    new Set(dynamicRoutes.keys())\n  );\n}\n\n/**\n * Generate a router.d.ts file that contains all of the routes in the project.\n * Should be debounced as its very common for developers to make changes to multiple files at once (eg Save All)\n */\nconst regenerateRouterDotTS = debounce(\n  async (\n    typesDir: string,\n    staticRoutes: Set<string>,\n    dynamicRoutes: Set<string>,\n    dynamicRouteTemplates: Set<string>\n  ) => {\n    await fs.mkdir(typesDir, { recursive: true });\n    await fs.writeFile(\n      path.resolve(typesDir, './router.d.ts'),\n      getTemplateString(staticRoutes, dynamicRoutes, dynamicRouteTemplates)\n    );\n  },\n  100\n);\n\n/*\n * This is exported for testing purposes\n */\nexport function getTemplateString(\n  staticRoutes: Set<string>,\n  dynamicRoutes: Set<string>,\n  dynamicRouteTemplates: Set<string>\n) {\n  return routerDotTSTemplate({\n    staticRoutes: setToUnionType(staticRoutes),\n    dynamicRoutes: setToUnionType(dynamicRoutes),\n    dynamicRouteParams: setToUnionType(dynamicRouteTemplates),\n  });\n}\n\n/**\n * Utility functions for typed routes\n *\n * These are extracted for easier testing\n */\nexport function getTypedRoutesUtils(appRoot: string, filePathSeperator = path.sep) {\n  /*\n   * staticRoutes are a map where the key if the route without groups and the value\n   *   is another set of all group versions of the route. e.g,\n   *    Map([\n   *      [\"/\", [\"/(app)/(notes)\", \"/(app)/(profile)\"]\n   *    ])\n   */\n  const staticRoutes = new Map<string, Set<string>>([['/', new Set('/')]]);\n  /*\n   * dynamicRoutes are the same as staticRoutes (key if the resolved route,\n   *   and the value is a set of possible routes). e.g:\n   *\n   * /[...fruits] -> /${CatchAllRoutePart<T>}\n   * /color/[color] -> /color/${SingleRoutePart<T>}\n   *\n   * The keys of this map are also important, as they can be used as \"static\" types\n   * <Link href={{ pathname: \"/[...fruits]\",params: { fruits: [\"apple\"] } }} />\n   */\n  const dynamicRoutes = new Map<string, Set<string>>();\n\n  function normalizedFilePath(filePath: string) {\n    return filePath.replaceAll(filePathSeperator, '/');\n  }\n\n  const normalizedAppRoot = normalizedFilePath(appRoot);\n\n  const filePathToRoute = (filePath: string) => {\n    return normalizedFilePath(filePath)\n      .replace(normalizedAppRoot, '')\n      .replace(/index\\.[jt]sx?/, '')\n      .replace(/\\.[jt]sx?$/, '');\n  };\n\n  const isRouteFile = (filePath: string) => {\n    if (filePath.match(TYPED_ROUTES_EXCLUSION_REGEX)) {\n      return false;\n    }\n\n    // Route files must be nested with in the appRoot\n    const relative = path.relative(appRoot, filePath);\n    return relative && !relative.startsWith('..') && !path.isAbsolute(relative);\n  };\n\n  const addFilePath = (filePath: string): boolean => {\n    if (!isRouteFile(filePath)) {\n      return false;\n    }\n\n    const route = filePathToRoute(filePath);\n\n    // We have already processed this file\n    if (staticRoutes.has(route) || dynamicRoutes.has(route)) {\n      return false;\n    }\n\n    const dynamicParams = new Set(\n      [...route.matchAll(CAPTURE_DYNAMIC_PARAMS)].map((match) => match[1])\n    );\n    const isDynamic = dynamicParams.size > 0;\n\n    const addRoute = (originalRoute: string, route: string) => {\n      if (isDynamic) {\n        let set = dynamicRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          dynamicRoutes.set(originalRoute, set);\n        }\n\n        set.add(\n          route\n            .replaceAll(CATCH_ALL, '${CatchAllRoutePart<T>}')\n            .replaceAll(SLUG, '${SingleRoutePart<T>}')\n        );\n      } else {\n        let set = staticRoutes.get(originalRoute);\n\n        if (!set) {\n          set = new Set();\n          staticRoutes.set(originalRoute, set);\n        }\n\n        set.add(route);\n      }\n    };\n\n    if (!route.match(ARRAY_GROUP_REGEX)) {\n      addRoute(route, route);\n    }\n\n    // Does this route have a group? eg /(group)\n    if (route.includes('/(')) {\n      const routeWithoutGroups = route.replace(/\\/\\(.+?\\)/g, '');\n      addRoute(route, routeWithoutGroups);\n\n      // If there are multiple groups, we need to expand them\n      // eg /(test1,test2)/page => /test1/page & /test2/page\n      for (const routeWithSingleGroup of extrapolateGroupRoutes(route)) {\n        addRoute(route, routeWithSingleGroup);\n      }\n    }\n\n    return true;\n  };\n\n  return {\n    staticRoutes,\n    dynamicRoutes,\n    filePathToRoute,\n    addFilePath,\n    isRouteFile,\n  };\n}\n\nexport const setToUnionType = <T>(set: Set<T>) => {\n  return set.size > 0 ? [...set].map((s) => `\\`${s}\\``).join(' | ') : 'never';\n};\n\n/**\n * Recursively walk a directory and call the callback with the file path.\n */\nasync function walk(directory: string, callback: (filePath: string) => void) {\n  const files = await fs.readdir(directory);\n  for (const file of files) {\n    const p = path.join(directory, file);\n    if ((await fs.stat(p)).isDirectory()) {\n      await walk(p, callback);\n    } else {\n      // Normalise the paths so they are easier to convert to URLs\n      const normalizedPath = p.replaceAll(path.sep, '/');\n      callback(normalizedPath);\n    }\n  }\n}\n\n/**\n * Given a route, return all possible routes that could be generated from it.\n */\nexport function extrapolateGroupRoutes(\n  route: string,\n  routes: Set<string> = new Set()\n): Set<string> {\n  // Create a version with no groups. We will then need to cleanup double and/or trailing slashes\n  routes.add(route.replaceAll(ARRAY_GROUP_REGEX, '').replaceAll(/\\/+/g, '/').replace(/\\/$/, ''));\n\n  const match = route.match(ARRAY_GROUP_REGEX);\n\n  if (!match) {\n    routes.add(route);\n    return routes;\n  }\n\n  const groupsMatch = match[0];\n\n  for (const group of groupsMatch.matchAll(CAPTURE_GROUP_REGEX)) {\n    extrapolateGroupRoutes(route.replace(groupsMatch, `(${group[1].trim()})`), routes);\n  }\n\n  return routes;\n}\n\n/**\n * NOTE: This code refers to a specific version of `expo-router` and is therefore unsafe to\n * mix with arbitrary versions.\n * TODO: Version this code with `expo-router` or version expo-router with `@expo/cli`.\n */\nconst routerDotTSTemplate = unsafeTemplate`/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable import/export */\n/* eslint-disable @typescript-eslint/ban-types */\ndeclare module \"expo-router\" {\n  import type { LinkProps as OriginalLinkProps } from 'expo-router/build/link/Link';\n  import type { Router as OriginalRouter } from 'expo-router/build/types';\n  export * from 'expo-router/build';\n\n  // prettier-ignore\n  type StaticRoutes = ${'staticRoutes'};\n  // prettier-ignore\n  type DynamicRoutes<T extends string> = ${'dynamicRoutes'};\n  // prettier-ignore\n  type DynamicRouteTemplate = ${'dynamicRouteParams'};\n\n  type RelativePathString = \\`./\\${string}\\` | \\`../\\${string}\\` | '..';\n  type AbsoluteRoute = DynamicRouteTemplate | StaticRoutes;\n  type ExternalPathString = \\`\\${string}:\\${string}\\`;\n\n  type ExpoRouterRoutes = DynamicRouteTemplate | StaticRoutes | RelativePathString;\n  export type AllRoutes = ExpoRouterRoutes | ExternalPathString;\n\n  /****************\n   * Route Utils  *\n   ****************/\n\n  type SearchOrHash = \\`?\\${string}\\` | \\`#\\${string}\\`;\n  type UnknownInputParams = Record<string, string | number | (string | number)[]>;\n  type UnknownOutputParams = Record<string, string | string[]>;\n\n  /**\n   * Return only the RoutePart of a string. If the string has multiple parts return never\n   *\n   * string   | type\n   * ---------|------\n   * 123      | 123\n   * /123/abc | never\n   * 123?abc  | never\n   * ./123    | never\n   * /123     | never\n   * 123/../  | never\n   */\n  type SingleRoutePart<S extends string> = S extends \\`\\${string}/\\${string}\\`\n    ? never\n    : S extends \\`\\${string}\\${SearchOrHash}\\`\n      ? never\n      : S extends ''\n        ? never\n        : S extends \\`(\\${string})\\`\n          ? never\n          : S extends \\`[\\${string}]\\`\n            ? never\n            : S;\n\n  /**\n   * Return only the CatchAll router part. If the string has search parameters or a hash return never\n   */\n  type CatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\`\n    ? never\n    : S extends ''\n      ? never\n      : S extends \\`\\${string}(\\${string})\\${string}\\`\n        ? never\n        : S extends \\`\\${string}[\\${string}]\\${string}\\`\n          ? never\n          : S;\n\n  // type OptionalCatchAllRoutePart<S extends string> = S extends \\`\\${string}\\${SearchOrHash}\\` ? never : S\n\n  /**\n   * Return the name of a route parameter\n   * '[test]'    -> 'test'\n   * 'test'      -> never\n   * '[...test]' -> '...test'\n   */\n  type IsParameter<Part> = Part extends \\`[\\${infer ParamName}]\\` ? ParamName : never;\n\n  /**\n   * Return a union of all parameter names. If there are no names return never\n   *\n   * /[test]         -> 'test'\n   * /[abc]/[...def] -> 'abc'|'...def'\n   */\n  type ParameterNames<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? IsParameter<PartA> | ParameterNames<PartB>\n    : IsParameter<Path>;\n\n  /**\n   * Returns all segements of a route.\n   *\n   * /(group)/123/abc/[id]/[...rest] -> ['(group)', '123', 'abc', '[id]', '[...rest]'\n   */\n  type RouteSegments<Path> = Path extends \\`\\${infer PartA}/\\${infer PartB}\\`\n    ? PartA extends '' | '.'\n      ? [...RouteSegments<PartB>]\n      : [PartA, ...RouteSegments<PartB>]\n    : Path extends ''\n      ? []\n      : [Path];\n\n  /**\n   * Returns a Record of the routes parameters as strings and CatchAll parameters\n   *\n   * There are two versions, input and output, as you can input 'string | number' but\n   *  the output will always be 'string'\n   *\n   * /[id]/[...rest] -> { id: string, rest: string[] }\n   * /no-params      -> {}\n   */\n  type InputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? (string | number)[] : string | number;\n  } & UnknownInputParams;\n\n  type OutputRouteParams<Path> = {\n    [Key in ParameterNames<Path> as Key extends \\`...\\${infer Name}\\`\n      ? Name\n      : Key]: Key extends \\`...\\${string}\\` ? string[] : string;\n  } & UnknownOutputParams;\n\n  /**\n   * Returns the search parameters for a route.\n   */\n  export type SearchParams<T extends AllRoutes> = T extends DynamicRouteTemplate\n    ? OutputRouteParams<T>\n    : T extends StaticRoutes\n      ? never\n      : UnknownOutputParams;\n\n  /**\n   * Route is mostly used as part of Href to ensure that a valid route is provided\n   *\n   * Given a dynamic route, this will return never. This is helpful for conditional logic\n   *\n   * /test         -> /test, /test2, etc\n   * /test/[abc]   -> never\n   * /test/resolve -> /test, /test2, etc\n   *\n   * Note that if we provide a value for [abc] then the route is allowed\n   *\n   * This is named Route to prevent confusion, as users they will often see it in tooltips\n   */\n  export type Route<T> = T extends string\n    ? T extends DynamicRouteTemplate\n      ? never\n      :\n          | StaticRoutes\n          | RelativePathString\n          | ExternalPathString\n          | (T extends \\`\\${infer P}\\${SearchOrHash}\\`\n              ? P extends DynamicRoutes<infer _>\n                ? T\n                : never\n              : T extends DynamicRoutes<infer _>\n                ? T\n                : never)\n    : never;\n\n  /*********\n   * Href  *\n   *********/\n\n  export type Href<T> = T extends Record<'pathname', string> ? HrefObject<T> : Route<T>;\n\n  export type HrefObject<\n    R extends Record<'pathname', string>,\n    P = R['pathname'],\n  > = P extends DynamicRouteTemplate\n    ? { pathname: P; params: InputRouteParams<P> }\n    : P extends Route<P>\n      ? { pathname: Route<P> | DynamicRouteTemplate; params?: never | InputRouteParams<never> }\n      : never;\n\n  /***********************\n   * Expo Router Exports *\n   ***********************/\n\n  export type Router = Omit<OriginalRouter, 'push' | 'replace' | 'setParams'> & {\n    /** Navigate to the provided href. */\n    push: <T>(href: Href<T>) => void;\n    /** Navigate to route without appending to the history. */\n    replace: <T>(href: Href<T>) => void;\n    /** Update the current route query params. */\n    setParams: <T = ''>(params?: T extends '' ? Record<string, string> : InputRouteParams<T>) => void;\n  };\n\n  /** The imperative router. */\n  export const router: Router;\n\n  /************\n   * <Link /> *\n   ************/\n  export interface LinkProps<T> extends OriginalLinkProps {\n    href: Href<T>;\n  }\n\n  export interface LinkComponent {\n    <T>(props: React.PropsWithChildren<LinkProps<T>>): JSX.Element;\n    /** Helper method to resolve an Href object into a string. */\n    resolveHref: <T>(href: Href<T>) => string;\n  }\n\n  /**\n   * Component to render link to another route using a path.\n   * Uses an anchor tag on the web.\n   *\n   * @param props.href Absolute path to route (e.g. \\`/feeds/hot\\`).\n   * @param props.replace Should replace the current route without adding to the history.\n   * @param props.asChild Forward props to child component. Useful for custom buttons.\n   * @param props.children Child elements to render the content.\n   * @param props.className On web, this sets the HTML \\`class\\` directly. On native, this can be used with CSS interop tools like Nativewind.\n   */\n  export const Link: LinkComponent;\n\n  /** Redirects to the href as soon as the component is mounted. */\n  export const Redirect: <T>(\n    props: React.PropsWithChildren<{ href: Href<T> }>\n  ) => JSX.Element;\n\n  /************\n   * Hooks *\n   ************/\n  export function useRouter(): Router;\n\n  export function useLocalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  /** @deprecated renamed to \\`useGlobalSearchParams\\` */\n  export function useSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useGlobalSearchParams<\n    T extends AllRoutes | UnknownOutputParams = UnknownOutputParams,\n  >(): T extends AllRoutes ? SearchParams<T> : T;\n\n  export function useSegments<\n    T extends AbsoluteRoute | RouteSegments<AbsoluteRoute> | RelativePathString,\n  >(): T extends AbsoluteRoute ? RouteSegments<T> : T extends string ? string[] : T;\n}\n`;\n"], "names": ["CAPTURE_DYNAMIC_PARAMS", "CATCH_ALL", "SLUG", "ARRAY_GROUP_REGEX", "CAPTURE_GROUP_REGEX", "TYPED_ROUTES_EXCLUSION_REGEX", "setupTypedRoutes", "getTemplateString", "getTypedRoutesUtils", "setToUnionType", "extrapolateGroupRoutes", "options", "typedRoutesModule", "resolveFrom", "silent", "projectRoot", "typedRoutes", "legacyTypedRoutes", "typedRoutesModulePath", "server", "metro", "typesDirectory", "routerDirectory", "plugin", "process", "env", "EXPO_ROUTER_APP_ROOT", "require", "metroWatchTypeScriptFiles", "eventTypes", "callback", "getWatchHandler", "version", "regenerateDeclarations", "filePathToRoute", "staticRoutes", "dynamicRoutes", "addFilePath", "isRouteFile", "filePath", "type", "shouldRegenerate", "route", "delete", "regenerateRouterDotTS", "Set", "values", "flatMap", "v", "Array", "from", "keys", "directoryExistsAsync", "walk", "debounce", "typesDir", "dynamicRouteTemplates", "fs", "mkdir", "recursive", "writeFile", "path", "resolve", "routerDotTSTemplate", "dynamicRouteParams", "appRoot", "filePathSeperator", "sep", "Map", "normalizedFilePath", "replaceAll", "normalizedAppRoot", "replace", "match", "relative", "startsWith", "isAbsolute", "has", "dynamicParams", "matchAll", "map", "isDynamic", "size", "addRoute", "originalRoute", "set", "get", "add", "includes", "routeWithoutGroups", "routeWithSingleGroup", "s", "join", "directory", "files", "readdir", "file", "p", "stat", "isDirectory", "normalizedPath", "routes", "groupsMatch", "group", "trim", "unsafeTemplate"], "mappings": "AAAA;;;;;;;;;;;IAYaA,sBAAsB,MAAtBA,sBAAsB;IAEtBC,SAAS,MAATA,SAAS;IAETC,IAAI,MAAJA,IAAI;IAEJC,iBAAiB,MAAjBA,iBAAiB;IAEjBC,mBAAmB,MAAnBA,mBAAmB;IAMnBC,4BAA4B,MAA5BA,4BAA4B;IAYnBC,gBAAgB,MAAhBA,gBAAgB;IAyItBC,iBAAiB,MAAjBA,iBAAiB;IAiBjBC,mBAAmB,MAAnBA,mBAAmB;IAmHtBC,cAAc,MAAdA,cAAc;IAwBXC,sBAAsB,MAAtBA,sBAAsB;;;8DA3UvB,aAAa;;;;;;;8DACP,iBAAiB;;;;;;;8DAErB,MAAM;;;;;;;8DACC,cAAc;;;;;;qBAED,oBAAoB;0BAC1B,yBAAyB;2CAEd,oCAAoC;;;;;;AAGvE,MAAMV,sBAAsB,6BAA6B,AAAC;AAE1D,MAAMC,SAAS,mBAAmB,AAAC;AAEnC,MAAMC,IAAI,aAAa,AAAC;AAExB,MAAMC,iBAAiB,2BAA2B,AAAC;AAEnD,MAAMC,mBAAmB,wCAAwC,AAAC;AAMlE,MAAMC,4BAA4B,uCAAuC,AAAC;AAY1E,eAAeC,gBAAgB,CAACK,OAAgC,EAAE;IACvE;;;;;GAKC,GACD,MAAMC,iBAAiB,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAC1CH,OAAO,CAACI,WAAW,EACnB,gCAAgC,CACjC,AAAC;IACF,OAAOH,iBAAiB,GAAGI,WAAW,CAACJ,iBAAiB,EAAED,OAAO,CAAC,GAAGM,iBAAiB,CAACN,OAAO,CAAC,CAAC;AAClG,CAAC;AAED,eAAeK,WAAW,CACxBE,qBAA0B,EAC1B,EAAEC,MAAM,CAAA,EAAEC,KAAK,CAAA,EAAEC,cAAc,CAAA,EAAEN,WAAW,CAAA,EAAEO,eAAe,CAAA,EAAEC,MAAM,CAAA,EAA2B,EAChG;IACA;;;;GAIC,GACDC,OAAO,CAACC,GAAG,CAACC,oBAAoB,GAAGJ,eAAe,CAAC;IAEnD,MAAMV,iBAAiB,GAAGe,OAAO,CAACT,qBAAqB,CAAC,AAAC;IAEzD;;GAEC,GACD,IAAIE,KAAK,IAAID,MAAM,EAAE;QACnB,0BAA0B;QAC1BS,IAAAA,0BAAyB,0BAAA,EAAC;YACxBb,WAAW;YACXI,MAAM;YACNC,KAAK;YACLS,UAAU,EAAE;gBAAC,KAAK;gBAAE,QAAQ;gBAAE,QAAQ;aAAC;YACvCC,QAAQ,EAAElB,iBAAiB,CAACmB,eAAe,CAACV,cAAc,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;IAED;;;;;;GAMC,GACD,IAAI,SAAS,IAAIT,iBAAiB,IAAIA,iBAAiB,CAACoB,OAAO,IAAI,EAAE,EAAE;QACrEpB,iBAAiB,CAACqB,sBAAsB,CAACZ,cAAc,EAAEE,MAAM,CAAC,CAAC;IACnE,OAAO;QACLX,iBAAiB,CAACqB,sBAAsB,CAACZ,cAAc,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC;AAED,eAAeJ,iBAAiB,CAAC,EAC/BE,MAAM,CAAA,EACNC,KAAK,CAAA,EACLC,cAAc,CAAA,EACdN,WAAW,CAAA,EACXO,eAAe,CAAA,EACS,EAAE;IAC1B,MAAM,EAAEY,eAAe,CAAA,EAAEC,YAAY,CAAA,EAAEC,aAAa,CAAA,EAAEC,WAAW,CAAA,EAAEC,WAAW,CAAA,EAAE,GAC9E9B,mBAAmB,CAACc,eAAe,CAAC,AAAC;IAEvC,0FAA0F;IAC1F,IAAIF,KAAK,IAAID,MAAM,EAAE;QACnBS,IAAAA,0BAAyB,0BAAA,EAAC;YACxBb,WAAW;YACXI,MAAM;YACNC,KAAK;YACLS,UAAU,EAAE;gBAAC,KAAK;gBAAE,QAAQ;gBAAE,QAAQ;aAAC;YACvC,MAAMC,QAAQ,EAAC,EAAES,QAAQ,CAAA,EAAEC,IAAI,CAAA,EAAE,EAAE;gBACjC,IAAI,CAACF,WAAW,CAACC,QAAQ,CAAC,EAAE;oBAC1B,OAAO;gBACT,CAAC;gBAED,IAAIE,gBAAgB,GAAG,KAAK,AAAC;gBAE7B,IAAID,IAAI,KAAK,QAAQ,EAAE;oBACrB,MAAME,KAAK,GAAGR,eAAe,CAACK,QAAQ,CAAC,AAAC;oBACxCJ,YAAY,CAACQ,MAAM,CAACD,KAAK,CAAC,CAAC;oBAC3BN,aAAa,CAACO,MAAM,CAACD,KAAK,CAAC,CAAC;oBAC5BD,gBAAgB,GAAG,IAAI,CAAC;gBAC1B,OAAO;oBACLA,gBAAgB,GAAGJ,WAAW,CAACE,QAAQ,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAIE,gBAAgB,EAAE;oBACpBG,qBAAqB,CACnBvB,cAAc,EACd,IAAIwB,GAAG,CAAC;2BAAIV,YAAY,CAACW,MAAM,EAAE;qBAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC,EACjE,IAAIH,GAAG,CAAC;2BAAIT,aAAa,CAACU,MAAM,EAAE;qBAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC,EAClE,IAAIH,GAAG,CAACT,aAAa,CAACe,IAAI,EAAE,CAAC,CAC9B,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,MAAMC,IAAAA,IAAoB,qBAAA,EAAC9B,eAAe,CAAC,EAAE;QAC/C,iDAAiD;QACjD,qGAAqG;QACrG,MAAM+B,IAAI,CAAC/B,eAAe,EAAEe,WAAW,CAAC,CAAC;IAC3C,CAAC;IAEDO,qBAAqB,CACnBvB,cAAc,EACd,IAAIwB,GAAG,CAAC;WAAIV,YAAY,CAACW,MAAM,EAAE;KAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC,EACjE,IAAIH,GAAG,CAAC;WAAIT,aAAa,CAACU,MAAM,EAAE;KAAC,CAACC,OAAO,CAAC,CAACC,CAAC,GAAKC,KAAK,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,CAAC,EAClE,IAAIH,GAAG,CAACT,aAAa,CAACe,IAAI,EAAE,CAAC,CAC9B,CAAC;AACJ,CAAC;AAED;;;CAGC,GACD,MAAMP,qBAAqB,GAAGU,IAAAA,eAAQ,EAAA,QAAA,EACpC,OACEC,QAAgB,EAChBpB,YAAyB,EACzBC,aAA0B,EAC1BoB,qBAAkC,GAC/B;IACH,MAAMC,SAAE,EAAA,QAAA,CAACC,KAAK,CAACH,QAAQ,EAAE;QAAEI,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAC9C,MAAMF,SAAE,EAAA,QAAA,CAACG,SAAS,CAChBC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACP,QAAQ,EAAE,eAAe,CAAC,EACvChD,iBAAiB,CAAC4B,YAAY,EAAEC,aAAa,EAAEoB,qBAAqB,CAAC,CACtE,CAAC;AACJ,CAAC,EACD,GAAG,CACJ,AAAC;AAKK,SAASjD,iBAAiB,CAC/B4B,YAAyB,EACzBC,aAA0B,EAC1BoB,qBAAkC,EAClC;IACA,OAAOO,mBAAmB,CAAC;QACzB5B,YAAY,EAAE1B,cAAc,CAAC0B,YAAY,CAAC;QAC1CC,aAAa,EAAE3B,cAAc,CAAC2B,aAAa,CAAC;QAC5C4B,kBAAkB,EAAEvD,cAAc,CAAC+C,qBAAqB,CAAC;KAC1D,CAAC,CAAC;AACL,CAAC;AAOM,SAAShD,mBAAmB,CAACyD,OAAe,EAAEC,iBAAiB,GAAGL,KAAI,EAAA,QAAA,CAACM,GAAG,EAAE;IACjF;;;;;;GAMC,GACD,MAAMhC,YAAY,GAAG,IAAIiC,GAAG,CAAsB;QAAC;YAAC,GAAG;YAAE,IAAIvB,GAAG,CAAC,GAAG,CAAC;SAAC;KAAC,CAAC,AAAC;IACzE;;;;;;;;;GASC,GACD,MAAMT,aAAa,GAAG,IAAIgC,GAAG,EAAuB,AAAC;IAErD,SAASC,kBAAkB,CAAC9B,QAAgB,EAAE;QAC5C,OAAOA,QAAQ,CAAC+B,UAAU,CAACJ,iBAAiB,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAED,MAAMK,iBAAiB,GAAGF,kBAAkB,CAACJ,OAAO,CAAC,AAAC;IAEtD,MAAM/B,eAAe,GAAG,CAACK,QAAgB,GAAK;QAC5C,OAAO8B,kBAAkB,CAAC9B,QAAQ,CAAC,CAChCiC,OAAO,CAACD,iBAAiB,EAAE,EAAE,CAAC,CAC9BC,OAAO,mBAAmB,EAAE,CAAC,CAC7BA,OAAO,eAAe,EAAE,CAAC,CAAC;IAC/B,CAAC,AAAC;IAEF,MAAMlC,WAAW,GAAG,CAACC,QAAgB,GAAK;QACxC,IAAIA,QAAQ,CAACkC,KAAK,CAACpE,4BAA4B,CAAC,EAAE;YAChD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iDAAiD;QACjD,MAAMqE,QAAQ,GAAGb,KAAI,EAAA,QAAA,CAACa,QAAQ,CAACT,OAAO,EAAE1B,QAAQ,CAAC,AAAC;QAClD,OAAOmC,QAAQ,IAAI,CAACA,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,IAAI,CAACd,KAAI,EAAA,QAAA,CAACe,UAAU,CAACF,QAAQ,CAAC,CAAC;IAC9E,CAAC,AAAC;IAEF,MAAMrC,WAAW,GAAG,CAACE,QAAgB,GAAc;QACjD,IAAI,CAACD,WAAW,CAACC,QAAQ,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAMG,KAAK,GAAGR,eAAe,CAACK,QAAQ,CAAC,AAAC;QAExC,sCAAsC;QACtC,IAAIJ,YAAY,CAAC0C,GAAG,CAACnC,KAAK,CAAC,IAAIN,aAAa,CAACyC,GAAG,CAACnC,KAAK,CAAC,EAAE;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAMoC,aAAa,GAAG,IAAIjC,GAAG,CAC3B;eAAIH,KAAK,CAACqC,QAAQ,CAAC/E,sBAAsB,CAAC;SAAC,CAACgF,GAAG,CAAC,CAACP,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CACrE,AAAC;QACF,MAAMQ,SAAS,GAAGH,aAAa,CAACI,IAAI,GAAG,CAAC,AAAC;QAEzC,MAAMC,QAAQ,GAAG,CAACC,aAAqB,EAAE1C,KAAa,GAAK;YACzD,IAAIuC,SAAS,EAAE;gBACb,IAAII,GAAG,GAAGjD,aAAa,CAACkD,GAAG,CAACF,aAAa,CAAC,AAAC;gBAE3C,IAAI,CAACC,GAAG,EAAE;oBACRA,GAAG,GAAG,IAAIxC,GAAG,EAAE,CAAC;oBAChBT,aAAa,CAACiD,GAAG,CAACD,aAAa,EAAEC,GAAG,CAAC,CAAC;gBACxC,CAAC;gBAEDA,GAAG,CAACE,GAAG,CACL7C,KAAK,CACF4B,UAAU,CAACrE,SAAS,EAAE,yBAAyB,CAAC,CAChDqE,UAAU,CAACpE,IAAI,EAAE,uBAAuB,CAAC,CAC7C,CAAC;YACJ,OAAO;gBACL,IAAImF,IAAG,GAAGlD,YAAY,CAACmD,GAAG,CAACF,aAAa,CAAC,AAAC;gBAE1C,IAAI,CAACC,IAAG,EAAE;oBACRA,IAAG,GAAG,IAAIxC,GAAG,EAAE,CAAC;oBAChBV,YAAY,CAACkD,GAAG,CAACD,aAAa,EAAEC,IAAG,CAAC,CAAC;gBACvC,CAAC;gBAEDA,IAAG,CAACE,GAAG,CAAC7C,KAAK,CAAC,CAAC;YACjB,CAAC;QACH,CAAC,AAAC;QAEF,IAAI,CAACA,KAAK,CAAC+B,KAAK,CAACtE,iBAAiB,CAAC,EAAE;YACnCgF,QAAQ,CAACzC,KAAK,EAAEA,KAAK,CAAC,CAAC;QACzB,CAAC;QAED,4CAA4C;QAC5C,IAAIA,KAAK,CAAC8C,QAAQ,CAAC,IAAI,CAAC,EAAE;YACxB,MAAMC,kBAAkB,GAAG/C,KAAK,CAAC8B,OAAO,eAAe,EAAE,CAAC,AAAC;YAC3DW,QAAQ,CAACzC,KAAK,EAAE+C,kBAAkB,CAAC,CAAC;YAEpC,uDAAuD;YACvD,sDAAsD;YACtD,KAAK,MAAMC,oBAAoB,IAAIhF,sBAAsB,CAACgC,KAAK,CAAC,CAAE;gBAChEyC,QAAQ,CAACzC,KAAK,EAAEgD,oBAAoB,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,AAAC;IAEF,OAAO;QACLvD,YAAY;QACZC,aAAa;QACbF,eAAe;QACfG,WAAW;QACXC,WAAW;KACZ,CAAC;AACJ,CAAC;AAEM,MAAM7B,cAAc,GAAG,CAAI4E,GAAW,GAAK;IAChD,OAAOA,GAAG,CAACH,IAAI,GAAG,CAAC,GAAG;WAAIG,GAAG;KAAC,CAACL,GAAG,CAAC,CAACW,CAAC,GAAK,CAAC,EAAE,EAAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAC9E,CAAC,AAAC;AAEF;;CAEC,GACD,eAAevC,IAAI,CAACwC,SAAiB,EAAE/D,QAAoC,EAAE;IAC3E,MAAMgE,KAAK,GAAG,MAAMrC,SAAE,EAAA,QAAA,CAACsC,OAAO,CAACF,SAAS,CAAC,AAAC;IAC1C,KAAK,MAAMG,IAAI,IAAIF,KAAK,CAAE;QACxB,MAAMG,CAAC,GAAGpC,KAAI,EAAA,QAAA,CAAC+B,IAAI,CAACC,SAAS,EAAEG,IAAI,CAAC,AAAC;QACrC,IAAI,CAAC,MAAMvC,SAAE,EAAA,QAAA,CAACyC,IAAI,CAACD,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE,EAAE;YACpC,MAAM9C,IAAI,CAAC4C,CAAC,EAAEnE,QAAQ,CAAC,CAAC;QAC1B,OAAO;YACL,4DAA4D;YAC5D,MAAMsE,cAAc,GAAGH,CAAC,CAAC3B,UAAU,CAACT,KAAI,EAAA,QAAA,CAACM,GAAG,EAAE,GAAG,CAAC,AAAC;YACnDrC,QAAQ,CAACsE,cAAc,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;AACH,CAAC;AAKM,SAAS1F,sBAAsB,CACpCgC,KAAa,EACb2D,MAAmB,GAAG,IAAIxD,GAAG,EAAE,EAClB;IACb,+FAA+F;IAC/FwD,MAAM,CAACd,GAAG,CAAC7C,KAAK,CAAC4B,UAAU,CAACnE,iBAAiB,EAAE,EAAE,CAAC,CAACmE,UAAU,SAAS,GAAG,CAAC,CAACE,OAAO,QAAQ,EAAE,CAAC,CAAC,CAAC;IAE/F,MAAMC,KAAK,GAAG/B,KAAK,CAAC+B,KAAK,CAACtE,iBAAiB,CAAC,AAAC;IAE7C,IAAI,CAACsE,KAAK,EAAE;QACV4B,MAAM,CAACd,GAAG,CAAC7C,KAAK,CAAC,CAAC;QAClB,OAAO2D,MAAM,CAAC;IAChB,CAAC;IAED,MAAMC,WAAW,GAAG7B,KAAK,CAAC,CAAC,CAAC,AAAC;IAE7B,KAAK,MAAM8B,KAAK,IAAID,WAAW,CAACvB,QAAQ,CAAC3E,mBAAmB,CAAC,CAAE;QAC7DM,sBAAsB,CAACgC,KAAK,CAAC8B,OAAO,CAAC8B,WAAW,EAAE,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC,CAAC;IACrF,CAAC;IAED,OAAOA,MAAM,CAAC;AAChB,CAAC;AAED;;;;CAIC,GACD,MAAMtC,mBAAmB,GAAG0C,IAAAA,SAAc,eAAA,CAAA,CAAC;;;;;;;;;sBASrB,EAAE,cAAc,CAAC;;yCAEE,EAAE,eAAe,CAAC;;8BAE7B,EAAE,oBAAoB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOrD,CAAC,AAAC"}