{"name": "@expo/env", "version": "1.0.7", "description": "hydrate environment variables from .env files into process.env", "main": "build/index.js", "scripts": {"build": "tsc --emitDeclarationOnly && babel src --out-dir build --extensions \".ts\" --source-maps --ignore \"src/**/__mocks__/*\",\"src/**/__tests__/*\"", "clean": "expo-module clean", "lint": "expo-module lint", "prepare": "expo-module clean && yarn run build", "prepublishOnly": "expo-module prepublishOnly", "test": "expo-module test", "typecheck": "expo-module typecheck"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/env"}, "keywords": [], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/env#readme", "files": ["build"], "dependencies": {"chalk": "^4.0.0", "debug": "^4.3.4", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.15.5", "@types/getenv": "^1.0.0", "expo-module-scripts": "^4.1.9"}, "publishConfig": {"access": "public"}, "gitHead": "03d3724918c94f6a46df0b48ba8ec43b995a8e96"}