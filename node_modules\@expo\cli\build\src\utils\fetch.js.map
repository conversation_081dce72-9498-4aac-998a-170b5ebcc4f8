{"version": 3, "sources": ["../../../src/utils/fetch.ts"], "sourcesContent": ["import type { FetchLike } from '../api/rest/client.types';\n\n/**\n * The Node's built-in fetch API, but polyfilled from `undici` if necessary.\n * @todo(cedric): remove this once we min-support a Node version where fetch can't be disabled\n */\nexport const fetch: FetchLike =\n  typeof globalThis.fetch !== 'undefined' ? globalThis.fetch : require('undici').fetch;\n\n/**\n * Node's built-in fetch Headers class, or the polyfilled Headers from `undici` when unavailable.\n * @todo(cedric): remove this once we min-support a Node version where fetch can't be disabled\n */\nexport const Headers: typeof import('undici').Headers =\n  typeof globalThis.Headers !== 'undefined' ? globalThis.Headers : require('undici').Headers;\n"], "names": ["fetch", "Headers", "globalThis", "require"], "mappings": "AAAA;;;;;;;;;;;IAMaA,KAAK,MAALA,KAAK;IAOLC,OAAO,MAAPA,OAAO;;AAPb,MAAMD,KAAK,GAChB,OAAOE,UAAU,CAACF,KAAK,KAAK,WAAW,GAAGE,UAAU,CAACF,KAAK,GAAGG,OAAO,CAAC,QAAQ,CAAC,CAACH,KAAK,AAAC;AAMhF,MAAMC,OAAO,GAClB,OAAOC,UAAU,CAACD,OAAO,KAAK,WAAW,GAAGC,UAAU,CAACD,OAAO,GAAGE,OAAO,CAAC,QAAQ,CAAC,CAACF,OAAO,AAAC"}