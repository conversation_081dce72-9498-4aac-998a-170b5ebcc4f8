{"version": 3, "sources": ["../../../src/utils/findUp.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { CommandError } from '../utils/errors';\n\n/** Look up directories until one with a `package.json` can be found, assert if none can be found. */\nexport function findUpProjectRootOrAssert(cwd: string): string {\n  const projectRoot = findUpProjectRoot(cwd);\n  if (!projectRoot) {\n    throw new CommandError(`Project root directory not found (working directory: ${cwd})`);\n  }\n  return projectRoot;\n}\n\nfunction findUpProjectRoot(cwd: string): string | null {\n  const found = resolveFrom.silent(cwd, './package.json');\n  if (found) return path.dirname(found);\n\n  const parent = path.dirname(cwd);\n  if (parent === cwd) return null;\n\n  return findUpProjectRoot(parent);\n}\n\n/**\n * Find a file in the (closest) parent directories.\n * This will recursively look for the file, until the root directory is reached.\n */\nexport function findFileInParents(cwd: string, fileName: string): string | null {\n  const found = resolveFrom.silent(cwd, `./${fileName}`);\n  if (found) return found;\n\n  const parent = path.dirname(cwd);\n  if (parent === cwd) return null;\n\n  return findFileInParents(parent, fileName);\n}\n"], "names": ["findUpProjectRootOrAssert", "findFileInParents", "cwd", "projectRoot", "findUpProjectRoot", "CommandError", "found", "resolveFrom", "silent", "path", "dirname", "parent", "fileName"], "mappings": "AAAA;;;;;;;;;;;IAMgBA,yBAAyB,MAAzBA,yBAAyB;IAsBzBC,iBAAiB,MAAjBA,iBAAiB;;;8DA5BhB,MAAM;;;;;;;8DACC,cAAc;;;;;;wBAET,iBAAiB;;;;;;AAGvC,SAASD,yBAAyB,CAACE,GAAW,EAAU;IAC7D,MAAMC,WAAW,GAAGC,iBAAiB,CAACF,GAAG,CAAC,AAAC;IAC3C,IAAI,CAACC,WAAW,EAAE;QAChB,MAAM,IAAIE,OAAY,aAAA,CAAC,CAAC,qDAAqD,EAAEH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IACD,OAAOC,WAAW,CAAC;AACrB,CAAC;AAED,SAASC,iBAAiB,CAACF,GAAW,EAAiB;IACrD,MAAMI,KAAK,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACN,GAAG,EAAE,gBAAgB,CAAC,AAAC;IACxD,IAAII,KAAK,EAAE,OAAOG,KAAI,EAAA,QAAA,CAACC,OAAO,CAACJ,KAAK,CAAC,CAAC;IAEtC,MAAMK,MAAM,GAAGF,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,GAAG,CAAC,AAAC;IACjC,IAAIS,MAAM,KAAKT,GAAG,EAAE,OAAO,IAAI,CAAC;IAEhC,OAAOE,iBAAiB,CAACO,MAAM,CAAC,CAAC;AACnC,CAAC;AAMM,SAASV,iBAAiB,CAACC,GAAW,EAAEU,QAAgB,EAAiB;IAC9E,MAAMN,KAAK,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACN,GAAG,EAAE,CAAC,EAAE,EAAEU,QAAQ,CAAC,CAAC,CAAC,AAAC;IACvD,IAAIN,KAAK,EAAE,OAAOA,KAAK,CAAC;IAExB,MAAMK,MAAM,GAAGF,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,GAAG,CAAC,AAAC;IACjC,IAAIS,MAAM,KAAKT,GAAG,EAAE,OAAO,IAAI,CAAC;IAEhC,OAAOD,iBAAiB,CAACU,MAAM,EAAEC,QAAQ,CAAC,CAAC;AAC7C,CAAC"}