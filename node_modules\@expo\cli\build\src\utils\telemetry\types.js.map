{"version": 3, "sources": ["../../../../src/utils/telemetry/types.ts"], "sourcesContent": ["export type TelemetryEvent = 'action';\nexport type TelemetryProperties = Record<string, any>;\nexport type TelemetryRecord = {\n  event: TelemetryEvent;\n  properties?: TelemetryProperties;\n};\n\nexport type TelemetryClientStrategy = 'instant' | 'detached' | 'debug';\n\nexport interface TelemetryClient {\n  /** The telemetry client's strategy */\n  get strategy(): TelemetryClientStrategy;\n  /** Abort all pending records, and return them */\n  abort(): TelemetryRecordInternal[];\n  /** Record a (custom) event */\n  record(record: TelemetryRecordInternal[]): Promise<void> | void;\n  /** Clear the record queue and send all recorded events */\n  flush(): Promise<void> | void;\n}\n\nexport type TelemetryRecordInternal = TelemetryRecord & {\n  /**\n   * The type of telemetry event.\n   * This is added automatically by Rudderstack.\n   *\n   * @see https://github.com/expo/rudder-sdk-node/blob/79c1bef800d94522f293557d7db367fbd3e64d4a/index.ts#L43\n   */\n  type: 'identify' | 'track' | 'page' | 'screen' | 'group' | 'alias';\n\n  /**\n   * When the telemetry event was sent to the server.\n   * This is added automatically by Rudderstack.\n   *\n   * @see https://github.com/expo/rudder-sdk-node/blob/79c1bef800d94522f293557d7db367fbd3e64d4a/index.ts#L370\n   */\n  sentAt: Date;\n\n  /**\n   * A randomly generated message identifier, using the message content.\n   * This is added automatically by Rudderstack.\n   *\n   * @see https://github.com/expo/rudder-sdk-node/blob/79c1bef800d94522f293557d7db367fbd3e64d4a/index.ts#L263\n   */\n  messageId: string;\n\n  /**\n   * The anonymous identifier, generated and cached locally.\n   * This cannot be resolved to a single user.\n   */\n  anonymousId: string;\n\n  /**\n   * Additional context of the event, including a locally generated session ID.\n   * Session ID cannot be resolved to a single user, but will be the same for a single invocation of the CLI.\n   */\n  context: Record<'sessionId' | string, any>;\n\n  /**\n   * The original timestamp when this event was created.\n   * Note, this is only applicable for detached telemetry.\n   */\n  originalTimestamp?: Date;\n};\n"], "names": [], "mappings": "AAAA"}