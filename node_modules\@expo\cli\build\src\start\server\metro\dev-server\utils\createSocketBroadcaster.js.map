{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/createSocketBroadcaster.ts"], "sourcesContent": ["import type { SocketId, SocketMap } from './createSocketMap';\n\nconst debug = require('debug')('expo:metro:dev-server:broadcaster') as typeof console.log;\n\nexport function createBroadcaster(sockets: SocketMap) {\n  return function broadcast(senderSocketId: SocketId | null, message: string) {\n    // Ignore if there are no connected sockets\n    if (!sockets.size) return;\n\n    for (const [socketId, socket] of sockets) {\n      if (socketId === senderSocketId) continue;\n\n      try {\n        socket.send(message);\n      } catch (error) {\n        debug(`Failed to broadcast message to socket \"${socketId}\"`, error);\n      }\n    }\n  };\n}\n"], "names": ["createBroadcaster", "debug", "require", "sockets", "broadcast", "senderSocketId", "message", "size", "socketId", "socket", "send", "error"], "mappings": "AAAA;;;;+BAIgBA,mBAAiB;;aAAjBA,iBAAiB;;AAFjC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC,AAAsB,AAAC;AAEnF,SAASF,iBAAiB,CAACG,OAAkB,EAAE;IACpD,OAAO,SAASC,SAAS,CAACC,cAA+B,EAAEC,OAAe,EAAE;QAC1E,2CAA2C;QAC3C,IAAI,CAACH,OAAO,CAACI,IAAI,EAAE,OAAO;QAE1B,KAAK,MAAM,CAACC,QAAQ,EAAEC,MAAM,CAAC,IAAIN,OAAO,CAAE;YACxC,IAAIK,QAAQ,KAAKH,cAAc,EAAE,SAAS;YAE1C,IAAI;gBACFI,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAAC;YACvB,EAAE,OAAOK,KAAK,EAAE;gBACdV,KAAK,CAAC,CAAC,uCAAuC,EAAEO,QAAQ,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;IACH,CAAC,CAAC;AACJ,CAAC"}