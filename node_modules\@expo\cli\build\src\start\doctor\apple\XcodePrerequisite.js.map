{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcodePrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\nimport semver from 'semver';\n\nimport * as Log from '../../../log';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { confirmAsync } from '../../../utils/prompts';\nimport { Prerequisite } from '../Prerequisite';\n\nconst debug = require('debug')('expo:doctor:apple:xcode') as typeof console.log;\n\n// Based on the Apple announcement (last updated: Aug 2023).\n// https://developer.apple.com/news/upcoming-requirements/?id=04252023a\nconst MIN_XCODE_VERSION = '14.1';\nconst APP_STORE_ID = '497799835';\n\nconst SUGGESTED_XCODE_VERSION = `${MIN_XCODE_VERSION}.0`;\n\nconst promptToOpenAppStoreAsync = async (message: string) => {\n  // This prompt serves no purpose accept informing the user what to do next, we could just open the App Store but it could be confusing if they don't know what's going on.\n  const confirm = await confirmAsync({ initial: true, message });\n  if (confirm) {\n    Log.log(`Going to the App Store, re-run Expo CLI when Xcode has finished installing.`);\n    openAppStore(APP_STORE_ID);\n  }\n};\n\nlet _xcodeVersionPromise: Promise<{ value: string | null | false; error?: string }> | null = null;\n\nexport const getXcodeVersionAsync = async ({\n  silent,\n  force,\n}: { silent?: boolean; force?: boolean } = {}): Promise<string | null | false> => {\n  const logError = silent ? debug : Log.warn;\n  const getVersion = async (): Promise<{ value: string | null | false; error?: string }> => {\n    try {\n      const { stdout } = await spawnAsync('xcodebuild', ['-version']);\n      const last = stdout.match(/^Xcode (\\d+\\.\\d+)/)?.[1];\n      // Convert to a semver string\n      if (last) {\n        const version = `${last}.0`;\n\n        if (!semver.valid(version)) {\n          // Not sure why this would happen, if it does we should add a more confident error message.\n          return { error: `Xcode version is in an unknown format: ${version}`, value: false };\n        }\n        return { value: version };\n      }\n\n      // not sure what's going on\n      return {\n        error:\n          'Unable to check Xcode version. Command ran successfully but no version number was found.',\n        value: null,\n      };\n    } catch {\n      // not installed\n    }\n    return { value: null };\n  };\n\n  if (force) {\n    _xcodeVersionPromise = null;\n  }\n\n  _xcodeVersionPromise = _xcodeVersionPromise ?? getVersion();\n\n  const result = await _xcodeVersionPromise;\n\n  if (result.error) {\n    logError(result.error);\n  }\n\n  return result.value;\n};\n\n/**\n * Open a link to the App Store. Just link in mobile apps, **never** redirect without prompting first.\n *\n * @param appId\n */\nfunction openAppStore(appId: string) {\n  const link = getAppStoreLink(appId);\n  execSync(`open ${link}`, { stdio: 'ignore' });\n}\n\nfunction getAppStoreLink(appId: string): string {\n  if (process.platform === 'darwin') {\n    // TODO: Is there ever a case where the macappstore isn't available on mac?\n    return `macappstore://itunes.apple.com/app/id${appId}`;\n  }\n  return `https://apps.apple.com/us/app/id${appId}`;\n}\n\nfunction spawnForString(cmd: string): string | null {\n  try {\n    return execSync(cmd, { stdio: 'pipe' }).toString().trim();\n  } catch {}\n  return null;\n}\n\n/** @returns a string like `/Applications/Xcode.app/Contents/Developer` when Xcode has a correctly selected path. */\nfunction getXcodeSelectPathAsync() {\n  return spawnForString('/usr/bin/xcode-select --print-path');\n}\nfunction getXcodeInstalled() {\n  return spawnForString('ls /Applications/Xcode.app/Contents/Developer');\n}\n\nexport class XcodePrerequisite extends Prerequisite {\n  static instance = new XcodePrerequisite();\n\n  /**\n   * Ensure Xcode is installed and recent enough to be used with Expo.\n   */\n  async assertImplementation(): Promise<void> {\n    const version = await profile(getXcodeVersionAsync)({ force: process.env.NODE_ENV === 'test' });\n    debug(`Xcode version: ${version}`);\n    if (!version) {\n      // A couple different issues could have occurred, let's check them after we're past the point of no return\n      // since we no longer need to be fast about validation.\n\n      // Ensure Xcode.app can be found before we prompt to sudo select it.\n      if (getXcodeInstalled()) {\n        const selectPath = profile(getXcodeSelectPathAsync)();\n        debug(`Xcode select path: ${selectPath}`);\n        if (!selectPath) {\n          Log.error(\n            [\n              '',\n              chalk.bold('Xcode has not been fully setup for Apple development yet.'),\n              'Download at: https://developer.apple.com/xcode/',\n              'or in the App Store.',\n              '',\n              'After downloading Xcode, run the following two commands in your terminal:',\n              chalk.cyan('  sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer'),\n              chalk.cyan('  sudo xcodebuild -runFirstLaunch'),\n              '',\n              'Then you can re-run Expo CLI. Alternatively, you can build apps in the cloud with EAS CLI, or preview using the Expo Go app on a physical device.',\n              '',\n            ].join('\\n')\n          );\n          throw new AbortCommandError();\n        } else {\n          debug(`Unexpected Xcode setup (version: ${version}, select: ${selectPath})`);\n        }\n      }\n\n      // Almost certainly Xcode isn't installed.\n      await promptToOpenAppStoreAsync(\n        `Xcode must be fully installed before you can continue. Continue to the App Store?`\n      );\n      throw new AbortCommandError();\n    }\n\n    if (semver.lt(version, SUGGESTED_XCODE_VERSION)) {\n      // Xcode version is too old.\n      await promptToOpenAppStoreAsync(\n        `Xcode (${version}) needs to be updated to at least version ${MIN_XCODE_VERSION}. Continue to the App Store?`\n      );\n      throw new AbortCommandError();\n    }\n  }\n}\n"], "names": ["getXcodeVersionAsync", "XcodePrerequisite", "debug", "require", "MIN_XCODE_VERSION", "APP_STORE_ID", "SUGGESTED_XCODE_VERSION", "promptToOpenAppStoreAsync", "message", "confirm", "<PERSON><PERSON><PERSON>", "initial", "Log", "log", "openAppStore", "_xcodeVersionPromise", "silent", "force", "logError", "warn", "getVersion", "stdout", "spawnAsync", "last", "match", "version", "semver", "valid", "error", "value", "result", "appId", "link", "getAppStoreLink", "execSync", "stdio", "process", "platform", "spawnForString", "cmd", "toString", "trim", "getXcodeSelectPathAsync", "getXcodeInstalled", "Prerequisite", "instance", "assertImplementation", "profile", "env", "NODE_ENV", "selectPath", "chalk", "bold", "cyan", "join", "AbortCommandError", "lt"], "mappings": "AAAA;;;;;;;;;;;IA+BaA,oBAAoB,MAApBA,oBAAoB;IAgFpBC,iBAAiB,MAAjBA,iBAAiB;;;8DA/GP,mBAAmB;;;;;;;8DACxB,OAAO;;;;;;;yBACA,eAAe;;;;;;;8DACrB,QAAQ;;;;;;2DAEN,cAAc;wBACD,uBAAuB;yBACjC,wBAAwB;yBACnB,wBAAwB;8BACxB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,4DAA4D;AAC5D,uEAAuE;AACvE,MAAMC,iBAAiB,GAAG,MAAM,AAAC;AACjC,MAAMC,YAAY,GAAG,WAAW,AAAC;AAEjC,MAAMC,uBAAuB,GAAG,CAAC,EAAEF,iBAAiB,CAAC,EAAE,CAAC,AAAC;AAEzD,MAAMG,yBAAyB,GAAG,OAAOC,OAAe,GAAK;IAC3D,0KAA0K;IAC1K,MAAMC,OAAO,GAAG,MAAMC,IAAAA,QAAY,aAAA,EAAC;QAAEC,OAAO,EAAE,IAAI;QAAEH,OAAO;KAAE,CAAC,AAAC;IAC/D,IAAIC,OAAO,EAAE;QACXG,IAAG,CAACC,GAAG,CAAC,CAAC,2EAA2E,CAAC,CAAC,CAAC;QACvFC,YAAY,CAACT,YAAY,CAAC,CAAC;IAC7B,CAAC;AACH,CAAC,AAAC;AAEF,IAAIU,oBAAoB,GAAqE,IAAI,AAAC;AAE3F,MAAMf,oBAAoB,GAAG,OAAO,EACzCgB,MAAM,CAAA,EACNC,KAAK,CAAA,EACiC,GAAG,EAAE,GAAqC;IAChF,MAAMC,QAAQ,GAAGF,MAAM,GAAGd,KAAK,GAAGU,IAAG,CAACO,IAAI,AAAC;IAC3C,MAAMC,UAAU,GAAG,UAAuE;QACxF,IAAI;gBAEWC,GAAiC;YAD9C,MAAM,EAAEA,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,YAAY,EAAE;gBAAC,UAAU;aAAC,CAAC,AAAC;YAChE,MAAMC,IAAI,GAAGF,CAAAA,GAAiC,GAAjCA,MAAM,CAACG,KAAK,qBAAqB,SAAK,GAAtCH,KAAAA,CAAsC,GAAtCA,GAAiC,AAAE,CAAC,CAAC,CAAC,AAAC;YACpD,6BAA6B;YAC7B,IAAIE,IAAI,EAAE;gBACR,MAAME,OAAO,GAAG,CAAC,EAAEF,IAAI,CAAC,EAAE,CAAC,AAAC;gBAE5B,IAAI,CAACG,OAAM,EAAA,QAAA,CAACC,KAAK,CAACF,OAAO,CAAC,EAAE;oBAC1B,2FAA2F;oBAC3F,OAAO;wBAAEG,KAAK,EAAE,CAAC,uCAAuC,EAAEH,OAAO,CAAC,CAAC;wBAAEI,KAAK,EAAE,KAAK;qBAAE,CAAC;gBACtF,CAAC;gBACD,OAAO;oBAAEA,KAAK,EAAEJ,OAAO;iBAAE,CAAC;YAC5B,CAAC;YAED,2BAA2B;YAC3B,OAAO;gBACLG,KAAK,EACH,0FAA0F;gBAC5FC,KAAK,EAAE,IAAI;aACZ,CAAC;QACJ,EAAE,OAAM;QACN,gBAAgB;QAClB,CAAC;QACD,OAAO;YAAEA,KAAK,EAAE,IAAI;SAAE,CAAC;IACzB,CAAC,AAAC;IAEF,IAAIZ,KAAK,EAAE;QACTF,oBAAoB,GAAG,IAAI,CAAC;IAC9B,CAAC;IAEDA,oBAAoB,GAAGA,oBAAoB,IAAIK,UAAU,EAAE,CAAC;IAE5D,MAAMU,MAAM,GAAG,MAAMf,oBAAoB,AAAC;IAE1C,IAAIe,MAAM,CAACF,KAAK,EAAE;QAChBV,QAAQ,CAACY,MAAM,CAACF,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,OAAOE,MAAM,CAACD,KAAK,CAAC;AACtB,CAAC,AAAC;AAEF;;;;CAIC,GACD,SAASf,YAAY,CAACiB,KAAa,EAAE;IACnC,MAAMC,IAAI,GAAGC,eAAe,CAACF,KAAK,CAAC,AAAC;IACpCG,IAAAA,aAAQ,EAAA,SAAA,EAAC,CAAC,KAAK,EAAEF,IAAI,CAAC,CAAC,EAAE;QAAEG,KAAK,EAAE,QAAQ;KAAE,CAAC,CAAC;AAChD,CAAC;AAED,SAASF,eAAe,CAACF,KAAa,EAAU;IAC9C,IAAIK,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;QACjC,2EAA2E;QAC3E,OAAO,CAAC,qCAAqC,EAAEN,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,CAAC,gCAAgC,EAAEA,KAAK,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,SAASO,cAAc,CAACC,GAAW,EAAiB;IAClD,IAAI;QACF,OAAOL,IAAAA,aAAQ,EAAA,SAAA,EAACK,GAAG,EAAE;YAAEJ,KAAK,EAAE,MAAM;SAAE,CAAC,CAACK,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC;IAC5D,EAAE,OAAM,CAAC,CAAC;IACV,OAAO,IAAI,CAAC;AACd,CAAC;AAED,kHAAkH,GAClH,SAASC,uBAAuB,GAAG;IACjC,OAAOJ,cAAc,CAAC,oCAAoC,CAAC,CAAC;AAC9D,CAAC;AACD,SAASK,iBAAiB,GAAG;IAC3B,OAAOL,cAAc,CAAC,+CAA+C,CAAC,CAAC;AACzE,CAAC;AAEM,MAAMrC,iBAAiB,SAAS2C,aAAY,aAAA;IACjD,OAAOC,QAAQ,GAAG,IAAI5C,iBAAiB,EAAE,CAAC;IAE1C;;GAEC,SACK6C,oBAAoB,GAAkB;QAC1C,MAAMrB,OAAO,GAAG,MAAMsB,IAAAA,QAAO,QAAA,EAAC/C,oBAAoB,CAAC,CAAC;YAAEiB,KAAK,EAAEmB,OAAO,CAACY,GAAG,CAACC,QAAQ,KAAK,MAAM;SAAE,CAAC,AAAC;QAChG/C,KAAK,CAAC,CAAC,eAAe,EAAEuB,OAAO,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAACA,OAAO,EAAE;YACZ,0GAA0G;YAC1G,uDAAuD;YAEvD,oEAAoE;YACpE,IAAIkB,iBAAiB,EAAE,EAAE;gBACvB,MAAMO,UAAU,GAAGH,IAAAA,QAAO,QAAA,EAACL,uBAAuB,CAAC,EAAE,AAAC;gBACtDxC,KAAK,CAAC,CAAC,mBAAmB,EAAEgD,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC1C,IAAI,CAACA,UAAU,EAAE;oBACftC,IAAG,CAACgB,KAAK,CACP;wBACE,EAAE;wBACFuB,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,2DAA2D,CAAC;wBACvE,iDAAiD;wBACjD,sBAAsB;wBACtB,EAAE;wBACF,2EAA2E;wBAC3ED,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,yEAAyE,CAAC;wBACrFF,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,mCAAmC,CAAC;wBAC/C,EAAE;wBACF,mJAAmJ;wBACnJ,EAAE;qBACH,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;oBACF,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;gBAChC,OAAO;oBACLrD,KAAK,CAAC,CAAC,iCAAiC,EAAEuB,OAAO,CAAC,UAAU,EAAEyB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;YAED,0CAA0C;YAC1C,MAAM3C,yBAAyB,CAC7B,CAAC,iFAAiF,CAAC,CACpF,CAAC;YACF,MAAM,IAAIgD,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QAED,IAAI7B,OAAM,EAAA,QAAA,CAAC8B,EAAE,CAAC/B,OAAO,EAAEnB,uBAAuB,CAAC,EAAE;YAC/C,4BAA4B;YAC5B,MAAMC,yBAAyB,CAC7B,CAAC,OAAO,EAAEkB,OAAO,CAAC,0CAA0C,EAAErB,iBAAiB,CAAC,4BAA4B,CAAC,CAC9G,CAAC;YACF,MAAM,IAAImD,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;IACH;CACD"}