{"version": 3, "sources": ["../../../../src/utils/telemetry/index.ts"], "sourcesContent": ["import process from 'node:process';\n\nimport type { Telemetry } from './Telemetry';\nimport { commandEvent } from './events';\nimport type { TelemetryRecord } from './types';\nimport { getUserAsync } from '../../api/user/user';\nimport { env } from '../env';\n\n/** The singleton telemetry manager to use */\nlet telemetry: Telemetry | null = null;\n\nexport function getTelemetry(): Telemetry | null {\n  if (env.EXPO_NO_TELEMETRY || env.EXPO_OFFLINE) return null;\n\n  if (!telemetry) {\n    // Lazy load the telemetry client, only when enabled\n    const { Telemetry } = require('./Telemetry') as typeof import('./Telemetry');\n    telemetry = new Telemetry();\n\n    // Flush any pending events on exit\n    process.once('SIGINT', () => telemetry?.flushOnExit());\n    process.once('SIGTERM', () => telemetry?.flushOnExit());\n    process.once('beforeExit', () => telemetry?.flushOnExit());\n\n    // Initialize the telemetry\n    getUserAsync()\n      .then((actor) => telemetry?.initialize({ userId: actor?.id ?? null }))\n      .catch(() => telemetry?.initialize({ userId: null }));\n  }\n\n  return telemetry;\n}\n\n/**\n * Record a single telemetry event, or multiple in a single batch.\n * The event does not need to be awaited, its:\n *   - Not sent when using `EXPO_NO_TELEMETRY` or `EXPO_OFFLINE`, and returns `null`\n *   - Sent immediately for long running commands, returns the `fetch` promise\n *   - Queued and sent in background, returns `undefined`\n */\nexport function record(records: TelemetryRecord | TelemetryRecord[]) {\n  return getTelemetry()?.record(records);\n}\n\n/**\n * Record a command invocation, and the name of the command.\n * This can be disabled with the $EXPO_NO_TELEMETRY environment variable.\n * We do this to determine how well deprecations are going before remove a command.\n */\nexport function recordCommand(command: string) {\n  if (isLongRunningCommand(command)) {\n    getTelemetry()?.setStrategy('instant');\n  }\n\n  return record(commandEvent(command));\n}\n\n/** Determine if the command is a long-running command, based on the command name */\nfunction isLongRunningCommand(command: string) {\n  return command === 'start' || command.startsWith('run') || command.startsWith('export');\n}\n"], "names": ["getTelemetry", "record", "recordCommand", "telemetry", "env", "EXPO_NO_TELEMETRY", "EXPO_OFFLINE", "Telemetry", "require", "process", "once", "flushOnExit", "getUserAsync", "then", "actor", "initialize", "userId", "id", "catch", "records", "command", "isLongRunningCommand", "setStrategy", "commandEvent", "startsWith"], "mappings": "AAAA;;;;;;;;;;;IAWgBA,YAAY,MAAZA,YAAY;IA6BZC,MAAM,MAANA,MAAM;IASNC,aAAa,MAAbA,aAAa;;;8DAjDT,cAAc;;;;;;wBAGL,UAAU;sBAEV,qBAAqB;qBAC9B,QAAQ;;;;;;AAE5B,2CAA2C,GAC3C,IAAIC,SAAS,GAAqB,IAAI,AAAC;AAEhC,SAASH,YAAY,GAAqB;IAC/C,IAAII,IAAG,IAAA,CAACC,iBAAiB,IAAID,IAAG,IAAA,CAACE,YAAY,EAAE,OAAO,IAAI,CAAC;IAE3D,IAAI,CAACH,SAAS,EAAE;QACd,oDAAoD;QACpD,MAAM,EAAEI,SAAS,CAAA,EAAE,GAAGC,OAAO,CAAC,aAAa,CAAC,AAAgC,AAAC;QAC7EL,SAAS,GAAG,IAAII,SAAS,EAAE,CAAC;QAE5B,mCAAmC;QACnCE,YAAO,EAAA,QAAA,CAACC,IAAI,CAAC,QAAQ,EAAE;YAAMP,OAAAA,SAAS,QAAa,GAAtBA,KAAAA,CAAsB,GAAtBA,SAAS,CAAEQ,WAAW,EAAE,CAAA;SAAA,CAAC,CAAC;QACvDF,YAAO,EAAA,QAAA,CAACC,IAAI,CAAC,SAAS,EAAE;YAAMP,OAAAA,SAAS,QAAa,GAAtBA,KAAAA,CAAsB,GAAtBA,SAAS,CAAEQ,WAAW,EAAE,CAAA;SAAA,CAAC,CAAC;QACxDF,YAAO,EAAA,QAAA,CAACC,IAAI,CAAC,YAAY,EAAE;YAAMP,OAAAA,SAAS,QAAa,GAAtBA,KAAAA,CAAsB,GAAtBA,SAAS,CAAEQ,WAAW,EAAE,CAAA;SAAA,CAAC,CAAC;QAE3D,2BAA2B;QAC3BC,IAAAA,KAAY,aAAA,GAAE,CACXC,IAAI,CAAC,CAACC,KAAK;YAAKX,OAAAA,SAAS,QAAY,GAArBA,KAAAA,CAAqB,GAArBA,SAAS,CAAEY,UAAU,CAAC;gBAAEC,MAAM,EAAEF,CAAAA,KAAK,QAAI,GAATA,KAAAA,CAAS,GAATA,KAAK,CAAEG,EAAE,CAAA,IAAI,IAAI;aAAE,CAAC,CAAA;SAAA,CAAC,CACrEC,KAAK,CAAC;YAAMf,OAAAA,SAAS,QAAY,GAArBA,KAAAA,CAAqB,GAArBA,SAAS,CAAEY,UAAU,CAAC;gBAAEC,MAAM,EAAE,IAAI;aAAE,CAAC,CAAA;SAAA,CAAC,CAAC;IAC1D,CAAC;IAED,OAAOb,SAAS,CAAC;AACnB,CAAC;AASM,SAASF,MAAM,CAACkB,OAA4C,EAAE;QAC5DnB,GAAc;IAArB,OAAOA,CAAAA,GAAc,GAAdA,YAAY,EAAE,SAAQ,GAAtBA,KAAAA,CAAsB,GAAtBA,GAAc,CAAEC,MAAM,CAACkB,OAAO,CAAC,CAAC;AACzC,CAAC;AAOM,SAASjB,aAAa,CAACkB,OAAe,EAAE;IAC7C,IAAIC,oBAAoB,CAACD,OAAO,CAAC,EAAE;YACjCpB,GAAc;QAAdA,CAAAA,GAAc,GAAdA,YAAY,EAAE,SAAa,GAA3BA,KAAAA,CAA2B,GAA3BA,GAAc,CAAEsB,WAAW,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAED,OAAOrB,MAAM,CAACsB,IAAAA,OAAY,aAAA,EAACH,OAAO,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,kFAAkF,GAClF,SAASC,oBAAoB,CAACD,OAAe,EAAE;IAC7C,OAAOA,OAAO,KAAK,OAAO,IAAIA,OAAO,CAACI,UAAU,CAAC,KAAK,CAAC,IAAIJ,OAAO,CAACI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC1F,CAAC"}