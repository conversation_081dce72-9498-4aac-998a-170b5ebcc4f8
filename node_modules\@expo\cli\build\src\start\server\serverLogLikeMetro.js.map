{"version": 3, "sources": ["../../../../src/start/server/serverLogLikeMetro.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { INTERNAL_CALLSITES_REGEX } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport path from 'path';\n// @ts-expect-error\nimport { mapSourcePosition } from 'source-map-support';\nimport * as stackTraceParser from 'stacktrace-parser';\n\nimport { parseErrorStack } from './metro/log-box/LogBoxSymbolication';\nimport { env } from '../../utils/env';\nimport { memoize } from '../../utils/fn';\n\nconst groupStack: any = [];\nlet collapsedGuardTimer: ReturnType<typeof setTimeout> | undefined;\n\nexport function logLikeMetro(\n  originalLogFunction: (...args: any[]) => void,\n  level: string,\n  platform: string,\n  ...data: any[]\n) {\n  // @ts-expect-error\n  const logFunction = console[level] && level !== 'trace' ? level : 'log';\n  const color =\n    level === 'error'\n      ? chalk.inverse.red\n      : level === 'warn'\n        ? chalk.inverse.yellow\n        : chalk.inverse.white;\n\n  if (level === 'group') {\n    groupStack.push(level);\n  } else if (level === 'groupCollapsed') {\n    groupStack.push(level);\n    clearTimeout(collapsedGuardTimer);\n    // Inform users that logs get swallowed if they forget to call `groupEnd`.\n    collapsedGuardTimer = setTimeout(() => {\n      if (groupStack.includes('groupCollapsed')) {\n        originalLogFunction(\n          chalk.inverse.yellow.bold(' WARN '),\n          'Expected `console.groupEnd` to be called after `console.groupCollapsed`.'\n        );\n        groupStack.length = 0;\n      }\n    }, 3000);\n    return;\n  } else if (level === 'groupEnd') {\n    groupStack.pop();\n    if (!groupStack.length) {\n      clearTimeout(collapsedGuardTimer);\n    }\n    return;\n  }\n\n  if (!groupStack.includes('groupCollapsed')) {\n    // Remove excess whitespace at the end of a log message, if possible.\n    const lastItem = data[data.length - 1];\n    if (typeof lastItem === 'string') {\n      data[data.length - 1] = lastItem.trimEnd();\n    }\n\n    const modePrefix = chalk.bold`${platform}`;\n    originalLogFunction(\n      modePrefix +\n        ' ' +\n        color.bold(` ${logFunction.toUpperCase()} `) +\n        ''.padEnd(groupStack.length * 2, ' '),\n      ...data\n    );\n  }\n}\n\nconst escapedPathSep = path.sep === '\\\\' ? '\\\\\\\\' : path.sep;\nconst SERVER_STACK_MATCHER = new RegExp(\n  `${escapedPathSep}(react-dom|metro-runtime|expo-router)${escapedPathSep}`\n);\n\nfunction augmentLogsInternal(projectRoot: string) {\n  const augmentLog = (name: string, fn: typeof console.log) => {\n    // @ts-expect-error: TypeScript doesn't know about polyfilled functions.\n    if (fn.__polyfilled) {\n      return fn;\n    }\n    const originalFn = fn.bind(console);\n    function logWithStack(...args: any[]) {\n      const stack = new Error().stack;\n      // Check if the log originates from the server.\n      const isServerLog = !!stack?.match(SERVER_STACK_MATCHER);\n\n      if (isServerLog) {\n        if (name === 'error' || name === 'warn') {\n          if (\n            args.length === 2 &&\n            typeof args[1] === 'string' &&\n            args[1].trim().startsWith('at ')\n          ) {\n            // react-dom custom stacks which are always broken.\n            // A stack string like:\n            //    at div\n            //    at http://localhost:8081/node_modules/expo-router/node/render.bundle?platform=web&dev=true&hot=false&transform.engine=hermes&transform.routerRoot=app&resolver.environment=node&transform.environment=node:38008:27\n            //    at Background (http://localhost:8081/node_modules/expo-router/node/render.bundle?platform=web&dev=true&hot=false&transform.engine=hermes&transform.routerRoot=app&resolver.environment=node&transform.environment=node:151009:7)\n            const customStack = args[1];\n\n            try {\n              const parsedStack = parseErrorStack(customStack);\n              const symbolicatedStack = parsedStack.map((line: any) => {\n                const mapped = mapSourcePosition({\n                  source: line.file,\n                  line: line.lineNumber,\n                  column: line.column,\n                }) as {\n                  // '/Users/<USER>/Documents/GitHub/lab/sdk51-beta/node_modules/react-native-web/dist/exports/View/index.js',\n                  source: string;\n                  line: number;\n                  column: number;\n                  // 'hrefAttrs'\n                  name: string | null;\n                };\n\n                const fallbackName = mapped.name ?? '<unknown>';\n                return {\n                  file: mapped.source,\n                  lineNumber: mapped.line,\n                  column: mapped.column,\n                  // Attempt to preserve the react component name if possible.\n                  methodName: line.methodName\n                    ? line.methodName === '<unknown>'\n                      ? fallbackName\n                      : line.methodName\n                    : fallbackName,\n                  arguments: line.arguments ?? [],\n                };\n              });\n\n              // Replace args[1] with the formatted stack.\n              args[1] = '\\n' + formatParsedStackLikeMetro(projectRoot, symbolicatedStack, true);\n            } catch {\n              // If symbolication fails, log the original stack.\n              args.push('\\n' + formatStackLikeMetro(projectRoot, customStack));\n            }\n          } else {\n            args.push('\\n' + formatStackLikeMetro(projectRoot, stack!));\n          }\n        }\n\n        logLikeMetro(originalFn, name, 'λ', ...args);\n      } else {\n        originalFn(...args);\n      }\n    }\n    logWithStack.__polyfilled = true;\n    return logWithStack;\n  };\n\n  ['trace', 'info', 'error', 'warn', 'log', 'group', 'groupCollapsed', 'groupEnd', 'debug'].forEach(\n    (name) => {\n      // @ts-expect-error\n      console[name] = augmentLog(name, console[name]);\n    }\n  );\n}\n\nexport function formatStackLikeMetro(projectRoot: string, stack: string) {\n  // Remove `Error: ` from the beginning of the stack trace.\n  // Dim traces that match `INTERNAL_CALLSITES_REGEX`\n\n  const stackTrace = stackTraceParser.parse(stack);\n  return formatParsedStackLikeMetro(projectRoot, stackTrace);\n}\n\nfunction formatParsedStackLikeMetro(\n  projectRoot: string,\n  stackTrace: stackTraceParser.StackFrame[],\n  isComponentStack = false\n) {\n  // Remove `Error: ` from the beginning of the stack trace.\n  // Dim traces that match `INTERNAL_CALLSITES_REGEX`\n\n  return stackTrace\n    .filter(\n      (line) =>\n        line.file &&\n        // Ignore unsymbolicated stack frames. It's not clear how this is possible but it sometimes happens when the graph changes.\n        !/^https?:\\/\\//.test(line.file) &&\n        (isComponentStack ? true : line.file !== '<anonymous>')\n    )\n    .map((line) => {\n      // Use the same regex we use in Metro config to filter out traces:\n      const isCollapsed = INTERNAL_CALLSITES_REGEX.test(line.file!);\n      if (!isComponentStack && isCollapsed && !env.EXPO_DEBUG) {\n        return null;\n      }\n      // If a file is collapsed, print it with dim styling.\n      const style = isCollapsed ? chalk.dim : chalk.gray;\n      // Use the `at` prefix to match Node.js\n      let fileName = line.file!;\n      if (fileName.startsWith(path.sep)) {\n        fileName = path.relative(projectRoot, fileName);\n      }\n      if (line.lineNumber != null) {\n        fileName += `:${line.lineNumber}`;\n        if (line.column != null) {\n          fileName += `:${line.column}`;\n        }\n      }\n\n      return style(`  ${line.methodName} (${fileName})`);\n    })\n    .filter(Boolean)\n    .join('\\n');\n}\n\n/** Augment console logs to check the stack trace and format like Metro logs if we think the log came from the SSR renderer or an API route. */\nexport const augmentLogs = memoize(augmentLogsInternal);\n"], "names": ["logLikeMetro", "formatStackLikeMetro", "augmentLogs", "groupStack", "<PERSON><PERSON><PERSON><PERSON><PERSON>r", "originalLogFunction", "level", "platform", "data", "logFunction", "console", "color", "chalk", "inverse", "red", "yellow", "white", "push", "clearTimeout", "setTimeout", "includes", "bold", "length", "pop", "lastItem", "trimEnd", "modePrefix", "toUpperCase", "padEnd", "escapedPathSep", "path", "sep", "SERVER_STACK_MATCHER", "RegExp", "augmentLogsInternal", "projectRoot", "augmentLog", "name", "fn", "__polyfilled", "originalFn", "bind", "logWithStack", "args", "stack", "Error", "isServerLog", "match", "trim", "startsWith", "customStack", "parsedStack", "parseError<PERSON>tack", "symbolicatedStack", "map", "line", "mapped", "mapSourcePosition", "source", "file", "lineNumber", "column", "fallback<PERSON><PERSON>", "methodName", "arguments", "formatParsedStackLikeMetro", "for<PERSON>ach", "stackTrace", "stackTrace<PERSON><PERSON><PERSON>", "parse", "isComponentStack", "filter", "test", "isCollapsed", "INTERNAL_CALLSITES_REGEX", "env", "EXPO_DEBUG", "style", "dim", "gray", "fileName", "relative", "Boolean", "join", "memoize"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAcgBA,YAAY,MAAZA,YAAY;IAmJZC,oBAAoB,MAApBA,oBAAoB;IAmDvBC,WAAW,MAAXA,WAAW;;;yBApNiB,oBAAoB;;;;;;;8DAC3C,OAAO;;;;;;;8DACR,MAAM;;;;;;;yBAEW,oBAAoB;;;;;;;+DACpB,mBAAmB;;;;;;qCAErB,qCAAqC;qBACjD,iBAAiB;oBACb,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMC,UAAU,GAAQ,EAAE,AAAC;AAC3B,IAAIC,mBAAmB,AAA2C,AAAC;AAE5D,SAASJ,YAAY,CAC1BK,mBAA6C,EAC7CC,KAAa,EACbC,QAAgB,EAChB,GAAGC,IAAI,AAAO,EACd;IACA,mBAAmB;IACnB,MAAMC,WAAW,GAAGC,OAAO,CAACJ,KAAK,CAAC,IAAIA,KAAK,KAAK,OAAO,GAAGA,KAAK,GAAG,KAAK,AAAC;IACxE,MAAMK,KAAK,GACTL,KAAK,KAAK,OAAO,GACbM,MAAK,EAAA,QAAA,CAACC,OAAO,CAACC,GAAG,GACjBR,KAAK,KAAK,MAAM,GACdM,MAAK,EAAA,QAAA,CAACC,OAAO,CAACE,MAAM,GACpBH,MAAK,EAAA,QAAA,CAACC,OAAO,CAACG,KAAK,AAAC;IAE5B,IAAIV,KAAK,KAAK,OAAO,EAAE;QACrBH,UAAU,CAACc,IAAI,CAACX,KAAK,CAAC,CAAC;IACzB,OAAO,IAAIA,KAAK,KAAK,gBAAgB,EAAE;QACrCH,UAAU,CAACc,IAAI,CAACX,KAAK,CAAC,CAAC;QACvBY,YAAY,CAACd,mBAAmB,CAAC,CAAC;QAClC,0EAA0E;QAC1EA,mBAAmB,GAAGe,UAAU,CAAC,IAAM;YACrC,IAAIhB,UAAU,CAACiB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;gBACzCf,mBAAmB,CACjBO,MAAK,EAAA,QAAA,CAACC,OAAO,CAACE,MAAM,CAACM,IAAI,CAAC,QAAQ,CAAC,EACnC,0EAA0E,CAC3E,CAAC;gBACFlB,UAAU,CAACmB,MAAM,GAAG,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,OAAO;IACT,OAAO,IAAIhB,KAAK,KAAK,UAAU,EAAE;QAC/BH,UAAU,CAACoB,GAAG,EAAE,CAAC;QACjB,IAAI,CAACpB,UAAU,CAACmB,MAAM,EAAE;YACtBJ,YAAY,CAACd,mBAAmB,CAAC,CAAC;QACpC,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAI,CAACD,UAAU,CAACiB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QAC1C,qEAAqE;QACrE,MAAMI,QAAQ,GAAGhB,IAAI,CAACA,IAAI,CAACc,MAAM,GAAG,CAAC,CAAC,AAAC;QACvC,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;YAChChB,IAAI,CAACA,IAAI,CAACc,MAAM,GAAG,CAAC,CAAC,GAAGE,QAAQ,CAACC,OAAO,EAAE,CAAC;QAC7C,CAAC;QAED,MAAMC,UAAU,GAAGd,MAAK,EAAA,QAAA,CAACS,IAAI,CAAC,EAAEd,QAAQ,CAAC,CAAC,AAAC;QAC3CF,mBAAmB,CACjBqB,UAAU,GACR,GAAG,GACHf,KAAK,CAACU,IAAI,CAAC,CAAC,CAAC,EAAEZ,WAAW,CAACkB,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,GAC5C,EAAE,CAACC,MAAM,CAACzB,UAAU,CAACmB,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,KACpCd,IAAI,CACR,CAAC;IACJ,CAAC;AACH,CAAC;AAED,MAAMqB,cAAc,GAAGC,KAAI,EAAA,QAAA,CAACC,GAAG,KAAK,IAAI,GAAG,MAAM,GAAGD,KAAI,EAAA,QAAA,CAACC,GAAG,AAAC;AAC7D,MAAMC,oBAAoB,GAAG,IAAIC,MAAM,CACrC,CAAC,EAAEJ,cAAc,CAAC,qCAAqC,EAAEA,cAAc,CAAC,CAAC,CAC1E,AAAC;AAEF,SAASK,mBAAmB,CAACC,WAAmB,EAAE;IAChD,MAAMC,UAAU,GAAG,CAACC,IAAY,EAAEC,EAAsB,GAAK;QAC3D,wEAAwE;QACxE,IAAIA,EAAE,CAACC,YAAY,EAAE;YACnB,OAAOD,EAAE,CAAC;QACZ,CAAC;QACD,MAAME,UAAU,GAAGF,EAAE,CAACG,IAAI,CAAC/B,OAAO,CAAC,AAAC;QACpC,SAASgC,YAAY,CAAC,GAAGC,IAAI,AAAO,EAAE;YACpC,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE,CAACD,KAAK,AAAC;YAChC,+CAA+C;YAC/C,MAAME,WAAW,GAAG,CAAC,EAACF,KAAK,QAAO,GAAZA,KAAAA,CAAY,GAAZA,KAAK,CAAEG,KAAK,CAACf,oBAAoB,CAAC,CAAA,AAAC;YAEzD,IAAIc,WAAW,EAAE;gBACf,IAAIT,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,MAAM,EAAE;oBACvC,IACEM,IAAI,CAACrB,MAAM,KAAK,CAAC,IACjB,OAAOqB,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC3BA,IAAI,CAAC,CAAC,CAAC,CAACK,IAAI,EAAE,CAACC,UAAU,CAAC,KAAK,CAAC,EAChC;wBACA,mDAAmD;wBACnD,uBAAuB;wBACvB,YAAY;wBACZ,yNAAyN;wBACzN,sOAAsO;wBACtO,MAAMC,WAAW,GAAGP,IAAI,CAAC,CAAC,CAAC,AAAC;wBAE5B,IAAI;4BACF,MAAMQ,WAAW,GAAGC,IAAAA,oBAAe,gBAAA,EAACF,WAAW,CAAC,AAAC;4BACjD,MAAMG,iBAAiB,GAAGF,WAAW,CAACG,GAAG,CAAC,CAACC,IAAS,GAAK;gCACvD,MAAMC,MAAM,GAAGC,IAAAA,iBAAiB,EAAA,kBAAA,EAAC;oCAC/BC,MAAM,EAAEH,IAAI,CAACI,IAAI;oCACjBJ,IAAI,EAAEA,IAAI,CAACK,UAAU;oCACrBC,MAAM,EAAEN,IAAI,CAACM,MAAM;iCACpB,CAAC,AAOD,AAAC;gCAEF,MAAMC,YAAY,GAAGN,MAAM,CAACnB,IAAI,IAAI,WAAW,AAAC;gCAChD,OAAO;oCACLsB,IAAI,EAAEH,MAAM,CAACE,MAAM;oCACnBE,UAAU,EAAEJ,MAAM,CAACD,IAAI;oCACvBM,MAAM,EAAEL,MAAM,CAACK,MAAM;oCACrB,4DAA4D;oCAC5DE,UAAU,EAAER,IAAI,CAACQ,UAAU,GACvBR,IAAI,CAACQ,UAAU,KAAK,WAAW,GAC7BD,YAAY,GACZP,IAAI,CAACQ,UAAU,GACjBD,YAAY;oCAChBE,SAAS,EAAET,IAAI,CAACS,SAAS,IAAI,EAAE;iCAChC,CAAC;4BACJ,CAAC,CAAC,AAAC;4BAEH,4CAA4C;4BAC5CrB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGsB,0BAA0B,CAAC9B,WAAW,EAAEkB,iBAAiB,EAAE,IAAI,CAAC,CAAC;wBACpF,EAAE,OAAM;4BACN,kDAAkD;4BAClDV,IAAI,CAAC1B,IAAI,CAAC,IAAI,GAAGhB,oBAAoB,CAACkC,WAAW,EAAEe,WAAW,CAAC,CAAC,CAAC;wBACnE,CAAC;oBACH,OAAO;wBACLP,IAAI,CAAC1B,IAAI,CAAC,IAAI,GAAGhB,oBAAoB,CAACkC,WAAW,EAAES,KAAK,CAAE,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;gBAED5C,YAAY,CAACwC,UAAU,EAAEH,IAAI,EAAE,GAAG,KAAKM,IAAI,CAAC,CAAC;YAC/C,OAAO;gBACLH,UAAU,IAAIG,IAAI,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QACDD,YAAY,CAACH,YAAY,GAAG,IAAI,CAAC;QACjC,OAAOG,YAAY,CAAC;IACtB,CAAC,AAAC;IAEF;QAAC,OAAO;QAAE,MAAM;QAAE,OAAO;QAAE,MAAM;QAAE,KAAK;QAAE,OAAO;QAAE,gBAAgB;QAAE,UAAU;QAAE,OAAO;KAAC,CAACwB,OAAO,CAC/F,CAAC7B,IAAI,GAAK;QACR,mBAAmB;QACnB3B,OAAO,CAAC2B,IAAI,CAAC,GAAGD,UAAU,CAACC,IAAI,EAAE3B,OAAO,CAAC2B,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC,CACF,CAAC;AACJ,CAAC;AAEM,SAASpC,oBAAoB,CAACkC,WAAmB,EAAES,KAAa,EAAE;IACvE,0DAA0D;IAC1D,mDAAmD;IAEnD,MAAMuB,UAAU,GAAGC,iBAAgB,EAAA,CAACC,KAAK,CAACzB,KAAK,CAAC,AAAC;IACjD,OAAOqB,0BAA0B,CAAC9B,WAAW,EAAEgC,UAAU,CAAC,CAAC;AAC7D,CAAC;AAED,SAASF,0BAA0B,CACjC9B,WAAmB,EACnBgC,UAAyC,EACzCG,gBAAgB,GAAG,KAAK,EACxB;IACA,0DAA0D;IAC1D,mDAAmD;IAEnD,OAAOH,UAAU,CACdI,MAAM,CACL,CAAChB,IAAI,GACHA,IAAI,CAACI,IAAI,IACT,2HAA2H;QAC3H,CAAC,eAAea,IAAI,CAACjB,IAAI,CAACI,IAAI,CAAC,IAC/B,CAACW,gBAAgB,GAAG,IAAI,GAAGf,IAAI,CAACI,IAAI,KAAK,aAAa,CAAC,CAC1D,CACAL,GAAG,CAAC,CAACC,IAAI,GAAK;QACb,kEAAkE;QAClE,MAAMkB,WAAW,GAAGC,YAAwB,EAAA,yBAAA,CAACF,IAAI,CAACjB,IAAI,CAACI,IAAI,CAAE,AAAC;QAC9D,IAAI,CAACW,gBAAgB,IAAIG,WAAW,IAAI,CAACE,IAAG,IAAA,CAACC,UAAU,EAAE;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,qDAAqD;QACrD,MAAMC,KAAK,GAAGJ,WAAW,GAAG7D,MAAK,EAAA,QAAA,CAACkE,GAAG,GAAGlE,MAAK,EAAA,QAAA,CAACmE,IAAI,AAAC;QACnD,uCAAuC;QACvC,IAAIC,QAAQ,GAAGzB,IAAI,CAACI,IAAI,AAAC,AAAC;QAC1B,IAAIqB,QAAQ,CAAC/B,UAAU,CAACnB,KAAI,EAAA,QAAA,CAACC,GAAG,CAAC,EAAE;YACjCiD,QAAQ,GAAGlD,KAAI,EAAA,QAAA,CAACmD,QAAQ,CAAC9C,WAAW,EAAE6C,QAAQ,CAAC,CAAC;QAClD,CAAC;QACD,IAAIzB,IAAI,CAACK,UAAU,IAAI,IAAI,EAAE;YAC3BoB,QAAQ,IAAI,CAAC,CAAC,EAAEzB,IAAI,CAACK,UAAU,CAAC,CAAC,CAAC;YAClC,IAAIL,IAAI,CAACM,MAAM,IAAI,IAAI,EAAE;gBACvBmB,QAAQ,IAAI,CAAC,CAAC,EAAEzB,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAOgB,KAAK,CAAC,CAAC,EAAE,EAAEtB,IAAI,CAACQ,UAAU,CAAC,EAAE,EAAEiB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CACDT,MAAM,CAACW,OAAO,CAAC,CACfC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAGM,MAAMjF,WAAW,GAAGkF,IAAAA,GAAO,QAAA,EAAClD,mBAAmB,CAAC,AAAC"}