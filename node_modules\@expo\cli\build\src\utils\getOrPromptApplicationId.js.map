{"version": 3, "sources": ["../../../src/utils/getOrPromptApplicationId.ts"], "sourcesContent": ["import { ExpoConfig, getAccountUsername, getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { memoize } from './fn';\nimport { learnMore } from './link';\nimport { attemptModification } from './modifyConfigAsync';\nimport prompt, { confirmAsync } from './prompts';\nimport {\n  assertValidBundleId,\n  assertValidPackage,\n  getBundleIdWarningAsync,\n  getPackageNameWarningAsync,\n  getSanitizedBundleIdentifier,\n  getSanitizedPackage,\n  validateBundleId,\n  validatePackage,\n  validatePackageWithWarning,\n} from './validateApplicationId';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:app-id') as typeof console.log;\n\nfunction getExpoUsername(exp: ExpoConfig) {\n  // Account for if the environment variable was an empty string.\n  return getAccountUsername(exp) || 'anonymous';\n}\n\nconst NO_BUNDLE_ID_MESSAGE = `Project must have a \\`ios.bundleIdentifier\\` set in the Expo config (app.json or app.config.js).`;\n\nconst NO_PACKAGE_MESSAGE = `Project must have a \\`android.package\\` set in the Expo config (app.json or app.config.js).`;\n\n/**\n * Get the bundle identifier from the Expo config or prompt the user to choose a new bundle identifier.\n * Prompted value will be validated against the App Store and a local regex.\n * If the project Expo config is a static JSON file, the bundle identifier will be updated in the config automatically.\n */\nexport async function getOrPromptForBundleIdentifier(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.ios?.bundleIdentifier;\n  if (current) {\n    assertValidBundleId(current);\n    return current;\n  }\n\n  return promptForBundleIdWithInitialAsync(projectRoot, exp, getRecommendedBundleId(exp));\n}\n\nconst memoLog = memoize(Log.log);\n\nasync function promptForBundleIdWithInitialAsync(\n  projectRoot: string,\n  exp: ExpoConfig,\n  bundleIdentifier?: string\n): Promise<string> {\n  if (!bundleIdentifier) {\n    memoLog(\n      chalk`\\n{bold 📝  iOS Bundle Identifier} {dim ${learnMore(\n        'https://expo.fyi/bundle-identifier'\n      )}}\\n`\n    );\n\n    // Prompt the user for the bundle ID.\n    // Even if the project is using a dynamic config we can still\n    // prompt a better error message, recommend a default value, and help the user\n    // validate their custom bundle ID upfront.\n    const { input } = await prompt(\n      {\n        type: 'text',\n        name: 'input',\n        // The Apple helps people know this isn't an EAS feature.\n        message: `What would you like your iOS bundle identifier to be?`,\n        validate: validateBundleId,\n      },\n      {\n        nonInteractiveHelp: NO_BUNDLE_ID_MESSAGE,\n      }\n    );\n    bundleIdentifier = input as string;\n  }\n\n  // Warn the user if the bundle ID is already in use.\n  const warning = await getBundleIdWarningAsync(bundleIdentifier);\n\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Bundle ID prompt to try again.\n    return await promptForBundleIdWithInitialAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  if (\n    await attemptModification(\n      projectRoot,\n      {\n        ios: { ...(exp.ios || {}), bundleIdentifier },\n      },\n      { ios: { bundleIdentifier } }\n    )\n  ) {\n    Log.log(chalk.gray`\\u203A Apple bundle identifier: ${bundleIdentifier}`);\n  }\n\n  return bundleIdentifier;\n}\n\nasync function warnAndConfirmAsync(warning: string): Promise<boolean> {\n  Log.log();\n  Log.warn(warning);\n  Log.log();\n  if (\n    !(await confirmAsync({\n      message: `Continue?`,\n      initial: true,\n    }))\n  ) {\n    return false;\n  }\n  return true;\n}\n\n// Recommend a bundle identifier based on the username and project slug.\nfunction getRecommendedBundleId(exp: ExpoConfig): string | undefined {\n  const possibleIdFromAndroid = exp.android?.package\n    ? getSanitizedBundleIdentifier(exp.android.package)\n    : undefined;\n  // Attempt to use the android package name first since it's convenient to have them aligned.\n  if (possibleIdFromAndroid && validateBundleId(possibleIdFromAndroid)) {\n    return possibleIdFromAndroid;\n  } else {\n    const username = getExpoUsername(exp);\n    const possibleId = getSanitizedBundleIdentifier(`com.${username}.${exp.slug}`);\n    if (validateBundleId(possibleId)) {\n      return possibleId;\n    }\n  }\n\n  return undefined;\n}\n\n// Recommend a package name based on the username and project slug.\nfunction getRecommendedPackageName(exp: ExpoConfig): string | undefined {\n  const possibleIdFromApple = exp.ios?.bundleIdentifier\n    ? getSanitizedPackage(exp.ios.bundleIdentifier)\n    : undefined;\n\n  // Attempt to use the ios bundle id first since it's convenient to have them aligned.\n  if (possibleIdFromApple && validatePackage(possibleIdFromApple)) {\n    return possibleIdFromApple;\n  } else {\n    const username = getExpoUsername(exp);\n    const possibleId = getSanitizedPackage(`com.${username}.${exp.slug}`);\n    if (validatePackage(possibleId)) {\n      return possibleId;\n    } else {\n      debug(\n        `Recommended package name is invalid: \"${possibleId}\" (username: ${username}, slug: ${exp.slug})`\n      );\n    }\n  }\n  return undefined;\n}\n\n/**\n * Get the package name from the Expo config or prompt the user to choose a new package name.\n * Prompted value will be validated against the Play Store and a local regex.\n * If the project Expo config is a static JSON file, the package name will be updated in the config automatically.\n */\nexport async function getOrPromptForPackage(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.android?.package;\n  if (current) {\n    assertValidPackage(current);\n    return current;\n  }\n\n  return await promptForPackageAsync(projectRoot, exp);\n}\n\nfunction promptForPackageAsync(projectRoot: string, exp: ExpoConfig): Promise<string> {\n  return promptForPackageWithInitialAsync(projectRoot, exp, getRecommendedPackageName(exp));\n}\n\nasync function promptForPackageWithInitialAsync(\n  projectRoot: string,\n  exp: ExpoConfig,\n  packageName?: string\n): Promise<string> {\n  if (!packageName) {\n    memoLog(\n      chalk`\\n{bold 📝  Android package} {dim ${learnMore('https://expo.fyi/android-package')}}\\n`\n    );\n\n    // Prompt the user for the android package.\n    // Even if the project is using a dynamic config we can still\n    // prompt a better error message, recommend a default value, and help the user\n    // validate their custom android package upfront.\n    const { input } = await prompt(\n      {\n        type: 'text',\n        name: 'input',\n        message: `What would you like your Android package name to be?`,\n        validate: validatePackageWithWarning,\n      },\n      {\n        nonInteractiveHelp: NO_PACKAGE_MESSAGE,\n      }\n    );\n    packageName = input as string;\n  }\n\n  // Warn the user if the package name is already in use.\n  const warning = await getPackageNameWarningAsync(packageName);\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Package name prompt to try again.\n    return promptForPackageWithInitialAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  if (\n    await attemptModification(\n      projectRoot,\n      {\n        android: { ...(exp.android || {}), package: packageName },\n      },\n      {\n        android: { package: packageName },\n      }\n    )\n  ) {\n    Log.log(chalk.gray`\\u203A Android package name: ${packageName}`);\n  }\n\n  return packageName;\n}\n"], "names": ["getOrPromptForBundleIdentifier", "getOrPromptForPackage", "debug", "require", "getExpoUsername", "exp", "getAccountUsername", "NO_BUNDLE_ID_MESSAGE", "NO_PACKAGE_MESSAGE", "projectRoot", "getConfig", "current", "ios", "bundleIdentifier", "assertValidBundleId", "promptForBundleIdWithInitialAsync", "getRecommendedBundleId", "memoLog", "memoize", "Log", "log", "chalk", "learnMore", "input", "prompt", "type", "name", "message", "validate", "validateBundleId", "nonInteractiveHelp", "warning", "getBundleIdWarningAsync", "warnAndConfirmAsync", "attemptModification", "gray", "warn", "<PERSON><PERSON><PERSON>", "initial", "possibleIdFromAndroid", "android", "package", "getSanitizedBundleIdentifier", "undefined", "username", "possibleId", "slug", "getRecommendedPackageName", "possibleIdFromApple", "getSanitizedPackage", "validatePackage", "assertValidPackage", "promptForPackageAsync", "promptForPackageWithInitialAsync", "packageName", "validatePackageWithWarning", "getPackageNameWarningAsync"], "mappings": "AAAA;;;;;;;;;;;IAoCsBA,8BAA8B,MAA9BA,8BAA8B;IAoI9BC,qBAAqB,MAArBA,qBAAqB;;;yBAxKe,cAAc;;;;;;;8DACtD,OAAO;;;;;;oBAED,MAAM;sBACJ,QAAQ;mCACE,qBAAqB;+DACpB,WAAW;uCAWzC,yBAAyB;2DACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,AAAsB,AAAC;AAEpE,SAASC,eAAe,CAACC,GAAe,EAAE;IACxC,+DAA+D;IAC/D,OAAOC,IAAAA,OAAkB,EAAA,mBAAA,EAACD,GAAG,CAAC,IAAI,WAAW,CAAC;AAChD,CAAC;AAED,MAAME,oBAAoB,GAAG,CAAC,gGAAgG,CAAC,AAAC;AAEhI,MAAMC,kBAAkB,GAAG,CAAC,2FAA2F,CAAC,AAAC;AAOlH,eAAeR,8BAA8B,CAClDS,WAAmB,EACnBJ,GAAe,GAAGK,IAAAA,OAAS,EAAA,UAAA,EAACD,WAAW,CAAC,CAACJ,GAAG,EAC3B;QACDA,GAAO;IAAvB,MAAMM,OAAO,GAAGN,CAAAA,GAAO,GAAPA,GAAG,CAACO,GAAG,SAAkB,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAO,CAAEQ,gBAAgB,AAAC;IAC1C,IAAIF,OAAO,EAAE;QACXG,IAAAA,sBAAmB,oBAAA,EAACH,OAAO,CAAC,CAAC;QAC7B,OAAOA,OAAO,CAAC;IACjB,CAAC;IAED,OAAOI,iCAAiC,CAACN,WAAW,EAAEJ,GAAG,EAAEW,sBAAsB,CAACX,GAAG,CAAC,CAAC,CAAC;AAC1F,CAAC;AAED,MAAMY,OAAO,GAAGC,IAAAA,GAAO,QAAA,EAACC,IAAG,CAACC,GAAG,CAAC,AAAC;AAEjC,eAAeL,iCAAiC,CAC9CN,WAAmB,EACnBJ,GAAe,EACfQ,gBAAyB,EACR;IACjB,IAAI,CAACA,gBAAgB,EAAE;QACrBI,OAAO,CACLI,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,uCAAuC,EAAEC,IAAAA,KAAS,UAAA,EACtD,oCAAoC,CACrC,CAAC,GAAG,CAAC,CACP,CAAC;QAEF,qCAAqC;QACrC,6DAA6D;QAC7D,8EAA8E;QAC9E,2CAA2C;QAC3C,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAM,QAAA,EAC5B;YACEC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,OAAO;YACb,yDAAyD;YACzDC,OAAO,EAAE,CAAC,qDAAqD,CAAC;YAChEC,QAAQ,EAAEC,sBAAgB,iBAAA;SAC3B,EACD;YACEC,kBAAkB,EAAEvB,oBAAoB;SACzC,CACF,AAAC;QACFM,gBAAgB,GAAGU,KAAK,AAAU,CAAC;IACrC,CAAC;IAED,oDAAoD;IACpD,MAAMQ,OAAO,GAAG,MAAMC,IAAAA,sBAAuB,wBAAA,EAACnB,gBAAgB,CAAC,AAAC;IAEhE,IAAIkB,OAAO,IAAI,CAAE,MAAME,mBAAmB,CAACF,OAAO,CAAC,AAAC,EAAE;QACpD,2CAA2C;QAC3C,OAAO,MAAMhB,iCAAiC,CAACN,WAAW,EAAEJ,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,mCAAmC;IACnC,IACE,MAAM6B,IAAAA,kBAAmB,oBAAA,EACvBzB,WAAW,EACX;QACEG,GAAG,EAAE;YAAE,GAAIP,GAAG,CAACO,GAAG,IAAI,EAAE;YAAGC,gBAAgB;SAAE;KAC9C,EACD;QAAED,GAAG,EAAE;YAAEC,gBAAgB;SAAE;KAAE,CAC9B,EACD;QACAM,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,gCAAgC,EAAEtB,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,OAAOA,gBAAgB,CAAC;AAC1B,CAAC;AAED,eAAeoB,mBAAmB,CAACF,OAAe,EAAoB;IACpEZ,IAAG,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,CAACiB,IAAI,CAACL,OAAO,CAAC,CAAC;IAClBZ,IAAG,CAACC,GAAG,EAAE,CAAC;IACV,IACE,CAAE,MAAMiB,IAAAA,QAAY,aAAA,EAAC;QACnBV,OAAO,EAAE,CAAC,SAAS,CAAC;QACpBW,OAAO,EAAE,IAAI;KACd,CAAC,AAAC,EACH;QACA,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,wEAAwE;AACxE,SAAStB,sBAAsB,CAACX,GAAe,EAAsB;QACrCA,GAAW;IAAzC,MAAMkC,qBAAqB,GAAGlC,CAAAA,CAAAA,GAAW,GAAXA,GAAG,CAACmC,OAAO,SAAS,GAApBnC,KAAAA,CAAoB,GAApBA,GAAW,CAAEoC,OAAO,CAAA,GAC9CC,IAAAA,sBAA4B,6BAAA,EAACrC,GAAG,CAACmC,OAAO,CAACC,OAAO,CAAC,GACjDE,SAAS,AAAC;IACd,4FAA4F;IAC5F,IAAIJ,qBAAqB,IAAIV,IAAAA,sBAAgB,iBAAA,EAACU,qBAAqB,CAAC,EAAE;QACpE,OAAOA,qBAAqB,CAAC;IAC/B,OAAO;QACL,MAAMK,QAAQ,GAAGxC,eAAe,CAACC,GAAG,CAAC,AAAC;QACtC,MAAMwC,UAAU,GAAGH,IAAAA,sBAA4B,6BAAA,EAAC,CAAC,IAAI,EAAEE,QAAQ,CAAC,CAAC,EAAEvC,GAAG,CAACyC,IAAI,CAAC,CAAC,CAAC,AAAC;QAC/E,IAAIjB,IAAAA,sBAAgB,iBAAA,EAACgB,UAAU,CAAC,EAAE;YAChC,OAAOA,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAED,OAAOF,SAAS,CAAC;AACnB,CAAC;AAED,mEAAmE;AACnE,SAASI,yBAAyB,CAAC1C,GAAe,EAAsB;QAC1CA,GAAO;IAAnC,MAAM2C,mBAAmB,GAAG3C,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACO,GAAG,SAAkB,GAAzBP,KAAAA,CAAyB,GAAzBA,GAAO,CAAEQ,gBAAgB,CAAA,GACjDoC,IAAAA,sBAAmB,oBAAA,EAAC5C,GAAG,CAACO,GAAG,CAACC,gBAAgB,CAAC,GAC7C8B,SAAS,AAAC;IAEd,qFAAqF;IACrF,IAAIK,mBAAmB,IAAIE,IAAAA,sBAAe,gBAAA,EAACF,mBAAmB,CAAC,EAAE;QAC/D,OAAOA,mBAAmB,CAAC;IAC7B,OAAO;QACL,MAAMJ,QAAQ,GAAGxC,eAAe,CAACC,GAAG,CAAC,AAAC;QACtC,MAAMwC,UAAU,GAAGI,IAAAA,sBAAmB,oBAAA,EAAC,CAAC,IAAI,EAAEL,QAAQ,CAAC,CAAC,EAAEvC,GAAG,CAACyC,IAAI,CAAC,CAAC,CAAC,AAAC;QACtE,IAAII,IAAAA,sBAAe,gBAAA,EAACL,UAAU,CAAC,EAAE;YAC/B,OAAOA,UAAU,CAAC;QACpB,OAAO;YACL3C,KAAK,CACH,CAAC,sCAAsC,EAAE2C,UAAU,CAAC,aAAa,EAAED,QAAQ,CAAC,QAAQ,EAAEvC,GAAG,CAACyC,IAAI,CAAC,CAAC,CAAC,CAClG,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAOH,SAAS,CAAC;AACnB,CAAC;AAOM,eAAe1C,qBAAqB,CACzCQ,WAAmB,EACnBJ,GAAe,GAAGK,IAAAA,OAAS,EAAA,UAAA,EAACD,WAAW,CAAC,CAACJ,GAAG,EAC3B;QACDA,GAAW;IAA3B,MAAMM,OAAO,GAAGN,CAAAA,GAAW,GAAXA,GAAG,CAACmC,OAAO,SAAS,GAApBnC,KAAAA,CAAoB,GAApBA,GAAW,CAAEoC,OAAO,AAAC;IACrC,IAAI9B,OAAO,EAAE;QACXwC,IAAAA,sBAAkB,mBAAA,EAACxC,OAAO,CAAC,CAAC;QAC5B,OAAOA,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,MAAMyC,qBAAqB,CAAC3C,WAAW,EAAEJ,GAAG,CAAC,CAAC;AACvD,CAAC;AAED,SAAS+C,qBAAqB,CAAC3C,WAAmB,EAAEJ,GAAe,EAAmB;IACpF,OAAOgD,gCAAgC,CAAC5C,WAAW,EAAEJ,GAAG,EAAE0C,yBAAyB,CAAC1C,GAAG,CAAC,CAAC,CAAC;AAC5F,CAAC;AAED,eAAegD,gCAAgC,CAC7C5C,WAAmB,EACnBJ,GAAe,EACfiD,WAAoB,EACH;IACjB,IAAI,CAACA,WAAW,EAAE;QAChBrC,OAAO,CACLI,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,iCAAiC,EAAEC,IAAAA,KAAS,UAAA,EAAC,kCAAkC,CAAC,CAAC,GAAG,CAAC,CAC5F,CAAC;QAEF,2CAA2C;QAC3C,6DAA6D;QAC7D,8EAA8E;QAC9E,iDAAiD;QACjD,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAM,QAAA,EAC5B;YACEC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,OAAO;YACbC,OAAO,EAAE,CAAC,oDAAoD,CAAC;YAC/DC,QAAQ,EAAE2B,sBAA0B,2BAAA;SACrC,EACD;YACEzB,kBAAkB,EAAEtB,kBAAkB;SACvC,CACF,AAAC;QACF8C,WAAW,GAAG/B,KAAK,AAAU,CAAC;IAChC,CAAC;IAED,uDAAuD;IACvD,MAAMQ,OAAO,GAAG,MAAMyB,IAAAA,sBAA0B,2BAAA,EAACF,WAAW,CAAC,AAAC;IAC9D,IAAIvB,OAAO,IAAI,CAAE,MAAME,mBAAmB,CAACF,OAAO,CAAC,AAAC,EAAE;QACpD,8CAA8C;QAC9C,OAAOsB,gCAAgC,CAAC5C,WAAW,EAAEJ,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,mCAAmC;IACnC,IACE,MAAM6B,IAAAA,kBAAmB,oBAAA,EACvBzB,WAAW,EACX;QACE+B,OAAO,EAAE;YAAE,GAAInC,GAAG,CAACmC,OAAO,IAAI,EAAE;YAAGC,OAAO,EAAEa,WAAW;SAAE;KAC1D,EACD;QACEd,OAAO,EAAE;YAAEC,OAAO,EAAEa,WAAW;SAAE;KAClC,CACF,EACD;QACAnC,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,6BAA6B,EAAEmB,WAAW,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,OAAOA,WAAW,CAAC;AACrB,CAAC"}