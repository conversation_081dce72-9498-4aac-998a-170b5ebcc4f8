{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": false, "target": "esnext", "skipLibCheck": true, "resolveJsonModule": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}