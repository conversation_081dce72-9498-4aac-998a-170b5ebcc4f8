{"version": 3, "sources": ["../../../../src/start/platforms/AppIdResolver.ts"], "sourcesContent": ["import { getConfig, getProjectConfigDescriptionWithPaths } from '@expo/config';\n\nimport { CommandError, UnimplementedError } from '../../utils/errors';\nimport { get } from '../../utils/obj';\n\n/** Resolves a native app identifier (bundle identifier, package name) from the project files. */\nexport class AppIdResolver {\n  constructor(\n    protected projectRoot: string,\n    /** Platform to use. */\n    protected platform: string,\n    /** Nested key in the Expo config like `android.package` or `ios.bundleIdentifier`. */\n    protected configProperty: string\n  ) {}\n\n  /** Resolve the application ID for the project. */\n  async getAppIdAsync(): Promise<string> {\n    if (await this.hasNativeProjectAsync()) {\n      return this.getAppIdFromNativeAsync();\n    }\n    return this.getAppIdFromConfigAsync();\n  }\n\n  /** Returns `true` if the project has native project code. */\n  async hasNativeProjectAsync(): Promise<boolean> {\n    throw new UnimplementedError();\n  }\n\n  /** Return the app ID from the Expo config or assert. */\n  async getAppIdFromConfigAsync(): Promise<string> {\n    const config = getConfig(this.projectRoot);\n\n    const appId = get(config.exp, this.configProperty);\n    if (!appId) {\n      throw new CommandError(\n        'NO_APP_ID',\n        `Required property '${\n          this.configProperty\n        }' is not found in the project ${getProjectConfigDescriptionWithPaths(\n          this.projectRoot,\n          config\n        )}. This is required to open the app.`\n      );\n    }\n    return appId;\n  }\n\n  /** Return the app ID from the native project files or null if the app ID cannot be found. */\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    throw new UnimplementedError();\n  }\n\n  /** Return the app ID from the native project files or assert. */\n  async getAppIdFromNativeAsync(): Promise<string> {\n    const appId = await this.resolveAppIdFromNativeAsync();\n    if (!appId) {\n      throw new CommandError(\n        'NO_APP_ID',\n        `Failed to locate the ${this.platform} application identifier in the \"${this.platform}/\" folder. This is required to open the app.`\n      );\n    }\n    return appId;\n  }\n}\n"], "names": ["AppIdResolver", "constructor", "projectRoot", "platform", "configProperty", "getAppIdAsync", "hasNativeProjectAsync", "getAppIdFromNativeAsync", "getAppIdFromConfigAsync", "UnimplementedError", "config", "getConfig", "appId", "get", "exp", "CommandError", "getProjectConfigDescriptionWithPaths", "resolveAppIdFromNativeAsync"], "mappings": "AAAA;;;;+BAMaA,eAAa;;aAAbA,aAAa;;;yBANsC,cAAc;;;;;;wBAE7B,oBAAoB;qBACjD,iBAAiB;AAG9B,MAAMA,aAAa;IACxBC,YACYC,WAAmB,EAEnBC,QAAgB,EAEhBC,cAAsB,CAChC;QALUF,mBAAAA,WAAmB,CAAA;QAEnBC,gBAAAA,QAAgB,CAAA;QAEhBC,sBAAAA,cAAsB,CAAA;IAC/B;IAEH,gDAAgD,SAC1CC,aAAa,GAAoB;QACrC,IAAI,MAAM,IAAI,CAACC,qBAAqB,EAAE,EAAE;YACtC,OAAO,IAAI,CAACC,uBAAuB,EAAE,CAAC;QACxC,CAAC;QACD,OAAO,IAAI,CAACC,uBAAuB,EAAE,CAAC;IACxC;IAEA,2DAA2D,SACrDF,qBAAqB,GAAqB;QAC9C,MAAM,IAAIG,OAAkB,mBAAA,EAAE,CAAC;IACjC;IAEA,sDAAsD,SAChDD,uBAAuB,GAAoB;QAC/C,MAAME,MAAM,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAACT,WAAW,CAAC,AAAC;QAE3C,MAAMU,KAAK,GAAGC,IAAAA,IAAG,IAAA,EAACH,MAAM,CAACI,GAAG,EAAE,IAAI,CAACV,cAAc,CAAC,AAAC;QACnD,IAAI,CAACQ,KAAK,EAAE;YACV,MAAM,IAAIG,OAAY,aAAA,CACpB,WAAW,EACX,CAAC,mBAAmB,EAClB,IAAI,CAACX,cAAc,CACpB,8BAA8B,EAAEY,IAAAA,OAAoC,EAAA,qCAAA,EACnE,IAAI,CAACd,WAAW,EAChBQ,MAAM,CACP,CAAC,mCAAmC,CAAC,CACvC,CAAC;QACJ,CAAC;QACD,OAAOE,KAAK,CAAC;IACf;IAEA,2FAA2F,SACrFK,2BAA2B,GAA2B;QAC1D,MAAM,IAAIR,OAAkB,mBAAA,EAAE,CAAC;IACjC;IAEA,+DAA+D,SACzDF,uBAAuB,GAAoB;QAC/C,MAAMK,KAAK,GAAG,MAAM,IAAI,CAACK,2BAA2B,EAAE,AAAC;QACvD,IAAI,CAACL,KAAK,EAAE;YACV,MAAM,IAAIG,OAAY,aAAA,CACpB,WAAW,EACX,CAAC,qBAAqB,EAAE,IAAI,CAACZ,QAAQ,CAAC,gCAAgC,EAAE,IAAI,CAACA,QAAQ,CAAC,4CAA4C,CAAC,CACpI,CAAC;QACJ,CAAC;QACD,OAAOS,KAAK,CAAC;IACf;CACD"}