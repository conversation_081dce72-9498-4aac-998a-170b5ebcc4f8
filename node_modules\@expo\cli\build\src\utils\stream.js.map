{"version": 3, "sources": ["../../../src/utils/stream.ts"], "sourcesContent": ["export async function streamToStringAsync(stream: ReadableStream): Promise<string> {\n  const decoder = new TextDecoder();\n  const reader = stream.getReader();\n  const outs: string[] = [];\n  let result: ReadableStreamReadResult<unknown>;\n  do {\n    result = await reader.read();\n    if (result.value) {\n      if (!(result.value instanceof Uint8Array)) {\n        throw new Error('Unexepected buffer type');\n      }\n      outs.push(decoder.decode(result.value, { stream: true }));\n    }\n  } while (!result.done);\n  outs.push(decoder.decode());\n  return outs.join('');\n}\n"], "names": ["streamToStringAsync", "stream", "decoder", "TextDecoder", "reader", "<PERSON><PERSON><PERSON><PERSON>", "outs", "result", "read", "value", "Uint8Array", "Error", "push", "decode", "done", "join"], "mappings": "AAAA;;;;+BAAs<PERSON>,qBAAmB;;aAAnBA,mBAAmB;;AAAlC,eAAeA,mBAAmB,CAACC,MAAsB,EAAmB;IACjF,MAAMC,OAAO,GAAG,IAAIC,WAAW,EAAE,AAAC;IAClC,MAAMC,MAAM,GAAGH,MAAM,CAACI,SAAS,EAAE,AAAC;IAClC,MAAMC,IAAI,GAAa,EAAE,AAAC;IAC1B,IAAIC,MAAM,AAAmC,AAAC;IAC9C,GAAG;QACDA,MAAM,GAAG,MAAMH,MAAM,CAACI,IAAI,EAAE,CAAC;QAC7B,IAAID,MAAM,CAACE,KAAK,EAAE;YAChB,IAAI,CAAC,CAACF,MAAM,CAACE,KAAK,YAAYC,UAAU,CAAC,EAAE;gBACzC,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YACDL,IAAI,CAACM,IAAI,CAACV,OAAO,CAACW,MAAM,CAACN,MAAM,CAACE,KAAK,EAAE;gBAAER,MAAM,EAAE,IAAI;aAAE,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,QAAS,CAACM,MAAM,CAACO,IAAI,EAAE;IACvBR,IAAI,CAACM,IAAI,CAACV,OAAO,CAACW,MAAM,EAAE,CAAC,CAAC;IAC5B,OAAOP,IAAI,CAACS,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC"}