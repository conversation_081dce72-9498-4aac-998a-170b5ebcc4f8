{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/attachAtlas.ts"], "sourcesContent": ["import type { Server as ConnectServer } from 'connect';\nimport type { ConfigT as MetroConfig } from 'metro-config';\n\nimport { AtlasPrerequisite } from './AtlasPrerequisite';\nimport { env } from '../../../../utils/env';\nimport { type EnsureDependenciesOptions } from '../../../doctor/dependencies/ensureDependenciesAsync';\n\nconst debug = require('debug')('expo:metro:debugging:attachAtlas') as typeof console.log;\n\ntype AttachAtlasOptions = Pick<EnsureDependenciesOptions, 'exp'> & {\n  isExporting: boolean;\n  projectRoot: string;\n  middleware?: ConnectServer;\n  metroConfig: MetroConfig;\n  resetAtlasFile?: boolean;\n};\n\nexport async function attachAtlasAsync(\n  options: AttachAtlasOptions\n): Promise<void | ReturnType<typeof import('expo-atlas/cli').createExpoAtlasMiddleware>> {\n  if (!env.EXPO_UNSTABLE_ATLAS) {\n    return;\n  }\n\n  debug('Atlas is enabled, initializing for this project...');\n  await new AtlasPrerequisite(options.projectRoot).bootstrapAsync({ exp: options.exp });\n\n  return !options.isExporting\n    ? attachAtlasToDevServer(options)\n    : await attachAtlasToExport(options);\n}\n\n/**\n * Attach Atlas to the Metro bundler for development mode.\n * This includes attaching to Metro's middleware stack to host the Atlas UI.\n */\nfunction attachAtlasToDevServer(\n  options: Pick<AttachAtlasOptions, 'projectRoot' | 'middleware' | 'metroConfig'>\n): void | ReturnType<typeof import('expo-atlas/cli').createExpoAtlasMiddleware> {\n  if (!options.middleware) {\n    throw new Error(\n      'Expected middleware to be provided for Atlas when running in development mode'\n    );\n  }\n\n  const atlas = importAtlasForDev(options.projectRoot);\n  if (!atlas) {\n    return debug('Atlas is not installed in the project, skipping initialization');\n  }\n\n  const instance = atlas.createExpoAtlasMiddleware(options.metroConfig);\n  options.middleware.use('/_expo/atlas', instance.middleware);\n  debug('Attached Atlas middleware for development on: /_expo/atlas');\n  return instance;\n}\n\nfunction importAtlasForDev(projectRoot: string): null | typeof import('expo-atlas/cli') {\n  try {\n    return require(require.resolve('expo-atlas/cli', { paths: [projectRoot] }));\n  } catch (error: any) {\n    debug('Failed to load Atlas from project:', error);\n    return null;\n  }\n}\n\n/**\n * Attach Atlas to the Metro bundler for exporting mode.\n * This only includes attaching the custom serializer to the Metro config.\n */\nasync function attachAtlasToExport(\n  options: Pick<AttachAtlasOptions, 'projectRoot' | 'metroConfig' | 'resetAtlasFile'>\n): Promise<void> {\n  const atlas = importAtlasForExport(options.projectRoot);\n  if (!atlas) {\n    return debug('Atlas is not installed in the project, skipping initialization');\n  }\n\n  if (options.resetAtlasFile) {\n    const filePath = await atlas.resetExpoAtlasFile(options.projectRoot);\n    debug('(Re)created Atlas file at:', filePath);\n  }\n\n  atlas.withExpoAtlas(options.metroConfig);\n  debug('Attached Atlas to Metro config for exporting');\n}\n\nfunction importAtlasForExport(projectRoot: string): null | typeof import('expo-atlas/metro') {\n  try {\n    return require(require.resolve('expo-atlas/metro', { paths: [projectRoot] }));\n  } catch (error: any) {\n    debug('Failed to load Atlas from project:', error);\n    return null;\n  }\n}\n\n/**\n * Wait until the Atlas file has all data written.\n * Note, this is a workaround whenever `process.exit` is required, avoid if possible.\n * @internal\n */\nexport async function waitUntilAtlasExportIsReadyAsync(projectRoot: string) {\n  if (!env.EXPO_UNSTABLE_ATLAS) return;\n\n  const atlas = importAtlasForExport(projectRoot);\n\n  if (!atlas) {\n    return debug('Atlas is not loaded, cannot wait for export to finish');\n  }\n  if (typeof atlas.waitUntilAtlasFileReady !== 'function') {\n    return debug('Atlas is outdated, cannot wait for export to finish');\n  }\n\n  debug('Waiting for Atlas to finish exporting...');\n  await atlas.waitUntilAtlasFileReady();\n  debug('Atlas export is ready');\n}\n"], "names": ["attachAtlasAsync", "waitUntilAtlasExportIsReadyAsync", "debug", "require", "options", "env", "EXPO_UNSTABLE_ATLAS", "AtlasPrerequisite", "projectRoot", "bootstrapAsync", "exp", "isExporting", "attachAtlasToDevServer", "attachAtlasToExport", "middleware", "Error", "atlas", "importAtlasForDev", "instance", "createExpoAtlasMiddleware", "metroConfig", "use", "resolve", "paths", "error", "importAtlasForExport", "resetAtlasFile", "filePath", "resetExpoAtlasFile", "withExpoAtlas", "waitUntilAtlasFileReady"], "mappings": "AAAA;;;;;;;;;;;IAiBsBA,gBAAgB,MAAhBA,gBAAgB;IAmFhBC,gCAAgC,MAAhCA,gCAAgC;;mCAjGpB,qBAAqB;qBACnC,uBAAuB;AAG3C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,AAAsB,AAAC;AAUlF,eAAeH,gBAAgB,CACpCI,OAA2B,EAC4D;IACvF,IAAI,CAACC,IAAG,IAAA,CAACC,mBAAmB,EAAE;QAC5B,OAAO;IACT,CAAC;IAEDJ,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC5D,MAAM,IAAIK,kBAAiB,kBAAA,CAACH,OAAO,CAACI,WAAW,CAAC,CAACC,cAAc,CAAC;QAAEC,GAAG,EAAEN,OAAO,CAACM,GAAG;KAAE,CAAC,CAAC;IAEtF,OAAO,CAACN,OAAO,CAACO,WAAW,GACvBC,sBAAsB,CAACR,OAAO,CAAC,GAC/B,MAAMS,mBAAmB,CAACT,OAAO,CAAC,CAAC;AACzC,CAAC;AAED;;;CAGC,GACD,SAASQ,sBAAsB,CAC7BR,OAA+E,EACD;IAC9E,IAAI,CAACA,OAAO,CAACU,UAAU,EAAE;QACvB,MAAM,IAAIC,KAAK,CACb,+EAA+E,CAChF,CAAC;IACJ,CAAC;IAED,MAAMC,KAAK,GAAGC,iBAAiB,CAACb,OAAO,CAACI,WAAW,CAAC,AAAC;IACrD,IAAI,CAACQ,KAAK,EAAE;QACV,OAAOd,KAAK,CAAC,gEAAgE,CAAC,CAAC;IACjF,CAAC;IAED,MAAMgB,QAAQ,GAAGF,KAAK,CAACG,yBAAyB,CAACf,OAAO,CAACgB,WAAW,CAAC,AAAC;IACtEhB,OAAO,CAACU,UAAU,CAACO,GAAG,CAAC,cAAc,EAAEH,QAAQ,CAACJ,UAAU,CAAC,CAAC;IAC5DZ,KAAK,CAAC,4DAA4D,CAAC,CAAC;IACpE,OAAOgB,QAAQ,CAAC;AAClB,CAAC;AAED,SAASD,iBAAiB,CAACT,WAAmB,EAA0C;IACtF,IAAI;QACF,OAAOL,OAAO,CAACA,OAAO,CAACmB,OAAO,CAAC,gBAAgB,EAAE;YAAEC,KAAK,EAAE;gBAACf,WAAW;aAAC;SAAE,CAAC,CAAC,CAAC;IAC9E,EAAE,OAAOgB,KAAK,EAAO;QACnBtB,KAAK,CAAC,oCAAoC,EAAEsB,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;CAGC,GACD,eAAeX,mBAAmB,CAChCT,OAAmF,EACpE;IACf,MAAMY,KAAK,GAAGS,oBAAoB,CAACrB,OAAO,CAACI,WAAW,CAAC,AAAC;IACxD,IAAI,CAACQ,KAAK,EAAE;QACV,OAAOd,KAAK,CAAC,gEAAgE,CAAC,CAAC;IACjF,CAAC;IAED,IAAIE,OAAO,CAACsB,cAAc,EAAE;QAC1B,MAAMC,QAAQ,GAAG,MAAMX,KAAK,CAACY,kBAAkB,CAACxB,OAAO,CAACI,WAAW,CAAC,AAAC;QACrEN,KAAK,CAAC,4BAA4B,EAAEyB,QAAQ,CAAC,CAAC;IAChD,CAAC;IAEDX,KAAK,CAACa,aAAa,CAACzB,OAAO,CAACgB,WAAW,CAAC,CAAC;IACzClB,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACxD,CAAC;AAED,SAASuB,oBAAoB,CAACjB,WAAmB,EAA4C;IAC3F,IAAI;QACF,OAAOL,OAAO,CAACA,OAAO,CAACmB,OAAO,CAAC,kBAAkB,EAAE;YAAEC,KAAK,EAAE;gBAACf,WAAW;aAAC;SAAE,CAAC,CAAC,CAAC;IAChF,EAAE,OAAOgB,KAAK,EAAO;QACnBtB,KAAK,CAAC,oCAAoC,EAAEsB,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAOM,eAAevB,gCAAgC,CAACO,WAAmB,EAAE;IAC1E,IAAI,CAACH,IAAG,IAAA,CAACC,mBAAmB,EAAE,OAAO;IAErC,MAAMU,KAAK,GAAGS,oBAAoB,CAACjB,WAAW,CAAC,AAAC;IAEhD,IAAI,CAACQ,KAAK,EAAE;QACV,OAAOd,KAAK,CAAC,uDAAuD,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,OAAOc,KAAK,CAACc,uBAAuB,KAAK,UAAU,EAAE;QACvD,OAAO5B,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACtE,CAAC;IAEDA,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAClD,MAAMc,KAAK,CAACc,uBAAuB,EAAE,CAAC;IACtC5B,KAAK,CAAC,uBAAuB,CAAC,CAAC;AACjC,CAAC"}