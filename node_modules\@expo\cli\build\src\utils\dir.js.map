{"version": 3, "sources": ["../../../src/utils/dir.ts"], "sourcesContent": ["import fs from 'fs';\nimport fse from 'fs-extra';\n\nexport function fileExistsSync(file: string): boolean {\n  return !!fs\n    .statSync(file, {\n      throwIfNoEntry: false,\n    })\n    ?.isFile();\n}\n\nexport function directoryExistsSync(file: string): boolean {\n  return !!fs\n    .statSync(file, {\n      throwIfNoEntry: false,\n    })\n    ?.isDirectory();\n}\n\nexport async function directoryExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isDirectory() ?? false;\n}\n\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await fs.promises.stat(file).catch(() => null))?.isFile() ?? false;\n}\n\nexport const ensureDirectoryAsync = (path: string) => fs.promises.mkdir(path, { recursive: true });\n\nexport const ensureDirectory = (path: string) => fse.mkdirSync(path, { recursive: true });\n\nexport const copySync = fse.copySync;\n\nexport const copyAsync = fse.copy;\n\nexport const removeAsync = fse.remove;\n"], "names": ["fileExistsSync", "directoryExistsSync", "directoryExistsAsync", "fileExistsAsync", "ensureDirectoryAsync", "ensureDirectory", "copySync", "copyAsync", "removeAsync", "file", "fs", "statSync", "throwIfNoEntry", "isFile", "isDirectory", "promises", "stat", "catch", "path", "mkdir", "recursive", "fse", "mkdirSync", "copy", "remove"], "mappings": "AAAA;;;;;;;;;;;IAGgBA,cAAc,MAAdA,cAAc;IAQdC,mBAAmB,MAAnBA,mBAAmB;IAQbC,oBAAoB,MAApBA,oBAAoB;IAIpBC,eAAe,MAAfA,eAAe;IAIxBC,oBAAoB,MAApBA,oBAAoB;IAEpBC,eAAe,MAAfA,eAAe;IAEfC,QAAQ,MAARA,QAAQ;IAERC,SAAS,MAATA,SAAS;IAETC,WAAW,MAAXA,WAAW;;;8DAnCT,IAAI;;;;;;;8DACH,UAAU;;;;;;;;;;;AAEnB,SAASR,cAAc,CAACS,IAAY,EAAW;QAC3CC,GAGL;IAHJ,OAAO,CAAC,EAACA,CAAAA,GAGL,GAHKA,GAAE,EAAA,QAAA,CACRC,QAAQ,CAACF,IAAI,EAAE;QACdG,cAAc,EAAE,KAAK;KACtB,CAAC,SACM,GAJDF,KAAAA,CAIC,GAJDA,GAGL,CACAG,MAAM,EAAE,CAAA,CAAC;AACf,CAAC;AAEM,SAASZ,mBAAmB,CAACQ,IAAY,EAAW;QAChDC,GAGL;IAHJ,OAAO,CAAC,EAACA,CAAAA,GAGL,GAHKA,GAAE,EAAA,QAAA,CACRC,QAAQ,CAACF,IAAI,EAAE;QACdG,cAAc,EAAE,KAAK;KACtB,CAAC,SACW,GAJNF,KAAAA,CAIM,GAJNA,GAGL,CACAI,WAAW,EAAE,CAAA,CAAC;AACpB,CAAC;AAEM,eAAeZ,oBAAoB,CAACO,IAAY,EAAoB;QAClE,GAAgD;IAAvD,OAAO,CAAA,CAAA,GAAgD,GAA/C,MAAMC,GAAE,EAAA,QAAA,CAACK,QAAQ,CAACC,IAAI,CAACP,IAAI,CAAC,CAACQ,KAAK,CAAC,IAAM,IAAI,CAAC,SAAc,GAA7D,KAAA,CAA6D,GAA7D,GAAgD,CAAEH,WAAW,EAAE,KAAI,KAAK,CAAC;AAClF,CAAC;AAEM,eAAeX,eAAe,CAACM,IAAY,EAAoB;QAC7D,GAAgD;IAAvD,OAAO,CAAA,CAAA,GAAgD,GAA/C,MAAMC,GAAE,EAAA,QAAA,CAACK,QAAQ,CAACC,IAAI,CAACP,IAAI,CAAC,CAACQ,KAAK,CAAC,IAAM,IAAI,CAAC,SAAS,GAAxD,KAAA,CAAwD,GAAxD,GAAgD,CAAEJ,MAAM,EAAE,KAAI,KAAK,CAAC;AAC7E,CAAC;AAEM,MAAMT,oBAAoB,GAAG,CAACc,IAAY,GAAKR,GAAE,EAAA,QAAA,CAACK,QAAQ,CAACI,KAAK,CAACD,IAAI,EAAE;QAAEE,SAAS,EAAE,IAAI;KAAE,CAAC,AAAC;AAE5F,MAAMf,eAAe,GAAG,CAACa,IAAY,GAAKG,QAAG,EAAA,QAAA,CAACC,SAAS,CAACJ,IAAI,EAAE;QAAEE,SAAS,EAAE,IAAI;KAAE,CAAC,AAAC;AAEnF,MAAMd,QAAQ,GAAGe,QAAG,EAAA,QAAA,CAACf,QAAQ,AAAC;AAE9B,MAAMC,SAAS,GAAGc,QAAG,EAAA,QAAA,CAACE,IAAI,AAAC;AAE3B,MAAMf,WAAW,GAAGa,QAAG,EAAA,QAAA,CAACG,MAAM,AAAC"}