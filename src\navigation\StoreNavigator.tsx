import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import DashboardScreen from '../screens/store/DashboardScreen';
import ProductsScreen from '../screens/store/ProductsScreen';
import ProductDetailScreen from '../screens/store/ProductDetailScreen';
import OrdersScreen from '../screens/store/OrdersScreen';
import OrderDetailScreen from '../screens/store/OrderDetailScreen';
import ProfileScreen from '../screens/store/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const ProductsStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="ProductsList" component={ProductsScreen} options={{ title: 'Products' }} />
    <Stack.Screen name="ProductDetail" component={ProductDetailScreen} options={({ route }) => ({ title: route.params?.isNew ? 'New Product' : 'Edit Product' })} />
  </Stack.Navigator>
);

const OrdersStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="OrdersList" component={OrdersScreen} options={{ title: 'Orders' }} />
    <Stack.Screen name="OrderDetail" component={OrderDetailScreen} options={{ title: 'Order Details' }} />
  </Stack.Navigator>
);

const StoreNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: '#1A1A1A',
        tabBarStyle: { backgroundColor: '#F7F3E9' }
      }}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen} 
        options={{
          tabBarIcon: ({ color }) => <Icon name="dashboard" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="Products" 
        component={ProductsStack} 
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <Icon name="inventory" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersStack} 
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <Icon name="receipt" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{
          tabBarIcon: ({ color }) => <Icon name="person" size={24} color={color} />
        }}
      />
    </Tab.Navigator>
  );
};

export default StoreNavigator;