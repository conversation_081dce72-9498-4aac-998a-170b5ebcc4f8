{"version": 3, "sources": ["../../../../src/start/server/BundlerDevServer.ts"], "sourcesContent": ["import assert from 'assert';\nimport resolveFrom from 'resolve-from';\n\nimport { AsyncNgrok } from './AsyncNgrok';\nimport { AsyncWsTunnel } from './AsyncWsTunnel';\nimport DevToolsPluginManager from './DevToolsPluginManager';\nimport { DevelopmentSession } from './DevelopmentSession';\nimport { CreateURLOptions, UrlCreator } from './UrlCreator';\nimport { PlatformBundlers } from './platformBundlers';\nimport * as Log from '../../log';\nimport { FileNotifier } from '../../utils/FileNotifier';\nimport { resolveWithTimeout } from '../../utils/delay';\nimport { env, envIsWebcontainer } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { openBrowserAsync } from '../../utils/open';\nimport {\n  BaseOpenInCustomProps,\n  BaseResolveDeviceProps,\n  PlatformManager,\n} from '../platforms/PlatformManager';\n\nconst debug = require('debug')('expo:start:server:devServer') as typeof console.log;\n\nexport type MessageSocket = {\n  broadcast: (method: string, params?: Record<string, any> | undefined) => void;\n};\n\nexport type ServerLike = {\n  close(callback?: (err?: Error) => void): void;\n  addListener?(event: string, listener: (...args: any[]) => void): unknown;\n};\n\nexport type DevServerInstance = {\n  /** Bundler dev server instance. */\n  server: ServerLike;\n  /** Dev server URL location properties. */\n  location: {\n    url: string;\n    port: number;\n    protocol: 'http' | 'https';\n    host?: string;\n  };\n  /** Additional middleware that's attached to the `server`. */\n  middleware: any;\n  /** Message socket for communicating with the runtime. */\n  messageSocket: MessageSocket;\n};\n\nexport interface BundlerStartOptions {\n  /** Should the dev server use `https` protocol. */\n  https?: boolean;\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Is dev client enabled. */\n  devClient?: boolean;\n  /** Should run dev servers with clean caches. */\n  resetDevServer?: boolean;\n  /** Code signing private key path (defaults to same directory as certificate) */\n  privateKeyPath?: string;\n\n  /** Max amount of workers (threads) to use with Metro bundler, defaults to undefined for max workers. */\n  maxWorkers?: number;\n  /** Port to start the dev server on. */\n  port?: number;\n\n  /** Should start a headless dev server e.g. mock representation to approximate info from a server running in a different process. */\n  headless?: boolean;\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n\n  /** Will the bundler be used for exporting. NOTE: This is an odd option to pass to the dev server. */\n  isExporting?: boolean;\n\n  // Webpack options\n  /** Should modify and create PWA icons. */\n  isImageEditingEnabled?: boolean;\n\n  location: CreateURLOptions;\n}\n\nconst PLATFORM_MANAGERS = {\n  simulator: () =>\n    require('../platforms/ios/ApplePlatformManager')\n      .ApplePlatformManager as typeof import('../platforms/ios/ApplePlatformManager').ApplePlatformManager,\n  emulator: () =>\n    require('../platforms/android/AndroidPlatformManager')\n      .AndroidPlatformManager as typeof import('../platforms/android/AndroidPlatformManager').AndroidPlatformManager,\n};\n\nexport abstract class BundlerDevServer {\n  /** Name of the bundler. */\n  abstract get name(): string;\n\n  /** Tunnel instance for managing tunnel connections. */\n  protected tunnel: AsyncNgrok | AsyncWsTunnel | null = null;\n  /** Interfaces with the Expo 'Development Session' API. */\n  protected devSession: DevelopmentSession | null = null;\n  /** Http server and related info. */\n  protected instance: DevServerInstance | null = null;\n  /** Native platform interfaces for opening projects.  */\n  private platformManagers: Record<string, PlatformManager<any>> = {};\n  /** Manages the creation of dev server URLs. */\n  protected urlCreator?: UrlCreator | null = null;\n\n  private notifier: FileNotifier | null = null;\n  protected readonly devToolsPluginManager: DevToolsPluginManager;\n  public isDevClient: boolean;\n\n  constructor(\n    /** Project root folder. */\n    public projectRoot: string,\n    /** A mapping of bundlers to platforms. */\n    public platformBundlers: PlatformBundlers,\n    /** Advanced options */\n    options?: {\n      /**\n       * The instance of DevToolsPluginManager\n       * @default new DevToolsPluginManager(projectRoot)\n       */\n      devToolsPluginManager?: DevToolsPluginManager;\n      // TODO: Replace with custom scheme maybe...\n      isDevClient?: boolean;\n    }\n  ) {\n    this.devToolsPluginManager =\n      options?.devToolsPluginManager ?? new DevToolsPluginManager(projectRoot);\n    this.isDevClient = options?.isDevClient ?? false;\n  }\n\n  protected setInstance(instance: DevServerInstance) {\n    this.instance = instance;\n  }\n\n  /** Get the manifest middleware function. */\n  protected async getManifestMiddlewareAsync(\n    options: Pick<BundlerStartOptions, 'minify' | 'mode' | 'privateKeyPath'> = {}\n  ) {\n    const Middleware = require('./middleware/ExpoGoManifestHandlerMiddleware')\n      .ExpoGoManifestHandlerMiddleware as typeof import('./middleware/ExpoGoManifestHandlerMiddleware').ExpoGoManifestHandlerMiddleware;\n\n    const urlCreator = this.getUrlCreator();\n    const middleware = new Middleware(this.projectRoot, {\n      constructUrl: urlCreator.constructUrl.bind(urlCreator),\n      mode: options.mode,\n      minify: options.minify,\n      isNativeWebpack: this.name === 'webpack' && this.isTargetingNative(),\n      privateKeyPath: options.privateKeyPath,\n    });\n    return middleware;\n  }\n\n  /** Start the dev server using settings defined in the start command. */\n  public async startAsync(options: BundlerStartOptions): Promise<DevServerInstance> {\n    await this.stopAsync();\n\n    let instance: DevServerInstance;\n    if (options.headless) {\n      instance = await this.startHeadlessAsync(options);\n    } else {\n      instance = await this.startImplementationAsync(options);\n    }\n\n    this.setInstance(instance);\n    await this.postStartAsync(options);\n    return instance;\n  }\n\n  protected abstract startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance>;\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    return false;\n  }\n\n  public abstract startTypeScriptServices(): Promise<void>;\n\n  public async watchEnvironmentVariables(): Promise<void> {\n    // noop -- We've only implemented this functionality in Metro.\n  }\n\n  /**\n   * Creates a mock server representation that can be used to estimate URLs for a server started in another process.\n   * This is used for the run commands where you can reuse the server from a previous run.\n   */\n  private async startHeadlessAsync(options: BundlerStartOptions): Promise<DevServerInstance> {\n    if (!options.port)\n      throw new CommandError('HEADLESS_SERVER', 'headless dev server requires a port option');\n    this.urlCreator = this.getUrlCreator(options);\n\n    return {\n      // Create a mock server\n      server: {\n        close: (callback: () => void) => {\n          this.instance = null;\n          callback?.();\n        },\n        addListener() {},\n      },\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware: {},\n      messageSocket: {\n        broadcast: () => {\n          throw new CommandError('HEADLESS_SERVER', 'Cannot broadcast messages to headless server');\n        },\n      },\n    };\n  }\n\n  /**\n   * Runs after the `startAsync` function, performing any additional common operations.\n   * You can assume the dev server is started by the time this function is called.\n   */\n  protected async postStartAsync(options: BundlerStartOptions) {\n    if (\n      options.location.hostType === 'tunnel' &&\n      !env.EXPO_OFFLINE &&\n      // This is a hack to prevent using tunnel on web since we block it upstream for some reason.\n      this.isTargetingNative()\n    ) {\n      await this._startTunnelAsync();\n    } else if (envIsWebcontainer()) {\n      await this._startTunnelAsync();\n    }\n\n    if (!options.isExporting) {\n      await this.startDevSessionAsync();\n      this.watchConfig();\n    }\n  }\n\n  protected abstract getConfigModuleIds(): string[];\n\n  protected watchConfig() {\n    this.notifier?.stopObserving();\n    this.notifier = new FileNotifier(this.projectRoot, this.getConfigModuleIds());\n    this.notifier.startObserving();\n  }\n\n  /** Create ngrok instance and start the tunnel server. Exposed for testing. */\n  public async _startTunnelAsync(): Promise<AsyncNgrok | AsyncWsTunnel | null> {\n    const port = this.getInstance()?.location.port;\n    if (!port) return null;\n    debug('[tunnel] connect to port: ' + port);\n    this.tunnel = envIsWebcontainer()\n      ? new AsyncWsTunnel(this.projectRoot, port)\n      : new AsyncNgrok(this.projectRoot, port);\n    await this.tunnel.startAsync();\n    return this.tunnel;\n  }\n\n  protected async startDevSessionAsync() {\n    // This is used to make Expo Go open the project in either Expo Go, or the web browser.\n    // Must come after ngrok (`startTunnelAsync`) setup.\n    this.devSession = new DevelopmentSession(\n      this.projectRoot,\n      // This URL will be used on external devices so the computer IP won't be relevant.\n      this.isTargetingNative()\n        ? this.getNativeRuntimeUrl()\n        : this.getDevServerUrl({ hostType: 'localhost' })\n    );\n\n    await this.devSession.startAsync({\n      runtime: this.isTargetingNative() ? 'native' : 'web',\n    });\n  }\n\n  public isTargetingNative() {\n    // Temporary hack while we implement multi-bundler dev server proxy.\n    return true;\n  }\n\n  public isTargetingWeb() {\n    return this.platformBundlers.web === this.name;\n  }\n\n  /**\n   * Sends a message over web sockets to any connected device,\n   * does nothing when the dev server is not running.\n   *\n   * @param method name of the command. In RN projects `reload`, and `devMenu` are available. In Expo Go, `sendDevCommand` is available.\n   * @param params\n   */\n  public broadcastMessage(\n    method: 'reload' | 'devMenu' | 'sendDevCommand',\n    params?: Record<string, any>\n  ) {\n    this.getInstance()?.messageSocket.broadcast(method, params);\n  }\n\n  /** Get the running dev server instance. */\n  public getInstance() {\n    return this.instance;\n  }\n\n  /** Stop the running dev server instance. */\n  async stopAsync() {\n    // Stop file watching.\n    this.notifier?.stopObserving();\n\n    // Stop the dev session timer and tell Expo API to remove dev session.\n    await this.devSession?.closeAsync();\n\n    // Stop tunnel if running.\n    await this.tunnel?.stopAsync().catch((e) => {\n      Log.error(`Error stopping tunnel:`);\n      Log.exception(e);\n    });\n\n    return resolveWithTimeout(\n      () =>\n        new Promise<void>((resolve, reject) => {\n          // Close the server.\n          debug(`Stopping dev server (bundler: ${this.name})`);\n\n          if (this.instance?.server) {\n            // Check if server is even running.\n            this.instance.server.close((error) => {\n              debug(`Stopped dev server (bundler: ${this.name})`);\n              this.instance = null;\n              if (error) {\n                if ('code' in error && error.code === 'ERR_SERVER_NOT_RUNNING') {\n                  resolve();\n                } else {\n                  reject(error);\n                }\n              } else {\n                resolve();\n              }\n            });\n          } else {\n            debug(`Stopped dev server (bundler: ${this.name})`);\n            this.instance = null;\n            resolve();\n          }\n        }),\n      {\n        // NOTE(Bacon): Metro dev server doesn't seem to be closing in time.\n        timeout: 1000,\n        errorMessage: `Timeout waiting for '${this.name}' dev server to close`,\n      }\n    );\n  }\n\n  public getUrlCreator(options: Partial<Pick<BundlerStartOptions, 'port' | 'location'>> = {}) {\n    if (!this.urlCreator) {\n      assert(options?.port, 'Dev server instance not found');\n      this.urlCreator = new UrlCreator(options.location, {\n        port: options.port,\n        getTunnelUrl: this.getTunnelUrl.bind(this),\n      });\n    }\n    return this.urlCreator;\n  }\n\n  public getNativeRuntimeUrl(opts: Partial<CreateURLOptions> = {}) {\n    return this.isDevClient\n      ? (this.getUrlCreator().constructDevClientUrl(opts) ?? this.getDevServerUrl())\n      : this.getUrlCreator().constructUrl({ ...opts, scheme: 'exp' });\n  }\n\n  /** Get the URL for the running instance of the dev server. */\n  public getDevServerUrl(options: { hostType?: 'localhost' } = {}): string | null {\n    const instance = this.getInstance();\n    if (!instance?.location) {\n      return null;\n    }\n\n    // If we have an active WS tunnel instance, we always need to return the tunnel location.\n    if (this.tunnel && this.tunnel instanceof AsyncWsTunnel) {\n      return this.getUrlCreator().constructUrl();\n    }\n\n    const { location } = instance;\n    if (options.hostType === 'localhost') {\n      return `${location.protocol}://localhost:${location.port}`;\n    }\n\n    return location.url ?? null;\n  }\n\n  public getDevServerUrlOrAssert(options: { hostType?: 'localhost' } = {}): string {\n    const instance = this.getDevServerUrl(options);\n    if (!instance) {\n      throw new CommandError(\n        'DEV_SERVER',\n        `Cannot get the dev server URL before the server has started - bundler[${this.name}]`\n      );\n    }\n\n    return instance;\n  }\n\n  /** Get the base URL for JS inspector */\n  public getJsInspectorBaseUrl(): string {\n    if (this.name !== 'metro') {\n      throw new CommandError(\n        'DEV_SERVER',\n        `Cannot get the JS inspector base url - bundler[${this.name}]`\n      );\n    }\n    return this.getUrlCreator().constructUrl({ scheme: 'http' });\n  }\n\n  /** Get the tunnel URL from the tunnel. */\n  public getTunnelUrl(): string | null {\n    return this.tunnel?.getActiveUrl() ?? null;\n  }\n\n  /** Open the dev server in a runtime. */\n  public async openPlatformAsync(\n    launchTarget: keyof typeof PLATFORM_MANAGERS | 'desktop',\n    resolver: BaseResolveDeviceProps<any> = {}\n  ) {\n    if (launchTarget === 'desktop') {\n      const serverUrl = this.getDevServerUrl({ hostType: 'localhost' });\n      // Allow opening the tunnel URL when using Metro web.\n      const url = this.name === 'metro' ? (this.getTunnelUrl() ?? serverUrl) : serverUrl;\n      await openBrowserAsync(url!);\n      return { url };\n    }\n\n    const runtime = this.isTargetingNative() ? (this.isDevClient ? 'custom' : 'expo') : 'web';\n    const manager = await this.getPlatformManagerAsync(launchTarget);\n    return manager.openAsync({ runtime }, resolver);\n  }\n\n  /** Open the dev server in a runtime. */\n  public async openCustomRuntimeAsync<T extends BaseOpenInCustomProps = BaseOpenInCustomProps>(\n    launchTarget: keyof typeof PLATFORM_MANAGERS,\n    launchProps: Partial<T> = {},\n    resolver: BaseResolveDeviceProps<any> = {}\n  ) {\n    const runtime = this.isTargetingNative() ? (this.isDevClient ? 'custom' : 'expo') : 'web';\n    if (runtime !== 'custom') {\n      throw new CommandError(\n        `dev server cannot open custom runtimes either because it does not target native platforms or because it is not targeting dev clients. (target: ${runtime})`\n      );\n    }\n\n    const manager = await this.getPlatformManagerAsync(launchTarget);\n    return manager.openAsync({ runtime: 'custom', props: launchProps }, resolver);\n  }\n\n  /** Get the URL for opening in Expo Go. */\n  protected getExpoGoUrl(): string {\n    return this.getUrlCreator().constructUrl({ scheme: 'exp' });\n  }\n\n  /** Should use the interstitial page for selecting which runtime to use. */\n  protected isRedirectPageEnabled(): boolean {\n    return (\n      !env.EXPO_NO_REDIRECT_PAGE &&\n      // if user passed --dev-client flag, skip interstitial page\n      !this.isDevClient &&\n      // Checks if dev client is installed.\n      !!resolveFrom.silent(this.projectRoot, 'expo-dev-client')\n    );\n  }\n\n  /** Get the redirect URL when redirecting is enabled. */\n  public getRedirectUrl(platform: keyof typeof PLATFORM_MANAGERS | null = null): string | null {\n    if (!this.isRedirectPageEnabled()) {\n      debug('Redirect page is disabled');\n      return null;\n    }\n\n    return (\n      this.getUrlCreator().constructLoadingUrl(\n        {},\n        platform === 'emulator' ? 'android' : platform === 'simulator' ? 'ios' : null\n      ) ?? null\n    );\n  }\n\n  public getReactDevToolsUrl(): string {\n    return new URL(\n      '_expo/react-devtools',\n      this.getUrlCreator().constructUrl({ scheme: 'http' })\n    ).toString();\n  }\n\n  protected async getPlatformManagerAsync(platform: keyof typeof PLATFORM_MANAGERS) {\n    if (!this.platformManagers[platform]) {\n      const Manager = PLATFORM_MANAGERS[platform]();\n      const port = this.getInstance()?.location.port;\n      if (!port || !this.urlCreator) {\n        throw new CommandError(\n          'DEV_SERVER',\n          'Cannot interact with native platforms until dev server has started'\n        );\n      }\n      debug(`Creating platform manager (platform: ${platform}, port: ${port})`);\n      this.platformManagers[platform] = new Manager(this.projectRoot, port, {\n        getCustomRuntimeUrl: this.urlCreator.constructDevClientUrl.bind(this.urlCreator),\n        getExpoGoUrl: this.getExpoGoUrl.bind(this),\n        getRedirectUrl: this.getRedirectUrl.bind(this, platform),\n        getDevServerUrl: this.getDevServerUrl.bind(this, { hostType: 'localhost' }),\n      });\n    }\n    return this.platformManagers[platform];\n  }\n}\n"], "names": ["BundlerDevServer", "debug", "require", "PLATFORM_MANAGERS", "simulator", "ApplePlatformManager", "emulator", "AndroidPlatformManager", "constructor", "projectRoot", "platformBundlers", "options", "tunnel", "devSession", "instance", "platformManagers", "urlCreator", "notifier", "devToolsPluginManager", "DevToolsPluginManager", "isDevClient", "setInstance", "getManifestMiddlewareAsync", "Middleware", "ExpoGoManifestHandlerMiddleware", "getUrlCreator", "middleware", "constructUrl", "bind", "mode", "minify", "isNativeWebpack", "name", "isTargetingNative", "privateKeyPath", "startAsync", "stopAsync", "headless", "startHeadlessAsync", "startImplementationAsync", "postStartAsync", "waitForTypeScriptAsync", "watchEnvironmentVariables", "port", "CommandError", "server", "close", "callback", "addListener", "location", "host", "url", "protocol", "messageSocket", "broadcast", "hostType", "env", "EXPO_OFFLINE", "_startTunnelAsync", "envIsWebcontainer", "isExporting", "startDevSessionAsync", "watchConfig", "stopObserving", "FileNotifier", "getConfigModuleIds", "startObserving", "getInstance", "AsyncWsTunnel", "AsyncNgrok", "DevelopmentSession", "getNativeRuntimeUrl", "getDevServerUrl", "runtime", "isTargetingWeb", "web", "broadcastMessage", "method", "params", "closeAsync", "catch", "e", "Log", "error", "exception", "resolveWithTimeout", "Promise", "resolve", "reject", "code", "timeout", "errorMessage", "assert", "UrlCreator", "getTunnelUrl", "opts", "constructDevClientUrl", "scheme", "getDevServerUrlOrAssert", "getJsInspectorBaseUrl", "getActiveUrl", "openPlatformAsync", "launchTarget", "resolver", "serverUrl", "openBrowserAsync", "manager", "getPlatformManagerAsync", "openAsync", "openCustomRuntimeAsync", "launchProps", "props", "getExpoGoUrl", "isRedirectPageEnabled", "EXPO_NO_REDIRECT_PAGE", "resolveFrom", "silent", "getRedirectUrl", "platform", "constructLoadingUrl", "getReactDevToolsUrl", "URL", "toString", "Manager", "getCustomRuntimeUrl"], "mappings": "AAAA;;;;+<PERSON>y<PERSON>s<PERSON>,kBAAgB;;aAAhBA,gBAAgB;;;8DAzFnB,QAAQ;;;;;;;8DACH,cAAc;;;;;;4BAEX,cAAc;+BACX,iBAAiB;4EACb,yBAAyB;oCACxB,sBAAsB;4BACZ,cAAc;2DAEtC,WAAW;8BACH,0BAA0B;uBACpB,mBAAmB;qBACf,iBAAiB;wBAC3B,oBAAoB;sBAChB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AA2DpF,MAAMC,iBAAiB,GAAG;IACxBC,SAAS,EAAE,IACTF,OAAO,CAAC,uCAAuC,CAAC,CAC7CG,oBAAoB,AAA+E;IACxGC,QAAQ,EAAE,IACRJ,OAAO,CAAC,6CAA6C,CAAC,CACnDK,sBAAsB,AAAuF;CACnH,AAAC;AAEK,MAAeP,gBAAgB;IAmBpCQ,YAESC,WAAmB,EAEnBC,gBAAkC,EACzC,qBAAqB,GACrBC,OAQC,CACD;QAbOF,mBAAAA,WAAmB,CAAA;QAEnBC,wBAAAA,gBAAkC,CAAA;aAlBjCE,MAAM,GAAsC,IAAI;aAEhDC,UAAU,GAA8B,IAAI;aAE5CC,QAAQ,GAA6B,IAAI;aAE3CC,gBAAgB,GAAyC,EAAE;aAEzDC,UAAU,GAAuB,IAAI;aAEvCC,QAAQ,GAAwB,IAAI;QAoB1C,IAAI,CAACC,qBAAqB,GACxBP,CAAAA,OAAO,QAAuB,GAA9BA,KAAAA,CAA8B,GAA9BA,OAAO,CAAEO,qBAAqB,CAAA,IAAI,IAAIC,sBAAqB,QAAA,CAACV,WAAW,CAAC,CAAC;QAC3E,IAAI,CAACW,WAAW,GAAGT,CAAAA,OAAO,QAAa,GAApBA,KAAAA,CAAoB,GAApBA,OAAO,CAAES,WAAW,CAAA,IAAI,KAAK,CAAC;IACnD;IAEUC,WAAW,CAACP,QAA2B,EAAE;QACjD,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC;IAC3B;IAEA,0CAA0C,SAC1BQ,0BAA0B,CACxCX,OAAwE,GAAG,EAAE,EAC7E;QACA,MAAMY,UAAU,GAAGrB,OAAO,CAAC,8CAA8C,CAAC,CACvEsB,+BAA+B,AAAiG,AAAC;QAEpI,MAAMR,UAAU,GAAG,IAAI,CAACS,aAAa,EAAE,AAAC;QACxC,MAAMC,UAAU,GAAG,IAAIH,UAAU,CAAC,IAAI,CAACd,WAAW,EAAE;YAClDkB,YAAY,EAAEX,UAAU,CAACW,YAAY,CAACC,IAAI,CAACZ,UAAU,CAAC;YACtDa,IAAI,EAAElB,OAAO,CAACkB,IAAI;YAClBC,MAAM,EAAEnB,OAAO,CAACmB,MAAM;YACtBC,eAAe,EAAE,IAAI,CAACC,IAAI,KAAK,SAAS,IAAI,IAAI,CAACC,iBAAiB,EAAE;YACpEC,cAAc,EAAEvB,OAAO,CAACuB,cAAc;SACvC,CAAC,AAAC;QACH,OAAOR,UAAU,CAAC;IACpB;IAEA,sEAAsE,SACzDS,UAAU,CAACxB,OAA4B,EAA8B;QAChF,MAAM,IAAI,CAACyB,SAAS,EAAE,CAAC;QAEvB,IAAItB,QAAQ,AAAmB,AAAC;QAChC,IAAIH,OAAO,CAAC0B,QAAQ,EAAE;YACpBvB,QAAQ,GAAG,MAAM,IAAI,CAACwB,kBAAkB,CAAC3B,OAAO,CAAC,CAAC;QACpD,OAAO;YACLG,QAAQ,GAAG,MAAM,IAAI,CAACyB,wBAAwB,CAAC5B,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAACU,WAAW,CAACP,QAAQ,CAAC,CAAC;QAC3B,MAAM,IAAI,CAAC0B,cAAc,CAAC7B,OAAO,CAAC,CAAC;QACnC,OAAOG,QAAQ,CAAC;IAClB;UAMa2B,sBAAsB,GAAqB;QACtD,OAAO,KAAK,CAAC;IACf;UAIaC,yBAAyB,GAAkB;IACtD,8DAA8D;IAChE;IAEA;;;GAGC,SACaJ,kBAAkB,CAAC3B,OAA4B,EAA8B;QACzF,IAAI,CAACA,OAAO,CAACgC,IAAI,EACf,MAAM,IAAIC,OAAY,aAAA,CAAC,iBAAiB,EAAE,4CAA4C,CAAC,CAAC;QAC1F,IAAI,CAAC5B,UAAU,GAAG,IAAI,CAACS,aAAa,CAACd,OAAO,CAAC,CAAC;QAE9C,OAAO;YACL,uBAAuB;YACvBkC,MAAM,EAAE;gBACNC,KAAK,EAAE,CAACC,QAAoB,GAAK;oBAC/B,IAAI,CAACjC,QAAQ,GAAG,IAAI,CAAC;oBACrBiC,QAAQ,QAAI,GAAZA,KAAAA,CAAY,GAAZA,QAAQ,EAAI,CAAC;gBACf,CAAC;gBACDC,WAAW,IAAG,CAAC,CAAC;aACjB;YACDC,QAAQ,EAAE;gBACR,mDAAmD;gBACnDN,IAAI,EAAEhC,OAAO,CAACgC,IAAI;gBAClB,kCAAkC;gBAClCO,IAAI,EAAE,WAAW;gBACjB,iDAAiD;gBACjDC,GAAG,EAAE,CAAC,iBAAiB,EAAExC,OAAO,CAACgC,IAAI,CAAC,CAAC;gBACvCS,QAAQ,EAAE,MAAM;aACjB;YACD1B,UAAU,EAAE,EAAE;YACd2B,aAAa,EAAE;gBACbC,SAAS,EAAE,IAAM;oBACf,MAAM,IAAIV,OAAY,aAAA,CAAC,iBAAiB,EAAE,8CAA8C,CAAC,CAAC;gBAC5F,CAAC;aACF;SACF,CAAC;IACJ;IAEA;;;GAGC,SACeJ,cAAc,CAAC7B,OAA4B,EAAE;QAC3D,IACEA,OAAO,CAACsC,QAAQ,CAACM,QAAQ,KAAK,QAAQ,IACtC,CAACC,IAAG,IAAA,CAACC,YAAY,IACjB,4FAA4F;QAC5F,IAAI,CAACxB,iBAAiB,EAAE,EACxB;YACA,MAAM,IAAI,CAACyB,iBAAiB,EAAE,CAAC;QACjC,OAAO,IAAIC,IAAAA,IAAiB,kBAAA,GAAE,EAAE;YAC9B,MAAM,IAAI,CAACD,iBAAiB,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC/C,OAAO,CAACiD,WAAW,EAAE;YACxB,MAAM,IAAI,CAACC,oBAAoB,EAAE,CAAC;YAClC,IAAI,CAACC,WAAW,EAAE,CAAC;QACrB,CAAC;IACH;IAIUA,WAAW,GAAG;YACtB,GAAa;QAAb,CAAA,GAAa,GAAb,IAAI,CAAC7C,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,GAAa,CAAE8C,aAAa,EAAE,CAAC;QAC/B,IAAI,CAAC9C,QAAQ,GAAG,IAAI+C,aAAY,aAAA,CAAC,IAAI,CAACvD,WAAW,EAAE,IAAI,CAACwD,kBAAkB,EAAE,CAAC,CAAC;QAC9E,IAAI,CAAChD,QAAQ,CAACiD,cAAc,EAAE,CAAC;IACjC;IAEA,4EAA4E,SAC/DR,iBAAiB,GAA+C;YAC9D,GAAkB;QAA/B,MAAMf,IAAI,GAAG,CAAA,GAAkB,GAAlB,IAAI,CAACwB,WAAW,EAAE,SAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAElB,QAAQ,CAACN,IAAI,AAAC;QAC/C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI,CAAC;QACvB1C,KAAK,CAAC,4BAA4B,GAAG0C,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC/B,MAAM,GAAG+C,IAAAA,IAAiB,kBAAA,GAAE,GAC7B,IAAIS,cAAa,cAAA,CAAC,IAAI,CAAC3D,WAAW,EAAEkC,IAAI,CAAC,GACzC,IAAI0B,WAAU,WAAA,CAAC,IAAI,CAAC5D,WAAW,EAAEkC,IAAI,CAAC,CAAC;QAC3C,MAAM,IAAI,CAAC/B,MAAM,CAACuB,UAAU,EAAE,CAAC;QAC/B,OAAO,IAAI,CAACvB,MAAM,CAAC;IACrB;UAEgBiD,oBAAoB,GAAG;QACrC,uFAAuF;QACvF,oDAAoD;QACpD,IAAI,CAAChD,UAAU,GAAG,IAAIyD,mBAAkB,mBAAA,CACtC,IAAI,CAAC7D,WAAW,EAChB,kFAAkF;QAClF,IAAI,CAACwB,iBAAiB,EAAE,GACpB,IAAI,CAACsC,mBAAmB,EAAE,GAC1B,IAAI,CAACC,eAAe,CAAC;YAAEjB,QAAQ,EAAE,WAAW;SAAE,CAAC,CACpD,CAAC;QAEF,MAAM,IAAI,CAAC1C,UAAU,CAACsB,UAAU,CAAC;YAC/BsC,OAAO,EAAE,IAAI,CAACxC,iBAAiB,EAAE,GAAG,QAAQ,GAAG,KAAK;SACrD,CAAC,CAAC;IACL;IAEOA,iBAAiB,GAAG;QACzB,oEAAoE;QACpE,OAAO,IAAI,CAAC;IACd;IAEOyC,cAAc,GAAG;QACtB,OAAO,IAAI,CAAChE,gBAAgB,CAACiE,GAAG,KAAK,IAAI,CAAC3C,IAAI,CAAC;IACjD;IAEA;;;;;;GAMC,GACM4C,gBAAgB,CACrBC,MAA+C,EAC/CC,MAA4B,EAC5B;YACA,GAAkB;QAAlB,CAAA,GAAkB,GAAlB,IAAI,CAACX,WAAW,EAAE,SAAe,GAAjC,KAAA,CAAiC,GAAjC,GAAkB,CAAEd,aAAa,CAACC,SAAS,CAACuB,MAAM,EAAEC,MAAM,CAAC,CAAC;IAC9D;IAEA,yCAAyC,GAClCX,WAAW,GAAG;QACnB,OAAO,IAAI,CAACrD,QAAQ,CAAC;IACvB;IAEA,0CAA0C,SACpCsB,SAAS,GAAG;YAChB,sBAAsB;QACtB,GAAa,EAGP,IAAe,EAGf,IAAW;QANjB,CAAA,GAAa,GAAb,IAAI,CAACnB,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,GAAa,CAAE8C,aAAa,EAAE,CAAC;QAE/B,sEAAsE;QACtE,OAAM,CAAA,IAAe,GAAf,IAAI,CAAClD,UAAU,SAAY,GAA3B,KAAA,CAA2B,GAA3B,IAAe,CAAEkE,UAAU,EAAE,CAAA,CAAC;QAEpC,0BAA0B;QAC1B,MAAM,CAAA,CAAA,IAAW,GAAX,IAAI,CAACnE,MAAM,SAAW,GAAtB,KAAA,CAAsB,GAAtB,IAAW,CAAEwB,SAAS,EAAE,CAAC4C,KAAK,CAAC,CAACC,CAAC,GAAK;YAC1CC,IAAG,CAACC,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACpCD,IAAG,CAACE,SAAS,CAACH,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAA,CAAC;QAEH,OAAOI,IAAAA,MAAkB,mBAAA,EACvB;YACE,OAAA,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,GAAK;oBAIjC,GAAa;gBAHjB,oBAAoB;gBACpBvF,KAAK,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAErD,IAAI,CAAA,GAAa,GAAb,IAAI,CAAClB,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAE+B,MAAM,EAAE;oBACzB,mCAAmC;oBACnC,IAAI,CAAC/B,QAAQ,CAAC+B,MAAM,CAACC,KAAK,CAAC,CAACqC,KAAK,GAAK;wBACpClF,KAAK,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpD,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAAC;wBACrB,IAAIqE,KAAK,EAAE;4BACT,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACM,IAAI,KAAK,wBAAwB,EAAE;gCAC9DF,OAAO,EAAE,CAAC;4BACZ,OAAO;gCACLC,MAAM,CAACL,KAAK,CAAC,CAAC;4BAChB,CAAC;wBACH,OAAO;4BACLI,OAAO,EAAE,CAAC;wBACZ,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,OAAO;oBACLtF,KAAK,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC+B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,IAAI,CAAClB,QAAQ,GAAG,IAAI,CAAC;oBACrByE,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAA;SAAA,EACJ;YACE,oEAAoE;YACpEG,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC3D,IAAI,CAAC,qBAAqB,CAAC;SACvE,CACF,CAAC;IACJ;IAEOP,aAAa,CAACd,OAAgE,GAAG,EAAE,EAAE;QAC1F,IAAI,CAAC,IAAI,CAACK,UAAU,EAAE;YACpB4E,IAAAA,OAAM,EAAA,QAAA,EAACjF,OAAO,QAAM,GAAbA,KAAAA,CAAa,GAAbA,OAAO,CAAEgC,IAAI,EAAE,+BAA+B,CAAC,CAAC;YACvD,IAAI,CAAC3B,UAAU,GAAG,IAAI6E,WAAU,WAAA,CAAClF,OAAO,CAACsC,QAAQ,EAAE;gBACjDN,IAAI,EAAEhC,OAAO,CAACgC,IAAI;gBAClBmD,YAAY,EAAE,IAAI,CAACA,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAC;aAC3C,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAACZ,UAAU,CAAC;IACzB;IAEOuD,mBAAmB,CAACwB,IAA+B,GAAG,EAAE,EAAE;QAC/D,OAAO,IAAI,CAAC3E,WAAW,GAClB,IAAI,CAACK,aAAa,EAAE,CAACuE,qBAAqB,CAACD,IAAI,CAAC,IAAI,IAAI,CAACvB,eAAe,EAAE,GAC3E,IAAI,CAAC/C,aAAa,EAAE,CAACE,YAAY,CAAC;YAAE,GAAGoE,IAAI;YAAEE,MAAM,EAAE,KAAK;SAAE,CAAC,CAAC;IACpE;IAEA,4DAA4D,GACrDzB,eAAe,CAAC7D,OAAmC,GAAG,EAAE,EAAiB;QAC9E,MAAMG,QAAQ,GAAG,IAAI,CAACqD,WAAW,EAAE,AAAC;QACpC,IAAI,CAACrD,CAAAA,QAAQ,QAAU,GAAlBA,KAAAA,CAAkB,GAAlBA,QAAQ,CAAEmC,QAAQ,CAAA,EAAE;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yFAAyF;QACzF,IAAI,IAAI,CAACrC,MAAM,IAAI,IAAI,CAACA,MAAM,YAAYwD,cAAa,cAAA,EAAE;YACvD,OAAO,IAAI,CAAC3C,aAAa,EAAE,CAACE,YAAY,EAAE,CAAC;QAC7C,CAAC;QAED,MAAM,EAAEsB,QAAQ,CAAA,EAAE,GAAGnC,QAAQ,AAAC;QAC9B,IAAIH,OAAO,CAAC4C,QAAQ,KAAK,WAAW,EAAE;YACpC,OAAO,CAAC,EAAEN,QAAQ,CAACG,QAAQ,CAAC,aAAa,EAAEH,QAAQ,CAACN,IAAI,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,OAAOM,QAAQ,CAACE,GAAG,IAAI,IAAI,CAAC;IAC9B;IAEO+C,uBAAuB,CAACvF,OAAmC,GAAG,EAAE,EAAU;QAC/E,MAAMG,QAAQ,GAAG,IAAI,CAAC0D,eAAe,CAAC7D,OAAO,CAAC,AAAC;QAC/C,IAAI,CAACG,QAAQ,EAAE;YACb,MAAM,IAAI8B,OAAY,aAAA,CACpB,YAAY,EACZ,CAAC,sEAAsE,EAAE,IAAI,CAACZ,IAAI,CAAC,CAAC,CAAC,CACtF,CAAC;QACJ,CAAC;QAED,OAAOlB,QAAQ,CAAC;IAClB;IAEA,sCAAsC,GAC/BqF,qBAAqB,GAAW;QACrC,IAAI,IAAI,CAACnE,IAAI,KAAK,OAAO,EAAE;YACzB,MAAM,IAAIY,OAAY,aAAA,CACpB,YAAY,EACZ,CAAC,+CAA+C,EAAE,IAAI,CAACZ,IAAI,CAAC,CAAC,CAAC,CAC/D,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAACP,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEsE,MAAM,EAAE,MAAM;SAAE,CAAC,CAAC;IAC/D;IAEA,wCAAwC,GACjCH,YAAY,GAAkB;YAC5B,GAAW;QAAlB,OAAO,CAAA,CAAA,GAAW,GAAX,IAAI,CAAClF,MAAM,SAAc,GAAzB,KAAA,CAAyB,GAAzB,GAAW,CAAEwF,YAAY,EAAE,KAAI,IAAI,CAAC;IAC7C;IAEA,sCAAsC,SACzBC,iBAAiB,CAC5BC,YAAwD,EACxDC,QAAqC,GAAG,EAAE,EAC1C;QACA,IAAID,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAME,SAAS,GAAG,IAAI,CAAChC,eAAe,CAAC;gBAAEjB,QAAQ,EAAE,WAAW;aAAE,CAAC,AAAC;YAClE,qDAAqD;YACrD,MAAMJ,GAAG,GAAG,IAAI,CAACnB,IAAI,KAAK,OAAO,GAAI,IAAI,CAAC8D,YAAY,EAAE,IAAIU,SAAS,GAAIA,SAAS,AAAC;YACnF,MAAMC,IAAAA,KAAgB,iBAAA,EAACtD,GAAG,CAAE,CAAC;YAC7B,OAAO;gBAAEA,GAAG;aAAE,CAAC;QACjB,CAAC;QAED,MAAMsB,OAAO,GAAG,IAAI,CAACxC,iBAAiB,EAAE,GAAI,IAAI,CAACb,WAAW,GAAG,QAAQ,GAAG,MAAM,GAAI,KAAK,AAAC;QAC1F,MAAMsF,OAAO,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACL,YAAY,CAAC,AAAC;QACjE,OAAOI,OAAO,CAACE,SAAS,CAAC;YAAEnC,OAAO;SAAE,EAAE8B,QAAQ,CAAC,CAAC;IAClD;IAEA,sCAAsC,SACzBM,sBAAsB,CACjCP,YAA4C,EAC5CQ,WAAuB,GAAG,EAAE,EAC5BP,QAAqC,GAAG,EAAE,EAC1C;QACA,MAAM9B,OAAO,GAAG,IAAI,CAACxC,iBAAiB,EAAE,GAAI,IAAI,CAACb,WAAW,GAAG,QAAQ,GAAG,MAAM,GAAI,KAAK,AAAC;QAC1F,IAAIqD,OAAO,KAAK,QAAQ,EAAE;YACxB,MAAM,IAAI7B,OAAY,aAAA,CACpB,CAAC,+IAA+I,EAAE6B,OAAO,CAAC,CAAC,CAAC,CAC7J,CAAC;QACJ,CAAC;QAED,MAAMiC,OAAO,GAAG,MAAM,IAAI,CAACC,uBAAuB,CAACL,YAAY,CAAC,AAAC;QACjE,OAAOI,OAAO,CAACE,SAAS,CAAC;YAAEnC,OAAO,EAAE,QAAQ;YAAEsC,KAAK,EAAED,WAAW;SAAE,EAAEP,QAAQ,CAAC,CAAC;IAChF;IAEA,wCAAwC,GAC9BS,YAAY,GAAW;QAC/B,OAAO,IAAI,CAACvF,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEsE,MAAM,EAAE,KAAK;SAAE,CAAC,CAAC;IAC9D;IAEA,yEAAyE,GAC/DgB,qBAAqB,GAAY;QACzC,OACE,CAACzD,IAAG,IAAA,CAAC0D,qBAAqB,IAC1B,2DAA2D;QAC3D,CAAC,IAAI,CAAC9F,WAAW,IACjB,qCAAqC;QACrC,CAAC,CAAC+F,YAAW,EAAA,QAAA,CAACC,MAAM,CAAC,IAAI,CAAC3G,WAAW,EAAE,iBAAiB,CAAC,CACzD;IACJ;IAEA,sDAAsD,GAC/C4G,cAAc,CAACC,QAA+C,GAAG,IAAI,EAAiB;QAC3F,IAAI,CAAC,IAAI,CAACL,qBAAqB,EAAE,EAAE;YACjChH,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OACE,IAAI,CAACwB,aAAa,EAAE,CAAC8F,mBAAmB,CACtC,EAAE,EACFD,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAGA,QAAQ,KAAK,WAAW,GAAG,KAAK,GAAG,IAAI,CAC9E,IAAI,IAAI,CACT;IACJ;IAEOE,mBAAmB,GAAW;QACnC,OAAO,IAAIC,GAAG,CACZ,sBAAsB,EACtB,IAAI,CAAChG,aAAa,EAAE,CAACE,YAAY,CAAC;YAAEsE,MAAM,EAAE,MAAM;SAAE,CAAC,CACtD,CAACyB,QAAQ,EAAE,CAAC;IACf;UAEgBf,uBAAuB,CAACW,QAAwC,EAAE;QAChF,IAAI,CAAC,IAAI,CAACvG,gBAAgB,CAACuG,QAAQ,CAAC,EAAE;gBAEvB,GAAkB;YAD/B,MAAMK,OAAO,GAAGxH,iBAAiB,CAACmH,QAAQ,CAAC,EAAE,AAAC;YAC9C,MAAM3E,IAAI,GAAG,CAAA,GAAkB,GAAlB,IAAI,CAACwB,WAAW,EAAE,SAAU,GAA5B,KAAA,CAA4B,GAA5B,GAAkB,CAAElB,QAAQ,CAACN,IAAI,AAAC;YAC/C,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE;gBAC7B,MAAM,IAAI4B,OAAY,aAAA,CACpB,YAAY,EACZ,oEAAoE,CACrE,CAAC;YACJ,CAAC;YACD3C,KAAK,CAAC,CAAC,qCAAqC,EAAEqH,QAAQ,CAAC,QAAQ,EAAE3E,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,IAAI,CAAC5B,gBAAgB,CAACuG,QAAQ,CAAC,GAAG,IAAIK,OAAO,CAAC,IAAI,CAAClH,WAAW,EAAEkC,IAAI,EAAE;gBACpEiF,mBAAmB,EAAE,IAAI,CAAC5G,UAAU,CAACgF,qBAAqB,CAACpE,IAAI,CAAC,IAAI,CAACZ,UAAU,CAAC;gBAChFgG,YAAY,EAAE,IAAI,CAACA,YAAY,CAACpF,IAAI,CAAC,IAAI,CAAC;gBAC1CyF,cAAc,EAAE,IAAI,CAACA,cAAc,CAACzF,IAAI,CAAC,IAAI,EAAE0F,QAAQ,CAAC;gBACxD9C,eAAe,EAAE,IAAI,CAACA,eAAe,CAAC5C,IAAI,CAAC,IAAI,EAAE;oBAAE2B,QAAQ,EAAE,WAAW;iBAAE,CAAC;aAC5E,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAACxC,gBAAgB,CAACuG,QAAQ,CAAC,CAAC;IACzC;CACD"}