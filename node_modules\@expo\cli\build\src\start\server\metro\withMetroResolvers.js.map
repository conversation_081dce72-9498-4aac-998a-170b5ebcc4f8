{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroResolvers.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport chalk from 'chalk';\nimport { ConfigT as MetroConfig } from 'metro-config';\nimport type { ResolutionContext, CustomResolutionContext } from 'metro-resolver';\nimport * as metroResolver from 'metro-resolver';\nimport path from 'path';\n\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:metro:withMetroResolvers') as typeof console.log;\n\nexport type MetroResolver = NonNullable<MetroConfig['resolver']['resolveRequest']>;\n\n/** Expo Metro Resolvers can return `null` to skip without throwing an error. Metro Resolvers will throw either a `FailedToResolveNameError` or `FailedToResolvePathError`. */\nexport type ExpoCustomMetroResolver = (\n  ...args: Parameters<MetroResolver>\n) => ReturnType<MetroResolver> | null;\n\n/** @returns `MetroResolver` utilizing the upstream `resolve` method. */\nexport function getDefaultMetroResolver(projectRoot: string): MetroResolver {\n  return (context: ResolutionContext, moduleName: string, platform: string | null) => {\n    return metroResolver.resolve(context, moduleName, platform);\n  };\n}\n\nfunction optionsKeyForContext(context: ResolutionContext) {\n  const canonicalize = require('metro-core/src/canonicalize');\n\n  // Compound key for the resolver cache\n  return JSON.stringify(context.customResolverOptions ?? {}, canonicalize) ?? '';\n}\n\n/**\n * Extend the Metro config `resolver.resolveRequest` method with additional resolvers that can\n * exit early by returning a `Resolution` or skip to the next resolver by returning `null`.\n *\n * @param config Metro config.\n * @param resolvers custom MetroResolver to chain.\n * @returns a new `MetroConfig` with the `resolver.resolveRequest` method chained.\n */\nexport function withMetroResolvers(\n  config: MetroConfig,\n  resolvers: ExpoCustomMetroResolver[]\n): MetroConfig {\n  debug(\n    `Appending ${\n      resolvers.length\n    } custom resolvers to Metro config. (has custom resolver: ${!!config.resolver?.resolveRequest})`\n  );\n  // const hasUserDefinedResolver = !!config.resolver?.resolveRequest;\n  // const defaultResolveRequest = getDefaultMetroResolver(projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const upstreamResolveRequest = context.resolveRequest;\n\n        const universalContext = {\n          ...context,\n          resolveRequest(\n            ctx: CustomResolutionContext,\n            moduleName: string,\n            platform: string | null\n          ) {\n            for (const resolver of resolvers) {\n              try {\n                const res = resolver(ctx, moduleName, platform);\n                if (res) {\n                  return res;\n                }\n              } catch (error: any) {\n                // If the error is directly related to a resolver not being able to resolve a module, then\n                // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n                const isResolutionError =\n                  isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n                if (!isResolutionError) {\n                  throw error;\n                }\n                debug(\n                  `Custom resolver threw: ${error.constructor.name}. (module: ${moduleName}, platform: ${platform}, env: ${ctx.customResolverOptions?.environment}, origin: ${ctx.originModulePath})`\n                );\n              }\n            }\n            // If we haven't returned by now, use the original resolver or upstream resolver.\n            return upstreamResolveRequest(ctx, moduleName, platform);\n          },\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        const firstResolver = originalResolveRequest ?? universalContext.resolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n\n/**\n * Hook into the Metro resolver chain and mutate the context so users can resolve against our custom assumptions.\n * For example, this will set `preferNativePlatform` to false when bundling for web.\n * */\nexport function withMetroMutatedResolverContext(\n  config: MetroConfig,\n  getContext: (\n    ctx: CustomResolutionContext,\n    moduleName: string,\n    platform: string | null\n  ) => CustomResolutionContext\n): MetroConfig {\n  const defaultResolveRequest = getDefaultMetroResolver(config.projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const universalContext = getContext(context, moduleName, platform);\n        const firstResolver =\n          originalResolveRequest ?? universalContext.resolveRequest ?? defaultResolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n\nexport function withMetroErrorReportingResolver(config: MetroConfig): MetroConfig {\n  if (!env.EXPO_METRO_UNSTABLE_ERRORS) {\n    return config;\n  }\n\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  function mutateResolutionError(\n    error: Error,\n    context: ResolutionContext,\n    moduleName: string,\n    platform: string | null\n  ) {\n    const inputPlatform = platform ?? 'null';\n\n    const mapByOrigin = depGraph.get(optionsKeyForContext(context));\n    const mapByPlatform = mapByOrigin?.get(inputPlatform);\n\n    if (!mapByPlatform) {\n      return error;\n    }\n\n    // collect all references inversely using some expensive lookup\n\n    const getReferences = (origin: string) => {\n      const inverseOrigin: { origin: string; previous: string; request: string }[] = [];\n\n      if (!mapByPlatform) {\n        return inverseOrigin;\n      }\n\n      for (const [originKey, mapByTarget] of mapByPlatform) {\n        // search comparing origin to path\n\n        const found = [...mapByTarget.values()].find((resolution) => resolution.path === origin);\n        if (found) {\n          inverseOrigin.push({\n            origin,\n            previous: originKey,\n            request: found.request,\n          });\n        }\n      }\n\n      return inverseOrigin;\n    };\n\n    const pad = (num: number) => {\n      return new Array(num).fill(' ').join('');\n    };\n\n    const root = config.server?.unstable_serverRoot ?? config.projectRoot;\n\n    type InverseDepResult = {\n      origin: string;\n      request: string;\n      previous: InverseDepResult[];\n    };\n    const recurseBackWithLimit = (\n      req: { origin: string; request: string },\n      limit: number,\n      count: number = 0\n    ) => {\n      const results: InverseDepResult = {\n        origin: req.origin,\n        request: req.request,\n        previous: [],\n      };\n\n      if (count >= limit) {\n        return results;\n      }\n\n      const inverse = getReferences(req.origin);\n      for (const match of inverse) {\n        // Use more qualified name if possible\n        // results.origin = match.origin;\n        // Found entry point\n        if (req.origin === match.previous) {\n          continue;\n        }\n        results.previous.push(\n          recurseBackWithLimit({ origin: match.previous, request: match.request }, limit, count + 1)\n        );\n      }\n      return results;\n    };\n\n    const inverseTree = recurseBackWithLimit(\n      { origin: context.originModulePath, request: moduleName },\n      // TODO: Do we need to expose this?\n      35\n    );\n\n    if (inverseTree.previous.length > 0) {\n      debug('Found inverse graph:', JSON.stringify(inverseTree, null, 2));\n      let extraMessage = chalk.bold('Import stack:');\n      const printRecursive = (tree: InverseDepResult, depth: number = 0) => {\n        let filename = path.relative(root, tree.origin);\n        if (filename.match(/\\?ctx=[\\w\\d]+$/)) {\n          filename = filename.replace(/\\?ctx=[\\w\\d]+$/, chalk.dim(' (require.context)'));\n        } else {\n          let formattedRequest = chalk.green(`\"${tree.request}\"`);\n\n          if (\n            // If bundling for web and the import is pulling internals from outside of react-native\n            // then mark it as an invalid import.\n            inputPlatform === 'web' &&\n            !/^(node_modules\\/)?react-native\\//.test(filename) &&\n            tree.request.match(/^react-native\\/.*/)\n          ) {\n            formattedRequest =\n              formattedRequest +\n              chalk`\\n          {yellow Importing react-native internals is not supported on web.}`;\n          }\n\n          filename = filename + chalk`\\n{gray  |} {cyan import} ${formattedRequest}\\n`;\n        }\n        let line = '\\n' + pad(depth) + chalk.gray(' ') + filename;\n        if (filename.match(/node_modules/)) {\n          line = chalk.gray(\n            // Bold the node module name\n            line.replace(/node_modules\\/([^/]+)/, (_match, p1) => {\n              return 'node_modules/' + chalk.bold(p1);\n            })\n          );\n        }\n        extraMessage += line;\n        for (const child of tree.previous) {\n          printRecursive(\n            child,\n            // Only add depth if there are multiple children\n            tree.previous.length > 1 ? depth + 1 : depth\n          );\n        }\n      };\n      printRecursive(inverseTree);\n\n      debug('inverse graph message:', extraMessage);\n\n      // @ts-expect-error\n      error._expoImportStack = extraMessage;\n    } else {\n      debug('Found no inverse tree for:', context.originModulePath);\n    }\n\n    return error;\n  }\n\n  const depGraph: Map<\n    // custom options\n    string,\n    Map<\n      // platform\n      string,\n      Map<\n        // origin module name\n        string,\n        Set<{\n          // required module name\n          path: string;\n          // This isn't entirely accurate since a module can be imported multiple times in a file,\n          // and use different names. But it's good enough for now.\n          request: string;\n        }>\n      >\n    >\n  > = new Map();\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const storeResult = (res: NonNullable<ReturnType<ExpoCustomMetroResolver>>) => {\n          const inputPlatform = platform ?? 'null';\n\n          const key = optionsKeyForContext(context);\n          if (!depGraph.has(key)) depGraph.set(key, new Map());\n          const mapByTarget = depGraph.get(key);\n          if (!mapByTarget!.has(inputPlatform)) mapByTarget!.set(inputPlatform, new Map());\n          const mapByPlatform = mapByTarget!.get(inputPlatform);\n          if (!mapByPlatform!.has(context.originModulePath))\n            mapByPlatform!.set(context.originModulePath, new Set());\n          const setForModule = mapByPlatform!.get(context.originModulePath)!;\n\n          const qualifiedModuleName = res?.type === 'sourceFile' ? res.filePath : moduleName;\n          setForModule.add({ path: qualifiedModuleName, request: moduleName });\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        try {\n          const firstResolver = originalResolveRequest ?? context.resolveRequest;\n          const res = firstResolver(context, moduleName, platform);\n          storeResult(res);\n          return res;\n        } catch (error: any) {\n          throw mutateResolutionError(error, context, moduleName, platform);\n        }\n      },\n    },\n  };\n}\n"], "names": ["getDefaultMetroResolver", "withMetroResolvers", "withMetroMutatedResolverContext", "withMetroErrorReportingResolver", "debug", "require", "projectRoot", "context", "moduleName", "platform", "metroResolver", "resolve", "optionsKeyForContext", "canonicalize", "JSON", "stringify", "customResolverOptions", "config", "resolvers", "length", "resolver", "resolveRequest", "originalResolveRequest", "upstreamResolveRequest", "universalContext", "ctx", "res", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "constructor", "name", "environment", "originModulePath", "firstResolver", "getContext", "defaultResolveRequest", "env", "EXPO_METRO_UNSTABLE_ERRORS", "mutateResolutionError", "inputPlatform", "mapByOrigin", "depGraph", "get", "mapByPlatform", "getReferences", "origin", "inverse<PERSON><PERSON>in", "<PERSON><PERSON><PERSON>", "mapByTarget", "found", "values", "find", "resolution", "path", "push", "previous", "request", "pad", "num", "Array", "fill", "join", "root", "server", "unstable_serverRoot", "recurseBackWithLimit", "req", "limit", "count", "results", "inverse", "match", "inverseTree", "extraMessage", "chalk", "bold", "printRecursive", "tree", "depth", "filename", "relative", "replace", "dim", "formattedRequest", "green", "test", "line", "gray", "_match", "p1", "child", "_expoImportStack", "Map", "storeResult", "key", "has", "set", "Set", "setForModule", "qualifiedModuleName", "type", "filePath", "add"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAmBgBA,uBAAuB,MAAvBA,uBAAuB;IAqBvBC,kBAAkB,MAAlBA,kBAAkB;IAuElBC,+BAA+B,MAA/BA,+BAA+B;IAyB/BC,+BAA+B,MAA/BA,+BAA+B;;;8DAxI7B,OAAO;;;;;;;+DAGM,gBAAgB;;;;;;;8DAC9B,MAAM;;;;;;6BAEgD,eAAe;qBAClE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,AAAsB,AAAC;AAU/E,SAASL,uBAAuB,CAACM,WAAmB,EAAiB;IAC1E,OAAO,CAACC,OAA0B,EAAEC,UAAkB,EAAEC,QAAuB,GAAK;QAClF,OAAOC,cAAa,EAAA,CAACC,OAAO,CAACJ,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAAC;IAC9D,CAAC,CAAC;AACJ,CAAC;AAED,SAASG,oBAAoB,CAACL,OAA0B,EAAE;IACxD,MAAMM,YAAY,GAAGR,OAAO,CAAC,6BAA6B,CAAC,AAAC;IAE5D,sCAAsC;IACtC,OAAOS,IAAI,CAACC,SAAS,CAACR,OAAO,CAACS,qBAAqB,IAAI,EAAE,EAAEH,YAAY,CAAC,IAAI,EAAE,CAAC;AACjF,CAAC;AAUM,SAASZ,kBAAkB,CAChCgB,MAAmB,EACnBC,SAAoC,EACvB;QAImDD,GAAe,EAIhDA,IAAe;IAP9Cb,KAAK,CACH,CAAC,UAAU,EACTc,SAAS,CAACC,MAAM,CACjB,yDAAyD,EAAE,CAAC,CAACF,CAAAA,CAAAA,GAAe,GAAfA,MAAM,CAACG,QAAQ,SAAgB,GAA/BH,KAAAA,CAA+B,GAA/BA,GAAe,CAAEI,cAAc,CAAA,CAAC,CAAC,CAAC,CACjG,CAAC;IACF,oEAAoE;IACpE,sEAAsE;IACtE,MAAMC,sBAAsB,GAAGL,CAAAA,IAAe,GAAfA,MAAM,CAACG,QAAQ,SAAgB,GAA/BH,KAAAA,CAA+B,GAA/BA,IAAe,CAAEI,cAAc,AAAC;IAE/D,OAAO;QACL,GAAGJ,MAAM;QACTG,QAAQ,EAAE;YACR,GAAGH,MAAM,CAACG,QAAQ;YAClBC,cAAc,EAACd,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;gBAC5C,MAAMc,sBAAsB,GAAGhB,OAAO,CAACc,cAAc,AAAC;gBAEtD,MAAMG,gBAAgB,GAAG;oBACvB,GAAGjB,OAAO;oBACVc,cAAc,EACZI,GAA4B,EAC5BjB,UAAkB,EAClBC,QAAuB,EACvB;wBACA,KAAK,MAAMW,QAAQ,IAAIF,SAAS,CAAE;4BAChC,IAAI;gCACF,MAAMQ,GAAG,GAAGN,QAAQ,CAACK,GAAG,EAAEjB,UAAU,EAAEC,QAAQ,CAAC,AAAC;gCAChD,IAAIiB,GAAG,EAAE;oCACP,OAAOA,GAAG,CAAC;gCACb,CAAC;4BACH,EAAE,OAAOC,KAAK,EAAO;oCASwFF,GAAyB;gCARpI,0FAA0F;gCAC1F,2FAA2F;gCAC3F,MAAMG,iBAAiB,GACrBC,IAAAA,YAA0B,2BAAA,EAACF,KAAK,CAAC,IAAIG,IAAAA,YAA0B,2BAAA,EAACH,KAAK,CAAC,AAAC;gCACzE,IAAI,CAACC,iBAAiB,EAAE;oCACtB,MAAMD,KAAK,CAAC;gCACd,CAAC;gCACDvB,KAAK,CACH,CAAC,uBAAuB,EAAEuB,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,WAAW,EAAExB,UAAU,CAAC,YAAY,EAAEC,QAAQ,CAAC,OAAO,EAAEgB,CAAAA,GAAyB,GAAzBA,GAAG,CAACT,qBAAqB,SAAa,GAAtCS,KAAAA,CAAsC,GAAtCA,GAAyB,CAAEQ,WAAW,CAAC,UAAU,EAAER,GAAG,CAACS,gBAAgB,CAAC,CAAC,CAAC,CACpL,CAAC;4BACJ,CAAC;wBACH,CAAC;wBACD,iFAAiF;wBACjF,OAAOX,sBAAsB,CAACE,GAAG,EAAEjB,UAAU,EAAEC,QAAQ,CAAC,CAAC;oBAC3D,CAAC;iBACF,AAAC;gBAEF,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,MAAM0B,aAAa,GAAGb,sBAAsB,IAAIE,gBAAgB,CAACH,cAAc,AAAC;gBAChF,OAAOc,aAAa,CAACX,gBAAgB,EAAEhB,UAAU,EAAEC,QAAQ,CAAC,CAAC;YAC/D,CAAC;SACF;KACF,CAAC;AACJ,CAAC;AAMM,SAASP,+BAA+B,CAC7Ce,MAAmB,EACnBmB,UAI4B,EACf;QAEkBnB,GAAe;IAD9C,MAAMoB,qBAAqB,GAAGrC,uBAAuB,CAACiB,MAAM,CAACX,WAAW,CAAC,AAAC;IAC1E,MAAMgB,sBAAsB,GAAGL,CAAAA,GAAe,GAAfA,MAAM,CAACG,QAAQ,SAAgB,GAA/BH,KAAAA,CAA+B,GAA/BA,GAAe,CAAEI,cAAc,AAAC;IAE/D,OAAO;QACL,GAAGJ,MAAM;QACTG,QAAQ,EAAE;YACR,GAAGH,MAAM,CAACG,QAAQ;YAClBC,cAAc,EAACd,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;gBAC5C,MAAMe,gBAAgB,GAAGY,UAAU,CAAC7B,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,AAAC;gBACnE,MAAM0B,aAAa,GACjBb,sBAAsB,IAAIE,gBAAgB,CAACH,cAAc,IAAIgB,qBAAqB,AAAC;gBACrF,OAAOF,aAAa,CAACX,gBAAgB,EAAEhB,UAAU,EAAEC,QAAQ,CAAC,CAAC;YAC/D,CAAC;SACF;KACF,CAAC;AACJ,CAAC;AAEM,SAASN,+BAA+B,CAACc,MAAmB,EAAe;QAKjDA,GAAe;IAJ9C,IAAI,CAACqB,IAAG,IAAA,CAACC,0BAA0B,EAAE;QACnC,OAAOtB,MAAM,CAAC;IAChB,CAAC;IAED,MAAMK,sBAAsB,GAAGL,CAAAA,GAAe,GAAfA,MAAM,CAACG,QAAQ,SAAgB,GAA/BH,KAAAA,CAA+B,GAA/BA,GAAe,CAAEI,cAAc,AAAC;IAE/D,SAASmB,qBAAqB,CAC5Bb,KAAY,EACZpB,OAA0B,EAC1BC,UAAkB,EAClBC,QAAuB,EACvB;YAuCaQ,GAAa;QAtC1B,MAAMwB,aAAa,GAAGhC,QAAQ,IAAI,MAAM,AAAC;QAEzC,MAAMiC,WAAW,GAAGC,QAAQ,CAACC,GAAG,CAAChC,oBAAoB,CAACL,OAAO,CAAC,CAAC,AAAC;QAChE,MAAMsC,aAAa,GAAGH,WAAW,QAAK,GAAhBA,KAAAA,CAAgB,GAAhBA,WAAW,CAAEE,GAAG,CAACH,aAAa,CAAC,AAAC;QAEtD,IAAI,CAACI,aAAa,EAAE;YAClB,OAAOlB,KAAK,CAAC;QACf,CAAC;QAED,+DAA+D;QAE/D,MAAMmB,aAAa,GAAG,CAACC,MAAc,GAAK;YACxC,MAAMC,aAAa,GAA4D,EAAE,AAAC;YAElF,IAAI,CAACH,aAAa,EAAE;gBAClB,OAAOG,aAAa,CAAC;YACvB,CAAC;YAED,KAAK,MAAM,CAACC,SAAS,EAAEC,WAAW,CAAC,IAAIL,aAAa,CAAE;gBACpD,kCAAkC;gBAElC,MAAMM,KAAK,GAAG;uBAAID,WAAW,CAACE,MAAM,EAAE;iBAAC,CAACC,IAAI,CAAC,CAACC,UAAU,GAAKA,UAAU,CAACC,IAAI,KAAKR,MAAM,CAAC,AAAC;gBACzF,IAAII,KAAK,EAAE;oBACTH,aAAa,CAACQ,IAAI,CAAC;wBACjBT,MAAM;wBACNU,QAAQ,EAAER,SAAS;wBACnBS,OAAO,EAAEP,KAAK,CAACO,OAAO;qBACvB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAOV,aAAa,CAAC;QACvB,CAAC,AAAC;QAEF,MAAMW,GAAG,GAAG,CAACC,GAAW,GAAK;YAC3B,OAAO,IAAIC,KAAK,CAACD,GAAG,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3C,CAAC,AAAC;QAEF,MAAMC,IAAI,GAAG/C,CAAAA,CAAAA,GAAa,GAAbA,MAAM,CAACgD,MAAM,SAAqB,GAAlChD,KAAAA,CAAkC,GAAlCA,GAAa,CAAEiD,mBAAmB,CAAA,IAAIjD,MAAM,CAACX,WAAW,AAAC;QAOtE,MAAM6D,oBAAoB,GAAG,CAC3BC,GAAwC,EACxCC,KAAa,EACbC,KAAa,GAAG,CAAC,GACd;YACH,MAAMC,OAAO,GAAqB;gBAChCxB,MAAM,EAAEqB,GAAG,CAACrB,MAAM;gBAClBW,OAAO,EAAEU,GAAG,CAACV,OAAO;gBACpBD,QAAQ,EAAE,EAAE;aACb,AAAC;YAEF,IAAIa,KAAK,IAAID,KAAK,EAAE;gBAClB,OAAOE,OAAO,CAAC;YACjB,CAAC;YAED,MAAMC,OAAO,GAAG1B,aAAa,CAACsB,GAAG,CAACrB,MAAM,CAAC,AAAC;YAC1C,KAAK,MAAM0B,KAAK,IAAID,OAAO,CAAE;gBAC3B,sCAAsC;gBACtC,iCAAiC;gBACjC,oBAAoB;gBACpB,IAAIJ,GAAG,CAACrB,MAAM,KAAK0B,KAAK,CAAChB,QAAQ,EAAE;oBACjC,SAAS;gBACX,CAAC;gBACDc,OAAO,CAACd,QAAQ,CAACD,IAAI,CACnBW,oBAAoB,CAAC;oBAAEpB,MAAM,EAAE0B,KAAK,CAAChB,QAAQ;oBAAEC,OAAO,EAAEe,KAAK,CAACf,OAAO;iBAAE,EAAEW,KAAK,EAAEC,KAAK,GAAG,CAAC,CAAC,CAC3F,CAAC;YACJ,CAAC;YACD,OAAOC,OAAO,CAAC;QACjB,CAAC,AAAC;QAEF,MAAMG,WAAW,GAAGP,oBAAoB,CACtC;YAAEpB,MAAM,EAAExC,OAAO,CAAC2B,gBAAgB;YAAEwB,OAAO,EAAElD,UAAU;SAAE,EACzD,mCAAmC;AACnC,UAAE,CACH,AAAC;QAEF,IAAIkE,WAAW,CAACjB,QAAQ,CAACtC,MAAM,GAAG,CAAC,EAAE;YACnCf,KAAK,CAAC,sBAAsB,EAAEU,IAAI,CAACC,SAAS,CAAC2D,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACpE,IAAIC,YAAY,GAAGC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,eAAe,CAAC,AAAC;YAC/C,MAAMC,cAAc,GAAG,CAACC,IAAsB,EAAEC,KAAa,GAAG,CAAC,GAAK;gBACpE,IAAIC,QAAQ,GAAG1B,KAAI,EAAA,QAAA,CAAC2B,QAAQ,CAAClB,IAAI,EAAEe,IAAI,CAAChC,MAAM,CAAC,AAAC;gBAChD,IAAIkC,QAAQ,CAACR,KAAK,kBAAkB,EAAE;oBACpCQ,QAAQ,GAAGA,QAAQ,CAACE,OAAO,mBAAmBP,MAAK,EAAA,QAAA,CAACQ,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBACjF,OAAO;oBACL,IAAIC,gBAAgB,GAAGT,MAAK,EAAA,QAAA,CAACU,KAAK,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACrB,OAAO,CAAC,CAAC,CAAC,CAAC,AAAC;oBAExD,IACE,uFAAuF;oBACvF,qCAAqC;oBACrCjB,aAAa,KAAK,KAAK,IACvB,CAAC,mCAAmC8C,IAAI,CAACN,QAAQ,CAAC,IAClDF,IAAI,CAACrB,OAAO,CAACe,KAAK,qBAAqB,EACvC;wBACAY,gBAAgB,GACdA,gBAAgB,GAChBT,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,8EAA8E,CAAC,CAAC;oBAC1F,CAAC;oBAEDK,QAAQ,GAAGA,QAAQ,GAAGL,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,0BAA0B,EAAES,gBAAgB,CAAC,EAAE,CAAC,CAAC;gBAC/E,CAAC;gBACD,IAAIG,IAAI,GAAG,IAAI,GAAG7B,GAAG,CAACqB,KAAK,CAAC,GAAGJ,MAAK,EAAA,QAAA,CAACa,IAAI,CAAC,GAAG,CAAC,GAAGR,QAAQ,AAAC;gBAC1D,IAAIA,QAAQ,CAACR,KAAK,gBAAgB,EAAE;oBAClCe,IAAI,GAAGZ,MAAK,EAAA,QAAA,CAACa,IAAI,CACf,4BAA4B;oBAC5BD,IAAI,CAACL,OAAO,0BAA0B,CAACO,MAAM,EAAEC,EAAE,GAAK;wBACpD,OAAO,eAAe,GAAGf,MAAK,EAAA,QAAA,CAACC,IAAI,CAACc,EAAE,CAAC,CAAC;oBAC1C,CAAC,CAAC,CACH,CAAC;gBACJ,CAAC;gBACDhB,YAAY,IAAIa,IAAI,CAAC;gBACrB,KAAK,MAAMI,KAAK,IAAIb,IAAI,CAACtB,QAAQ,CAAE;oBACjCqB,cAAc,CACZc,KAAK,EACL,gDAAgD;oBAChDb,IAAI,CAACtB,QAAQ,CAACtC,MAAM,GAAG,CAAC,GAAG6D,KAAK,GAAG,CAAC,GAAGA,KAAK,CAC7C,CAAC;gBACJ,CAAC;YACH,CAAC,AAAC;YACFF,cAAc,CAACJ,WAAW,CAAC,CAAC;YAE5BtE,KAAK,CAAC,wBAAwB,EAAEuE,YAAY,CAAC,CAAC;YAE9C,mBAAmB;YACnBhD,KAAK,CAACkE,gBAAgB,GAAGlB,YAAY,CAAC;QACxC,OAAO;YACLvE,KAAK,CAAC,4BAA4B,EAAEG,OAAO,CAAC2B,gBAAgB,CAAC,CAAC;QAChE,CAAC;QAED,OAAOP,KAAK,CAAC;IACf,CAAC;IAED,MAAMgB,QAAQ,GAkBV,IAAImD,GAAG,EAAE,AAAC;IAEd,OAAO;QACL,GAAG7E,MAAM;QACTG,QAAQ,EAAE;YACR,GAAGH,MAAM,CAACG,QAAQ;YAClBC,cAAc,EAACd,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAE;gBAC5C,MAAMsF,WAAW,GAAG,CAACrE,GAAqD,GAAK;oBAC7E,MAAMe,aAAa,GAAGhC,QAAQ,IAAI,MAAM,AAAC;oBAEzC,MAAMuF,GAAG,GAAGpF,oBAAoB,CAACL,OAAO,CAAC,AAAC;oBAC1C,IAAI,CAACoC,QAAQ,CAACsD,GAAG,CAACD,GAAG,CAAC,EAAErD,QAAQ,CAACuD,GAAG,CAACF,GAAG,EAAE,IAAIF,GAAG,EAAE,CAAC,CAAC;oBACrD,MAAM5C,WAAW,GAAGP,QAAQ,CAACC,GAAG,CAACoD,GAAG,CAAC,AAAC;oBACtC,IAAI,CAAC9C,WAAW,CAAE+C,GAAG,CAACxD,aAAa,CAAC,EAAES,WAAW,CAAEgD,GAAG,CAACzD,aAAa,EAAE,IAAIqD,GAAG,EAAE,CAAC,CAAC;oBACjF,MAAMjD,aAAa,GAAGK,WAAW,CAAEN,GAAG,CAACH,aAAa,CAAC,AAAC;oBACtD,IAAI,CAACI,aAAa,CAAEoD,GAAG,CAAC1F,OAAO,CAAC2B,gBAAgB,CAAC,EAC/CW,aAAa,CAAEqD,GAAG,CAAC3F,OAAO,CAAC2B,gBAAgB,EAAE,IAAIiE,GAAG,EAAE,CAAC,CAAC;oBAC1D,MAAMC,YAAY,GAAGvD,aAAa,CAAED,GAAG,CAACrC,OAAO,CAAC2B,gBAAgB,CAAC,AAAC,AAAC;oBAEnE,MAAMmE,mBAAmB,GAAG3E,CAAAA,GAAG,QAAM,GAATA,KAAAA,CAAS,GAATA,GAAG,CAAE4E,IAAI,CAAA,KAAK,YAAY,GAAG5E,GAAG,CAAC6E,QAAQ,GAAG/F,UAAU,AAAC;oBACnF4F,YAAY,CAACI,GAAG,CAAC;wBAAEjD,IAAI,EAAE8C,mBAAmB;wBAAE3C,OAAO,EAAElD,UAAU;qBAAE,CAAC,CAAC;gBACvE,CAAC,AAAC;gBAEF,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,IAAI;oBACF,MAAM2B,aAAa,GAAGb,sBAAsB,IAAIf,OAAO,CAACc,cAAc,AAAC;oBACvE,MAAMK,GAAG,GAAGS,aAAa,CAAC5B,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,AAAC;oBACzDsF,WAAW,CAACrE,GAAG,CAAC,CAAC;oBACjB,OAAOA,GAAG,CAAC;gBACb,EAAE,OAAOC,KAAK,EAAO;oBACnB,MAAMa,qBAAqB,CAACb,KAAK,EAAEpB,OAAO,EAAEC,UAAU,EAAEC,QAAQ,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;SACF;KACF,CAAC;AACJ,CAAC"}