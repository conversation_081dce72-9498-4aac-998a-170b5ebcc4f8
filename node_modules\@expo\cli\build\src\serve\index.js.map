{"version": 3, "sources": ["../../../src/serve/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\n\nexport const expoServe: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--port': Number,\n\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Host the production server locally`,\n      chalk`npx expo serve {dim <dir>}`,\n      [\n        chalk`<dir>            Directory of the Expo project. {dim Default: Current working directory}`,\n        `--port <number>  Port to host the server on`,\n        `-h, --help       Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo config -h` shows as fast as possible.\n  const [\n    // ./configAsync\n    { serveAsync },\n    // ../utils/errors\n    { logCmdError },\n  ] = await Promise.all([import('./serveAsync.js'), import('../utils/errors.js')]);\n\n  return serveAsync(getProjectRoot(args), {\n    isDefaultDirectory: !args._[0],\n    // Parsed options\n    port: args['--port'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoServe", "argv", "args", "assertArgs", "Boolean", "Number", "printHelp", "chalk", "join", "serveAsync", "logCmdError", "Promise", "all", "getProjectRoot", "isDefaultDirectory", "_", "port", "catch"], "mappings": "AAAA;;;;;+<PERSON><PERSON>a<PERSON>,WAAS;;aAATA,SAAS;;;8DALJ,OAAO;;;;;;sBAG6B,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,MAAMA,SAAS,GAAY,OAAOC,IAAI,GAAK;IAChD,MAAMC,IAAI,GAAGC,IAAAA,KAAU,WAAA,EACrB;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,QAAQ,EAAEC,MAAM;QAEhB,UAAU;QACV,IAAI,EAAE,QAAQ;KACf,EACDJ,IAAI,CACL,AAAC;IAEF,IAAIC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBI,IAAAA,KAAS,UAAA,EACP,CAAC,kCAAkC,CAAC,EACpCC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,0BAA0B,CAAC,EACjC;YACEA,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,wFAAwF,CAAC;YAC/F,CAAC,2CAA2C,CAAC;YAC7C,CAAC,2BAA2B,CAAC;SAC9B,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACJ,CAAC;IAED,wFAAwF;IACxF,MAAM,CACJ,gBAAgB;IAChB,EAAEC,UAAU,CAAA,EAAE,EACd,kBAAkB;IAClB,EAAEC,WAAW,CAAA,EAAE,GAChB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;QAAC,iEAAA,OAAM,CAAC,iBAAiB,GAAC;QAAE,iEAAA,OAAM,CAAC,oBAAoB,GAAC;KAAC,CAAC,AAAC;IAEjF,OAAOH,UAAU,CAACI,IAAAA,KAAc,eAAA,EAACX,IAAI,CAAC,EAAE;QACtCY,kBAAkB,EAAE,CAACZ,IAAI,CAACa,CAAC,CAAC,CAAC,CAAC;QAC9B,iBAAiB;QACjBC,IAAI,EAAEd,IAAI,CAAC,QAAQ,CAAC;KACrB,CAAC,CAACe,KAAK,CAACP,WAAW,CAAC,CAAC;AACxB,CAAC,AAAC"}