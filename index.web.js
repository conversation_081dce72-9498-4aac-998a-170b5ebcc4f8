import React from 'react';
import ReactDOM from 'react-dom/client';
import ProductsScreenWeb from './src/screens/store/ProductsScreen.web';

// Simple test component
const TestApp = () => {
  return (
    <div style={{
      padding: 20,
      fontFamily: 'system-ui, -apple-system, sans-serif',
      backgroundColor: '#F7F3E9',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#1A1A1A', marginBottom: 20 }}>🏪 LOSCC Store Management</h1>
      <ProductsScreenWeb />
    </div>
  );
};

// Get the root element
const container = document.getElementById('root');
const root = ReactDOM.createRoot(container);

// Render the app
root.render(<TestApp />);
