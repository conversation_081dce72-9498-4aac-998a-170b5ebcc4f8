{"version": 3, "sources": ["../../../../src/utils/tsconfig/loadTsConfigPaths.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport path from 'path';\n\nimport { evaluateTsConfig, importTypeScriptFromProjectOptionally } from './evaluateTsConfig';\nimport { fileExistsAsync } from '../dir';\n\nexport type TsConfigPaths = {\n  paths?: Record<string, string[]>;\n  baseUrl?: string;\n};\n\ntype ConfigReadResults = [\n  string,\n  {\n    compilerOptions?: {\n      baseUrl?: string;\n      paths?: Record<string, string[]>;\n    };\n  },\n];\n\nconst debug = require('debug')('expo:utils:tsconfig:load') as typeof console.log;\n\nexport async function loadTsConfigPathsAsync(dir: string): Promise<TsConfigPaths | null> {\n  const options = (await readTsconfigAsync(dir)) ?? (await readJsconfigAsync(dir));\n  if (options) {\n    const [, config] = options;\n    return {\n      paths: config.compilerOptions?.paths,\n      baseUrl: config.compilerOptions?.baseUrl\n        ? path.resolve(dir, config.compilerOptions.baseUrl)\n        : undefined,\n    };\n  }\n  return null;\n}\n\nasync function readJsconfigAsync(projectRoot: string): Promise<null | ConfigReadResults> {\n  const configPath = path.join(projectRoot, 'jsconfig.json');\n  if (await fileExistsAsync(configPath)) {\n    const config = await JsonFile.readAsync(configPath, { json5: true });\n    if (config) {\n      return [configPath, config];\n    }\n  }\n  return null;\n}\n\n// TODO: Refactor for speed\nexport async function readTsconfigAsync(projectRoot: string): Promise<null | ConfigReadResults> {\n  const configPath = path.join(projectRoot, 'tsconfig.json');\n  if (await fileExistsAsync(configPath)) {\n    // We need to fully evaluate the tsconfig to get the baseUrl and paths in case they were applied in `extends`.\n    const ts = importTypeScriptFromProjectOptionally(projectRoot);\n    if (ts) {\n      return [configPath, evaluateTsConfig(ts, configPath)];\n    }\n    debug(`typescript module not found in: ${projectRoot}`);\n  }\n  return null;\n}\n"], "names": ["loadTsConfigPathsAsync", "readTsconfigAsync", "debug", "require", "dir", "options", "readJsconfigAsync", "config", "paths", "compilerOptions", "baseUrl", "path", "resolve", "undefined", "projectRoot", "config<PERSON><PERSON>", "join", "fileExistsAsync", "JsonFile", "readAsync", "json5", "ts", "importTypeScriptFromProjectOptionally", "evaluateTsConfig"], "mappings": "AAAA;;;;;;;;;;;IAuBsBA,sBAAsB,MAAtBA,sBAAsB;IA0BtBC,iBAAiB,MAAjBA,iBAAiB;;;8DAjDlB,iBAAiB;;;;;;;8DACrB,MAAM;;;;;;kCAEiD,oBAAoB;qBAC5D,QAAQ;;;;;;AAiBxC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,AAAsB,AAAC;AAE1E,eAAeH,sBAAsB,CAACI,GAAW,EAAiC;IACvF,MAAMC,OAAO,GAAG,AAAC,MAAMJ,iBAAiB,CAACG,GAAG,CAAC,IAAM,MAAME,iBAAiB,CAACF,GAAG,CAAC,AAAC,AAAC;IACjF,IAAIC,OAAO,EAAE;YAGFE,GAAsB,EACpBA,IAAsB;QAHjC,MAAM,GAAGA,MAAM,CAAC,GAAGF,OAAO,AAAC;QAC3B,OAAO;YACLG,KAAK,EAAED,CAAAA,GAAsB,GAAtBA,MAAM,CAACE,eAAe,SAAO,GAA7BF,KAAAA,CAA6B,GAA7BA,GAAsB,CAAEC,KAAK;YACpCE,OAAO,EAAEH,CAAAA,CAAAA,IAAsB,GAAtBA,MAAM,CAACE,eAAe,SAAS,GAA/BF,KAAAA,CAA+B,GAA/BA,IAAsB,CAAEG,OAAO,CAAA,GACpCC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,GAAG,EAAEG,MAAM,CAACE,eAAe,CAACC,OAAO,CAAC,GACjDG,SAAS;SACd,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAeP,iBAAiB,CAACQ,WAAmB,EAAqC;IACvF,MAAMC,UAAU,GAAGJ,KAAI,EAAA,QAAA,CAACK,IAAI,CAACF,WAAW,EAAE,eAAe,CAAC,AAAC;IAC3D,IAAI,MAAMG,IAAAA,IAAe,gBAAA,EAACF,UAAU,CAAC,EAAE;QACrC,MAAMR,MAAM,GAAG,MAAMW,SAAQ,EAAA,QAAA,CAACC,SAAS,CAACJ,UAAU,EAAE;YAAEK,KAAK,EAAE,IAAI;SAAE,CAAC,AAAC;QACrE,IAAIb,MAAM,EAAE;YACV,OAAO;gBAACQ,UAAU;gBAAER,MAAM;aAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAGM,eAAeN,iBAAiB,CAACa,WAAmB,EAAqC;IAC9F,MAAMC,UAAU,GAAGJ,KAAI,EAAA,QAAA,CAACK,IAAI,CAACF,WAAW,EAAE,eAAe,CAAC,AAAC;IAC3D,IAAI,MAAMG,IAAAA,IAAe,gBAAA,EAACF,UAAU,CAAC,EAAE;QACrC,8GAA8G;QAC9G,MAAMM,EAAE,GAAGC,IAAAA,iBAAqC,sCAAA,EAACR,WAAW,CAAC,AAAC;QAC9D,IAAIO,EAAE,EAAE;YACN,OAAO;gBAACN,UAAU;gBAAEQ,IAAAA,iBAAgB,iBAAA,EAACF,EAAE,EAAEN,UAAU,CAAC;aAAC,CAAC;QACxD,CAAC;QACDb,KAAK,CAAC,CAAC,gCAAgC,EAAEY,WAAW,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}