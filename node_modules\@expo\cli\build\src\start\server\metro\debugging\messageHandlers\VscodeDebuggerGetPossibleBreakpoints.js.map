{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeDebuggerGetPossibleBreakpoints.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * <PERSON><PERSON> doesn't seem to handle this request, but `locations` have to be returned.\n * Respond with an empty location to make it \"spec compliant\" with Chrome DevTools.\n */\nexport class VscodeDebuggerGetPossibleBreakpointsHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<DebuggerGetPossibleBreakpoints>) {\n    if (message.method === 'Debugger.getPossibleBreakpoints') {\n      return this.sendToDebugger<DeviceResponse<DebuggerGetPossibleBreakpoints>>({\n        id: message.id,\n        result: { locations: [] },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Debugger/#method-getPossibleBreakpoints */\nexport type DebuggerGetPossibleBreakpoints = CdpMessage<\n  'Debugger.getPossibleBreakpoints',\n  Protocol.Debugger.GetPossibleBreakpointsRequest,\n  Protocol.Debugger.GetPossibleBreakpointsResponse\n>;\n"], "names": ["VscodeDebuggerGetPossibleBreakpointsHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "sendToDebugger", "id", "result", "locations"], "mappings": "AAAA;;;;+BAUaA,6CAA2C;;aAA3CA,2CAA2C;;gCARzB,mBAAmB;iCAClB,oBAAoB;AAO7C,MAAMA,2CAA2C,SAASC,eAAc,eAAA;IAC7EC,SAAS,GAAG;QACV,OAAOC,IAAAA,gBAAe,gBAAA,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,CAAC,KAAK,QAAQ,CAAC;IAC/D;IAEAC,qBAAqB,CAACC,OAAwD,EAAE;QAC9E,IAAIA,OAAO,CAACC,MAAM,KAAK,iCAAiC,EAAE;YACxD,OAAO,IAAI,CAACC,cAAc,CAAiD;gBACzEC,EAAE,EAAEH,OAAO,CAACG,EAAE;gBACdC,MAAM,EAAE;oBAAEC,SAAS,EAAE,EAAE;iBAAE;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf;CACD"}