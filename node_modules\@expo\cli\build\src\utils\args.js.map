{"version": 3, "sources": ["../../../src/utils/args.ts"], "sourcesContent": ["// Common utilities for interacting with `args` library.\n// These functions should be used by every command.\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport { existsSync } from 'fs';\nimport { resolve } from 'path';\n\nimport * as Log from '../log';\n\n/**\n * Parse the first argument as a project directory.\n *\n * @returns valid project directory.\n */\nexport function getProjectRoot(args: arg.Result<arg.Spec>) {\n  const projectRoot = resolve(args._[0] || '.');\n\n  if (!existsSync(projectRoot)) {\n    Log.exit(`Invalid project root: ${projectRoot}`);\n  }\n\n  return projectRoot;\n}\n\n/**\n * Parse args and assert unknown options.\n *\n * @param schema the `args` schema for parsing the command line arguments.\n * @param argv extra strings\n * @returns processed args object.\n */\nexport function assertArgs(schema: arg.Spec, argv?: string[]): arg.Result<arg.Spec> {\n  return assertWithOptionsArgs(schema, { argv });\n}\n\nexport function assertWithOptionsArgs(\n  schema: arg.Spec,\n  options: arg.Options\n): arg.Result<arg.Spec> {\n  try {\n    return arg(schema, options);\n  } catch (error: any) {\n    // Handle errors caused by user input.\n    // Only errors from `arg`, which does not start with `ARG_CONFIG_` are user input errors.\n    // See: https://github.com/vercel/arg/releases/tag/5.0.0\n    if ('code' in error && error.code.startsWith('ARG_') && !error.code.startsWith('ARG_CONFIG_')) {\n      Log.exit(error.message, 1);\n    }\n    // Otherwise rethrow the error.\n    throw error;\n  }\n}\n\nexport function printHelp(info: string, usage: string, options: string, extra: string = ''): never {\n  Log.exit(\n    chalk`\n  {bold Info}\n    ${info}\n\n  {bold Usage}\n    {dim $} ${usage}\n\n  {bold Options}\n    ${options.split('\\n').join('\\n    ')}\n` + extra,\n    0\n  );\n}\n"], "names": ["getProjectRoot", "assertArgs", "assertWithOptionsArgs", "printHelp", "args", "projectRoot", "resolve", "_", "existsSync", "Log", "exit", "schema", "argv", "options", "arg", "error", "code", "startsWith", "message", "info", "usage", "extra", "chalk", "split", "join"], "mappings": "AAAA,wDAAwD;AACxD,mDAAmD;AACnD;;;;;;;;;;;IAYgBA,cAAc,MAAdA,cAAc;IAiBdC,UAAU,MAAVA,UAAU;IAIVC,qBAAqB,MAArBA,qBAAqB;IAkBrBC,SAAS,MAATA,SAAS;;;8DAnDT,KAAK;;;;;;;8DACH,OAAO;;;;;;;yBACE,IAAI;;;;;;;yBACP,MAAM;;;;;;2DAET,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB,SAASH,cAAc,CAACI,IAA0B,EAAE;IACzD,MAAMC,WAAW,GAAGC,IAAAA,KAAO,EAAA,QAAA,EAACF,IAAI,CAACG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,AAAC;IAE9C,IAAI,CAACC,IAAAA,GAAU,EAAA,WAAA,EAACH,WAAW,CAAC,EAAE;QAC5BI,IAAG,CAACC,IAAI,CAAC,CAAC,sBAAsB,EAAEL,WAAW,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,OAAOA,WAAW,CAAC;AACrB,CAAC;AASM,SAASJ,UAAU,CAACU,MAAgB,EAAEC,IAAe,EAAwB;IAClF,OAAOV,qBAAqB,CAACS,MAAM,EAAE;QAAEC,IAAI;KAAE,CAAC,CAAC;AACjD,CAAC;AAEM,SAASV,qBAAqB,CACnCS,MAAgB,EAChBE,OAAoB,EACE;IACtB,IAAI;QACF,OAAOC,IAAAA,IAAG,EAAA,QAAA,EAACH,MAAM,EAAEE,OAAO,CAAC,CAAC;IAC9B,EAAE,OAAOE,KAAK,EAAO;QACnB,sCAAsC;QACtC,yFAAyF;QACzF,wDAAwD;QACxD,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC,IAAI,CAACF,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;YAC7FR,IAAG,CAACC,IAAI,CAACK,KAAK,CAACG,OAAO,EAAE,CAAC,CAAC,CAAC;QAC7B,CAAC;QACD,+BAA+B;QAC/B,MAAMH,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAEM,SAASZ,SAAS,CAACgB,IAAY,EAAEC,KAAa,EAAEP,OAAe,EAAEQ,KAAa,GAAG,EAAE,EAAS;IACjGZ,IAAG,CAACC,IAAI,CACNY,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC;;IAEN,EAAEH,IAAI,CAAC;;;YAGC,EAAEC,KAAK,CAAC;;;IAGhB,EAAEP,OAAO,CAACU,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,CAAC,GAAGH,KAAK,EACL,CAAC,CACF,CAAC;AACJ,CAAC"}