{"version": 3, "sources": ["../../../../../src/start/server/metro/createExpoMetroResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { Resolution, ResolutionContext } from 'metro-resolver';\nimport path from 'path';\n\nimport jestResolver from './createJResolver';\nimport { isNodeExternal } from './externals';\nimport { formatFileCandidates } from './formatFileCandidates';\nimport { isServerEnvironment } from '../middleware/metroOptions';\n\nexport class FailedToResolvePathError extends Error {\n  // Added to ensure the error is matched by our tooling.\n  // TODO: Test that this matches `isFailedToResolvePathError`\n  candidates = {};\n}\n\nclass ShimModuleError extends Error {}\n\nconst debug = require('debug')('expo:metro:resolve') as typeof console.log;\n\nconst realpathFS =\n  process.platform !== 'win32' && fs.realpathSync && typeof fs.realpathSync.native === 'function'\n    ? fs.realpathSync.native\n    : fs.realpathSync;\n\nfunction realpathSync(x: string) {\n  try {\n    return realpathFS(x);\n  } catch (realpathErr: any) {\n    if (realpathErr.code !== 'ENOENT') {\n      throw realpathErr;\n    }\n  }\n  return x;\n}\n\nexport function createFastResolver({\n  preserveSymlinks,\n  blockList,\n}: {\n  preserveSymlinks: boolean;\n  blockList: RegExp[];\n}) {\n  debug('Creating with settings:', { preserveSymlinks, blockList });\n  const cachedExtensions: Map<string, readonly string[]> = new Map();\n\n  function getAdjustedExtensions({\n    metroSourceExtensions,\n    platform,\n    isNative,\n  }: {\n    metroSourceExtensions: readonly string[];\n    platform: string | null;\n    isNative: boolean;\n  }): readonly string[] {\n    const key = JSON.stringify({ metroSourceExtensions, platform, isNative });\n    if (cachedExtensions.has(key)) {\n      return cachedExtensions.get(key)!;\n    }\n\n    let output = metroSourceExtensions;\n    if (platform) {\n      const nextOutput: string[] = [];\n\n      output.forEach((ext) => {\n        nextOutput.push(`${platform}.${ext}`);\n        if (isNative) {\n          nextOutput.push(`native.${ext}`);\n        }\n        nextOutput.push(ext);\n      });\n\n      output = nextOutput;\n    }\n\n    output = Array.from(new Set<string>(output));\n\n    // resolve expects these to start with a dot.\n    output = output.map((ext) => `.${ext}`);\n\n    cachedExtensions.set(key, output);\n\n    return output;\n  }\n\n  function fastResolve(\n    context: Pick<\n      ResolutionContext,\n      | 'unstable_enablePackageExports'\n      | 'customResolverOptions'\n      | 'sourceExts'\n      | 'preferNativePlatform'\n      | 'originModulePath'\n      | 'getPackage'\n      | 'nodeModulesPaths'\n      | 'mainFields'\n      | 'resolveAsset'\n      | 'unstable_conditionNames'\n      | 'unstable_conditionsByPlatform'\n      | 'fileSystemLookup'\n    >,\n    moduleName: string,\n    platform: string | null\n  ): Resolution {\n    const environment = context.customResolverOptions?.environment;\n    const isServer = isServerEnvironment(environment);\n\n    const extensions = getAdjustedExtensions({\n      metroSourceExtensions: context.sourceExts,\n      platform,\n      isNative: context.preferNativePlatform,\n    }) as string[];\n\n    let fp: string;\n\n    const conditions = context.unstable_enablePackageExports\n      ? [\n          ...new Set([\n            'default',\n            ...context.unstable_conditionNames,\n            ...(platform != null ? (context.unstable_conditionsByPlatform[platform] ?? []) : []),\n          ]),\n        ]\n      : [];\n\n    // NOTE(cedric): metro@0.81.0 ships with `fileSystemLookup`, while `metro@0.80.12` ships as unstable\n    const fileSystemLookup = (\n      'unstable_fileSystemLookup' in context\n        ? context.unstable_fileSystemLookup\n        : context.fileSystemLookup\n    ) as ResolutionContext['fileSystemLookup'] | undefined;\n\n    if (!fileSystemLookup) {\n      throw new Error('Metro API fileSystemLookup is required for fast resolver');\n    }\n\n    try {\n      fp = jestResolver(moduleName, {\n        blockList,\n        enablePackageExports: context.unstable_enablePackageExports,\n        basedir: path.dirname(context.originModulePath),\n        moduleDirectory: context.nodeModulesPaths.length\n          ? (context.nodeModulesPaths as string[])\n          : undefined,\n        extensions,\n        conditions,\n        realpathSync(file: string): string {\n          let metroRealPath: string | null = null;\n\n          const res = fileSystemLookup(file);\n          if (res?.exists) {\n            metroRealPath = res.realPath;\n          }\n\n          if (metroRealPath == null && preserveSymlinks) {\n            return realpathSync(file);\n          }\n          return metroRealPath ?? file;\n        },\n        isDirectory(file: string): boolean {\n          const res = fileSystemLookup(file);\n          return res.exists && res.type === 'd';\n        },\n        isFile(file: string): boolean {\n          const res = fileSystemLookup(file);\n          return res.exists && res.type === 'f';\n        },\n        pathExists(file: string): boolean {\n          return fileSystemLookup(file).exists;\n        },\n        packageFilter(pkg) {\n          // set the pkg.main to the first available field in context.mainFields\n          for (const field of context.mainFields) {\n            if (\n              pkg[field] &&\n              // object-inspect uses browser: {} in package.json\n              typeof pkg[field] === 'string'\n            ) {\n              return {\n                ...pkg,\n                main: pkg[field],\n              };\n            }\n          }\n          return pkg;\n        },\n        // Used to ensure files trace to packages instead of node_modules in expo/expo. This is how Metro works and\n        // the app doesn't finish without it.\n        preserveSymlinks,\n        readPackageSync(readFileSync, pkgFile) {\n          return context.getPackage(pkgFile) ?? JSON.parse(fs.readFileSync(pkgFile, 'utf8'));\n        },\n        includeCoreModules: isServer,\n\n        pathFilter:\n          // Disable `browser` field for server environments.\n          isServer\n            ? undefined\n            : // Enable `browser` field support\n              (pkg: any, _resolvedPath: string, relativePathIn: string): string => {\n                let relativePath = relativePathIn;\n                if (relativePath[0] !== '.') {\n                  relativePath = `./${relativePath}`;\n                }\n\n                const replacements = pkg.browser;\n                if (replacements === undefined) {\n                  return '';\n                }\n\n                // TODO: Probably use a better extension matching system here.\n                // This was added for `uuid/v4` -> `./lib/rng` -> `./lib/rng-browser.js`\n                const mappedPath = replacements[relativePath] ?? replacements[relativePath + '.js'];\n                if (mappedPath === false) {\n                  throw new ShimModuleError();\n                }\n                return mappedPath;\n              },\n      });\n    } catch (error: any) {\n      if (error instanceof ShimModuleError) {\n        return {\n          type: 'empty',\n        };\n      }\n\n      if ('code' in error && error.code === 'MODULE_NOT_FOUND') {\n        if (isNodeExternal(moduleName)) {\n          // In this case, mock the file to use an empty module.\n          return {\n            type: 'empty',\n          };\n        }\n\n        debug({ moduleName, platform, conditions, isServer, preserveSymlinks }, context);\n\n        throw new FailedToResolvePathError(\n          'The module could not be resolved because no file or module matched the pattern:\\n' +\n            `  ${formatFileCandidates(\n              {\n                type: 'sourceFile',\n                filePathPrefix: moduleName,\n                candidateExts: extensions,\n              },\n              true\n            )}\\n\\nFrom:\\n  ${context.originModulePath}\\n`\n        );\n      }\n      throw error;\n    }\n\n    if (context.sourceExts.some((ext) => fp.endsWith(ext))) {\n      return {\n        type: 'sourceFile',\n        filePath: fp,\n      };\n    }\n\n    if (isNodeExternal(fp)) {\n      if (isServer) {\n        return {\n          type: 'sourceFile',\n          filePath: fp,\n        };\n      }\n      // NOTE: This shouldn't happen, the module should throw.\n      // Mock non-server built-in modules to empty.\n      return {\n        type: 'empty',\n      };\n    }\n\n    // NOTE: platform extensions may not be supported on assets.\n\n    if (platform === 'web') {\n      // Skip multi-resolution on web/server bundles. Only consideration here is that\n      // we may still need it in case the only image is a multi-resolution image.\n      return {\n        type: 'assetFiles',\n        filePaths: [fp],\n      };\n    }\n\n    const dirPath = path.dirname(fp);\n    const extension = path.extname(fp);\n    const basename = path.basename(fp, extension);\n    return {\n      type: 'assetFiles',\n      // Support multi-resolution asset extensions...\n      filePaths: context.resolveAsset(dirPath, basename, extension) ?? [fp],\n    };\n  }\n\n  return fastResolve;\n}\n"], "names": ["FailedToResolvePathError", "createFastResolver", "Error", "candidates", "ShimModuleError", "debug", "require", "realpathFS", "process", "platform", "fs", "realpathSync", "native", "x", "realpathErr", "code", "preserveSymlinks", "blockList", "cachedExtensions", "Map", "getAdjustedExtensions", "metroSourceExtensions", "isNative", "key", "JSON", "stringify", "has", "get", "output", "nextOutput", "for<PERSON>ach", "ext", "push", "Array", "from", "Set", "map", "set", "fastResolve", "context", "moduleName", "environment", "customResolverOptions", "isServer", "isServerEnvironment", "extensions", "sourceExts", "preferNativePlatform", "fp", "conditions", "unstable_enablePackageExports", "unstable_conditionNames", "unstable_conditionsByPlatform", "fileSystemLookup", "unstable_fileSystemLookup", "jestResolver", "enablePackageExports", "basedir", "path", "dirname", "originModulePath", "moduleDirectory", "nodeModulesPaths", "length", "undefined", "file", "metroRealPath", "res", "exists", "realPath", "isDirectory", "type", "isFile", "pathExists", "packageFilter", "pkg", "field", "mainFields", "main", "readPackageSync", "readFileSync", "pkgFile", "getPackage", "parse", "includeCoreModules", "pathFilter", "_resolvedPath", "relativePathIn", "relativePath", "replacements", "browser", "mappedPath", "error", "isNodeExternal", "formatFileCandidates", "filePathPrefix", "candidate<PERSON><PERSON><PERSON>", "some", "endsWith", "filePath", "filePaths", "<PERSON><PERSON><PERSON>", "extension", "extname", "basename", "resolveAsset"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IASaA,wBAAwB,MAAxBA,wBAAwB;IA0BrBC,kBAAkB,MAAlBA,kBAAkB;;;8DAnCnB,IAAI;;;;;;;8DAEF,MAAM;;;;;;sEAEE,mBAAmB;2BACb,aAAa;sCACP,wBAAwB;8BACzB,4BAA4B;;;;;;AAEzD,MAAMD,wBAAwB,SAASE,KAAK;IACjD,uDAAuD;IACvD,4DAA4D;IAC5DC,UAAU,GAAG,EAAE,CAAC;CACjB;AAED,MAAMC,eAAe,SAASF,KAAK;CAAG;AAEtC,MAAMG,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAE3E,MAAMC,UAAU,GACdC,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIC,GAAE,EAAA,QAAA,CAACC,YAAY,IAAI,OAAOD,GAAE,EAAA,QAAA,CAACC,YAAY,CAACC,MAAM,KAAK,UAAU,GAC3FF,GAAE,EAAA,QAAA,CAACC,YAAY,CAACC,MAAM,GACtBF,GAAE,EAAA,QAAA,CAACC,YAAY,AAAC;AAEtB,SAASA,YAAY,CAACE,CAAS,EAAE;IAC/B,IAAI;QACF,OAAON,UAAU,CAACM,CAAC,CAAC,CAAC;IACvB,EAAE,OAAOC,WAAW,EAAO;QACzB,IAAIA,WAAW,CAACC,IAAI,KAAK,QAAQ,EAAE;YACjC,MAAMD,WAAW,CAAC;QACpB,CAAC;IACH,CAAC;IACD,OAAOD,CAAC,CAAC;AACX,CAAC;AAEM,SAASZ,kBAAkB,CAAC,EACjCe,gBAAgB,CAAA,EAChBC,SAAS,CAAA,EAIV,EAAE;IACDZ,KAAK,CAAC,yBAAyB,EAAE;QAAEW,gBAAgB;QAAEC,SAAS;KAAE,CAAC,CAAC;IAClE,MAAMC,gBAAgB,GAAmC,IAAIC,GAAG,EAAE,AAAC;IAEnE,SAASC,qBAAqB,CAAC,EAC7BC,qBAAqB,CAAA,EACrBZ,QAAQ,CAAA,EACRa,QAAQ,CAAA,EAKT,EAAqB;QACpB,MAAMC,GAAG,GAAGC,IAAI,CAACC,SAAS,CAAC;YAAEJ,qBAAqB;YAAEZ,QAAQ;YAAEa,QAAQ;SAAE,CAAC,AAAC;QAC1E,IAAIJ,gBAAgB,CAACQ,GAAG,CAACH,GAAG,CAAC,EAAE;YAC7B,OAAOL,gBAAgB,CAACS,GAAG,CAACJ,GAAG,CAAC,CAAE;QACpC,CAAC;QAED,IAAIK,MAAM,GAAGP,qBAAqB,AAAC;QACnC,IAAIZ,QAAQ,EAAE;YACZ,MAAMoB,UAAU,GAAa,EAAE,AAAC;YAEhCD,MAAM,CAACE,OAAO,CAAC,CAACC,GAAG,GAAK;gBACtBF,UAAU,CAACG,IAAI,CAAC,CAAC,EAAEvB,QAAQ,CAAC,CAAC,EAAEsB,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAIT,QAAQ,EAAE;oBACZO,UAAU,CAACG,IAAI,CAAC,CAAC,OAAO,EAAED,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC;gBACDF,UAAU,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YAEHH,MAAM,GAAGC,UAAU,CAAC;QACtB,CAAC;QAEDD,MAAM,GAAGK,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAASP,MAAM,CAAC,CAAC,CAAC;QAE7C,6CAA6C;QAC7CA,MAAM,GAAGA,MAAM,CAACQ,GAAG,CAAC,CAACL,GAAG,GAAK,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;QAExCb,gBAAgB,CAACmB,GAAG,CAACd,GAAG,EAAEK,MAAM,CAAC,CAAC;QAElC,OAAOA,MAAM,CAAC;IAChB,CAAC;IAED,SAASU,WAAW,CAClBC,OAcC,EACDC,UAAkB,EAClB/B,QAAuB,EACX;YACQ8B,GAA6B;QAAjD,MAAME,WAAW,GAAGF,CAAAA,GAA6B,GAA7BA,OAAO,CAACG,qBAAqB,SAAa,GAA1CH,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEE,WAAW,AAAC;QAC/D,MAAME,QAAQ,GAAGC,IAAAA,aAAmB,oBAAA,EAACH,WAAW,CAAC,AAAC;QAElD,MAAMI,UAAU,GAAGzB,qBAAqB,CAAC;YACvCC,qBAAqB,EAAEkB,OAAO,CAACO,UAAU;YACzCrC,QAAQ;YACRa,QAAQ,EAAEiB,OAAO,CAACQ,oBAAoB;SACvC,CAAC,AAAY,AAAC;QAEf,IAAIC,EAAE,AAAQ,AAAC;QAEf,MAAMC,UAAU,GAAGV,OAAO,CAACW,6BAA6B,GACpD;eACK,IAAIf,GAAG,CAAC;gBACT,SAAS;mBACNI,OAAO,CAACY,uBAAuB;mBAC9B1C,QAAQ,IAAI,IAAI,GAAI8B,OAAO,CAACa,6BAA6B,CAAC3C,QAAQ,CAAC,IAAI,EAAE,GAAI,EAAE;aACpF,CAAC;SACH,GACD,EAAE,AAAC;QAEP,oGAAoG;QACpG,MAAM4C,gBAAgB,GACpB,2BAA2B,IAAId,OAAO,GAClCA,OAAO,CAACe,yBAAyB,GACjCf,OAAO,CAACc,gBAAgB,AACwB,AAAC;QAEvD,IAAI,CAACA,gBAAgB,EAAE;YACrB,MAAM,IAAInD,KAAK,CAAC,0DAA0D,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI;YACF8C,EAAE,GAAGO,IAAAA,gBAAY,QAAA,EAACf,UAAU,EAAE;gBAC5BvB,SAAS;gBACTuC,oBAAoB,EAAEjB,OAAO,CAACW,6BAA6B;gBAC3DO,OAAO,EAAEC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACpB,OAAO,CAACqB,gBAAgB,CAAC;gBAC/CC,eAAe,EAAEtB,OAAO,CAACuB,gBAAgB,CAACC,MAAM,GAC3CxB,OAAO,CAACuB,gBAAgB,GACzBE,SAAS;gBACbnB,UAAU;gBACVI,UAAU;gBACVtC,YAAY,EAACsD,IAAY,EAAU;oBACjC,IAAIC,aAAa,GAAkB,IAAI,AAAC;oBAExC,MAAMC,GAAG,GAAGd,gBAAgB,CAACY,IAAI,CAAC,AAAC;oBACnC,IAAIE,GAAG,QAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEC,MAAM,EAAE;wBACfF,aAAa,GAAGC,GAAG,CAACE,QAAQ,CAAC;oBAC/B,CAAC;oBAED,IAAIH,aAAa,IAAI,IAAI,IAAIlD,gBAAgB,EAAE;wBAC7C,OAAOL,YAAY,CAACsD,IAAI,CAAC,CAAC;oBAC5B,CAAC;oBACD,OAAOC,aAAa,IAAID,IAAI,CAAC;gBAC/B,CAAC;gBACDK,WAAW,EAACL,IAAY,EAAW;oBACjC,MAAME,GAAG,GAAGd,gBAAgB,CAACY,IAAI,CAAC,AAAC;oBACnC,OAAOE,GAAG,CAACC,MAAM,IAAID,GAAG,CAACI,IAAI,KAAK,GAAG,CAAC;gBACxC,CAAC;gBACDC,MAAM,EAACP,IAAY,EAAW;oBAC5B,MAAME,GAAG,GAAGd,gBAAgB,CAACY,IAAI,CAAC,AAAC;oBACnC,OAAOE,GAAG,CAACC,MAAM,IAAID,GAAG,CAACI,IAAI,KAAK,GAAG,CAAC;gBACxC,CAAC;gBACDE,UAAU,EAACR,IAAY,EAAW;oBAChC,OAAOZ,gBAAgB,CAACY,IAAI,CAAC,CAACG,MAAM,CAAC;gBACvC,CAAC;gBACDM,aAAa,EAACC,GAAG,EAAE;oBACjB,sEAAsE;oBACtE,KAAK,MAAMC,KAAK,IAAIrC,OAAO,CAACsC,UAAU,CAAE;wBACtC,IACEF,GAAG,CAACC,KAAK,CAAC,IACV,kDAAkD;wBAClD,OAAOD,GAAG,CAACC,KAAK,CAAC,KAAK,QAAQ,EAC9B;4BACA,OAAO;gCACL,GAAGD,GAAG;gCACNG,IAAI,EAAEH,GAAG,CAACC,KAAK,CAAC;6BACjB,CAAC;wBACJ,CAAC;oBACH,CAAC;oBACD,OAAOD,GAAG,CAAC;gBACb,CAAC;gBACD,2GAA2G;gBAC3G,qCAAqC;gBACrC3D,gBAAgB;gBAChB+D,eAAe,EAACC,YAAY,EAAEC,OAAO,EAAE;oBACrC,OAAO1C,OAAO,CAAC2C,UAAU,CAACD,OAAO,CAAC,IAAIzD,IAAI,CAAC2D,KAAK,CAACzE,GAAE,EAAA,QAAA,CAACsE,YAAY,CAACC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;gBACrF,CAAC;gBACDG,kBAAkB,EAAEzC,QAAQ;gBAE5B0C,UAAU,EACR,mDAAmD;gBACnD1C,QAAQ,GACJqB,SAAS,GAET,CAACW,GAAQ,EAAEW,aAAqB,EAAEC,cAAsB,GAAa;oBACnE,IAAIC,YAAY,GAAGD,cAAc,AAAC;oBAClC,IAAIC,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBAC3BA,YAAY,GAAG,CAAC,EAAE,EAAEA,YAAY,CAAC,CAAC,CAAC;oBACrC,CAAC;oBAED,MAAMC,YAAY,GAAGd,GAAG,CAACe,OAAO,AAAC;oBACjC,IAAID,YAAY,KAAKzB,SAAS,EAAE;wBAC9B,OAAO,EAAE,CAAC;oBACZ,CAAC;oBAED,8DAA8D;oBAC9D,wEAAwE;oBACxE,MAAM2B,UAAU,GAAGF,YAAY,CAACD,YAAY,CAAC,IAAIC,YAAY,CAACD,YAAY,GAAG,KAAK,CAAC,AAAC;oBACpF,IAAIG,UAAU,KAAK,KAAK,EAAE;wBACxB,MAAM,IAAIvF,eAAe,EAAE,CAAC;oBAC9B,CAAC;oBACD,OAAOuF,UAAU,CAAC;gBACpB,CAAC;aACR,CAAC,CAAC;QACL,EAAE,OAAOC,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAYxF,eAAe,EAAE;gBACpC,OAAO;oBACLmE,IAAI,EAAE,OAAO;iBACd,CAAC;YACJ,CAAC;YAED,IAAI,MAAM,IAAIqB,KAAK,IAAIA,KAAK,CAAC7E,IAAI,KAAK,kBAAkB,EAAE;gBACxD,IAAI8E,IAAAA,UAAc,eAAA,EAACrD,UAAU,CAAC,EAAE;oBAC9B,sDAAsD;oBACtD,OAAO;wBACL+B,IAAI,EAAE,OAAO;qBACd,CAAC;gBACJ,CAAC;gBAEDlE,KAAK,CAAC;oBAAEmC,UAAU;oBAAE/B,QAAQ;oBAAEwC,UAAU;oBAAEN,QAAQ;oBAAE3B,gBAAgB;iBAAE,EAAEuB,OAAO,CAAC,CAAC;gBAEjF,MAAM,IAAIvC,wBAAwB,CAChC,mFAAmF,GACjF,CAAC,EAAE,EAAE8F,IAAAA,qBAAoB,qBAAA,EACvB;oBACEvB,IAAI,EAAE,YAAY;oBAClBwB,cAAc,EAAEvD,UAAU;oBAC1BwD,aAAa,EAAEnD,UAAU;iBAC1B,EACD,IAAI,CACL,CAAC,aAAa,EAAEN,OAAO,CAACqB,gBAAgB,CAAC,EAAE,CAAC,CAChD,CAAC;YACJ,CAAC;YACD,MAAMgC,KAAK,CAAC;QACd,CAAC;QAED,IAAIrD,OAAO,CAACO,UAAU,CAACmD,IAAI,CAAC,CAAClE,GAAG,GAAKiB,EAAE,CAACkD,QAAQ,CAACnE,GAAG,CAAC,CAAC,EAAE;YACtD,OAAO;gBACLwC,IAAI,EAAE,YAAY;gBAClB4B,QAAQ,EAAEnD,EAAE;aACb,CAAC;QACJ,CAAC;QAED,IAAI6C,IAAAA,UAAc,eAAA,EAAC7C,EAAE,CAAC,EAAE;YACtB,IAAIL,QAAQ,EAAE;gBACZ,OAAO;oBACL4B,IAAI,EAAE,YAAY;oBAClB4B,QAAQ,EAAEnD,EAAE;iBACb,CAAC;YACJ,CAAC;YACD,wDAAwD;YACxD,6CAA6C;YAC7C,OAAO;gBACLuB,IAAI,EAAE,OAAO;aACd,CAAC;QACJ,CAAC;QAED,4DAA4D;QAE5D,IAAI9D,QAAQ,KAAK,KAAK,EAAE;YACtB,+EAA+E;YAC/E,2EAA2E;YAC3E,OAAO;gBACL8D,IAAI,EAAE,YAAY;gBAClB6B,SAAS,EAAE;oBAACpD,EAAE;iBAAC;aAChB,CAAC;QACJ,CAAC;QAED,MAAMqD,OAAO,GAAG3C,KAAI,EAAA,QAAA,CAACC,OAAO,CAACX,EAAE,CAAC,AAAC;QACjC,MAAMsD,SAAS,GAAG5C,KAAI,EAAA,QAAA,CAAC6C,OAAO,CAACvD,EAAE,CAAC,AAAC;QACnC,MAAMwD,QAAQ,GAAG9C,KAAI,EAAA,QAAA,CAAC8C,QAAQ,CAACxD,EAAE,EAAEsD,SAAS,CAAC,AAAC;QAC9C,OAAO;YACL/B,IAAI,EAAE,YAAY;YAClB,+CAA+C;YAC/C6B,SAAS,EAAE7D,OAAO,CAACkE,YAAY,CAACJ,OAAO,EAAEG,QAAQ,EAAEF,SAAS,CAAC,IAAI;gBAACtD,EAAE;aAAC;SACtE,CAAC;IACJ,CAAC;IAED,OAAOV,WAAW,CAAC;AACrB,CAAC"}