{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveRuntimeVersionWithExpoUpdatesAsync.ts"], "sourcesContent": ["import { RuntimePlatform } from './resolvePlatform';\nimport { env } from '../../../utils/env';\nimport {\n  ExpoUpdatesCLIModuleNotFoundError,\n  expoUpdatesCommandAsync,\n} from '../../../utils/expoUpdatesCli';\n\nconst debug = require('debug')('expo:start:server:middleware:resolveRuntimeVersion');\n\nexport async function resolveRuntimeVersionWithExpoUpdatesAsync({\n  projectRoot,\n  platform,\n}: {\n  projectRoot: string;\n  platform: RuntimePlatform;\n}): Promise<string | null> {\n  try {\n    debug('Using expo-updates runtimeversion:resolve CLI for runtime version resolution');\n    const extraArgs = env.EXPO_DEBUG ? ['--debug'] : [];\n    const resolvedRuntimeVersionJSONResult = await expoUpdatesCommandAsync(projectRoot, [\n      'runtimeversion:resolve',\n      '--platform',\n      platform,\n      ...extraArgs,\n    ]);\n    const runtimeVersionResult: { runtimeVersion: string | null } = JSON.parse(\n      resolvedRuntimeVersionJSONResult\n    );\n    debug('runtimeversion:resolve output:');\n    debug(resolvedRuntimeVersionJSONResult);\n\n    return runtimeVersionResult.runtimeVersion ?? null;\n  } catch (e: any) {\n    if (e instanceof ExpoUpdatesCLIModuleNotFoundError) {\n      return null;\n    }\n    throw e;\n  }\n}\n"], "names": ["resolveRuntimeVersionWithExpoUpdatesAsync", "debug", "require", "projectRoot", "platform", "extraArgs", "env", "EXPO_DEBUG", "resolvedRuntimeVersionJSONResult", "expoUpdatesCommandAsync", "runtimeVersionResult", "JSON", "parse", "runtimeVersion", "e", "ExpoUpdatesCLIModuleNotFoundError"], "mappings": "AAAA;;;;+BASsBA,2CAAyC;;aAAzCA,yCAAyC;;qBAR3C,oBAAoB;gCAIjC,+BAA+B;AAEtC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oDAAoD,CAAC,AAAC;AAE9E,eAAeF,yCAAyC,CAAC,EAC9DG,WAAW,CAAA,EACXC,QAAQ,CAAA,EAIT,EAA0B;IACzB,IAAI;QACFH,KAAK,CAAC,8EAA8E,CAAC,CAAC;QACtF,MAAMI,SAAS,GAAGC,IAAG,IAAA,CAACC,UAAU,GAAG;YAAC,SAAS;SAAC,GAAG,EAAE,AAAC;QACpD,MAAMC,gCAAgC,GAAG,MAAMC,IAAAA,eAAuB,wBAAA,EAACN,WAAW,EAAE;YAClF,wBAAwB;YACxB,YAAY;YACZC,QAAQ;eACLC,SAAS;SACb,CAAC,AAAC;QACH,MAAMK,oBAAoB,GAAsCC,IAAI,CAACC,KAAK,CACxEJ,gCAAgC,CACjC,AAAC;QACFP,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACxCA,KAAK,CAACO,gCAAgC,CAAC,CAAC;QAExC,OAAOE,oBAAoB,CAACG,cAAc,IAAI,IAAI,CAAC;IACrD,EAAE,OAAOC,CAAC,EAAO;QACf,IAAIA,CAAC,YAAYC,eAAiC,kCAAA,EAAE;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAMD,CAAC,CAAC;IACV,CAAC;AACH,CAAC"}