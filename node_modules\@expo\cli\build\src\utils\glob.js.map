{"version": 3, "sources": ["../../../src/utils/glob.ts"], "sourcesContent": ["import { glob, globStream, type GlobOptions } from 'glob';\n\n/**\n * Finds all matching files.\n * @deprecated Use `glob` directly instead.\n */\nexport const everyMatchAsync: typeof glob = glob;\n\n/** Bails out early after finding the first matching file. */\nexport function anyMatchAsync(\n  pattern: string,\n  options: Omit<GlobOptions, 'withFileTypes' | 'signal'>\n) {\n  return new Promise<string[]>((resolve, reject) => {\n    const controller = new AbortController();\n\n    globStream(pattern, { ...options, signal: controller.signal })\n      .on('error', (error) => {\n        if (!controller.signal.aborted) {\n          reject(error);\n        }\n      })\n      .once('end', () => resolve([]))\n      .once('data', (file) => {\n        controller.abort();\n        resolve([file]);\n      });\n  });\n}\n"], "names": ["everyMatchAsync", "anyMatchAsync", "glob", "pattern", "options", "Promise", "resolve", "reject", "controller", "AbortController", "globStream", "signal", "on", "error", "aborted", "once", "file", "abort"], "mappings": "AAAA;;;;;;;;;;;IAMaA,eAAe,MAAfA,eAAe;IAGZC,aAAa,MAAbA,aAAa;;;yBATsB,MAAM;;;;;;AAMlD,MAAMD,eAAe,GAAgBE,KAAI,EAAA,KAAA,AAAC;AAG1C,SAASD,aAAa,CAC3BE,OAAe,EACfC,OAAsD,EACtD;IACA,OAAO,IAAIC,OAAO,CAAW,CAACC,OAAO,EAAEC,MAAM,GAAK;QAChD,MAAMC,UAAU,GAAG,IAAIC,eAAe,EAAE,AAAC;QAEzCC,IAAAA,KAAU,EAAA,WAAA,EAACP,OAAO,EAAE;YAAE,GAAGC,OAAO;YAAEO,MAAM,EAAEH,UAAU,CAACG,MAAM;SAAE,CAAC,CAC3DC,EAAE,CAAC,OAAO,EAAE,CAACC,KAAK,GAAK;YACtB,IAAI,CAACL,UAAU,CAACG,MAAM,CAACG,OAAO,EAAE;gBAC9BP,MAAM,CAACM,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CACDE,IAAI,CAAC,KAAK,EAAE,IAAMT,OAAO,CAAC,EAAE,CAAC,CAAC,CAC9BS,IAAI,CAAC,MAAM,EAAE,CAACC,IAAI,GAAK;YACtBR,UAAU,CAACS,KAAK,EAAE,CAAC;YACnBX,OAAO,CAAC;gBAACU,IAAI;aAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC"}