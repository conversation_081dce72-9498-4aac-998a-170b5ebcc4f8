{"version": 3, "sources": ["../../../src/run/hints.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { Log } from '../log';\nimport { isInteractive } from '../utils/interactive';\n\n/** Log the device argument to use for the next run: `Using --device foobar` */\nexport function logDeviceArgument(id: string) {\n  Log.log(chalk.dim`› Using --device ${id}`);\n}\n\nexport function logPlatformRunCommand(platform: string, argv: string[] = []) {\n  Log.log(chalk.dim(`› Using expo run:${platform} ${argv.join(' ')}`));\n}\n\nexport function logProjectLogsLocation() {\n  Log.log(\n    chalk`\\n› Logs for your project will appear below.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n"], "names": ["logDeviceArgument", "logPlatformRunCommand", "logProjectLogsLocation", "id", "Log", "log", "chalk", "dim", "platform", "argv", "join", "isInteractive"], "mappings": "AAAA;;;;;;;;;;;IAMgBA,iBAAiB,MAAjBA,iBAAiB;IAIjBC,qBAAqB,MAArBA,qBAAqB;IAIrBC,sBAAsB,MAAtBA,sBAAsB;;;8DAdpB,OAAO;;;;;;qBAEL,QAAQ;6BACE,sBAAsB;;;;;;AAG7C,SAASF,iBAAiB,CAACG,EAAU,EAAE;IAC5CC,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7C,CAAC;AAEM,SAASF,qBAAqB,CAACO,QAAgB,EAAEC,IAAc,GAAG,EAAE,EAAE;IAC3EL,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,iBAAiB,EAAEC,QAAQ,CAAC,CAAC,EAAEC,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC;AAEM,SAASR,sBAAsB,GAAG;IACvCE,IAAG,IAAA,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,4CAA4C,EAChDK,IAAAA,YAAa,cAAA,GAAE,GAAGL,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAG,EAAE,CAC3D,CAAC,CACH,CAAC;AACJ,CAAC"}