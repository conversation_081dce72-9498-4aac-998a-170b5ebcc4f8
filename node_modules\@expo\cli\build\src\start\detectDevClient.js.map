{"version": 3, "sources": ["../../../src/start/detectDevClient.ts"], "sourcesContent": ["import { getPackage<PERSON>son } from '@expo/config';\nimport resolveFrom from 'resolve-from';\n\nexport function hasDirectDevClientDependency(projectRoot: string): boolean {\n  const { dependencies = {}, devDependencies = {} } = getPackageJson(projectRoot);\n  return !!dependencies['expo-dev-client'] || !!devDependencies['expo-dev-client'];\n}\n\nexport function canResolveDevClient(projectRoot: string): boolean {\n  try {\n    // we check if `expo-dev-launcher` is installed instead of `expo-dev-client`\n    // because someone could install only launcher.\n    resolveFrom(projectRoot, 'expo-dev-launcher');\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": ["hasDirectDevClientDependency", "canResolveDevClient", "projectRoot", "dependencies", "devDependencies", "getPackageJson", "resolveFrom"], "mappings": "AAAA;;;;;;;;;;;IAGgBA,4BAA4B,MAA5BA,4BAA4B;IAK5BC,mBAAmB,MAAnBA,mBAAmB;;;yBARJ,cAAc;;;;;;;8DACrB,cAAc;;;;;;;;;;;AAE/B,SAASD,4BAA4B,CAACE,WAAmB,EAAW;IACzE,MAAM,EAAEC,YAAY,EAAG,EAAE,CAAA,EAAEC,eAAe,EAAG,EAAE,CAAA,EAAE,GAAGC,IAAAA,OAAc,EAAA,eAAA,EAACH,WAAW,CAAC,AAAC;IAChF,OAAO,CAAC,CAACC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAACC,eAAe,CAAC,iBAAiB,CAAC,CAAC;AACnF,CAAC;AAEM,SAASH,mBAAmB,CAACC,WAAmB,EAAW;IAChE,IAAI;QACF,4EAA4E;QAC5E,+CAA+C;QAC/CI,IAAAA,YAAW,EAAA,QAAA,EAACJ,WAAW,EAAE,mBAAmB,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}