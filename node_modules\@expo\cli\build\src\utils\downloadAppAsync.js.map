{"version": 3, "sources": ["../../../src/utils/downloadAppAsync.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { Readable, Stream } from 'stream';\nimport { Agent } from 'undici';\nimport { promisify } from 'util';\n\nimport { createTempFilePath } from './createTempPath';\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { extractAsync } from './tar';\nimport { createCachedFetch, fetchAsync } from '../api/rest/client';\nimport { FetchLike, ProgressCallback } from '../api/rest/client.types';\n\nconst debug = require('debug')('expo:utils:downloadAppAsync') as typeof console.log;\n\nconst TIMER_DURATION = 30000;\n\nconst pipeline = promisify(Stream.pipeline);\n\nasync function downloadAsync({\n  url,\n  outputPath,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}) {\n  let fetchInstance: FetchLike = fetchAsync;\n  if (cacheDirectory) {\n    // Reconstruct the cached fetch since caching could be disabled.\n    fetchInstance = createCachedFetch({\n      // We'll use a 1 week cache for versions so older values get flushed out eventually.\n      ttl: 1000 * 60 * 60 * 24 * 7,\n      // Users can also nuke their `~/.expo` directory to clear the cache.\n      cacheDirectory,\n    });\n  }\n\n  debug(`Downloading ${url} to ${outputPath}`);\n  const res = await fetchInstance(url, {\n    onProgress,\n    dispatcher: new Agent({ connectTimeout: TIMER_DURATION }),\n  });\n  if (!res.ok || !res.body) {\n    throw new CommandError(\n      'FILE_DOWNLOAD',\n      `Unexpected response: ${res.statusText}. From url: ${url}`\n    );\n  }\n  return pipeline(Readable.fromWeb(res.body), fs.createWriteStream(outputPath));\n}\n\nexport async function downloadAppAsync({\n  url,\n  outputPath,\n  extract = false,\n  cacheDirectory,\n  onProgress,\n}: {\n  url: string;\n  outputPath: string;\n  extract?: boolean;\n  cacheDirectory?: string;\n  onProgress?: ProgressCallback;\n}): Promise<void> {\n  if (extract) {\n    // For iOS we download the ipa to a file then pass that file into the extractor.\n    // In the future we should just pipe the `res.body -> tar.extract` directly.\n    // I tried this and it created some weird errors where observing the data stream\n    // would corrupt the file causing tar to fail with `TAR_BAD_ARCHIVE`.\n    const tmpPath = createTempFilePath(path.basename(outputPath));\n    await downloadAsync({ url, outputPath: tmpPath, cacheDirectory, onProgress });\n    debug(`Extracting ${tmpPath} to ${outputPath}`);\n    await ensureDirectoryAsync(outputPath);\n    await extractAsync(tmpPath, outputPath);\n  } else {\n    await ensureDirectoryAsync(path.dirname(outputPath));\n    await downloadAsync({ url, outputPath, cacheDirectory, onProgress });\n  }\n}\n"], "names": ["downloadAppAsync", "debug", "require", "TIMER_DURATION", "pipeline", "promisify", "Stream", "downloadAsync", "url", "outputPath", "cacheDirectory", "onProgress", "fetchInstance", "fetchAsync", "createCachedFetch", "ttl", "res", "dispatcher", "Agent", "connectTimeout", "ok", "body", "CommandError", "statusText", "Readable", "fromWeb", "fs", "createWriteStream", "extract", "tmpPath", "createTempFilePath", "path", "basename", "ensureDirectoryAsync", "extractAsync", "dirname"], "mappings": "AAAA;;;;+BAuDs<PERSON>,kBAAgB;;aAAhBA,gBAAgB;;;8DAvDvB,IAAI;;;;;;;8DACF,MAAM;;;;;;;yBACU,QAAQ;;;;;;;yBACnB,QAAQ;;;;;;;yBACJ,MAAM;;;;;;gCAEG,kBAAkB;qBAChB,OAAO;wBACf,UAAU;qBACV,OAAO;wBACU,oBAAoB;;;;;;AAGlE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AAEpF,MAAMC,cAAc,GAAG,KAAK,AAAC;AAE7B,MAAMC,QAAQ,GAAGC,IAAAA,KAAS,EAAA,UAAA,EAACC,OAAM,EAAA,OAAA,CAACF,QAAQ,CAAC,AAAC;AAE5C,eAAeG,aAAa,CAAC,EAC3BC,GAAG,CAAA,EACHC,UAAU,CAAA,EACVC,cAAc,CAAA,EACdC,UAAU,CAAA,EAMX,EAAE;IACD,IAAIC,aAAa,GAAcC,OAAU,WAAA,AAAC;IAC1C,IAAIH,cAAc,EAAE;QAClB,gEAAgE;QAChEE,aAAa,GAAGE,IAAAA,OAAiB,kBAAA,EAAC;YAChC,oFAAoF;YACpFC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAC5B,oEAAoE;YACpEL,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAEDT,KAAK,CAAC,CAAC,YAAY,EAAEO,GAAG,CAAC,IAAI,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAMO,GAAG,GAAG,MAAMJ,aAAa,CAACJ,GAAG,EAAE;QACnCG,UAAU;QACVM,UAAU,EAAE,IAAIC,CAAAA,OAAK,EAAA,CAAA,MAAA,CAAC;YAAEC,cAAc,EAAEhB,cAAc;SAAE,CAAC;KAC1D,CAAC,AAAC;IACH,IAAI,CAACa,GAAG,CAACI,EAAE,IAAI,CAACJ,GAAG,CAACK,IAAI,EAAE;QACxB,MAAM,IAAIC,OAAY,aAAA,CACpB,eAAe,EACf,CAAC,qBAAqB,EAAEN,GAAG,CAACO,UAAU,CAAC,YAAY,EAAEf,GAAG,CAAC,CAAC,CAC3D,CAAC;IACJ,CAAC;IACD,OAAOJ,QAAQ,CAACoB,OAAQ,EAAA,SAAA,CAACC,OAAO,CAACT,GAAG,CAACK,IAAI,CAAC,EAAEK,GAAE,EAAA,QAAA,CAACC,iBAAiB,CAAClB,UAAU,CAAC,CAAC,CAAC;AAChF,CAAC;AAEM,eAAeT,gBAAgB,CAAC,EACrCQ,GAAG,CAAA,EACHC,UAAU,CAAA,EACVmB,OAAO,EAAG,KAAK,CAAA,EACflB,cAAc,CAAA,EACdC,UAAU,CAAA,EAOX,EAAiB;IAChB,IAAIiB,OAAO,EAAE;QACX,gFAAgF;QAChF,4EAA4E;QAC5E,gFAAgF;QAChF,qEAAqE;QACrE,MAAMC,OAAO,GAAGC,IAAAA,eAAkB,mBAAA,EAACC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACvB,UAAU,CAAC,CAAC,AAAC;QAC9D,MAAMF,aAAa,CAAC;YAAEC,GAAG;YAAEC,UAAU,EAAEoB,OAAO;YAAEnB,cAAc;YAAEC,UAAU;SAAE,CAAC,CAAC;QAC9EV,KAAK,CAAC,CAAC,WAAW,EAAE4B,OAAO,CAAC,IAAI,EAAEpB,UAAU,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMwB,IAAAA,IAAoB,qBAAA,EAACxB,UAAU,CAAC,CAAC;QACvC,MAAMyB,IAAAA,IAAY,aAAA,EAACL,OAAO,EAAEpB,UAAU,CAAC,CAAC;IAC1C,OAAO;QACL,MAAMwB,IAAAA,IAAoB,qBAAA,EAACF,KAAI,EAAA,QAAA,CAACI,OAAO,CAAC1B,UAAU,CAAC,CAAC,CAAC;QACrD,MAAMF,aAAa,CAAC;YAAEC,GAAG;YAAEC,UAAU;YAAEC,cAAc;YAAEC,UAAU;SAAE,CAAC,CAAC;IACvE,CAAC;AACH,CAAC"}