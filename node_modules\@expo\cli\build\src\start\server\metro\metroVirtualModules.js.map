{"version": 3, "sources": ["../../../../../src/start/server/metro/metroVirtualModules.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Bundler from 'metro/src/Bundler';\nimport DependencyGraph from 'metro/src/node-haste/DependencyGraph';\nimport { FileSystem } from 'metro-file-map';\n\ntype ExpoPatchedFileSystem = Omit<FileSystem, 'getSha1'> & {\n  getSha1: FileSystem['getSha1'] & { __patched?: boolean };\n  expoVirtualModules?: Map<string, Buffer>;\n};\n\ntype ActualDependencyGraph = DependencyGraph & {\n  _fileSystem: ExpoPatchedFileSystem;\n};\n\ntype ActualBundler = Bundler & {\n  _depGraph: ActualDependencyGraph;\n};\n\ntype ExpoPatchedBundler = Bundler & {\n  setVirtualModule: (id: string, contents: string) => void;\n  hasVirtualModule: (id: string) => boolean;\n};\n\nfunction assertBundlerHasPrivateMembers(bundler: <PERSON><PERSON><PERSON>): asserts bundler is ActualBundler {\n  if (!('_depGraph' in bundler)) {\n    throw new Error(\n      'Expected bundler to have member: _depGraph. Upstream metro may have removed this property.'\n    );\n  }\n\n  assertDepGraphHasPrivateMembers(bundler._depGraph);\n}\n\nfunction assertDepGraphHasPrivateMembers(\n  depGraph: unknown\n): asserts depGraph is ActualDependencyGraph {\n  if (!depGraph || typeof depGraph !== 'object' || !('_fileSystem' in depGraph)) {\n    throw new Error(\n      'Expected bundler._depGraph to have member: _fileSystem. Upstream metro may have removed this property.'\n    );\n  }\n}\n\nfunction ensureMetroBundlerPatchedWithSetVirtualModule(\n  bundler: Bundler & {\n    setVirtualModule?: (id: string, contents: string) => void;\n    hasVirtualModule?: (id: string) => boolean;\n  }\n): ExpoPatchedBundler {\n  if (!bundler.setVirtualModule) {\n    bundler.setVirtualModule = function (this: Bundler, id: string, contents: string) {\n      assertBundlerHasPrivateMembers(this);\n      const fs = ensureFileSystemPatched(this._depGraph._fileSystem);\n      fs.expoVirtualModules!.set(ensureStartsWithNullByte(id), Buffer.from(contents));\n    };\n    bundler.hasVirtualModule = function (this: Bundler, id: string) {\n      assertBundlerHasPrivateMembers(this);\n      const fs = ensureFileSystemPatched(this._depGraph._fileSystem);\n      return fs.expoVirtualModules!.has(ensureStartsWithNullByte(id));\n    };\n  }\n\n  return bundler as ExpoPatchedBundler;\n}\n\nfunction ensureStartsWithNullByte(id: string): string {\n  // Because you'll likely need to return the path somewhere, we should just assert with a useful error message instead of\n  // attempting to mutate the value behind the scenes. This ensures correctness in the resolution.\n  if (!id.startsWith('\\0')) {\n    throw new Error(`Virtual modules in Expo CLI must start with with null byte (\\\\0), got: ${id}`);\n  }\n  return id;\n}\n\nexport function getMetroBundlerWithVirtualModules(\n  bundler: Bundler & {\n    transformFile: Bundler['transformFile'] & { __patched?: boolean };\n  }\n): ExpoPatchedBundler {\n  if (!bundler.transformFile.__patched) {\n    const originalTransformFile = bundler.transformFile.bind(bundler);\n\n    bundler.transformFile = async function (\n      filePath: string,\n      transformOptions: any,\n      /** Optionally provide the file contents, this can be used to provide virtual contents for a file. */\n      fileBuffer?: Buffer\n    ) {\n      // file buffer will be defined for virtual modules in Metro, e.g. context modules.\n      if (!fileBuffer) {\n        if (filePath.startsWith('\\0')) {\n          const graph = await this.getDependencyGraph();\n\n          assertDepGraphHasPrivateMembers(graph);\n\n          if (graph._fileSystem.expoVirtualModules) {\n            fileBuffer = graph._fileSystem.expoVirtualModules.get(filePath);\n          }\n\n          if (!fileBuffer) {\n            throw new Error(`Virtual module \"${filePath}\" not found.`);\n          }\n        }\n      }\n      return originalTransformFile(filePath, transformOptions, fileBuffer);\n    };\n\n    bundler.transformFile.__patched = true;\n  }\n\n  return ensureMetroBundlerPatchedWithSetVirtualModule(bundler);\n}\n\nfunction ensureFileSystemPatched(fs: ExpoPatchedFileSystem): ExpoPatchedFileSystem {\n  if (!fs.getSha1.__patched) {\n    const original_getSha1 = fs.getSha1.bind(fs);\n    fs.getSha1 = (filename: string) => {\n      // Rollup virtual module format.\n      if (filename.startsWith('\\0')) {\n        return filename;\n      }\n\n      return original_getSha1(filename);\n    };\n    fs.getSha1.__patched = true;\n  }\n\n  // TODO: Connect virtual modules to a specific context so they don't cross-bundles.\n  if (!fs.expoVirtualModules) {\n    fs.expoVirtualModules = new Map<string, Buffer>();\n  }\n\n  return fs;\n}\n"], "names": ["getMetroBundlerWithVirtualModules", "assertBundlerHasPrivateMembers", "bundler", "Error", "assertDepGraphHasPrivateMembers", "_depGraph", "depGraph", "ensureMetroBundlerPatchedWithSetVirtualModule", "setVirtualModule", "id", "contents", "fs", "ensureFileSystemPatched", "_fileSystem", "expoVirtualModules", "set", "ensureStartsWithNullByte", "<PERSON><PERSON><PERSON>", "from", "hasVirtualModule", "has", "startsWith", "transformFile", "__patched", "originalTransformFile", "bind", "filePath", "transformOptions", "fileBuffer", "graph", "getDependencyGraph", "get", "getSha1", "original_getSha1", "filename", "Map"], "mappings": "AAAA;;;;;CAK<PERSON>,GACD;;;;+BAyEg<PERSON>,mCAAiC;;aAAjCA,iCAAiC;;AAnDjD,SAASC,8BAA8B,CAACC,OAAgB,EAAoC;IAC1F,IAAI,CAAC,CAAC,WAAW,IAAIA,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAIC,KAAK,CACb,4FAA4F,CAC7F,CAAC;IACJ,CAAC;IAEDC,+BAA+B,CAACF,OAAO,CAACG,SAAS,CAAC,CAAC;AACrD,CAAC;AAED,SAASD,+BAA+B,CACtCE,QAAiB,EAC0B;IAC3C,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,aAAa,IAAIA,QAAQ,CAAC,EAAE;QAC7E,MAAM,IAAIH,KAAK,CACb,wGAAwG,CACzG,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAASI,6CAA6C,CACpDL,OAGC,EACmB;IACpB,IAAI,CAACA,OAAO,CAACM,gBAAgB,EAAE;QAC7BN,OAAO,CAACM,gBAAgB,GAAG,SAAyBC,EAAU,EAAEC,QAAgB,EAAE;YAChFT,8BAA8B,CAAC,IAAI,CAAC,CAAC;YACrC,MAAMU,EAAE,GAAGC,uBAAuB,CAAC,IAAI,CAACP,SAAS,CAACQ,WAAW,CAAC,AAAC;YAC/DF,EAAE,CAACG,kBAAkB,CAAEC,GAAG,CAACC,wBAAwB,CAACP,EAAE,CAAC,EAAEQ,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC,CAAC,CAAC;QAClF,CAAC,CAAC;QACFR,OAAO,CAACiB,gBAAgB,GAAG,SAAyBV,EAAU,EAAE;YAC9DR,8BAA8B,CAAC,IAAI,CAAC,CAAC;YACrC,MAAMU,EAAE,GAAGC,uBAAuB,CAAC,IAAI,CAACP,SAAS,CAACQ,WAAW,CAAC,AAAC;YAC/D,OAAOF,EAAE,CAACG,kBAAkB,CAAEM,GAAG,CAACJ,wBAAwB,CAACP,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC;IACJ,CAAC;IAED,OAAOP,OAAO,CAAuB;AACvC,CAAC;AAED,SAASc,wBAAwB,CAACP,EAAU,EAAU;IACpD,wHAAwH;IACxH,gGAAgG;IAChG,IAAI,CAACA,EAAE,CAACY,UAAU,CAAC,IAAI,CAAC,EAAE;QACxB,MAAM,IAAIlB,KAAK,CAAC,CAAC,uEAAuE,EAAEM,EAAE,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;IACD,OAAOA,EAAE,CAAC;AACZ,CAAC;AAEM,SAAST,iCAAiC,CAC/CE,OAEC,EACmB;IACpB,IAAI,CAACA,OAAO,CAACoB,aAAa,CAACC,SAAS,EAAE;QACpC,MAAMC,qBAAqB,GAAGtB,OAAO,CAACoB,aAAa,CAACG,IAAI,CAACvB,OAAO,CAAC,AAAC;QAElEA,OAAO,CAACoB,aAAa,GAAG,eACtBI,QAAgB,EAChBC,gBAAqB,EACrB,mGAAmG,GACnGC,UAAmB,EACnB;YACA,kFAAkF;YAClF,IAAI,CAACA,UAAU,EAAE;gBACf,IAAIF,QAAQ,CAACL,UAAU,CAAC,IAAI,CAAC,EAAE;oBAC7B,MAAMQ,KAAK,GAAG,MAAM,IAAI,CAACC,kBAAkB,EAAE,AAAC;oBAE9C1B,+BAA+B,CAACyB,KAAK,CAAC,CAAC;oBAEvC,IAAIA,KAAK,CAAChB,WAAW,CAACC,kBAAkB,EAAE;wBACxCc,UAAU,GAAGC,KAAK,CAAChB,WAAW,CAACC,kBAAkB,CAACiB,GAAG,CAACL,QAAQ,CAAC,CAAC;oBAClE,CAAC;oBAED,IAAI,CAACE,UAAU,EAAE;wBACf,MAAM,IAAIzB,KAAK,CAAC,CAAC,gBAAgB,EAAEuB,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAOF,qBAAqB,CAACE,QAAQ,EAAEC,gBAAgB,EAAEC,UAAU,CAAC,CAAC;QACvE,CAAC,CAAC;QAEF1B,OAAO,CAACoB,aAAa,CAACC,SAAS,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,OAAOhB,6CAA6C,CAACL,OAAO,CAAC,CAAC;AAChE,CAAC;AAED,SAASU,uBAAuB,CAACD,EAAyB,EAAyB;IACjF,IAAI,CAACA,EAAE,CAACqB,OAAO,CAACT,SAAS,EAAE;QACzB,MAAMU,gBAAgB,GAAGtB,EAAE,CAACqB,OAAO,CAACP,IAAI,CAACd,EAAE,CAAC,AAAC;QAC7CA,EAAE,CAACqB,OAAO,GAAG,CAACE,QAAgB,GAAK;YACjC,gCAAgC;YAChC,IAAIA,QAAQ,CAACb,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC7B,OAAOa,QAAQ,CAAC;YAClB,CAAC;YAED,OAAOD,gBAAgB,CAACC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;QACFvB,EAAE,CAACqB,OAAO,CAACT,SAAS,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED,mFAAmF;IACnF,IAAI,CAACZ,EAAE,CAACG,kBAAkB,EAAE;QAC1BH,EAAE,CAACG,kBAAkB,GAAG,IAAIqB,GAAG,EAAkB,CAAC;IACpD,CAAC;IAED,OAAOxB,EAAE,CAAC;AACZ,CAAC"}