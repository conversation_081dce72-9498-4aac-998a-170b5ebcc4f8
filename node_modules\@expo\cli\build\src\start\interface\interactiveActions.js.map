{"version": 3, "sources": ["../../../../src/start/interface/interactiveActions.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { BLT, printHelp, printItem, printQRCode, printUsage, StartOptions } from './commandsTable';\nimport * as Log from '../../log';\nimport { delayAsync } from '../../utils/delay';\nimport { env } from '../../utils/env';\nimport { learnMore } from '../../utils/link';\nimport { openBrowserAsync } from '../../utils/open';\nimport { ExpoChoice, selectAsync } from '../../utils/prompts';\nimport { DevServerManager } from '../server/DevServerManager';\nimport {\n  addReactDevToolsReloadListener,\n  startReactDevToolsProxyAsync,\n} from '../server/ReactDevToolsProxy';\nimport {\n  openJsInspector,\n  queryAllInspectorAppsAsync,\n  promptInspectorAppAsync,\n} from '../server/middleware/inspector/JsInspector';\n\nconst debug = require('debug')('expo:start:interface:interactiveActions') as typeof console.log;\n\ninterface MoreToolMenuItem extends ExpoChoice<string> {\n  action?: () => unknown;\n}\n\n/** Wraps the DevServerManager and adds an interface for user actions. */\nexport class DevServerManagerActions {\n  constructor(\n    private devServerManager: DevServerManager,\n    private options: Pick<StartOptions, 'devClient' | 'platforms'>\n  ) {}\n\n  printDevServerInfo(\n    options: Pick<StartOptions, 'devClient' | 'isWebSocketsEnabled' | 'platforms'>\n  ) {\n    // If native dev server is running, print its URL.\n    if (this.devServerManager.getNativeDevServerPort()) {\n      const devServer = this.devServerManager.getDefaultDevServer();\n      try {\n        const nativeRuntimeUrl = devServer.getNativeRuntimeUrl()!;\n        const interstitialPageUrl = devServer.getRedirectUrl();\n\n        printQRCode(interstitialPageUrl ?? nativeRuntimeUrl);\n\n        if (interstitialPageUrl) {\n          Log.log(\n            printItem(\n              chalk`Choose an app to open your project at {underline ${interstitialPageUrl}}`\n            )\n          );\n        }\n\n        if (env.__EXPO_E2E_TEST) {\n          // Print the URL to stdout for tests\n          console.info(\n            `[__EXPO_E2E_TEST:server] ${JSON.stringify({ url: devServer.getDevServerUrl() })}`\n          );\n        }\n\n        Log.log(printItem(chalk`Metro waiting on {underline ${nativeRuntimeUrl}}`));\n        if (options.devClient === false) {\n          // TODO: if development build, change this message!\n          Log.log(\n            printItem('Scan the QR code above with Expo Go (Android) or the Camera app (iOS)')\n          );\n        } else {\n          Log.log(\n            printItem(\n              'Scan the QR code above to open the project in a development build. ' +\n                learnMore('https://expo.fyi/start')\n            )\n          );\n        }\n      } catch (error) {\n        console.log('err', error);\n        // @ts-ignore: If there is no development build scheme, then skip the QR code.\n        if (error.code !== 'NO_DEV_CLIENT_SCHEME') {\n          throw error;\n        } else {\n          const serverUrl = devServer.getDevServerUrl();\n          Log.log(printItem(chalk`Metro waiting on {underline ${serverUrl}}`));\n          Log.log(printItem(`Linking is disabled because the client scheme cannot be resolved.`));\n        }\n      }\n    }\n\n    if (this.options.platforms?.includes('web')) {\n      const webDevServer = this.devServerManager.getWebDevServer();\n      const webUrl = webDevServer?.getDevServerUrl({ hostType: 'localhost' });\n      if (webUrl) {\n        Log.log();\n        Log.log(printItem(chalk`Web is waiting on {underline ${webUrl}}`));\n      }\n    }\n\n    printUsage(options, { verbose: false });\n    printHelp();\n    Log.log();\n  }\n\n  async openJsInspectorAsync() {\n    try {\n      const metroServerOrigin = this.devServerManager.getDefaultDevServer().getJsInspectorBaseUrl();\n      const apps = await queryAllInspectorAppsAsync(metroServerOrigin);\n      if (!apps.length) {\n        return Log.warn(\n          chalk`{bold Debug:} No compatible apps connected, React Native DevTools can only be used with Hermes. ${learnMore(\n            'https://docs.expo.dev/guides/using-hermes/'\n          )}`\n        );\n      }\n\n      const app = await promptInspectorAppAsync(apps);\n      if (!app) {\n        return Log.error(chalk`{bold Debug:} No inspectable device selected`);\n      }\n\n      if (!(await openJsInspector(metroServerOrigin, app))) {\n        Log.warn(\n          chalk`{bold Debug:} Failed to open the React Native DevTools, see debug logs for more info.`\n        );\n      }\n    } catch (error: any) {\n      // Handle aborting prompt\n      if (error.code === 'ABORTED') return;\n\n      Log.error('Failed to open the React Native DevTools.');\n      Log.exception(error);\n    }\n  }\n\n  reloadApp() {\n    Log.log(`${BLT} Reloading apps`);\n    // Send reload requests over the dev servers\n    this.devServerManager.broadcastMessage('reload');\n  }\n\n  async openMoreToolsAsync() {\n    // Options match: Chrome > View > Developer\n    try {\n      const defaultMenuItems: MoreToolMenuItem[] = [\n        { title: 'Inspect elements', value: 'toggleElementInspector' },\n        { title: 'Toggle performance monitor', value: 'togglePerformanceMonitor' },\n        { title: 'Toggle developer menu', value: 'toggleDevMenu' },\n        { title: 'Reload app', value: 'reload' },\n        {\n          title: 'Open React devtools',\n          value: 'openReactDevTools',\n          action: this.openReactDevToolsAsync.bind(this),\n        },\n        // TODO: Maybe a \"View Source\" option to open code.\n      ];\n      const pluginMenuItems = (\n        await this.devServerManager.devtoolsPluginManager.queryPluginsAsync()\n      ).map((plugin) => ({\n        title: chalk`Open {bold ${plugin.packageName}}`,\n        value: `devtoolsPlugin:${plugin.packageName}`,\n        action: async () => {\n          const url = new URL(\n            plugin.webpageEndpoint,\n            this.devServerManager\n              .getDefaultDevServer()\n              .getUrlCreator()\n              .constructUrl({ scheme: 'http' })\n          );\n          await openBrowserAsync(url.toString());\n        },\n      }));\n      const menuItems = [...defaultMenuItems, ...pluginMenuItems];\n      const value = await selectAsync(chalk`Dev tools {dim (native only)}`, menuItems);\n      const menuItem = menuItems.find((item) => item.value === value);\n      if (menuItem?.action) {\n        menuItem.action();\n      } else if (menuItem?.value) {\n        this.devServerManager.broadcastMessage('sendDevCommand', { name: menuItem.value });\n      }\n    } catch (error: any) {\n      debug(error);\n      // do nothing\n    } finally {\n      printHelp();\n    }\n  }\n\n  async openReactDevToolsAsync() {\n    await startReactDevToolsProxyAsync();\n    const url = this.devServerManager.getDefaultDevServer().getReactDevToolsUrl();\n    await openBrowserAsync(url);\n    addReactDevToolsReloadListener(() => {\n      this.reconnectReactDevTools();\n    });\n    this.reconnectReactDevTools();\n  }\n\n  async reconnectReactDevTools() {\n    // Wait a little time for react-devtools to be initialized in browser\n    await delayAsync(3000);\n    this.devServerManager.broadcastMessage('sendDevCommand', { name: 'reconnectReactDevTools' });\n  }\n\n  toggleDevMenu() {\n    Log.log(`${BLT} Toggling dev menu`);\n    this.devServerManager.broadcastMessage('devMenu');\n  }\n}\n"], "names": ["DevServerManagerActions", "debug", "require", "constructor", "devServerManager", "options", "printDevServerInfo", "getNativeDevServerPort", "devServer", "getDefaultDevServer", "nativeRuntimeUrl", "getNativeRuntimeUrl", "interstitialPageUrl", "getRedirectUrl", "printQRCode", "Log", "log", "printItem", "chalk", "env", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "url", "getDevServerUrl", "devClient", "learnMore", "error", "code", "serverUrl", "platforms", "includes", "webDevServer", "getWebDevServer", "webUrl", "hostType", "printUsage", "verbose", "printHelp", "openJsInspectorAsync", "metroServerOrigin", "getJsInspectorBaseUrl", "apps", "queryAllInspectorAppsAsync", "length", "warn", "app", "promptInspectorAppAsync", "openJsInspector", "exception", "reloadApp", "BLT", "broadcastMessage", "openMoreToolsAsync", "defaultMenuItems", "title", "value", "action", "openReactDevToolsAsync", "bind", "pluginMenuItems", "devtoolsPluginManager", "queryPluginsAsync", "map", "plugin", "packageName", "URL", "webpageEndpoint", "getUrlCreator", "constructUrl", "scheme", "openBrowserAsync", "toString", "menuItems", "selectAsync", "menuItem", "find", "item", "name", "startReactDevToolsProxyAsync", "getReactDevToolsUrl", "addReactDevToolsReloadListener", "reconnectReactDevTools", "delayAsync", "toggleDevMenu"], "mappings": "AAAA;;;;+BA2BaA,yBAAuB;;aAAvBA,uBAAuB;;;8DA3BlB,OAAO;;;;;;+BAEwD,iBAAiB;2DAC7E,WAAW;uBACL,mBAAmB;qBAC1B,iBAAiB;sBACX,kBAAkB;sBACX,kBAAkB;yBACX,qBAAqB;oCAKtD,8BAA8B;6BAK9B,4CAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yCAAyC,CAAC,AAAsB,AAAC;AAOzF,MAAMF,uBAAuB;IAClCG,YACUC,gBAAkC,EAClCC,OAAsD,CAC9D;QAFQD,wBAAAA,gBAAkC,CAAA;QAClCC,eAAAA,OAAsD,CAAA;IAC7D;IAEHC,kBAAkB,CAChBD,OAA8E,EAC9E;YAoDI,GAAsB;QAnD1B,kDAAkD;QAClD,IAAI,IAAI,CAACD,gBAAgB,CAACG,sBAAsB,EAAE,EAAE;YAClD,MAAMC,SAAS,GAAG,IAAI,CAACJ,gBAAgB,CAACK,mBAAmB,EAAE,AAAC;YAC9D,IAAI;gBACF,MAAMC,gBAAgB,GAAGF,SAAS,CAACG,mBAAmB,EAAE,AAAC,AAAC;gBAC1D,MAAMC,mBAAmB,GAAGJ,SAAS,CAACK,cAAc,EAAE,AAAC;gBAEvDC,IAAAA,cAAW,YAAA,EAACF,mBAAmB,IAAIF,gBAAgB,CAAC,CAAC;gBAErD,IAAIE,mBAAmB,EAAE;oBACvBG,IAAG,CAACC,GAAG,CACLC,IAAAA,cAAS,UAAA,EACPC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,iDAAiD,EAAEN,mBAAmB,CAAC,CAAC,CAAC,CAChF,CACF,CAAC;gBACJ,CAAC;gBAED,IAAIO,IAAG,IAAA,CAACC,eAAe,EAAE;oBACvB,oCAAoC;oBACpCC,OAAO,CAACC,IAAI,CACV,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAAC;wBAAEC,GAAG,EAAEjB,SAAS,CAACkB,eAAe,EAAE;qBAAE,CAAC,CAAC,CAAC,CACnF,CAAC;gBACJ,CAAC;gBAEDX,IAAG,CAACC,GAAG,CAACC,IAAAA,cAAS,UAAA,EAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,4BAA4B,EAAER,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,IAAIL,OAAO,CAACsB,SAAS,KAAK,KAAK,EAAE;oBAC/B,mDAAmD;oBACnDZ,IAAG,CAACC,GAAG,CACLC,IAAAA,cAAS,UAAA,EAAC,uEAAuE,CAAC,CACnF,CAAC;gBACJ,OAAO;oBACLF,IAAG,CAACC,GAAG,CACLC,IAAAA,cAAS,UAAA,EACP,qEAAqE,GACnEW,IAAAA,KAAS,UAAA,EAAC,wBAAwB,CAAC,CACtC,CACF,CAAC;gBACJ,CAAC;YACH,EAAE,OAAOC,KAAK,EAAE;gBACdR,OAAO,CAACL,GAAG,CAAC,KAAK,EAAEa,KAAK,CAAC,CAAC;gBAC1B,8EAA8E;gBAC9E,IAAIA,KAAK,CAACC,IAAI,KAAK,sBAAsB,EAAE;oBACzC,MAAMD,KAAK,CAAC;gBACd,OAAO;oBACL,MAAME,SAAS,GAAGvB,SAAS,CAACkB,eAAe,EAAE,AAAC;oBAC9CX,IAAG,CAACC,GAAG,CAACC,IAAAA,cAAS,UAAA,EAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,4BAA4B,EAAEa,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrEhB,IAAG,CAACC,GAAG,CAACC,IAAAA,cAAS,UAAA,EAAC,CAAC,iEAAiE,CAAC,CAAC,CAAC,CAAC;gBAC1F,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAA,GAAsB,GAAtB,IAAI,CAACZ,OAAO,CAAC2B,SAAS,SAAU,GAAhC,KAAA,CAAgC,GAAhC,GAAsB,CAAEC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3C,MAAMC,YAAY,GAAG,IAAI,CAAC9B,gBAAgB,CAAC+B,eAAe,EAAE,AAAC;YAC7D,MAAMC,MAAM,GAAGF,YAAY,QAAiB,GAA7BA,KAAAA,CAA6B,GAA7BA,YAAY,CAAER,eAAe,CAAC;gBAAEW,QAAQ,EAAE,WAAW;aAAE,CAAC,AAAC;YACxE,IAAID,MAAM,EAAE;gBACVrB,IAAG,CAACC,GAAG,EAAE,CAAC;gBACVD,IAAG,CAACC,GAAG,CAACC,IAAAA,cAAS,UAAA,EAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,6BAA6B,EAAEkB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAEDE,IAAAA,cAAU,WAAA,EAACjC,OAAO,EAAE;YAAEkC,OAAO,EAAE,KAAK;SAAE,CAAC,CAAC;QACxCC,IAAAA,cAAS,UAAA,GAAE,CAAC;QACZzB,IAAG,CAACC,GAAG,EAAE,CAAC;IACZ;UAEMyB,oBAAoB,GAAG;QAC3B,IAAI;YACF,MAAMC,iBAAiB,GAAG,IAAI,CAACtC,gBAAgB,CAACK,mBAAmB,EAAE,CAACkC,qBAAqB,EAAE,AAAC;YAC9F,MAAMC,IAAI,GAAG,MAAMC,IAAAA,YAA0B,2BAAA,EAACH,iBAAiB,CAAC,AAAC;YACjE,IAAI,CAACE,IAAI,CAACE,MAAM,EAAE;gBAChB,OAAO/B,IAAG,CAACgC,IAAI,CACb7B,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,gGAAgG,EAAEU,IAAAA,KAAS,UAAA,EAC/G,4CAA4C,CAC7C,CAAC,CAAC,CACJ,CAAC;YACJ,CAAC;YAED,MAAMoB,GAAG,GAAG,MAAMC,IAAAA,YAAuB,wBAAA,EAACL,IAAI,CAAC,AAAC;YAChD,IAAI,CAACI,GAAG,EAAE;gBACR,OAAOjC,IAAG,CAACc,KAAK,CAACX,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,4CAA4C,CAAC,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,CAAE,MAAMgC,IAAAA,YAAe,gBAAA,EAACR,iBAAiB,EAAEM,GAAG,CAAC,AAAC,EAAE;gBACpDjC,IAAG,CAACgC,IAAI,CACN7B,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,qFAAqF,CAAC,CAC7F,CAAC;YACJ,CAAC;QACH,EAAE,OAAOW,KAAK,EAAO;YACnB,yBAAyB;YACzB,IAAIA,KAAK,CAACC,IAAI,KAAK,SAAS,EAAE,OAAO;YAErCf,IAAG,CAACc,KAAK,CAAC,2CAA2C,CAAC,CAAC;YACvDd,IAAG,CAACoC,SAAS,CAACtB,KAAK,CAAC,CAAC;QACvB,CAAC;IACH;IAEAuB,SAAS,GAAG;QACVrC,IAAG,CAACC,GAAG,CAAC,CAAC,EAAEqC,cAAG,IAAA,CAAC,eAAe,CAAC,CAAC,CAAC;QACjC,4CAA4C;QAC5C,IAAI,CAACjD,gBAAgB,CAACkD,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACnD;UAEMC,kBAAkB,GAAG;QACzB,2CAA2C;QAC3C,IAAI;YACF,MAAMC,gBAAgB,GAAuB;gBAC3C;oBAAEC,KAAK,EAAE,kBAAkB;oBAAEC,KAAK,EAAE,wBAAwB;iBAAE;gBAC9D;oBAAED,KAAK,EAAE,4BAA4B;oBAAEC,KAAK,EAAE,0BAA0B;iBAAE;gBAC1E;oBAAED,KAAK,EAAE,uBAAuB;oBAAEC,KAAK,EAAE,eAAe;iBAAE;gBAC1D;oBAAED,KAAK,EAAE,YAAY;oBAAEC,KAAK,EAAE,QAAQ;iBAAE;gBACxC;oBACED,KAAK,EAAE,qBAAqB;oBAC5BC,KAAK,EAAE,mBAAmB;oBAC1BC,MAAM,EAAE,IAAI,CAACC,sBAAsB,CAACC,IAAI,CAAC,IAAI,CAAC;iBAC/C;aAEF,AAAC;YACF,MAAMC,eAAe,GAAG,CACtB,MAAM,IAAI,CAAC1D,gBAAgB,CAAC2D,qBAAqB,CAACC,iBAAiB,EAAE,CACtE,CAACC,GAAG,CAAC,CAACC,MAAM,GAAK,CAAC;oBACjBT,KAAK,EAAEvC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,WAAW,EAAEgD,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC;oBAC/CT,KAAK,EAAE,CAAC,eAAe,EAAEQ,MAAM,CAACC,WAAW,CAAC,CAAC;oBAC7CR,MAAM,EAAE,UAAY;wBAClB,MAAMlC,GAAG,GAAG,IAAI2C,GAAG,CACjBF,MAAM,CAACG,eAAe,EACtB,IAAI,CAACjE,gBAAgB,CAClBK,mBAAmB,EAAE,CACrB6D,aAAa,EAAE,CACfC,YAAY,CAAC;4BAAEC,MAAM,EAAE,MAAM;yBAAE,CAAC,CACpC,AAAC;wBACF,MAAMC,IAAAA,KAAgB,iBAAA,EAAChD,GAAG,CAACiD,QAAQ,EAAE,CAAC,CAAC;oBACzC,CAAC;iBACF,CAAC,CAAC,AAAC;YACJ,MAAMC,SAAS,GAAG;mBAAInB,gBAAgB;mBAAKM,eAAe;aAAC,AAAC;YAC5D,MAAMJ,KAAK,GAAG,MAAMkB,IAAAA,QAAW,YAAA,EAAC1D,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,6BAA6B,CAAC,EAAEyD,SAAS,CAAC,AAAC;YACjF,MAAME,QAAQ,GAAGF,SAAS,CAACG,IAAI,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACrB,KAAK,KAAKA,KAAK,CAAC,AAAC;YAChE,IAAImB,QAAQ,QAAQ,GAAhBA,KAAAA,CAAgB,GAAhBA,QAAQ,CAAElB,MAAM,EAAE;gBACpBkB,QAAQ,CAAClB,MAAM,EAAE,CAAC;YACpB,OAAO,IAAIkB,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAEnB,KAAK,EAAE;gBAC1B,IAAI,CAACtD,gBAAgB,CAACkD,gBAAgB,CAAC,gBAAgB,EAAE;oBAAE0B,IAAI,EAAEH,QAAQ,CAACnB,KAAK;iBAAE,CAAC,CAAC;YACrF,CAAC;QACH,EAAE,OAAO7B,KAAK,EAAO;YACnB5B,KAAK,CAAC4B,KAAK,CAAC,CAAC;QACb,aAAa;QACf,CAAC,QAAS;YACRW,IAAAA,cAAS,UAAA,GAAE,CAAC;QACd,CAAC;IACH;UAEMoB,sBAAsB,GAAG;QAC7B,MAAMqB,IAAAA,mBAA4B,6BAAA,GAAE,CAAC;QACrC,MAAMxD,GAAG,GAAG,IAAI,CAACrB,gBAAgB,CAACK,mBAAmB,EAAE,CAACyE,mBAAmB,EAAE,AAAC;QAC9E,MAAMT,IAAAA,KAAgB,iBAAA,EAAChD,GAAG,CAAC,CAAC;QAC5B0D,IAAAA,mBAA8B,+BAAA,EAAC,IAAM;YACnC,IAAI,CAACC,sBAAsB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,IAAI,CAACA,sBAAsB,EAAE,CAAC;IAChC;UAEMA,sBAAsB,GAAG;QAC7B,qEAAqE;QACrE,MAAMC,IAAAA,MAAU,WAAA,EAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAACjF,gBAAgB,CAACkD,gBAAgB,CAAC,gBAAgB,EAAE;YAAE0B,IAAI,EAAE,wBAAwB;SAAE,CAAC,CAAC;IAC/F;IAEAM,aAAa,GAAG;QACdvE,IAAG,CAACC,GAAG,CAAC,CAAC,EAAEqC,cAAG,IAAA,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACpC,IAAI,CAACjD,gBAAgB,CAACkD,gBAAgB,CAAC,SAAS,CAAC,CAAC;IACpD;CACD"}