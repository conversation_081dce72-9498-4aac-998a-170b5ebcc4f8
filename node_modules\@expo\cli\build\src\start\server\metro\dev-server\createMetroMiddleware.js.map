{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createMetroMiddleware.ts"], "sourcesContent": ["import connect from 'connect';\nimport type { MetroConfig } from 'metro';\n\nimport { createEventsSocket } from './createEventSocket';\nimport { createMessagesSocket } from './createMessageSocket';\nimport { Log } from '../../../../log';\nimport { openInEditorAsync } from '../../../../utils/editor';\n\nconst compression = require('compression');\n\nexport function createMetroMiddleware(metroConfig: Pick<MetroConfig, 'projectRoot'>) {\n  const messages = createMessagesSocket({ logger: Log });\n  const events = createEventsSocket(messages);\n\n  const middleware = connect()\n    .use(noCacheMiddleware)\n    .use(compression())\n    // Support opening stack frames from clients directly in the editor\n    .use('/open-stack-frame', rawBodyMiddleware)\n    .use('/open-stack-frame', metroOpenStackFrameMiddleware)\n    // Support the symbolication endpoint of Metro\n    // See: https://github.com/facebook/metro/blob/a792d85ffde3c21c3fbf64ac9404ab0afe5ff957/packages/metro/src/Server.js#L1266\n    .use('/symbolicate', rawBodyMiddleware)\n    // Support status check to detect if the packager needs to be started from the native side\n    .use('/status', createMetroStatusMiddleware(metroConfig));\n\n  return {\n    middleware,\n    messagesSocket: messages,\n    eventsSocket: events,\n    websocketEndpoints: {\n      [messages.endpoint]: messages.server,\n      [events.endpoint]: events.server,\n    },\n  };\n}\n\nconst noCacheMiddleware: connect.NextHandleFunction = (req, res, next) => {\n  res.setHeader('Surrogate-Control', 'no-store');\n  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');\n  res.setHeader('Pragma', 'no-cache');\n  res.setHeader('Expires', '0');\n  next();\n};\n\nconst rawBodyMiddleware: connect.NextHandleFunction = (req, _res, next) => {\n  const reqWithBody = req as typeof req & { rawBody: string };\n  reqWithBody.setEncoding('utf8');\n  reqWithBody.rawBody = '';\n  reqWithBody.on('data', (chunk) => (reqWithBody.rawBody += chunk));\n  reqWithBody.on('end', next);\n};\n\nconst metroOpenStackFrameMiddleware: connect.NextHandleFunction = (req, res, next) => {\n  // Only accept POST requests\n  if (req.method !== 'POST') return next();\n  // Only handle requests with a raw body\n  if (!('rawBody' in req) || !req.rawBody) {\n    res.statusCode = 406;\n    return res.end('Open stack frame requires the JSON stack frame as request body');\n  }\n\n  const frame = JSON.parse(req.rawBody as string);\n  openInEditorAsync(frame.file, frame.lineNumber).finally(() => res.end('OK'));\n};\n\nfunction createMetroStatusMiddleware(\n  metroConfig: Pick<MetroConfig, 'projectRoot'>\n): connect.NextHandleFunction {\n  return (_req, res) => {\n    res.setHeader('X-React-Native-Project-Root', metroConfig.projectRoot!);\n    res.end('packager-status:running');\n  };\n}\n"], "names": ["createMetroMiddleware", "compression", "require", "metroConfig", "messages", "createMessagesSocket", "logger", "Log", "events", "createEventsSocket", "middleware", "connect", "use", "noCacheMiddleware", "rawBodyMiddleware", "metroOpenStackFrameMiddleware", "createMetroStatusMiddleware", "messagesSocket", "eventsSocket", "websocketEndpoints", "endpoint", "server", "req", "res", "next", "<PERSON><PERSON><PERSON><PERSON>", "_res", "reqWithBody", "setEncoding", "rawBody", "on", "chunk", "method", "statusCode", "end", "frame", "JSON", "parse", "openInEditorAsync", "file", "lineNumber", "finally", "_req", "projectRoot"], "mappings": "AAAA;;;;+BAUg<PERSON>,uBAAqB;;aAArBA,qBAAqB;;;8DAVjB,SAAS;;;;;;mCAGM,qBAAqB;qCACnB,uBAAuB;qBACxC,iBAAiB;wBACH,0BAA0B;;;;;;AAE5D,MAAMC,WAAW,GAAGC,OAAO,CAAC,aAAa,CAAC,AAAC;AAEpC,SAASF,qBAAqB,CAACG,WAA6C,EAAE;IACnF,MAAMC,QAAQ,GAAGC,IAAAA,oBAAoB,qBAAA,EAAC;QAAEC,MAAM,EAAEC,IAAG,IAAA;KAAE,CAAC,AAAC;IACvD,MAAMC,MAAM,GAAGC,IAAAA,kBAAkB,mBAAA,EAACL,QAAQ,CAAC,AAAC;IAE5C,MAAMM,UAAU,GAAGC,IAAAA,QAAO,EAAA,QAAA,GAAE,CACzBC,GAAG,CAACC,iBAAiB,CAAC,CACtBD,GAAG,CAACX,WAAW,EAAE,CAAC,AACnB,mEAAmE;KAClEW,GAAG,CAAC,mBAAmB,EAAEE,iBAAiB,CAAC,CAC3CF,GAAG,CAAC,mBAAmB,EAAEG,6BAA6B,CAAC,AACxD,8CAA8C;IAC9C,0HAA0H;KACzHH,GAAG,CAAC,cAAc,EAAEE,iBAAiB,CAAC,AACvC,0FAA0F;KACzFF,GAAG,CAAC,SAAS,EAAEI,2BAA2B,CAACb,WAAW,CAAC,CAAC,AAAC;IAE5D,OAAO;QACLO,UAAU;QACVO,cAAc,EAAEb,QAAQ;QACxBc,YAAY,EAAEV,MAAM;QACpBW,kBAAkB,EAAE;YAClB,CAACf,QAAQ,CAACgB,QAAQ,CAAC,EAAEhB,QAAQ,CAACiB,MAAM;YACpC,CAACb,MAAM,CAACY,QAAQ,CAAC,EAAEZ,MAAM,CAACa,MAAM;SACjC;KACF,CAAC;AACJ,CAAC;AAED,MAAMR,iBAAiB,GAA+B,CAACS,GAAG,EAAEC,GAAG,EAAEC,IAAI,GAAK;IACxED,GAAG,CAACE,SAAS,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;IAC/CF,GAAG,CAACE,SAAS,CAAC,eAAe,EAAE,uDAAuD,CAAC,CAAC;IACxFF,GAAG,CAACE,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACpCF,GAAG,CAACE,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC9BD,IAAI,EAAE,CAAC;AACT,CAAC,AAAC;AAEF,MAAMV,iBAAiB,GAA+B,CAACQ,GAAG,EAAEI,IAAI,EAAEF,IAAI,GAAK;IACzE,MAAMG,WAAW,GAAGL,GAAG,AAAoC,AAAC;IAC5DK,WAAW,CAACC,WAAW,CAAC,MAAM,CAAC,CAAC;IAChCD,WAAW,CAACE,OAAO,GAAG,EAAE,CAAC;IACzBF,WAAW,CAACG,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAMJ,WAAW,CAACE,OAAO,IAAIE,KAAK,AAAC,CAAC,CAAC;IAClEJ,WAAW,CAACG,EAAE,CAAC,KAAK,EAAEN,IAAI,CAAC,CAAC;AAC9B,CAAC,AAAC;AAEF,MAAMT,6BAA6B,GAA+B,CAACO,GAAG,EAAEC,GAAG,EAAEC,IAAI,GAAK;IACpF,4BAA4B;IAC5B,IAAIF,GAAG,CAACU,MAAM,KAAK,MAAM,EAAE,OAAOR,IAAI,EAAE,CAAC;IACzC,uCAAuC;IACvC,IAAI,CAAC,CAAC,SAAS,IAAIF,GAAG,CAAC,IAAI,CAACA,GAAG,CAACO,OAAO,EAAE;QACvCN,GAAG,CAACU,UAAU,GAAG,GAAG,CAAC;QACrB,OAAOV,GAAG,CAACW,GAAG,CAAC,gEAAgE,CAAC,CAAC;IACnF,CAAC;IAED,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACf,GAAG,CAACO,OAAO,CAAW,AAAC;IAChDS,IAAAA,OAAiB,kBAAA,EAACH,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACK,UAAU,CAAC,CAACC,OAAO,CAAC,IAAMlB,GAAG,CAACW,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/E,CAAC,AAAC;AAEF,SAASlB,2BAA2B,CAClCb,WAA6C,EACjB;IAC5B,OAAO,CAACuC,IAAI,EAAEnB,GAAG,GAAK;QACpBA,GAAG,CAACE,SAAS,CAAC,6BAA6B,EAAEtB,WAAW,CAACwC,WAAW,CAAE,CAAC;QACvEpB,GAAG,CAACW,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACrC,CAAC,CAAC;AACJ,CAAC"}