import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import AvailableOrdersScreen from '../screens/delivery/AvailableOrdersScreen';
import MyOrdersScreen from '../screens/delivery/MyOrdersScreen';
import OrderDetailScreen from '../screens/delivery/OrderDetailScreen';
import MapScreen from '../screens/delivery/MapScreen';
import ProfileScreen from '../screens/delivery/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const MyOrdersStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="MyOrdersList" component={MyOrdersScreen} options={{ title: 'My Orders' }} />
    <Stack.Screen name="OrderDetail" component={OrderDetailScreen} options={{ title: 'Order Details' }} />
  </Stack.Navigator>
);

const AvailableOrdersStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="AvailableOrdersList" component={AvailableOrdersScreen} options={{ title: 'Available Orders' }} />
    <Stack.Screen name="OrderDetail" component={OrderDetailScreen} options={{ title: 'Order Details' }} />
  </Stack.Navigator>
);

const DeliveryNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#FF6B35',
        tabBarInactiveTintColor: '#1A1A1A',
        tabBarStyle: { backgroundColor: '#F7F3E9' }
      }}
    >
      <Tab.Screen 
        name="AvailableOrders" 
        component={AvailableOrdersStack} 
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <Icon name="list" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="MyOrders" 
        component={MyOrdersStack} 
        options={{
          headerShown: false,
          tabBarIcon: ({ color }) => <Icon name="delivery-dining" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="Map" 
        component={MapScreen} 
        options={{
          tabBarIcon: ({ color }) => <Icon name="map" size={24} color={color} />
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={{
          tabBarIcon: ({ color }) => <Icon name="person" size={24} color={color} />
        }}
      />
    </Tab.Navigator>
  );
};

export default DeliveryNavigator;