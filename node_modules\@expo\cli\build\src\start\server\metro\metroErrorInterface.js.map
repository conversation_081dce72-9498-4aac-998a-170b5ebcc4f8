{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrorInterface.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport chalk from 'chalk';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { parse, StackFrame } from 'stacktrace-parser';\nimport terminalLink from 'terminal-link';\n\nimport { LogBoxLog } from './log-box/LogBoxLog';\nimport type { CodeFrame, StackFrame as MetroStackFrame } from './log-box/LogBoxSymbolication';\nimport { getStackFormattedLocation } from './log-box/formatProjectFilePath';\nimport { Log } from '../../../log';\nimport { stripAnsi } from '../../../utils/ansi';\nimport { CommandError, SilentError } from '../../../utils/errors';\nimport { createMetroEndpointAsync } from '../getStaticRenderFunctions';\n\nfunction fill(width: number): string {\n  return Array(width).join(' ');\n}\n\nfunction formatPaths(config: { filePath: string | null; line?: number; col?: number }) {\n  const filePath = chalk.reset(config.filePath);\n  return (\n    chalk.dim('(') +\n    filePath +\n    chalk.dim(`:${[config.line, config.col].filter(Boolean).join(':')})`)\n  );\n}\n\nexport async function logMetroErrorWithStack(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame?: CodeFrame;\n    error: Error;\n  }\n) {\n  if (error instanceof SilentError) {\n    return;\n  }\n\n  // process.stdout.write('\\u001b[0m'); // Reset attributes\n  // process.stdout.write('\\u001bc'); // Reset the terminal\n\n  Log.log();\n  Log.log(chalk.red('Metro error: ') + error.message);\n  Log.log();\n\n  if (error instanceof CommandError) {\n    return;\n  }\n\n  if (codeFrame) {\n    const maxWarningLineLength = Math.max(200, process.stdout.columns);\n\n    const lineText = codeFrame.content;\n    const isPreviewTooLong = codeFrame.content\n      .split('\\n')\n      .some((line) => line.length > maxWarningLineLength);\n    const column = codeFrame.location?.column;\n    // When the preview is too long, we skip reading the file and attempting to apply\n    // code coloring, this is because it can get very slow.\n    if (isPreviewTooLong) {\n      let previewLine = '';\n      let cursorLine = '';\n\n      const formattedPath = formatPaths({\n        filePath: codeFrame.fileName,\n        line: codeFrame.location?.row,\n        col: codeFrame.location?.column,\n      });\n      // Create a curtailed preview line like:\n      // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`\n      // If there is no text preview or column number, we can't do anything.\n      if (lineText && column != null) {\n        const rangeWindow = Math.round(\n          Math.max(codeFrame.fileName?.length ?? 0, Math.max(80, process.stdout.columns)) / 2\n        );\n        let minBounds = Math.max(0, column - rangeWindow);\n        const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);\n        previewLine = lineText.slice(minBounds, maxBounds);\n\n        // If we splice content off the start, then we should append `...`.\n        // This is unlikely to happen since we limit the activation size.\n        if (minBounds > 0) {\n          // Adjust the min bounds so the cursor is aligned after we add the \"...\"\n          minBounds -= 3;\n          previewLine = chalk.dim('...') + previewLine;\n        }\n        if (maxBounds < lineText.length) {\n          previewLine += chalk.dim('...');\n        }\n\n        // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n        cursorLine = (column == null ? '' : fill(column) + chalk.reset('^')).slice(minBounds);\n\n        Log.log(\n          [formattedPath, '', previewLine, cursorLine, chalk.dim('(error truncated)')].join('\\n')\n        );\n      }\n    } else {\n      Log.log(codeFrame.content);\n    }\n  }\n\n  if (stack?.length) {\n    const stackProps = stack.map((frame) => {\n      return {\n        title: frame.methodName,\n        subtitle: getStackFormattedLocation(projectRoot, frame),\n        collapse: frame.collapse,\n      };\n    });\n\n    const stackLines: string[] = [];\n\n    stackProps.forEach((frame) => {\n      const position = terminalLink.isSupported\n        ? terminalLink(frame.subtitle, frame.subtitle)\n        : frame.subtitle;\n      let lineItem = chalk.gray(`  ${frame.title} (${position})`);\n      if (frame.collapse) {\n        lineItem = chalk.dim(lineItem);\n      }\n      // Never show the internal module system.\n      if (!frame.subtitle.match(/\\/metro-require\\/require\\.js/)) {\n        stackLines.push(lineItem);\n      }\n    });\n\n    Log.log();\n    Log.log(chalk.bold`Call Stack`);\n    if (!stackLines.length) {\n      Log.log(chalk.gray('  No stack trace available.'));\n    } else {\n      Log.log(stackLines.join('\\n'));\n    }\n  } else {\n    Log.log(chalk.gray(`  ${error.stack}`));\n  }\n}\n\nexport const IS_METRO_BUNDLE_ERROR_SYMBOL = Symbol('_isMetroBundleError');\nconst HAS_LOGGED_SYMBOL = Symbol('_hasLoggedInCLI');\n\nexport async function logMetroError(\n  projectRoot: string,\n  {\n    error,\n  }: {\n    error: Error & {\n      [HAS_LOGGED_SYMBOL]?: boolean;\n    };\n  }\n) {\n  if (error instanceof SilentError || error[HAS_LOGGED_SYMBOL]) {\n    return;\n  }\n  error[HAS_LOGGED_SYMBOL] = true;\n\n  const stack = parseErrorStack(projectRoot, error.stack);\n\n  const log = new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n\n  await new Promise((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\nfunction isTransformError(\n  error: any\n): error is { type: 'TransformError'; filename: string; lineNumber: number; column: number } {\n  return error.type === 'TransformError';\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nfunction logFromError({ error, projectRoot }: { error: Error; projectRoot: string }) {\n  // Remap direct Metro Node.js errors to a format that will appear more client-friendly in the logbox UI.\n  let stack: MetroStackFrame[] | undefined;\n  if (isTransformError(error) && error.filename) {\n    // Syntax errors in static rendering.\n    stack = [\n      {\n        file: path.join(projectRoot, error.filename),\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: error.lineNumber,\n        column: error.column,\n      },\n    ];\n  } else if ('originModulePath' in error && typeof error.originModulePath === 'string') {\n    // TODO: Use import stack here when the error is resolution based.\n    stack = [\n      {\n        file: error.originModulePath,\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: 0,\n        column: 0,\n      },\n    ];\n  } else {\n    stack = parseErrorStack(projectRoot, error.stack);\n  }\n\n  return new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function logMetroErrorAsync({\n  error,\n  projectRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function getErrorOverlayHtmlAsync({\n  error,\n  projectRoot,\n  routerRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n  routerRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n\n  if ('message' in log && 'content' in log.message && typeof log.message.content === 'string') {\n    log.message.content = stripAnsi(log.message.content)!;\n  }\n\n  const logBoxContext = {\n    selectedLogIndex: 0,\n    isDisabled: false,\n    logs: [log],\n  };\n  const html = `<html><head><style>#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}</style></head><body><div id=\"root\"></div><script id=\"_expo-static-error\" type=\"application/json\">${JSON.stringify(\n    logBoxContext\n  )}</script></body></html>`;\n\n  const errorOverlayEntry = await createMetroEndpointAsync(\n    projectRoot,\n    // Keep the URL relative\n    '',\n    resolveFrom(projectRoot, 'expo-router/_error'),\n    {\n      mode: 'development',\n      platform: 'web',\n      minify: false,\n      optimize: false,\n      usedExports: false,\n      baseUrl: '',\n      routerRoot,\n      isExporting: false,\n      reactCompiler: false,\n    }\n  );\n\n  const htmlWithJs = html.replace('</body>', `<script src=${errorOverlayEntry}></script></body>`);\n  return htmlWithJs;\n}\n\nfunction parseErrorStack(\n  projectRoot: string,\n  stack?: string\n): (StackFrame & { collapse?: boolean })[] {\n  if (stack == null) {\n    return [];\n  }\n  if (Array.isArray(stack)) {\n    return stack;\n  }\n\n  const serverRoot = getMetroServerRoot(projectRoot);\n\n  return parse(stack)\n    .map((frame) => {\n      // frame.file will mostly look like `http://localhost:8081/index.bundle?platform=web&dev=true&hot=false`\n\n      if (frame.file) {\n        // SSR will sometimes have absolute paths followed by `.bundle?...`, we need to try and make them relative paths and append a dev server URL.\n        if (frame.file.startsWith('/') && frame.file.includes('bundle?') && !canParse(frame.file)) {\n          // Malformed stack file from SSR. Attempt to repair.\n          frame.file = 'https://localhost:8081/' + path.relative(serverRoot, frame.file);\n        }\n      }\n\n      return {\n        ...frame,\n        column: frame.column != null ? frame.column - 1 : null,\n      };\n    })\n    .filter((frame) => frame.file && !frame.file.includes('node_modules'));\n}\n\nfunction canParse(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": ["logMetroErrorWithStack", "IS_METRO_BUNDLE_ERROR_SYMBOL", "logMetroError", "logMetroErrorAsync", "getErrorOverlayHtmlAsync", "fill", "width", "Array", "join", "formatPaths", "config", "filePath", "chalk", "reset", "dim", "line", "col", "filter", "Boolean", "projectRoot", "stack", "codeFrame", "error", "SilentError", "Log", "log", "red", "message", "CommandError", "maxWarning<PERSON>ine<PERSON><PERSON><PERSON>", "Math", "max", "process", "stdout", "columns", "lineText", "content", "isPreviewTooLong", "split", "some", "length", "column", "location", "previewLine", "cursorLine", "formattedPath", "fileName", "row", "rangeWindow", "round", "minBounds", "maxBounds", "min", "slice", "stackProps", "map", "frame", "title", "methodName", "subtitle", "getStackFormattedLocation", "collapse", "stackLines", "for<PERSON>ach", "position", "terminalLink", "isSupported", "lineItem", "gray", "match", "push", "bold", "Symbol", "HAS_LOGGED_SYMBOL", "parseError<PERSON>tack", "LogBoxLog", "level", "substitutions", "isComponentError", "category", "componentStack", "Promise", "res", "symbolicate", "symbolicated", "isTransformError", "type", "logFromError", "filename", "file", "path", "arguments", "lineNumber", "originModulePath", "routerRoot", "stripAnsi", "logBoxContext", "selectedLogIndex", "isDisabled", "logs", "html", "JSON", "stringify", "errorOverlayEntry", "createMetroEndpointAsync", "resolveFrom", "mode", "platform", "minify", "optimize", "usedExports", "baseUrl", "isExporting", "reactCompiler", "htmlWithJs", "replace", "isArray", "serverRoot", "getMetroServerRoot", "parse", "startsWith", "includes", "canParse", "relative", "url", "URL"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IA4BsBA,sBAAsB,MAAtBA,sBAAsB;IAqH/BC,4BAA4B,MAA5BA,4BAA4B;IAGnBC,aAAa,MAAbA,aAAa;IA0FbC,kBAAkB,MAAlBA,kBAAkB;IAmBlBC,wBAAwB,MAAxBA,wBAAwB;;;yBAjQX,oBAAoB;;;;;;;8DACrC,OAAO;;;;;;;8DACR,MAAM;;;;;;;8DACC,cAAc;;;;;;;yBACJ,mBAAmB;;;;;;;8DAC5B,eAAe;;;;;;2BAEd,qBAAqB;uCAEL,iCAAiC;qBACvD,cAAc;sBACR,qBAAqB;wBACL,uBAAuB;0CACxB,6BAA6B;;;;;;AAEtE,SAASC,IAAI,CAACC,KAAa,EAAU;IACnC,OAAOC,KAAK,CAACD,KAAK,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,SAASC,WAAW,CAACC,MAAgE,EAAE;IACrF,MAAMC,QAAQ,GAAGC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACH,MAAM,CAACC,QAAQ,CAAC,AAAC;IAC9C,OACEC,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,GAAG,CAAC,GACdH,QAAQ,GACRC,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,CAAC,CAAC,EAAE;QAACJ,MAAM,CAACK,IAAI;QAAEL,MAAM,CAACM,GAAG;KAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACV,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CACrE;AACJ,CAAC;AAEM,eAAeR,sBAAsB,CAC1CmB,WAAmB,EACnB,EACEC,KAAK,CAAA,EACLC,SAAS,CAAA,EACTC,KAAK,CAAA,EAKN,EACD;IACA,IAAIA,KAAK,YAAYC,OAAW,YAAA,EAAE;QAChC,OAAO;IACT,CAAC;IAED,yDAAyD;IACzD,yDAAyD;IAEzDC,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,IAAA,CAACC,GAAG,CAACb,MAAK,EAAA,QAAA,CAACc,GAAG,CAAC,eAAe,CAAC,GAAGJ,KAAK,CAACK,OAAO,CAAC,CAAC;IACpDH,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;IAEV,IAAIH,KAAK,YAAYM,OAAY,aAAA,EAAE;QACjC,OAAO;IACT,CAAC;IAED,IAAIP,SAAS,EAAE;YAOEA,GAAkB;QANjC,MAAMQ,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,AAAC;QAEnE,MAAMC,QAAQ,GAAGd,SAAS,CAACe,OAAO,AAAC;QACnC,MAAMC,gBAAgB,GAAGhB,SAAS,CAACe,OAAO,CACvCE,KAAK,CAAC,IAAI,CAAC,CACXC,IAAI,CAAC,CAACxB,IAAI,GAAKA,IAAI,CAACyB,MAAM,GAAGX,oBAAoB,CAAC,AAAC;QACtD,MAAMY,MAAM,GAAGpB,CAAAA,GAAkB,GAAlBA,SAAS,CAACqB,QAAQ,SAAQ,GAA1BrB,KAAAA,CAA0B,GAA1BA,GAAkB,CAAEoB,MAAM,AAAC;QAC1C,iFAAiF;QACjF,uDAAuD;QACvD,IAAIJ,gBAAgB,EAAE;gBAMZhB,IAAkB,EACnBA,IAAkB;YANzB,IAAIsB,WAAW,GAAG,EAAE,AAAC;YACrB,IAAIC,UAAU,GAAG,EAAE,AAAC;YAEpB,MAAMC,aAAa,GAAGpC,WAAW,CAAC;gBAChCE,QAAQ,EAAEU,SAAS,CAACyB,QAAQ;gBAC5B/B,IAAI,EAAEM,CAAAA,IAAkB,GAAlBA,SAAS,CAACqB,QAAQ,SAAK,GAAvBrB,KAAAA,CAAuB,GAAvBA,IAAkB,CAAE0B,GAAG;gBAC7B/B,GAAG,EAAEK,CAAAA,IAAkB,GAAlBA,SAAS,CAACqB,QAAQ,SAAQ,GAA1BrB,KAAAA,CAA0B,GAA1BA,IAAkB,CAAEoB,MAAM;aAChC,CAAC,AAAC;YACH,wCAAwC;YACxC,kHAAkH;YAClH,sEAAsE;YACtE,IAAIN,QAAQ,IAAIM,MAAM,IAAI,IAAI,EAAE;oBAEnBpB,IAAkB;gBAD7B,MAAM2B,WAAW,GAAGlB,IAAI,CAACmB,KAAK,CAC5BnB,IAAI,CAACC,GAAG,CAACV,CAAAA,CAAAA,IAAkB,GAAlBA,SAAS,CAACyB,QAAQ,SAAQ,GAA1BzB,KAAAA,CAA0B,GAA1BA,IAAkB,CAAEmB,MAAM,CAAA,IAAI,CAAC,EAAEV,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEC,OAAO,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,GAAG,CAAC,CACpF,AAAC;gBACF,IAAIgB,SAAS,GAAGpB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEU,MAAM,GAAGO,WAAW,CAAC,AAAC;gBAClD,MAAMG,SAAS,GAAGrB,IAAI,CAACsB,GAAG,CAACF,SAAS,GAAGF,WAAW,GAAG,CAAC,EAAEb,QAAQ,CAACK,MAAM,CAAC,AAAC;gBACzEG,WAAW,GAAGR,QAAQ,CAACkB,KAAK,CAACH,SAAS,EAAEC,SAAS,CAAC,CAAC;gBAEnD,mEAAmE;gBACnE,iEAAiE;gBACjE,IAAID,SAAS,GAAG,CAAC,EAAE;oBACjB,wEAAwE;oBACxEA,SAAS,IAAI,CAAC,CAAC;oBACfP,WAAW,GAAG/B,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,KAAK,CAAC,GAAG6B,WAAW,CAAC;gBAC/C,CAAC;gBACD,IAAIQ,SAAS,GAAGhB,QAAQ,CAACK,MAAM,EAAE;oBAC/BG,WAAW,IAAI/B,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC;gBAClC,CAAC;gBAED,kHAAkH;gBAClH8B,UAAU,GAAG,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAGpC,IAAI,CAACoC,MAAM,CAAC,GAAG7B,MAAK,EAAA,QAAA,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACwC,KAAK,CAACH,SAAS,CAAC,CAAC;gBAEtF1B,IAAG,IAAA,CAACC,GAAG,CACL;oBAACoB,aAAa;oBAAE,EAAE;oBAAEF,WAAW;oBAAEC,UAAU;oBAAEhC,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,mBAAmB,CAAC;iBAAC,CAACN,IAAI,CAAC,IAAI,CAAC,CACxF,CAAC;YACJ,CAAC;QACH,OAAO;YACLgB,IAAG,IAAA,CAACC,GAAG,CAACJ,SAAS,CAACe,OAAO,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,IAAIhB,KAAK,QAAQ,GAAbA,KAAAA,CAAa,GAAbA,KAAK,CAAEoB,MAAM,EAAE;QACjB,MAAMc,UAAU,GAAGlC,KAAK,CAACmC,GAAG,CAAC,CAACC,KAAK,GAAK;YACtC,OAAO;gBACLC,KAAK,EAAED,KAAK,CAACE,UAAU;gBACvBC,QAAQ,EAAEC,IAAAA,sBAAyB,0BAAA,EAACzC,WAAW,EAAEqC,KAAK,CAAC;gBACvDK,QAAQ,EAAEL,KAAK,CAACK,QAAQ;aACzB,CAAC;QACJ,CAAC,CAAC,AAAC;QAEH,MAAMC,UAAU,GAAa,EAAE,AAAC;QAEhCR,UAAU,CAACS,OAAO,CAAC,CAACP,KAAK,GAAK;YAC5B,MAAMQ,QAAQ,GAAGC,aAAY,EAAA,QAAA,CAACC,WAAW,GACrCD,IAAAA,aAAY,EAAA,QAAA,EAACT,KAAK,CAACG,QAAQ,EAAEH,KAAK,CAACG,QAAQ,CAAC,GAC5CH,KAAK,CAACG,QAAQ,AAAC;YACnB,IAAIQ,QAAQ,GAAGvD,MAAK,EAAA,QAAA,CAACwD,IAAI,CAAC,CAAC,EAAE,EAAEZ,KAAK,CAACC,KAAK,CAAC,EAAE,EAAEO,QAAQ,CAAC,CAAC,CAAC,CAAC,AAAC;YAC5D,IAAIR,KAAK,CAACK,QAAQ,EAAE;gBAClBM,QAAQ,GAAGvD,MAAK,EAAA,QAAA,CAACE,GAAG,CAACqD,QAAQ,CAAC,CAAC;YACjC,CAAC;YACD,yCAAyC;YACzC,IAAI,CAACX,KAAK,CAACG,QAAQ,CAACU,KAAK,gCAAgC,EAAE;gBACzDP,UAAU,CAACQ,IAAI,CAACH,QAAQ,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH3C,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;QACVD,IAAG,IAAA,CAACC,GAAG,CAACb,MAAK,EAAA,QAAA,CAAC2D,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAChC,IAAI,CAACT,UAAU,CAACtB,MAAM,EAAE;YACtBhB,IAAG,IAAA,CAACC,GAAG,CAACb,MAAK,EAAA,QAAA,CAACwD,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;QACrD,OAAO;YACL5C,IAAG,IAAA,CAACC,GAAG,CAACqC,UAAU,CAACtD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,OAAO;QACLgB,IAAG,IAAA,CAACC,GAAG,CAACb,MAAK,EAAA,QAAA,CAACwD,IAAI,CAAC,CAAC,EAAE,EAAE9C,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;AACH,CAAC;AAEM,MAAMnB,4BAA4B,GAAGuE,MAAM,CAAC,qBAAqB,CAAC,AAAC;AAC1E,MAAMC,iBAAiB,GAAGD,MAAM,CAAC,iBAAiB,CAAC,AAAC;AAE7C,eAAetE,aAAa,CACjCiB,WAAmB,EACnB,EACEG,KAAK,CAAA,EAKN,EACD;QAuBSG,GAAgB;IAtBzB,IAAIH,KAAK,YAAYC,OAAW,YAAA,IAAID,KAAK,CAACmD,iBAAiB,CAAC,EAAE;QAC5D,OAAO;IACT,CAAC;IACDnD,KAAK,CAACmD,iBAAiB,CAAC,GAAG,IAAI,CAAC;IAEhC,MAAMrD,KAAK,GAAGsD,eAAe,CAACvD,WAAW,EAAEG,KAAK,CAACF,KAAK,CAAC,AAAC;IAExD,MAAMK,GAAG,GAAG,IAAIkD,UAAS,UAAA,CAAC;QACxBC,KAAK,EAAE,QAAQ;QACfjD,OAAO,EAAE;YACPS,OAAO,EAAEd,KAAK,CAACK,OAAO;YACtBkD,aAAa,EAAE,EAAE;SAClB;QACDC,gBAAgB,EAAE,KAAK;QACvB1D,KAAK;QACL2D,QAAQ,EAAE,QAAQ;QAClBC,cAAc,EAAE,EAAE;KACnB,CAAC,AAAC;IAEH,MAAM,IAAIC,OAAO,CAAC,CAACC,GAAG,GAAKzD,GAAG,CAAC0D,WAAW,CAAC,OAAO,EAAED,GAAG,CAAC,CAAC,CAAC;IAE1DlF,sBAAsB,CAACmB,WAAW,EAAE;QAClCC,KAAK,EAAEK,CAAAA,CAAAA,GAAgB,GAAhBA,GAAG,CAAC2D,YAAY,SAAO,GAAvB3D,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAEL,KAAK,SAAA,GAAvBK,KAAAA,CAAuB,QAAEL,KAAK,AAAP,CAAA,IAAW,EAAE;QAC3CC,SAAS,EAAEI,GAAG,CAACJ,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;AACL,CAAC;AAED,SAAS+D,gBAAgB,CACvB/D,KAAU,EACiF;IAC3F,OAAOA,KAAK,CAACgE,IAAI,KAAK,gBAAgB,CAAC;AACzC,CAAC;AAED,2EAA2E,GAC3E,SAASC,YAAY,CAAC,EAAEjE,KAAK,CAAA,EAAEH,WAAW,CAAA,EAAyC,EAAE;IACnF,wGAAwG;IACxG,IAAIC,KAAK,AAA+B,AAAC;IACzC,IAAIiE,gBAAgB,CAAC/D,KAAK,CAAC,IAAIA,KAAK,CAACkE,QAAQ,EAAE;QAC7C,qCAAqC;QACrCpE,KAAK,GAAG;YACN;gBACEqE,IAAI,EAAEC,KAAI,EAAA,QAAA,CAAClF,IAAI,CAACW,WAAW,EAAEG,KAAK,CAACkE,QAAQ,CAAC;gBAC5C9B,UAAU,EAAE,WAAW;gBACvBiC,SAAS,EAAE,EAAE;gBACb,qBAAqB;gBACrBC,UAAU,EAAEtE,KAAK,CAACsE,UAAU;gBAC5BnD,MAAM,EAAEnB,KAAK,CAACmB,MAAM;aACrB;SACF,CAAC;IACJ,OAAO,IAAI,kBAAkB,IAAInB,KAAK,IAAI,OAAOA,KAAK,CAACuE,gBAAgB,KAAK,QAAQ,EAAE;QACpF,kEAAkE;QAClEzE,KAAK,GAAG;YACN;gBACEqE,IAAI,EAAEnE,KAAK,CAACuE,gBAAgB;gBAC5BnC,UAAU,EAAE,WAAW;gBACvBiC,SAAS,EAAE,EAAE;gBACb,qBAAqB;gBACrBC,UAAU,EAAE,CAAC;gBACbnD,MAAM,EAAE,CAAC;aACV;SACF,CAAC;IACJ,OAAO;QACLrB,KAAK,GAAGsD,eAAe,CAACvD,WAAW,EAAEG,KAAK,CAACF,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,OAAO,IAAIuD,UAAS,UAAA,CAAC;QACnBC,KAAK,EAAE,QAAQ;QACfjD,OAAO,EAAE;YACPS,OAAO,EAAEd,KAAK,CAACK,OAAO;YACtBkD,aAAa,EAAE,EAAE;SAClB;QACDC,gBAAgB,EAAE,KAAK;QACvB1D,KAAK;QACL2D,QAAQ,EAAE,QAAQ;QAClBC,cAAc,EAAE,EAAE;KACnB,CAAC,CAAC;AACL,CAAC;AAGM,eAAe7E,kBAAkB,CAAC,EACvCmB,KAAK,CAAA,EACLH,WAAW,CAAA,EAIZ,EAAE;QAMQM,GAAgB;IALzB,MAAMA,GAAG,GAAG8D,YAAY,CAAC;QAAEpE,WAAW;QAAEG,KAAK;KAAE,CAAC,AAAC;IAEjD,MAAM,IAAI2D,OAAO,CAAO,CAACC,GAAG,GAAKzD,GAAG,CAAC0D,WAAW,CAAC,OAAO,EAAE,IAAMD,GAAG,EAAE,CAAC,CAAC,CAAC;IAExElF,sBAAsB,CAACmB,WAAW,EAAE;QAClCC,KAAK,EAAEK,CAAAA,CAAAA,GAAgB,GAAhBA,GAAG,CAAC2D,YAAY,SAAO,GAAvB3D,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAEL,KAAK,SAAA,GAAvBK,KAAAA,CAAuB,QAAEL,KAAK,AAAP,CAAA,IAAW,EAAE;QAC3CC,SAAS,EAAEI,GAAG,CAACJ,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;AACL,CAAC;AAGM,eAAelB,wBAAwB,CAAC,EAC7CkB,KAAK,CAAA,EACLH,WAAW,CAAA,EACX2E,UAAU,CAAA,EAKX,EAAE;QAMQrE,GAAgB;IALzB,MAAMA,GAAG,GAAG8D,YAAY,CAAC;QAAEpE,WAAW;QAAEG,KAAK;KAAE,CAAC,AAAC;IAEjD,MAAM,IAAI2D,OAAO,CAAO,CAACC,GAAG,GAAKzD,GAAG,CAAC0D,WAAW,CAAC,OAAO,EAAE,IAAMD,GAAG,EAAE,CAAC,CAAC,CAAC;IAExElF,sBAAsB,CAACmB,WAAW,EAAE;QAClCC,KAAK,EAAEK,CAAAA,CAAAA,GAAgB,GAAhBA,GAAG,CAAC2D,YAAY,SAAO,GAAvB3D,KAAAA,CAAuB,GAAvBA,QAAAA,GAAgB,CAAEL,KAAK,SAAA,GAAvBK,KAAAA,CAAuB,QAAEL,KAAK,AAAP,CAAA,IAAW,EAAE;QAC3CC,SAAS,EAAEI,GAAG,CAACJ,SAAS;QACxBC,KAAK;KACN,CAAC,CAAC;IAEH,IAAI,SAAS,IAAIG,GAAG,IAAI,SAAS,IAAIA,GAAG,CAACE,OAAO,IAAI,OAAOF,GAAG,CAACE,OAAO,CAACS,OAAO,KAAK,QAAQ,EAAE;QAC3FX,GAAG,CAACE,OAAO,CAACS,OAAO,GAAG2D,IAAAA,KAAS,UAAA,EAACtE,GAAG,CAACE,OAAO,CAACS,OAAO,CAAC,AAAC,CAAC;IACxD,CAAC;IAED,MAAM4D,aAAa,GAAG;QACpBC,gBAAgB,EAAE,CAAC;QACnBC,UAAU,EAAE,KAAK;QACjBC,IAAI,EAAE;YAAC1E,GAAG;SAAC;KACZ,AAAC;IACF,MAAM2E,IAAI,GAAG,CAAC,yLAAyL,EAAEC,IAAI,CAACC,SAAS,CACrNN,aAAa,CACd,CAAC,uBAAuB,CAAC,AAAC;IAE3B,MAAMO,iBAAiB,GAAG,MAAMC,IAAAA,yBAAwB,yBAAA,EACtDrF,WAAW,EACX,wBAAwB;IACxB,EAAE,EACFsF,IAAAA,YAAW,EAAA,QAAA,EAACtF,WAAW,EAAE,oBAAoB,CAAC,EAC9C;QACEuF,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE,KAAK;QAClBC,OAAO,EAAE,EAAE;QACXjB,UAAU;QACVkB,WAAW,EAAE,KAAK;QAClBC,aAAa,EAAE,KAAK;KACrB,CACF,AAAC;IAEF,MAAMC,UAAU,GAAGd,IAAI,CAACe,OAAO,CAAC,SAAS,EAAE,CAAC,YAAY,EAAEZ,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,AAAC;IAChG,OAAOW,UAAU,CAAC;AACpB,CAAC;AAED,SAASxC,eAAe,CACtBvD,WAAmB,EACnBC,KAAc,EAC2B;IACzC,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAIb,KAAK,CAAC6G,OAAO,CAAChG,KAAK,CAAC,EAAE;QACxB,OAAOA,KAAK,CAAC;IACf,CAAC;IAED,MAAMiG,UAAU,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAACnG,WAAW,CAAC,AAAC;IAEnD,OAAOoG,IAAAA,iBAAK,EAAA,MAAA,EAACnG,KAAK,CAAC,CAChBmC,GAAG,CAAC,CAACC,KAAK,GAAK;QACd,wGAAwG;QAExG,IAAIA,KAAK,CAACiC,IAAI,EAAE;YACd,6IAA6I;YAC7I,IAAIjC,KAAK,CAACiC,IAAI,CAAC+B,UAAU,CAAC,GAAG,CAAC,IAAIhE,KAAK,CAACiC,IAAI,CAACgC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAClE,KAAK,CAACiC,IAAI,CAAC,EAAE;gBACzF,oDAAoD;gBACpDjC,KAAK,CAACiC,IAAI,GAAG,yBAAyB,GAAGC,KAAI,EAAA,QAAA,CAACiC,QAAQ,CAACN,UAAU,EAAE7D,KAAK,CAACiC,IAAI,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,OAAO;YACL,GAAGjC,KAAK;YACRf,MAAM,EAAEe,KAAK,CAACf,MAAM,IAAI,IAAI,GAAGe,KAAK,CAACf,MAAM,GAAG,CAAC,GAAG,IAAI;SACvD,CAAC;IACJ,CAAC,CAAC,CACDxB,MAAM,CAAC,CAACuC,KAAK,GAAKA,KAAK,CAACiC,IAAI,IAAI,CAACjC,KAAK,CAACiC,IAAI,CAACgC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;AAC3E,CAAC;AAED,SAASC,QAAQ,CAACE,GAAW,EAAW;IACtC,IAAI;QACF,kCAAkC;QAClC,IAAIC,GAAG,CAACD,GAAG,CAAC,CAAC;QACb,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}