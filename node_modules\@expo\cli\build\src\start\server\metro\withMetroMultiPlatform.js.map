{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroMultiPlatform.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, Platform } from '@expo/config';\nimport fs from 'fs';\nimport Bundler from 'metro/src/Bundler';\nimport { ConfigT } from 'metro-config';\nimport { Resolution, ResolutionContext, CustomResolutionContext } from 'metro-resolver';\nimport * as metroResolver from 'metro-resolver';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { createFastResolver, FailedToResolvePathError } from './createExpoMetroResolver';\nimport { isNodeExternal, shouldCreateVirtualCanary, shouldCreateVirtualShim } from './externals';\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { getMetroBundlerWithVirtualModules } from './metroVirtualModules';\nimport {\n  withMetroErrorReportingResolver,\n  withMetroMutatedResolverContext,\n  withMetroResolvers,\n} from './withMetroResolvers';\nimport { Log } from '../../../log';\nimport { FileNotifier } from '../../../utils/FileNotifier';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\nimport { isInteractive } from '../../../utils/interactive';\nimport { loadTsConfigPathsAsync, TsConfigPaths } from '../../../utils/tsconfig/loadTsConfigPaths';\nimport { resolveWithTsConfigPaths } from '../../../utils/tsconfig/resolveWithTsConfigPaths';\nimport { isServerEnvironment } from '../middleware/metroOptions';\nimport { PlatformBundlers } from '../platformBundlers';\n\ntype Mutable<T> = { -readonly [K in keyof T]: T[K] };\n\nconst ASSET_REGISTRY_SRC = `const assets=[];module.exports={registerAsset:s=>assets.push(s),getAssetByID:s=>assets[s-1]};`;\n\nconst debug = require('debug')('expo:start:server:metro:multi-platform') as typeof console.log;\n\nfunction withWebPolyfills(\n  config: ConfigT,\n  {\n    getMetroBundler,\n  }: {\n    getMetroBundler: () => Bundler;\n  }\n): ConfigT {\n  const originalGetPolyfills = config.serializer.getPolyfills\n    ? config.serializer.getPolyfills.bind(config.serializer)\n    : () => [];\n\n  const getPolyfills = (ctx: { platform?: string | null }): readonly string[] => {\n    const virtualEnvVarId = `\\0polyfill:environment-variables`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualEnvVarId,\n      (() => {\n        return `//`;\n      })()\n    );\n\n    const virtualModuleId = `\\0polyfill:external-require`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualModuleId,\n      (() => {\n        if (ctx.platform === 'web') {\n          return `global.$$require_external = typeof window === \"undefined\" ? require : () => null;`;\n        } else {\n          // Wrap in try/catch to support Android.\n          return 'try { global.$$require_external = typeof expo === \"undefined\" ? require : (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} } catch { global.$$require_external = (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} }';\n        }\n      })()\n    );\n\n    if (ctx.platform === 'web') {\n      return [\n        virtualModuleId,\n        virtualEnvVarId,\n        // Ensure that the error-guard polyfill is included in the web polyfills to\n        // make metro-runtime work correctly.\n        // TODO: This module is pretty big for a function that simply re-throws an error that doesn't need to be caught.\n        require.resolve('@react-native/js-polyfills/error-guard'),\n      ];\n    }\n\n    // Generally uses `rn-get-polyfills`\n    const polyfills = originalGetPolyfills(ctx);\n    return [...polyfills, virtualModuleId, virtualEnvVarId];\n  };\n\n  return {\n    ...config,\n    serializer: {\n      ...config.serializer,\n      getPolyfills,\n    },\n  };\n}\n\nfunction normalizeSlashes(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nexport function getNodejsExtensions(srcExts: readonly string[]): string[] {\n  const mjsExts = srcExts.filter((ext) => /mjs$/.test(ext));\n  const nodejsSourceExtensions = srcExts.filter((ext) => !/mjs$/.test(ext));\n  // find index of last `*.js` extension\n  const jsIndex = nodejsSourceExtensions.reduce((index, ext, i) => {\n    return /jsx?$/.test(ext) ? i : index;\n  }, -1);\n\n  // insert `*.mjs` extensions after `*.js` extensions\n  nodejsSourceExtensions.splice(jsIndex + 1, 0, ...mjsExts);\n\n  return nodejsSourceExtensions;\n}\n\n/**\n * Apply custom resolvers to do the following:\n * - Disable `.native.js` extensions on web.\n * - Alias `react-native` to `react-native-web` on web.\n * - Redirect `react-native-web/dist/modules/AssetRegistry/index.js` to `@react-native/assets/registry.js` on web.\n * - Add support for `tsconfig.json`/`jsconfig.json` aliases via `compilerOptions.paths`.\n * - Alias react-native renderer code to a vendored React canary build on native.\n */\nexport function withExtendedResolver(\n  config: ConfigT,\n  {\n    tsconfig,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  }: {\n    tsconfig: TsConfigPaths | null;\n    isTsconfigPathsEnabled?: boolean;\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled?: boolean;\n    isReactServerComponentsEnabled?: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (isReactServerComponentsEnabled) {\n    Log.warn(\n      `Experimental React Server Components is enabled. Production exports are not supported yet.`\n    );\n  }\n  if (isFastResolverEnabled) {\n    Log.warn(`Experimental module resolution is enabled.`);\n  }\n\n  if (isReactCanaryEnabled) {\n    Log.warn(`Experimental React Canary version is enabled.`);\n  }\n\n  const defaultResolver = metroResolver.resolve;\n  const resolver = isFastResolverEnabled\n    ? createFastResolver({\n        preserveSymlinks: true,\n        blockList: !config.resolver?.blockList\n          ? []\n          : Array.isArray(config.resolver?.blockList)\n            ? config.resolver?.blockList\n            : [config.resolver?.blockList],\n      })\n    : defaultResolver;\n\n  const aliases: { [key: string]: Record<string, string> } = {\n    web: {\n      'react-native': 'react-native-web',\n      'react-native/index': 'react-native-web',\n      'react-native/Libraries/Image/resolveAssetSource': 'expo-asset/build/resolveAssetSource',\n    },\n  };\n\n  let _universalAliases: [RegExp, string][] | null;\n\n  function getUniversalAliases() {\n    if (_universalAliases) {\n      return _universalAliases;\n    }\n\n    _universalAliases = [];\n\n    // This package is currently always installed as it is included in the `expo` package.\n    if (resolveFrom.silent(config.projectRoot, '@expo/vector-icons')) {\n      debug('Enabling alias: react-native-vector-icons -> @expo/vector-icons');\n      _universalAliases.push([/^react-native-vector-icons(\\/.*)?/, '@expo/vector-icons$1']);\n    }\n    if (isReactServerComponentsEnabled) {\n      if (resolveFrom.silent(config.projectRoot, 'expo-router/rsc')) {\n        debug('Enabling bridge alias: expo-router -> expo-router/rsc');\n        _universalAliases.push([/^expo-router$/, 'expo-router/rsc']);\n        // Bridge the internal entry point which is a standalone import to ensure package.json resolution works as expected.\n        _universalAliases.push([/^expo-router\\/entry-classic$/, 'expo-router/rsc/entry']);\n      }\n    }\n    return _universalAliases;\n  }\n\n  const preferredMainFields: { [key: string]: string[] } = {\n    // Defaults from Expo Webpack. Most packages using `react-native` don't support web\n    // in the `react-native` field, so we should prefer the `browser` field.\n    // https://github.com/expo/router/issues/37\n    web: ['browser', 'module', 'main'],\n  };\n\n  let tsConfigResolve =\n    isTsconfigPathsEnabled && (tsconfig?.paths || tsconfig?.baseUrl != null)\n      ? resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n          paths: tsconfig.paths ?? {},\n          baseUrl: tsconfig.baseUrl ?? config.projectRoot,\n          hasBaseUrl: !!tsconfig.baseUrl,\n        })\n      : null;\n\n  // TODO: Move this to be a transform key for invalidation.\n  if (!isExporting && isInteractive()) {\n    if (isTsconfigPathsEnabled) {\n      // TODO: We should track all the files that used imports and invalidate them\n      // currently the user will need to save all the files that use imports to\n      // use the new aliases.\n      const configWatcher = new FileNotifier(config.projectRoot, [\n        './tsconfig.json',\n        './jsconfig.json',\n      ]);\n      configWatcher.startObserving(() => {\n        debug('Reloading tsconfig.json');\n        loadTsConfigPathsAsync(config.projectRoot).then((tsConfigPaths) => {\n          if (tsConfigPaths?.paths && !!Object.keys(tsConfigPaths.paths).length) {\n            debug('Enabling tsconfig.json paths support');\n            tsConfigResolve = resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n              paths: tsConfigPaths.paths ?? {},\n              baseUrl: tsConfigPaths.baseUrl ?? config.projectRoot,\n              hasBaseUrl: !!tsConfigPaths.baseUrl,\n            });\n          } else {\n            debug('Disabling tsconfig.json paths support');\n            tsConfigResolve = null;\n          }\n        });\n      });\n\n      // TODO: This probably prevents the process from exiting.\n      installExitHooks(() => {\n        configWatcher.stopObserving();\n      });\n    } else {\n      debug('Skipping tsconfig.json paths support');\n    }\n  }\n\n  let nodejsSourceExtensions: string[] | null = null;\n\n  function getStrictResolver(\n    { resolveRequest, ...context }: ResolutionContext,\n    platform: string | null\n  ) {\n    return function doResolve(moduleName: string): Resolution {\n      return resolver(context, moduleName, platform);\n    };\n  }\n\n  function getOptionalResolver(context: ResolutionContext, platform: string | null) {\n    const doResolve = getStrictResolver(context, platform);\n    return function optionalResolve(moduleName: string): Resolution | null {\n      try {\n        return doResolve(moduleName);\n      } catch (error) {\n        // If the error is directly related to a resolver not being able to resolve a module, then\n        // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n        const isResolutionError =\n          isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n        if (!isResolutionError) {\n          throw error;\n        }\n      }\n      return null;\n    };\n  }\n\n  // TODO: This is a hack to get resolveWeak working.\n  const idFactory = (config.serializer?.createModuleIdFactory?.() ??\n    ((id: number | string, context: { platform: string; environment?: string }): number | string =>\n      id)) as (\n    id: number | string,\n    context: { platform: string; environment?: string }\n  ) => number | string;\n\n  const getAssetRegistryModule = () => {\n    const virtualModuleId = `\\0polyfill:assets-registry`;\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualModuleId,\n      ASSET_REGISTRY_SRC\n    );\n    return {\n      type: 'sourceFile',\n      filePath: virtualModuleId,\n    } as const;\n  };\n\n  // If Node.js pass-through, then remap to a module like `module.exports = $$require_external(<module>)`.\n  // If module should be shimmed, remap to an empty module.\n  const externals: {\n    match: (context: ResolutionContext, moduleName: string, platform: string | null) => boolean;\n    replace: 'empty' | 'node' | 'weak';\n  }[] = [\n    {\n      match: (context: ResolutionContext, moduleName: string) => {\n        if (\n          // Disable internal externals when exporting for production.\n          context.customResolverOptions.exporting ||\n          // These externals are only for Node.js environments.\n          !isServerEnvironment(context.customResolverOptions?.environment)\n        ) {\n          return false;\n        }\n\n        if (context.customResolverOptions?.environment === 'react-server') {\n          // Ensure these non-react-server modules are excluded when bundling for React Server Components in development.\n          return /^(source-map-support(\\/.*)?|@babel\\/runtime\\/.+|debug|metro-runtime\\/src\\/modules\\/HMRClient|metro|acorn-loose|acorn|chalk|ws|ansi-styles|supports-color|color-convert|has-flag|utf-8-validate|color-name|react-refresh\\/runtime|@remix-run\\/node\\/.+)$/.test(\n            moduleName\n          );\n        }\n\n        // TODO: Windows doesn't support externals somehow.\n        if (process.platform === 'win32') {\n          return /^(source-map-support(\\/.*)?)$/.test(moduleName);\n        }\n\n        // Extern these modules in standard Node.js environments in development to prevent API routes side-effects\n        // from leaking into the dev server process.\n        return /^(source-map-support(\\/.*)?|react|react-native-helmet-async|@radix-ui\\/.+|@babel\\/runtime\\/.+|react-dom(\\/.+)?|debug|acorn-loose|acorn|css-in-js-utils\\/lib\\/.+|hyphenate-style-name|color|color-string|color-convert|color-name|fontfaceobserver|fast-deep-equal|query-string|escape-string-regexp|invariant|postcss-value-parser|memoize-one|nullthrows|strict-uri-encode|decode-uri-component|split-on-first|filter-obj|warn-once|simple-swizzle|is-arrayish|inline-style-prefixer\\/.+)$/.test(\n          moduleName\n        );\n      },\n      replace: 'node',\n    },\n    // Externals to speed up async split chunks by extern-ing common packages that appear in the root client chunk.\n    {\n      match: (context: ResolutionContext, moduleName: string, platform: string | null) => {\n        if (\n          // Disable internal externals when exporting for production.\n          context.customResolverOptions.exporting ||\n          // These externals are only for client environments.\n          isServerEnvironment(context.customResolverOptions?.environment) ||\n          // Only enable for client boundaries\n          !context.customResolverOptions.clientboundary\n        ) {\n          return false;\n        }\n\n        // We don't support this in the resolver at the moment.\n        if (moduleName.endsWith('/package.json')) {\n          return false;\n        }\n\n        const isExternal = // Extern these modules in standard Node.js environments.\n          /^(deprecated-react-native-prop-types|react|react\\/jsx-dev-runtime|scheduler|react-native|react-dom(\\/.+)?|metro-runtime(\\/.+)?)$/.test(\n            moduleName\n          ) ||\n          // TODO: Add more\n          /^@babel\\/runtime\\/helpers\\/(wrapNativeSuper)$/.test(moduleName);\n\n        return isExternal;\n      },\n      replace: 'weak',\n    },\n  ];\n\n  const metroConfigWithCustomResolver = withMetroResolvers(config, [\n    // Mock out production react imports in development.\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      // This resolution is dev-only to prevent bundling the production React packages in development.\n      if (!context.dev) return null;\n\n      if (\n        // Match react-native renderers.\n        (platform !== 'web' &&\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/]react-native[\\\\/]/) &&\n          moduleName.match(/([\\\\/]ReactFabric|ReactNativeRenderer)-prod/)) ||\n        // Match react production imports.\n        (moduleName.match(/\\.production(\\.min)?\\.js$/) &&\n          // Match if the import originated from a react package.\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/](react[-\\\\/]|scheduler[\\\\/])/))\n      ) {\n        debug(`Skipping production module: ${moduleName}`);\n        // /Users/<USER>/to/expo/node_modules/react/index.js ./cjs/react.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react/jsx-dev-runtime.js ./cjs/react-jsx-dev-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-refresh/runtime.js ./cjs/react-refresh-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/scheduler/index.native.js ./cjs/scheduler.native.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        return {\n          type: 'empty',\n        };\n      }\n      return null;\n    },\n    // tsconfig paths\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      return (\n        tsConfigResolve?.(\n          {\n            originModulePath: context.originModulePath,\n            moduleName,\n          },\n          getOptionalResolver(context, platform)\n        ) ?? null\n      );\n    },\n\n    // Node.js externals support\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      const isServer =\n        context.customResolverOptions?.environment === 'node' ||\n        context.customResolverOptions?.environment === 'react-server';\n\n      const moduleId = isNodeExternal(moduleName);\n      if (!moduleId) {\n        return null;\n      }\n\n      if (\n        // In browser runtimes, we want to either resolve a local node module by the same name, or shim the module to\n        // prevent crashing when Node.js built-ins are imported.\n        !isServer\n      ) {\n        // Perform optional resolve first. If the module doesn't exist (no module in the node_modules)\n        // then we can mock the file to use an empty module.\n        const result = getOptionalResolver(context, platform)(moduleName);\n\n        if (!result && platform !== 'web') {\n          // Preserve previous behavior where native throws an error on node.js internals.\n          return null;\n        }\n\n        return (\n          result ?? {\n            // In this case, mock the file to use an empty module.\n            type: 'empty',\n          }\n        );\n      }\n      const contents = `module.exports=$$require_external('node:${moduleId}');`;\n      debug(`Virtualizing Node.js \"${moduleId}\"`);\n      const virtualModuleId = `\\0node:${moduleId}`;\n      getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n        virtualModuleId,\n        contents\n      );\n      return {\n        type: 'sourceFile',\n        filePath: virtualModuleId,\n      };\n    },\n\n    // Custom externals support\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      // We don't support this in the resolver at the moment.\n      if (moduleName.endsWith('/package.json')) {\n        return null;\n      }\n      const environment = context.customResolverOptions?.environment;\n\n      const strictResolve = getStrictResolver(context, platform);\n\n      for (const external of externals) {\n        if (external.match(context, moduleName, platform)) {\n          if (external.replace === 'empty') {\n            debug(`Redirecting external \"${moduleName}\" to \"${external.replace}\"`);\n            return {\n              type: external.replace,\n            };\n          } else if (external.replace === 'weak') {\n            // TODO: Make this use require.resolveWeak again. Previously this was just resolving to the same path.\n            const realModule = strictResolve(moduleName);\n            const realPath = realModule.type === 'sourceFile' ? realModule.filePath : moduleName;\n            const opaqueId = idFactory(realPath, {\n              platform: platform!,\n              environment,\n            });\n\n            const contents =\n              typeof opaqueId === 'number'\n                ? `module.exports=/*${moduleName}*/__r(${opaqueId})`\n                : `module.exports=/*${moduleName}*/__r(${JSON.stringify(opaqueId)})`;\n            // const contents = `module.exports=/*${moduleName}*/__r(require.resolveWeak('${moduleName}'))`;\n            // const generatedModuleId = fastHashMemoized(contents);\n            const virtualModuleId = `\\0weak:${opaqueId}`;\n            debug('Virtualizing module:', moduleName, '->', virtualModuleId);\n            getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n              virtualModuleId,\n              contents\n            );\n            return {\n              type: 'sourceFile',\n              filePath: virtualModuleId,\n            };\n          } else if (external.replace === 'node') {\n            const contents = `module.exports=$$require_external('${moduleName}')`;\n            const virtualModuleId = `\\0node:${moduleName}`;\n            debug('Virtualizing Node.js (custom):', moduleName, '->', virtualModuleId);\n            getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n              virtualModuleId,\n              contents\n            );\n            return {\n              type: 'sourceFile',\n              filePath: virtualModuleId,\n            };\n          } else {\n            throw new CommandError(\n              `Invalid external alias type: \"${external.replace}\" for module \"${moduleName}\" (platform: ${platform}, originModulePath: ${context.originModulePath})`\n            );\n          }\n        }\n      }\n      return null;\n    },\n\n    // Basic moduleId aliases\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      // Conditionally remap `react-native` to `react-native-web` on web in\n      // a way that doesn't require Babel to resolve the alias.\n      if (platform && platform in aliases && aliases[platform][moduleName]) {\n        const redirectedModuleName = aliases[platform][moduleName];\n        return getStrictResolver(context, platform)(redirectedModuleName);\n      }\n\n      for (const [matcher, alias] of getUniversalAliases()) {\n        const match = moduleName.match(matcher);\n        if (match) {\n          const aliasedModule = alias.replace(\n            /\\$(\\d+)/g,\n            (_, index) => match[parseInt(index, 10)] ?? ''\n          );\n          const doResolve = getStrictResolver(context, platform);\n          debug(`Alias \"${moduleName}\" to \"${aliasedModule}\"`);\n          return doResolve(aliasedModule);\n        }\n      }\n\n      return null;\n    },\n\n    // Polyfill for asset registry\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      if (/^@react-native\\/assets-registry\\/registry(\\.js)?$/.test(moduleName)) {\n        return getAssetRegistryModule();\n      }\n\n      if (\n        platform === 'web' &&\n        context.originModulePath.match(/node_modules[\\\\/]react-native-web[\\\\/]/) &&\n        moduleName.includes('/modules/AssetRegistry')\n      ) {\n        return getAssetRegistryModule();\n      }\n\n      return null;\n    },\n\n    // TODO: Reduce these as much as possible in the future.\n    // Complex post-resolution rewrites.\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      const doResolve = getStrictResolver(context, platform);\n\n      const result = doResolve(moduleName);\n\n      if (result.type !== 'sourceFile') {\n        return result;\n      }\n\n      if (platform === 'web') {\n        if (result.filePath.includes('node_modules')) {\n          // // Disallow importing confusing native modules on web\n          if (moduleName.includes('react-native/Libraries/Utilities/codegenNativeCommands')) {\n            throw new FailedToResolvePathError(\n              `Importing native-only module \"${moduleName}\" on web from: ${context.originModulePath}`\n            );\n          }\n\n          // Replace with static shims\n\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const shimFile = shouldCreateVirtualShim(normalName);\n          if (shimFile) {\n            const virtualId = `\\0shim:${normalName}`;\n            const bundler = getMetroBundlerWithVirtualModules(getMetroBundler());\n            if (!bundler.hasVirtualModule(virtualId)) {\n              bundler.setVirtualModule(virtualId, fs.readFileSync(shimFile, 'utf8'));\n            }\n            debug(`Redirecting module \"${result.filePath}\" to shim`);\n\n            return {\n              ...result,\n              filePath: virtualId,\n            };\n          }\n        }\n      } else {\n        const isServer =\n          context.customResolverOptions?.environment === 'node' ||\n          context.customResolverOptions?.environment === 'react-server';\n\n        // react-native/Libraries/Core/InitializeCore\n        const normal = normalizeSlashes(result.filePath);\n\n        // Shim out React Native native runtime globals in server mode for native.\n        if (isServer) {\n          if (normal.endsWith('react-native/Libraries/Core/InitializeCore.js')) {\n            console.log('Shimming out InitializeCore for React Native in native SSR bundle');\n            return {\n              type: 'empty',\n            };\n          }\n        }\n\n        // When server components are enabled, redirect React Native's renderer to the canary build\n        // this will enable the use hook and other requisite features from React 19.\n        if (isReactCanaryEnabled && result.filePath.includes('node_modules')) {\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const canaryFile = shouldCreateVirtualCanary(normalName);\n          if (canaryFile) {\n            debug(`Redirecting React Native module \"${result.filePath}\" to canary build`);\n            return {\n              ...result,\n              filePath: canaryFile,\n            };\n          }\n        }\n      }\n\n      return result;\n    },\n  ]);\n\n  // Ensure we mutate the resolution context to include the custom resolver options for server and web.\n  const metroConfigWithCustomContext = withMetroMutatedResolverContext(\n    metroConfigWithCustomResolver,\n    (\n      immutableContext: CustomResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ): CustomResolutionContext => {\n      const context: Mutable<CustomResolutionContext> = {\n        ...immutableContext,\n        preferNativePlatform: platform !== 'web',\n      };\n\n      // TODO: Remove this when we have React 19 in the expo/expo monorepo.\n      if (\n        isReactCanaryEnabled &&\n        // Change the node modules path for react and react-dom to use the vendor in Expo CLI.\n        /^(react|react\\/.*|react-dom|react-dom\\/.*)$/.test(moduleName)\n      ) {\n        context.nodeModulesPaths = [\n          path.join(require.resolve('@expo/cli/package.json'), '../static/canary-full'),\n        ];\n      }\n\n      if (isServerEnvironment(context.customResolverOptions?.environment)) {\n        // Adjust nodejs source extensions to sort mjs after js, including platform variants.\n        if (nodejsSourceExtensions === null) {\n          nodejsSourceExtensions = getNodejsExtensions(context.sourceExts);\n        }\n        context.sourceExts = nodejsSourceExtensions;\n\n        context.unstable_enablePackageExports = true;\n        context.unstable_conditionsByPlatform = {};\n\n        const isReactServerComponents =\n          context.customResolverOptions?.environment === 'react-server';\n\n        if (isReactServerComponents) {\n          // NOTE: Align the behavior across server and client. This is a breaking change so we'll just roll it out with React Server Components.\n          // This ensures that react-server and client code both resolve `module` and `main` in the same order.\n          if (platform === 'web') {\n            // Node.js runtimes should only be importing main at the moment.\n            // This is a temporary fix until we can support the package.json exports.\n            context.mainFields = ['module', 'main'];\n          } else {\n            // In Node.js + native, use the standard main fields.\n            context.mainFields = ['react-native', 'module', 'main'];\n          }\n        } else {\n          if (platform === 'web') {\n            // Node.js runtimes should only be importing main at the moment.\n            // This is a temporary fix until we can support the package.json exports.\n            context.mainFields = ['main', 'module'];\n          } else {\n            // In Node.js + native, use the standard main fields.\n            context.mainFields = ['react-native', 'main', 'module'];\n          }\n        }\n\n        // Enable react-server import conditions.\n        if (context.customResolverOptions?.environment === 'react-server') {\n          context.unstable_conditionNames = [\n            'node',\n            'import',\n            'require',\n            'react-server',\n            'workerd',\n          ];\n        } else {\n          context.unstable_conditionNames = ['node', 'require'];\n        }\n      } else {\n        // Non-server changes\n\n        if (!env.EXPO_METRO_NO_MAIN_FIELD_OVERRIDE && platform && platform in preferredMainFields) {\n          context.mainFields = preferredMainFields[platform];\n        }\n      }\n\n      return context;\n    }\n  );\n\n  return withMetroErrorReportingResolver(metroConfigWithCustomContext);\n}\n\n/** @returns `true` if the incoming resolution should be swapped. */\nexport function shouldAliasModule(\n  input: {\n    platform: string | null;\n    result: Resolution;\n  },\n  alias: { platform: string; output: string }\n): boolean {\n  return (\n    input.platform === alias.platform &&\n    input.result?.type === 'sourceFile' &&\n    typeof input.result?.filePath === 'string' &&\n    normalizeSlashes(input.result.filePath).endsWith(alias.output)\n  );\n}\n\n/** Add support for `react-native-web` and the Web platform. */\nexport async function withMetroMultiPlatformAsync(\n  projectRoot: string,\n  {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    isNamedRequiresEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  }: {\n    config: ConfigT;\n    exp: ExpoConfig;\n    isTsconfigPathsEnabled: boolean;\n    platformBundlers: PlatformBundlers;\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled: boolean;\n    isReactServerComponentsEnabled: boolean;\n    isNamedRequiresEnabled: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (isNamedRequiresEnabled) {\n    debug('Using Expo metro require runtime.');\n    // Change the default metro-runtime to a custom one that supports bundle splitting.\n    require('metro-config/src/defaults/defaults').moduleSystem = require.resolve(\n      '@expo/cli/build/metro-require/require'\n    );\n  }\n\n  if (!config.projectRoot) {\n    // @ts-expect-error: read-only types\n    config.projectRoot = projectRoot;\n  }\n\n  // Required for @expo/metro-runtime to format paths in the web LogBox.\n  process.env.EXPO_PUBLIC_PROJECT_ROOT = process.env.EXPO_PUBLIC_PROJECT_ROOT ?? projectRoot;\n\n  // This is used for running Expo CLI in development against projects outside the monorepo.\n  if (!isDirectoryIn(__dirname, projectRoot)) {\n    if (!config.watchFolders) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders = [];\n    }\n    // @ts-expect-error: watchFolders is readonly\n    config.watchFolders.push(path.join(require.resolve('metro-runtime/package.json'), '../..'));\n    if (isReactCanaryEnabled) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders.push(path.join(require.resolve('@expo/cli/package.json'), '..'));\n    }\n  }\n\n  // TODO: Remove this\n  // @ts-expect-error: Invalidate the cache when the location of expo-router changes on-disk.\n  config.transformer._expoRouterPath = resolveFrom.silent(projectRoot, 'expo-router');\n\n  let tsconfig: null | TsConfigPaths = null;\n\n  if (isTsconfigPathsEnabled) {\n    tsconfig = await loadTsConfigPathsAsync(projectRoot);\n  }\n\n  let expoConfigPlatforms = Object.entries(platformBundlers)\n    .filter(\n      ([platform, bundler]) => bundler === 'metro' && exp.platforms?.includes(platform as Platform)\n    )\n    .map(([platform]) => platform);\n\n  if (Array.isArray(config.resolver.platforms)) {\n    expoConfigPlatforms = [...new Set(expoConfigPlatforms.concat(config.resolver.platforms))];\n  }\n\n  // @ts-expect-error: typed as `readonly`.\n  config.resolver.platforms = expoConfigPlatforms;\n\n  config = withWebPolyfills(config, { getMetroBundler });\n\n  return withExtendedResolver(config, {\n    tsconfig,\n    isExporting,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isReactCanaryEnabled,\n    isReactServerComponentsEnabled,\n    getMetroBundler,\n  });\n}\n\nfunction isDirectoryIn(targetPath: string, rootPath: string) {\n  return targetPath.startsWith(rootPath) && targetPath.length >= rootPath.length;\n}\n"], "names": ["getNodejsExtensions", "withExtendedResolver", "shouldAliasModule", "withMetroMultiPlatformAsync", "ASSET_REGISTRY_SRC", "debug", "require", "withWebPolyfills", "config", "getMetroBundler", "originalGetPolyfills", "serializer", "getPolyfills", "bind", "ctx", "virtualEnvVarId", "getMetroBundlerWithVirtualModules", "setVirtualModule", "virtualModuleId", "platform", "resolve", "polyfills", "normalizeSlashes", "p", "replace", "srcExts", "mjsExts", "filter", "ext", "test", "nodejsSourceExtensions", "jsIndex", "reduce", "index", "i", "splice", "tsconfig", "isTsconfigPathsEnabled", "isFastResolverEnabled", "isExporting", "isReactCanaryEnabled", "isReactServerComponentsEnabled", "Log", "warn", "defaultResolver", "metroResolver", "resolver", "createFastResolver", "preserveSymlinks", "blockList", "Array", "isArray", "aliases", "web", "_universalAliases", "getUniversalAliases", "resolveFrom", "silent", "projectRoot", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsConfigResolve", "paths", "baseUrl", "resolveWithTsConfigPaths", "hasBaseUrl", "isInteractive", "config<PERSON><PERSON><PERSON>", "FileNotifier", "startObserving", "loadTsConfigPathsAsync", "then", "tsConfigPaths", "Object", "keys", "length", "installExitHooks", "stopObserving", "getStrictResolver", "resolveRequest", "context", "doResolve", "moduleName", "getOptionalResolver", "optionalResolve", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "idFactory", "createModuleIdFactory", "id", "getAssetRegistryModule", "type", "filePath", "externals", "match", "customResolverOptions", "exporting", "isServerEnvironment", "environment", "process", "clientboundary", "endsWith", "isExternal", "metroConfigWithCustomResolver", "withMetroResolvers", "dev", "originModulePath", "isServer", "moduleId", "isNodeExternal", "result", "contents", "strictResolve", "external", "realModule", "realPath", "opaqueId", "JSON", "stringify", "CommandError", "redirectedModuleName", "matcher", "alias", "aliasedModule", "_", "parseInt", "includes", "FailedToResolvePathError", "normalName", "shimFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtualId", "bundler", "hasVirtualModule", "fs", "readFileSync", "normal", "console", "log", "canaryFile", "shouldCreateVirtualCanary", "metroConfigWithCustomContext", "withMetroMutatedResolverContext", "immutableContext", "preferNativePlatform", "nodeModulesPaths", "path", "join", "sourceExts", "unstable_enablePackageExports", "unstable_conditionsByPlatform", "isReactServerComponents", "mainFields", "unstable_conditionNames", "env", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "withMetroErrorReportingResolver", "input", "output", "exp", "platformBundlers", "isNamedRequiresEnabled", "moduleSystem", "EXPO_PUBLIC_PROJECT_ROOT", "isDirectoryIn", "__dirname", "watchFolders", "transformer", "_expoRouterPath", "expoConfigPlatforms", "entries", "platforms", "map", "Set", "concat", "targetPath", "rootPath", "startsWith"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAoGgBA,mBAAmB,MAAnBA,mBAAmB;IAsBnBC,oBAAoB,MAApBA,oBAAoB;IAimBpBC,iBAAiB,MAAjBA,iBAAiB;IAgBXC,2BAA2B,MAA3BA,2BAA2B;;;8DA1uBlC,IAAI;;;;;;;+DAIY,gBAAgB;;;;;;;8DAC9B,MAAM;;;;;;;8DACC,cAAc;;;;;;yCAEuB,2BAA2B;2BACL,aAAa;6BACzB,eAAe;qCACpC,uBAAuB;oCAKlE,sBAAsB;qBACT,cAAc;8BACL,6BAA6B;qBACtC,oBAAoB;wBACX,uBAAuB;sBACnB,qBAAqB;6BACxB,4BAA4B;mCACJ,2CAA2C;0CACxD,kDAAkD;8BACvD,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKhE,MAAMC,kBAAkB,GAAG,CAAC,6FAA6F,CAAC,AAAC;AAE3H,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wCAAwC,CAAC,AAAsB,AAAC;AAE/F,SAASC,gBAAgB,CACvBC,MAAe,EACf,EACEC,eAAe,CAAA,EAGhB,EACQ;IACT,MAAMC,oBAAoB,GAAGF,MAAM,CAACG,UAAU,CAACC,YAAY,GACvDJ,MAAM,CAACG,UAAU,CAACC,YAAY,CAACC,IAAI,CAACL,MAAM,CAACG,UAAU,CAAC,GACtD,IAAM,EAAE,AAAC;IAEb,MAAMC,YAAY,GAAG,CAACE,GAAiC,GAAwB;QAC7E,MAAMC,eAAe,GAAG,CAAC,gCAAgC,CAAC,AAAC;QAE3DC,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEF,eAAe,EACf,CAAC,IAAM;YACL,OAAO,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CACL,CAAC;QAEF,MAAMG,eAAe,GAAG,CAAC,2BAA2B,CAAC,AAAC;QAEtDF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACf,CAAC,IAAM;YACL,IAAIJ,GAAG,CAACK,QAAQ,KAAK,KAAK,EAAE;gBAC1B,OAAO,CAAC,iFAAiF,CAAC,CAAC;YAC7F,OAAO;gBACL,wCAAwC;gBACxC,OAAO,qXAAqX,CAAC;YAC/X,CAAC;QACH,CAAC,CAAC,EAAE,CACL,CAAC;QAEF,IAAIL,GAAG,CAACK,QAAQ,KAAK,KAAK,EAAE;YAC1B,OAAO;gBACLD,eAAe;gBACfH,eAAe;gBACf,2EAA2E;gBAC3E,qCAAqC;gBACrC,gHAAgH;gBAChHT,OAAO,CAACc,OAAO,CAAC,wCAAwC,CAAC;aAC1D,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAMC,SAAS,GAAGX,oBAAoB,CAACI,GAAG,CAAC,AAAC;QAC5C,OAAO;eAAIO,SAAS;YAAEH,eAAe;YAAEH,eAAe;SAAC,CAAC;IAC1D,CAAC,AAAC;IAEF,OAAO;QACL,GAAGP,MAAM;QACTG,UAAU,EAAE;YACV,GAAGH,MAAM,CAACG,UAAU;YACpBC,YAAY;SACb;KACF,CAAC;AACJ,CAAC;AAED,SAASU,gBAAgB,CAACC,CAAS,EAAE;IACnC,OAAOA,CAAC,CAACC,OAAO,QAAQ,GAAG,CAAC,CAAC;AAC/B,CAAC;AAEM,SAASxB,mBAAmB,CAACyB,OAA0B,EAAY;IACxE,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,GAAK,OAAOC,IAAI,CAACD,GAAG,CAAC,CAAC,AAAC;IAC1D,MAAME,sBAAsB,GAAGL,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,GAAK,CAAC,OAAOC,IAAI,CAACD,GAAG,CAAC,CAAC,AAAC;IAC1E,sCAAsC;IACtC,MAAMG,OAAO,GAAGD,sBAAsB,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEL,GAAG,EAAEM,CAAC,GAAK;QAC/D,OAAO,QAAQL,IAAI,CAACD,GAAG,CAAC,GAAGM,CAAC,GAAGD,KAAK,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC,CAAC,AAAC;IAEP,oDAAoD;IACpDH,sBAAsB,CAACK,MAAM,CAACJ,OAAO,GAAG,CAAC,EAAE,CAAC,KAAKL,OAAO,CAAC,CAAC;IAE1D,OAAOI,sBAAsB,CAAC;AAChC,CAAC;AAUM,SAAS7B,oBAAoB,CAClCO,MAAe,EACf,EACE4B,QAAQ,CAAA,EACRC,sBAAsB,CAAA,EACtBC,qBAAqB,CAAA,EACrBC,WAAW,CAAA,EACXC,oBAAoB,CAAA,EACpBC,8BAA8B,CAAA,EAC9BhC,eAAe,CAAA,EAShB,EACD;QAkBkBD,GAAe,EAETA,IAAe,EAC3BA,IAAe,EACdA,IAAe,EAuHTA,IAAiB;IA5IpC,IAAIiC,8BAA8B,EAAE;QAClCC,IAAG,IAAA,CAACC,IAAI,CACN,CAAC,0FAA0F,CAAC,CAC7F,CAAC;IACJ,CAAC;IACD,IAAIL,qBAAqB,EAAE;QACzBI,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAIH,oBAAoB,EAAE;QACxBE,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,MAAMC,eAAe,GAAGC,cAAa,EAAA,CAACzB,OAAO,AAAC;IAC9C,MAAM0B,QAAQ,GAAGR,qBAAqB,GAClCS,IAAAA,wBAAkB,mBAAA,EAAC;QACjBC,gBAAgB,EAAE,IAAI;QACtBC,SAAS,EAAE,CAACzC,CAAAA,CAAAA,GAAe,GAAfA,MAAM,CAACsC,QAAQ,SAAW,GAA1BtC,KAAAA,CAA0B,GAA1BA,GAAe,CAAEyC,SAAS,CAAA,GAClC,EAAE,GACFC,KAAK,CAACC,OAAO,CAAC3C,CAAAA,IAAe,GAAfA,MAAM,CAACsC,QAAQ,SAAW,GAA1BtC,KAAAA,CAA0B,GAA1BA,IAAe,CAAEyC,SAAS,CAAC,GACvCzC,CAAAA,IAAe,GAAfA,MAAM,CAACsC,QAAQ,SAAW,GAA1BtC,KAAAA,CAA0B,GAA1BA,IAAe,CAAEyC,SAAS,GAC1B;YAACzC,CAAAA,IAAe,GAAfA,MAAM,CAACsC,QAAQ,SAAW,GAA1BtC,KAAAA,CAA0B,GAA1BA,IAAe,CAAEyC,SAAS;SAAC;KACnC,CAAC,GACFL,eAAe,AAAC;IAEpB,MAAMQ,OAAO,GAA8C;QACzDC,GAAG,EAAE;YACH,cAAc,EAAE,kBAAkB;YAClC,oBAAoB,EAAE,kBAAkB;YACxC,iDAAiD,EAAE,qCAAqC;SACzF;KACF,AAAC;IAEF,IAAIC,iBAAiB,AAA2B,AAAC;IAEjD,SAASC,mBAAmB,GAAG;QAC7B,IAAID,iBAAiB,EAAE;YACrB,OAAOA,iBAAiB,CAAC;QAC3B,CAAC;QAEDA,iBAAiB,GAAG,EAAE,CAAC;QAEvB,sFAAsF;QACtF,IAAIE,YAAW,EAAA,QAAA,CAACC,MAAM,CAACjD,MAAM,CAACkD,WAAW,EAAE,oBAAoB,CAAC,EAAE;YAChErD,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACzEiD,iBAAiB,CAACK,IAAI,CAAC;;gBAAsC,sBAAsB;aAAC,CAAC,CAAC;QACxF,CAAC;QACD,IAAIlB,8BAA8B,EAAE;YAClC,IAAIe,YAAW,EAAA,QAAA,CAACC,MAAM,CAACjD,MAAM,CAACkD,WAAW,EAAE,iBAAiB,CAAC,EAAE;gBAC7DrD,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBAC/DiD,iBAAiB,CAACK,IAAI,CAAC;;oBAAkB,iBAAiB;iBAAC,CAAC,CAAC;gBAC7D,oHAAoH;gBACpHL,iBAAiB,CAACK,IAAI,CAAC;;oBAAiC,uBAAuB;iBAAC,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QACD,OAAOL,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAMM,mBAAmB,GAAgC;QACvD,mFAAmF;QACnF,wEAAwE;QACxE,2CAA2C;QAC3CP,GAAG,EAAE;YAAC,SAAS;YAAE,QAAQ;YAAE,MAAM;SAAC;KACnC,AAAC;IAEF,IAAIQ,eAAe,GACjBxB,sBAAsB,IAAI,CAACD,CAAAA,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAE0B,KAAK,CAAA,IAAI1B,CAAAA,QAAQ,QAAS,GAAjBA,KAAAA,CAAiB,GAAjBA,QAAQ,CAAE2B,OAAO,CAAA,IAAI,IAAI,CAAC,GACpEC,yBAAwB,yBAAA,CAACnD,IAAI,CAACmD,yBAAwB,yBAAA,EAAE;QACtDF,KAAK,EAAE1B,QAAQ,CAAC0B,KAAK,IAAI,EAAE;QAC3BC,OAAO,EAAE3B,QAAQ,CAAC2B,OAAO,IAAIvD,MAAM,CAACkD,WAAW;QAC/CO,UAAU,EAAE,CAAC,CAAC7B,QAAQ,CAAC2B,OAAO;KAC/B,CAAC,GACF,IAAI,AAAC;IAEX,0DAA0D;IAC1D,IAAI,CAACxB,WAAW,IAAI2B,IAAAA,YAAa,cAAA,GAAE,EAAE;QACnC,IAAI7B,sBAAsB,EAAE;YAC1B,4EAA4E;YAC5E,yEAAyE;YACzE,uBAAuB;YACvB,MAAM8B,aAAa,GAAG,IAAIC,aAAY,aAAA,CAAC5D,MAAM,CAACkD,WAAW,EAAE;gBACzD,iBAAiB;gBACjB,iBAAiB;aAClB,CAAC,AAAC;YACHS,aAAa,CAACE,cAAc,CAAC,IAAM;gBACjChE,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBACjCiE,IAAAA,kBAAsB,uBAAA,EAAC9D,MAAM,CAACkD,WAAW,CAAC,CAACa,IAAI,CAAC,CAACC,aAAa,GAAK;oBACjE,IAAIA,CAAAA,aAAa,QAAO,GAApBA,KAAAA,CAAoB,GAApBA,aAAa,CAAEV,KAAK,CAAA,IAAI,CAAC,CAACW,MAAM,CAACC,IAAI,CAACF,aAAa,CAACV,KAAK,CAAC,CAACa,MAAM,EAAE;wBACrEtE,KAAK,CAAC,sCAAsC,CAAC,CAAC;wBAC9CwD,eAAe,GAAGG,yBAAwB,yBAAA,CAACnD,IAAI,CAACmD,yBAAwB,yBAAA,EAAE;4BACxEF,KAAK,EAAEU,aAAa,CAACV,KAAK,IAAI,EAAE;4BAChCC,OAAO,EAAES,aAAa,CAACT,OAAO,IAAIvD,MAAM,CAACkD,WAAW;4BACpDO,UAAU,EAAE,CAAC,CAACO,aAAa,CAACT,OAAO;yBACpC,CAAC,CAAC;oBACL,OAAO;wBACL1D,KAAK,CAAC,uCAAuC,CAAC,CAAC;wBAC/CwD,eAAe,GAAG,IAAI,CAAC;oBACzB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,yDAAyD;YACzDe,IAAAA,KAAgB,iBAAA,EAAC,IAAM;gBACrBT,aAAa,CAACU,aAAa,EAAE,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,OAAO;YACLxE,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,IAAIyB,sBAAsB,GAAoB,IAAI,AAAC;IAEnD,SAASgD,iBAAiB,CACxB,EAAEC,cAAc,CAAA,EAAE,GAAGC,OAAO,EAAqB,EACjD7D,QAAuB,EACvB;QACA,OAAO,SAAS8D,SAAS,CAACC,UAAkB,EAAc;YACxD,OAAOpC,QAAQ,CAACkC,OAAO,EAAEE,UAAU,EAAE/D,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC;IACJ,CAAC;IAED,SAASgE,mBAAmB,CAACH,OAA0B,EAAE7D,QAAuB,EAAE;QAChF,MAAM8D,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAE7D,QAAQ,CAAC,AAAC;QACvD,OAAO,SAASiE,eAAe,CAACF,UAAkB,EAAqB;YACrE,IAAI;gBACF,OAAOD,SAAS,CAACC,UAAU,CAAC,CAAC;YAC/B,EAAE,OAAOG,KAAK,EAAE;gBACd,0FAA0F;gBAC1F,2FAA2F;gBAC3F,MAAMC,iBAAiB,GACrBC,IAAAA,YAA0B,2BAAA,EAACF,KAAK,CAAC,IAAIG,IAAAA,YAA0B,2BAAA,EAACH,KAAK,CAAC,AAAC;gBACzE,IAAI,CAACC,iBAAiB,EAAE;oBACtB,MAAMD,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,mDAAmD;IACnD,MAAMI,SAAS,GAAIjF,CAAAA,CAAAA,IAAiB,GAAjBA,MAAM,CAACG,UAAU,SAAuB,GAAxCH,KAAAA,CAAwC,GAAxCA,IAAiB,CAAEkF,qBAAqB,QAAI,GAA5ClF,KAAAA,CAA4C,GAA5CA,IAAiB,CAAEkF,qBAAqB,EAAI,KAC7D,CAAC,CAACC,EAAmB,EAAEX,OAAmD,GACxEW,EAAE,CAAC,AAGa,AAAC;IAErB,MAAMC,sBAAsB,GAAG,IAAM;QACnC,MAAM1E,eAAe,GAAG,CAAC,0BAA0B,CAAC,AAAC;QACrDF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACfd,kBAAkB,CACnB,CAAC;QACF,OAAO;YACLyF,IAAI,EAAE,YAAY;YAClBC,QAAQ,EAAE5E,eAAe;SAC1B,CAAU;IACb,CAAC,AAAC;IAEF,wGAAwG;IACxG,yDAAyD;IACzD,MAAM6E,SAAS,GAGT;QACJ;YACEC,KAAK,EAAE,CAAChB,OAA0B,EAAEE,UAAkB,GAAK;oBAKlCF,GAA6B,EAKhDA,IAA6B;gBATjC,IACE,4DAA4D;gBAC5DA,OAAO,CAACiB,qBAAqB,CAACC,SAAS,IACvC,qDAAqD;gBACrD,CAACC,IAAAA,aAAmB,oBAAA,EAACnB,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,CAAC,EAChE;oBACA,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAIpB,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEoB,WAAW,CAAA,KAAK,cAAc,EAAE;oBACjE,+GAA+G;oBAC/G,OAAO,0PAA0PvE,IAAI,CACnQqD,UAAU,CACX,CAAC;gBACJ,CAAC;gBAED,mDAAmD;gBACnD,IAAImB,OAAO,CAAClF,QAAQ,KAAK,OAAO,EAAE;oBAChC,OAAO,gCAAgCU,IAAI,CAACqD,UAAU,CAAC,CAAC;gBAC1D,CAAC;gBAED,0GAA0G;gBAC1G,4CAA4C;gBAC5C,OAAO,8dAA8drD,IAAI,CACveqD,UAAU,CACX,CAAC;YACJ,CAAC;YACD1D,OAAO,EAAE,MAAM;SAChB;QACD,+GAA+G;QAC/G;YACEwE,KAAK,EAAE,CAAChB,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;oBAK5D6D,GAA6B;gBAJnD,IACE,4DAA4D;gBAC5DA,OAAO,CAACiB,qBAAqB,CAACC,SAAS,IACvC,oDAAoD;gBACpDC,IAAAA,aAAmB,oBAAA,EAACnB,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,CAAC,IAC/D,oCAAoC;gBACpC,CAACpB,OAAO,CAACiB,qBAAqB,CAACK,cAAc,EAC7C;oBACA,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,uDAAuD;gBACvD,IAAIpB,UAAU,CAACqB,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACxC,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,MAAMC,UAAU,GACd,mIAAmI3E,IAAI,CACrIqD,UAAU,CACX,IACD,iBAAiB;gBACjB,gDAAgDrD,IAAI,CAACqD,UAAU,CAAC,AAAC;gBAEnE,OAAOsB,UAAU,CAAC;YACpB,CAAC;YACDhF,OAAO,EAAE,MAAM;SAChB;KACF,AAAC;IAEF,MAAMiF,6BAA6B,GAAGC,IAAAA,mBAAkB,mBAAA,EAAClG,MAAM,EAAE;QAC/D,oDAAoD;QACpD,CAACwE,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;YAC3E,gGAAgG;YAChG,IAAI,CAAC6D,OAAO,CAAC2B,GAAG,EAAE,OAAO,IAAI,CAAC;YAE9B,IACE,gCAAgC;YAChC,CAACxF,QAAQ,KAAK,KAAK,IACjB6D,OAAO,CAAC4B,gBAAgB,CAACZ,KAAK,2CAA2C,IACzEd,UAAU,CAACc,KAAK,+CAA+C,CAAC,IAClE,kCAAkC;YAClC,CAACd,UAAU,CAACc,KAAK,6BAA6B,IAC5C,uDAAuD;YACvDhB,OAAO,CAAC4B,gBAAgB,CAACZ,KAAK,sDAAsD,CAAC,EACvF;gBACA3F,KAAK,CAAC,CAAC,4BAA4B,EAAE6E,UAAU,CAAC,CAAC,CAAC,CAAC;gBACnD,gFAAgF;gBAChF,0GAA0G;gBAC1G,sFAAsF;gBACtF,0GAA0G;gBAC1G,gIAAgI;gBAChI,gHAAgH;gBAChH,OAAO;oBACLW,IAAI,EAAE,OAAO;iBACd,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,iBAAiB;QACjB,CAACb,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;YAC3E,OACE0C,CAAAA,eAAe,QAMd,GANDA,KAAAA,CAMC,GANDA,eAAe,CACb;gBACE+C,gBAAgB,EAAE5B,OAAO,CAAC4B,gBAAgB;gBAC1C1B,UAAU;aACX,EACDC,mBAAmB,CAACH,OAAO,EAAE7D,QAAQ,CAAC,CACvC,KAAI,IAAI,CACT;QACJ,CAAC;QAED,4BAA4B;QAC5B,CAAC6D,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;gBAEzE6D,GAA6B,EAC7BA,IAA6B;YAF/B,MAAM6B,QAAQ,GACZ7B,CAAAA,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,CAAA,KAAK,MAAM,IACrDpB,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEoB,WAAW,CAAA,KAAK,cAAc,AAAC;YAEhE,MAAMU,QAAQ,GAAGC,IAAAA,UAAc,eAAA,EAAC7B,UAAU,CAAC,AAAC;YAC5C,IAAI,CAAC4B,QAAQ,EAAE;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,6GAA6G;YAC7G,wDAAwD;YACxD,CAACD,QAAQ,EACT;gBACA,8FAA8F;gBAC9F,oDAAoD;gBACpD,MAAMG,MAAM,GAAG7B,mBAAmB,CAACH,OAAO,EAAE7D,QAAQ,CAAC,CAAC+D,UAAU,CAAC,AAAC;gBAElE,IAAI,CAAC8B,MAAM,IAAI7F,QAAQ,KAAK,KAAK,EAAE;oBACjC,gFAAgF;oBAChF,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,OACE6F,MAAM,IAAI;oBACR,sDAAsD;oBACtDnB,IAAI,EAAE,OAAO;iBACd,CACD;YACJ,CAAC;YACD,MAAMoB,QAAQ,GAAG,CAAC,wCAAwC,EAAEH,QAAQ,CAAC,GAAG,CAAC,AAAC;YAC1EzG,KAAK,CAAC,CAAC,sBAAsB,EAAEyG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM5F,eAAe,GAAG,CAAC,OAAO,EAAE4F,QAAQ,CAAC,CAAC,AAAC;YAC7C9F,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACf+F,QAAQ,CACT,CAAC;YACF,OAAO;gBACLpB,IAAI,EAAE,YAAY;gBAClBC,QAAQ,EAAE5E,eAAe;aAC1B,CAAC;QACJ,CAAC;QAED,2BAA2B;QAC3B,CAAC8D,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;gBAKvD6D,GAA6B;YAJjD,uDAAuD;YACvD,IAAIE,UAAU,CAACqB,QAAQ,CAAC,eAAe,CAAC,EAAE;gBACxC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAMH,WAAW,GAAGpB,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,AAAC;YAE/D,MAAMc,aAAa,GAAGpC,iBAAiB,CAACE,OAAO,EAAE7D,QAAQ,CAAC,AAAC;YAE3D,KAAK,MAAMgG,QAAQ,IAAIpB,SAAS,CAAE;gBAChC,IAAIoB,QAAQ,CAACnB,KAAK,CAAChB,OAAO,EAAEE,UAAU,EAAE/D,QAAQ,CAAC,EAAE;oBACjD,IAAIgG,QAAQ,CAAC3F,OAAO,KAAK,OAAO,EAAE;wBAChCnB,KAAK,CAAC,CAAC,sBAAsB,EAAE6E,UAAU,CAAC,MAAM,EAAEiC,QAAQ,CAAC3F,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvE,OAAO;4BACLqE,IAAI,EAAEsB,QAAQ,CAAC3F,OAAO;yBACvB,CAAC;oBACJ,OAAO,IAAI2F,QAAQ,CAAC3F,OAAO,KAAK,MAAM,EAAE;wBACtC,sGAAsG;wBACtG,MAAM4F,UAAU,GAAGF,aAAa,CAAChC,UAAU,CAAC,AAAC;wBAC7C,MAAMmC,QAAQ,GAAGD,UAAU,CAACvB,IAAI,KAAK,YAAY,GAAGuB,UAAU,CAACtB,QAAQ,GAAGZ,UAAU,AAAC;wBACrF,MAAMoC,QAAQ,GAAG7B,SAAS,CAAC4B,QAAQ,EAAE;4BACnClG,QAAQ,EAAEA,QAAQ;4BAClBiF,WAAW;yBACZ,CAAC,AAAC;wBAEH,MAAMa,QAAQ,GACZ,OAAOK,QAAQ,KAAK,QAAQ,GACxB,CAAC,iBAAiB,EAAEpC,UAAU,CAAC,MAAM,EAAEoC,QAAQ,CAAC,CAAC,CAAC,GAClD,CAAC,iBAAiB,EAAEpC,UAAU,CAAC,MAAM,EAAEqC,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,AAAC;wBACzE,gGAAgG;wBAChG,wDAAwD;wBACxD,MAAMpG,eAAe,GAAG,CAAC,OAAO,EAAEoG,QAAQ,CAAC,CAAC,AAAC;wBAC7CjH,KAAK,CAAC,sBAAsB,EAAE6E,UAAU,EAAE,IAAI,EAAEhE,eAAe,CAAC,CAAC;wBACjEF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACf+F,QAAQ,CACT,CAAC;wBACF,OAAO;4BACLpB,IAAI,EAAE,YAAY;4BAClBC,QAAQ,EAAE5E,eAAe;yBAC1B,CAAC;oBACJ,OAAO,IAAIiG,QAAQ,CAAC3F,OAAO,KAAK,MAAM,EAAE;wBACtC,MAAMyF,SAAQ,GAAG,CAAC,mCAAmC,EAAE/B,UAAU,CAAC,EAAE,CAAC,AAAC;wBACtE,MAAMhE,gBAAe,GAAG,CAAC,OAAO,EAAEgE,UAAU,CAAC,CAAC,AAAC;wBAC/C7E,KAAK,CAAC,gCAAgC,EAAE6E,UAAU,EAAE,IAAI,EAAEhE,gBAAe,CAAC,CAAC;wBAC3EF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,gBAAe,EACf+F,SAAQ,CACT,CAAC;wBACF,OAAO;4BACLpB,IAAI,EAAE,YAAY;4BAClBC,QAAQ,EAAE5E,gBAAe;yBAC1B,CAAC;oBACJ,OAAO;wBACL,MAAM,IAAIuG,OAAY,aAAA,CACpB,CAAC,8BAA8B,EAAEN,QAAQ,CAAC3F,OAAO,CAAC,cAAc,EAAE0D,UAAU,CAAC,aAAa,EAAE/D,QAAQ,CAAC,oBAAoB,EAAE6D,OAAO,CAAC4B,gBAAgB,CAAC,CAAC,CAAC,CACvJ,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,CAAC5B,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;YAC3E,qEAAqE;YACrE,yDAAyD;YACzD,IAAIA,QAAQ,IAAIA,QAAQ,IAAIiC,OAAO,IAAIA,OAAO,CAACjC,QAAQ,CAAC,CAAC+D,UAAU,CAAC,EAAE;gBACpE,MAAMwC,oBAAoB,GAAGtE,OAAO,CAACjC,QAAQ,CAAC,CAAC+D,UAAU,CAAC,AAAC;gBAC3D,OAAOJ,iBAAiB,CAACE,OAAO,EAAE7D,QAAQ,CAAC,CAACuG,oBAAoB,CAAC,CAAC;YACpE,CAAC;YAED,KAAK,MAAM,CAACC,OAAO,EAAEC,KAAK,CAAC,IAAIrE,mBAAmB,EAAE,CAAE;gBACpD,MAAMyC,KAAK,GAAGd,UAAU,CAACc,KAAK,CAAC2B,OAAO,CAAC,AAAC;gBACxC,IAAI3B,KAAK,EAAE;oBACT,MAAM6B,aAAa,GAAGD,KAAK,CAACpG,OAAO,aAEjC,CAACsG,CAAC,EAAE7F,KAAK,GAAK+D,KAAK,CAAC+B,QAAQ,CAAC9F,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAC/C,AAAC;oBACF,MAAMgD,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAE7D,QAAQ,CAAC,AAAC;oBACvDd,KAAK,CAAC,CAAC,OAAO,EAAE6E,UAAU,CAAC,MAAM,EAAE2C,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,OAAO5C,SAAS,CAAC4C,aAAa,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,8BAA8B;QAC9B,CAAC7C,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;YAC3E,IAAI,oDAAoDU,IAAI,CAACqD,UAAU,CAAC,EAAE;gBACxE,OAAOU,sBAAsB,EAAE,CAAC;YAClC,CAAC;YAED,IACEzE,QAAQ,KAAK,KAAK,IAClB6D,OAAO,CAAC4B,gBAAgB,CAACZ,KAAK,0CAA0C,IACxEd,UAAU,CAAC8C,QAAQ,CAAC,wBAAwB,CAAC,EAC7C;gBACA,OAAOpC,sBAAsB,EAAE,CAAC;YAClC,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,oCAAoC;QACpC,CAACZ,OAA0B,EAAEE,UAAkB,EAAE/D,QAAuB,GAAK;YAC3E,MAAM8D,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAE7D,QAAQ,CAAC,AAAC;YAEvD,MAAM6F,MAAM,GAAG/B,SAAS,CAACC,UAAU,CAAC,AAAC;YAErC,IAAI8B,MAAM,CAACnB,IAAI,KAAK,YAAY,EAAE;gBAChC,OAAOmB,MAAM,CAAC;YAChB,CAAC;YAED,IAAI7F,QAAQ,KAAK,KAAK,EAAE;gBACtB,IAAI6F,MAAM,CAAClB,QAAQ,CAACkC,QAAQ,CAAC,cAAc,CAAC,EAAE;oBAC5C,wDAAwD;oBACxD,IAAI9C,UAAU,CAAC8C,QAAQ,CAAC,wDAAwD,CAAC,EAAE;wBACjF,MAAM,IAAIC,wBAAwB,yBAAA,CAChC,CAAC,8BAA8B,EAAE/C,UAAU,CAAC,eAAe,EAAEF,OAAO,CAAC4B,gBAAgB,CAAC,CAAC,CACxF,CAAC;oBACJ,CAAC;oBAED,4BAA4B;oBAE5B,MAAMsB,UAAU,GAAG5G,gBAAgB,CAAC0F,MAAM,CAAClB,QAAQ,CAAC,AAClD,sDAAsD;qBACrDtE,OAAO,qBAAqB,EAAE,CAAC,AAAC;oBAEnC,MAAM2G,QAAQ,GAAGC,IAAAA,UAAuB,wBAAA,EAACF,UAAU,CAAC,AAAC;oBACrD,IAAIC,QAAQ,EAAE;wBACZ,MAAME,SAAS,GAAG,CAAC,OAAO,EAAEH,UAAU,CAAC,CAAC,AAAC;wBACzC,MAAMI,OAAO,GAAGtH,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,AAAC;wBACrE,IAAI,CAAC6H,OAAO,CAACC,gBAAgB,CAACF,SAAS,CAAC,EAAE;4BACxCC,OAAO,CAACrH,gBAAgB,CAACoH,SAAS,EAAEG,GAAE,EAAA,QAAA,CAACC,YAAY,CAACN,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;wBACzE,CAAC;wBACD9H,KAAK,CAAC,CAAC,oBAAoB,EAAE2G,MAAM,CAAClB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;wBAEzD,OAAO;4BACL,GAAGkB,MAAM;4BACTlB,QAAQ,EAAEuC,SAAS;yBACpB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,OAAO;oBAEHrD,GAA6B,EAC7BA,IAA6B;gBAF/B,MAAM6B,QAAQ,GACZ7B,CAAAA,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,CAAA,KAAK,MAAM,IACrDpB,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEoB,WAAW,CAAA,KAAK,cAAc,AAAC;gBAEhE,6CAA6C;gBAC7C,MAAMsC,MAAM,GAAGpH,gBAAgB,CAAC0F,MAAM,CAAClB,QAAQ,CAAC,AAAC;gBAEjD,0EAA0E;gBAC1E,IAAIe,QAAQ,EAAE;oBACZ,IAAI6B,MAAM,CAACnC,QAAQ,CAAC,+CAA+C,CAAC,EAAE;wBACpEoC,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC,CAAC;wBACjF,OAAO;4BACL/C,IAAI,EAAE,OAAO;yBACd,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,2FAA2F;gBAC3F,4EAA4E;gBAC5E,IAAIrD,oBAAoB,IAAIwE,MAAM,CAAClB,QAAQ,CAACkC,QAAQ,CAAC,cAAc,CAAC,EAAE;oBACpE,MAAME,WAAU,GAAG5G,gBAAgB,CAAC0F,MAAM,CAAClB,QAAQ,CAAC,AAClD,sDAAsD;qBACrDtE,OAAO,qBAAqB,EAAE,CAAC,AAAC;oBAEnC,MAAMqH,UAAU,GAAGC,IAAAA,UAAyB,0BAAA,EAACZ,WAAU,CAAC,AAAC;oBACzD,IAAIW,UAAU,EAAE;wBACdxI,KAAK,CAAC,CAAC,iCAAiC,EAAE2G,MAAM,CAAClB,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;wBAC9E,OAAO;4BACL,GAAGkB,MAAM;4BACTlB,QAAQ,EAAE+C,UAAU;yBACrB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO7B,MAAM,CAAC;QAChB,CAAC;KACF,CAAC,AAAC;IAEH,qGAAqG;IACrG,MAAM+B,4BAA4B,GAAGC,IAAAA,mBAA+B,gCAAA,EAClEvC,6BAA6B,EAC7B,CACEwC,gBAAyC,EACzC/D,UAAkB,EAClB/D,QAAuB,GACK;YAiBJ6D,GAA6B;QAhBrD,MAAMA,OAAO,GAAqC;YAChD,GAAGiE,gBAAgB;YACnBC,oBAAoB,EAAE/H,QAAQ,KAAK,KAAK;SACzC,AAAC;QAEF,qEAAqE;QACrE,IACEqB,oBAAoB,IACpB,sFAAsF;QACtF,8CAA8CX,IAAI,CAACqD,UAAU,CAAC,EAC9D;YACAF,OAAO,CAACmE,gBAAgB,GAAG;gBACzBC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC/I,OAAO,CAACc,OAAO,CAAC,wBAAwB,CAAC,EAAE,uBAAuB,CAAC;aAC9E,CAAC;QACJ,CAAC;QAED,IAAI+E,IAAAA,aAAmB,oBAAA,EAACnB,CAAAA,GAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEoB,WAAW,CAAC,EAAE;gBAWjEpB,IAA6B,EAyB3BA,IAA6B;YAnCjC,qFAAqF;YACrF,IAAIlD,sBAAsB,KAAK,IAAI,EAAE;gBACnCA,sBAAsB,GAAG9B,mBAAmB,CAACgF,OAAO,CAACsE,UAAU,CAAC,CAAC;YACnE,CAAC;YACDtE,OAAO,CAACsE,UAAU,GAAGxH,sBAAsB,CAAC;YAE5CkD,OAAO,CAACuE,6BAA6B,GAAG,IAAI,CAAC;YAC7CvE,OAAO,CAACwE,6BAA6B,GAAG,EAAE,CAAC;YAE3C,MAAMC,uBAAuB,GAC3BzE,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEoB,WAAW,CAAA,KAAK,cAAc,AAAC;YAEhE,IAAIqD,uBAAuB,EAAE;gBAC3B,uIAAuI;gBACvI,qGAAqG;gBACrG,IAAItI,QAAQ,KAAK,KAAK,EAAE;oBACtB,gEAAgE;oBAChE,yEAAyE;oBACzE6D,OAAO,CAAC0E,UAAU,GAAG;wBAAC,QAAQ;wBAAE,MAAM;qBAAC,CAAC;gBAC1C,OAAO;oBACL,qDAAqD;oBACrD1E,OAAO,CAAC0E,UAAU,GAAG;wBAAC,cAAc;wBAAE,QAAQ;wBAAE,MAAM;qBAAC,CAAC;gBAC1D,CAAC;YACH,OAAO;gBACL,IAAIvI,QAAQ,KAAK,KAAK,EAAE;oBACtB,gEAAgE;oBAChE,yEAAyE;oBACzE6D,OAAO,CAAC0E,UAAU,GAAG;wBAAC,MAAM;wBAAE,QAAQ;qBAAC,CAAC;gBAC1C,OAAO;oBACL,qDAAqD;oBACrD1E,OAAO,CAAC0E,UAAU,GAAG;wBAAC,cAAc;wBAAE,MAAM;wBAAE,QAAQ;qBAAC,CAAC;gBAC1D,CAAC;YACH,CAAC;YAED,yCAAyC;YACzC,IAAI1E,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACiB,qBAAqB,SAAa,GAA1CjB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEoB,WAAW,CAAA,KAAK,cAAc,EAAE;gBACjEpB,OAAO,CAAC2E,uBAAuB,GAAG;oBAChC,MAAM;oBACN,QAAQ;oBACR,SAAS;oBACT,cAAc;oBACd,SAAS;iBACV,CAAC;YACJ,OAAO;gBACL3E,OAAO,CAAC2E,uBAAuB,GAAG;oBAAC,MAAM;oBAAE,SAAS;iBAAC,CAAC;YACxD,CAAC;QACH,OAAO;YACL,qBAAqB;YAErB,IAAI,CAACC,IAAG,IAAA,CAACC,iCAAiC,IAAI1I,QAAQ,IAAIA,QAAQ,IAAIyC,mBAAmB,EAAE;gBACzFoB,OAAO,CAAC0E,UAAU,GAAG9F,mBAAmB,CAACzC,QAAQ,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO6D,OAAO,CAAC;IACjB,CAAC,CACF,AAAC;IAEF,OAAO8E,IAAAA,mBAA+B,gCAAA,EAACf,4BAA4B,CAAC,CAAC;AACvE,CAAC;AAGM,SAAS7I,iBAAiB,CAC/B6J,KAGC,EACDnC,KAA2C,EAClC;QAGPmC,GAAY,EACLA,IAAY;IAHrB,OACEA,KAAK,CAAC5I,QAAQ,KAAKyG,KAAK,CAACzG,QAAQ,IACjC4I,CAAAA,CAAAA,GAAY,GAAZA,KAAK,CAAC/C,MAAM,SAAM,GAAlB+C,KAAAA,CAAkB,GAAlBA,GAAY,CAAElE,IAAI,CAAA,KAAK,YAAY,IACnC,OAAOkE,CAAAA,CAAAA,IAAY,GAAZA,KAAK,CAAC/C,MAAM,SAAU,GAAtB+C,KAAAA,CAAsB,GAAtBA,IAAY,CAAEjE,QAAQ,CAAA,KAAK,QAAQ,IAC1CxE,gBAAgB,CAACyI,KAAK,CAAC/C,MAAM,CAAClB,QAAQ,CAAC,CAACS,QAAQ,CAACqB,KAAK,CAACoC,MAAM,CAAC,CAC9D;AACJ,CAAC;AAGM,eAAe7J,2BAA2B,CAC/CuD,WAAmB,EACnB,EACElD,MAAM,CAAA,EACNyJ,GAAG,CAAA,EACHC,gBAAgB,CAAA,EAChB7H,sBAAsB,CAAA,EACtBC,qBAAqB,CAAA,EACrBC,WAAW,CAAA,EACXC,oBAAoB,CAAA,EACpB2H,sBAAsB,CAAA,EACtB1H,8BAA8B,CAAA,EAC9BhC,eAAe,CAAA,EAYhB,EACD;IACA,IAAI0J,sBAAsB,EAAE;QAC1B9J,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAC3C,mFAAmF;QACnFC,OAAO,CAAC,oCAAoC,CAAC,CAAC8J,YAAY,GAAG9J,OAAO,CAACc,OAAO,CAC1E,uCAAuC,CACxC,CAAC;IACJ,CAAC;IAED,IAAI,CAACZ,MAAM,CAACkD,WAAW,EAAE;QACvB,oCAAoC;QACpClD,MAAM,CAACkD,WAAW,GAAGA,WAAW,CAAC;IACnC,CAAC;IAED,sEAAsE;IACtE2C,OAAO,CAACuD,GAAG,CAACS,wBAAwB,GAAGhE,OAAO,CAACuD,GAAG,CAACS,wBAAwB,IAAI3G,WAAW,CAAC;IAE3F,0FAA0F;IAC1F,IAAI,CAAC4G,aAAa,CAACC,SAAS,EAAE7G,WAAW,CAAC,EAAE;QAC1C,IAAI,CAAClD,MAAM,CAACgK,YAAY,EAAE;YACxB,6CAA6C;YAC7ChK,MAAM,CAACgK,YAAY,GAAG,EAAE,CAAC;QAC3B,CAAC;QACD,6CAA6C;QAC7ChK,MAAM,CAACgK,YAAY,CAAC7G,IAAI,CAACyF,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC/I,OAAO,CAACc,OAAO,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5F,IAAIoB,oBAAoB,EAAE;YACxB,6CAA6C;YAC7ChC,MAAM,CAACgK,YAAY,CAAC7G,IAAI,CAACyF,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC/I,OAAO,CAACc,OAAO,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,oBAAoB;IACpB,2FAA2F;IAC3FZ,MAAM,CAACiK,WAAW,CAACC,eAAe,GAAGlH,YAAW,EAAA,QAAA,CAACC,MAAM,CAACC,WAAW,EAAE,aAAa,CAAC,CAAC;IAEpF,IAAItB,QAAQ,GAAyB,IAAI,AAAC;IAE1C,IAAIC,sBAAsB,EAAE;QAC1BD,QAAQ,GAAG,MAAMkC,IAAAA,kBAAsB,uBAAA,EAACZ,WAAW,CAAC,CAAC;IACvD,CAAC;IAED,IAAIiH,mBAAmB,GAAGlG,MAAM,CAACmG,OAAO,CAACV,gBAAgB,CAAC,CACvDvI,MAAM,CACL,CAAC,CAACR,QAAQ,EAAEmH,OAAO,CAAC;YAA4B2B,GAAa;QAApC3B,OAAAA,OAAO,KAAK,OAAO,KAAI2B,CAAAA,GAAa,GAAbA,GAAG,CAACY,SAAS,SAAU,GAAvBZ,KAAAA,CAAuB,GAAvBA,GAAa,CAAEjC,QAAQ,CAAC7G,QAAQ,CAAa,CAAA,CAAA;KAAA,CAC9F,CACA2J,GAAG,CAAC,CAAC,CAAC3J,QAAQ,CAAC,GAAKA,QAAQ,CAAC,AAAC;IAEjC,IAAI+B,KAAK,CAACC,OAAO,CAAC3C,MAAM,CAACsC,QAAQ,CAAC+H,SAAS,CAAC,EAAE;QAC5CF,mBAAmB,GAAG;eAAI,IAAII,GAAG,CAACJ,mBAAmB,CAACK,MAAM,CAACxK,MAAM,CAACsC,QAAQ,CAAC+H,SAAS,CAAC,CAAC;SAAC,CAAC;IAC5F,CAAC;IAED,yCAAyC;IACzCrK,MAAM,CAACsC,QAAQ,CAAC+H,SAAS,GAAGF,mBAAmB,CAAC;IAEhDnK,MAAM,GAAGD,gBAAgB,CAACC,MAAM,EAAE;QAAEC,eAAe;KAAE,CAAC,CAAC;IAEvD,OAAOR,oBAAoB,CAACO,MAAM,EAAE;QAClC4B,QAAQ;QACRG,WAAW;QACXF,sBAAsB;QACtBC,qBAAqB;QACrBE,oBAAoB;QACpBC,8BAA8B;QAC9BhC,eAAe;KAChB,CAAC,CAAC;AACL,CAAC;AAED,SAAS6J,aAAa,CAACW,UAAkB,EAAEC,QAAgB,EAAE;IAC3D,OAAOD,UAAU,CAACE,UAAU,CAACD,QAAQ,CAAC,IAAID,UAAU,CAACtG,MAAM,IAAIuG,QAAQ,CAACvG,MAAM,CAAC;AACjF,CAAC"}