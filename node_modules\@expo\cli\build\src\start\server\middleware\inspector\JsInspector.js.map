{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/JsInspector.ts"], "sourcesContent": ["import type { CustomMessageHandlerConnection } from '@react-native/dev-middleware';\nimport chalk from 'chalk';\n\nimport { evaluateJsFromCdpAsync } from './CdpClient';\nimport { selectAsync } from '../../../../utils/prompts';\nimport { pageIsSupported } from '../../metro/debugging/pageIsSupported';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:inspector:jsInspector'\n) as typeof console.log;\n\nexport interface MetroInspectorProxyApp {\n  /** Unique device ID combined with the page ID */\n  id: string;\n  /** Information about the underlying CDP implementation, e.g. \"React Native Bridgeless [C++ connection]\" */\n  title: string;\n  /** The application ID that is currently running on the device, e.g. \"dev.expo.bareexpo\" */\n  description: string;\n  /** The CDP debugger type, which should always be \"node\" */\n  type: 'node';\n  /** The internal `devtools://..` URL for the debugger to connect to */\n  devtoolsFrontendUrl: string;\n  /** The websocket URL for the debugger to connect to */\n  webSocketDebuggerUrl: string;\n  /**\n   * Human-readable device name\n   * @since react-native@0.73\n   */\n  deviceName: string;\n  /**\n   * React Native specific information, like the unique device ID and native capabilities\n   * @since react-native@0.74\n   */\n  reactNative?: {\n    /** The unique device ID */\n    logicalDeviceId: string;\n    /** All supported native capabilities */\n    capabilities: CustomMessageHandlerConnection['page']['capabilities'];\n  };\n}\n\n/**\n * Launch the React Native DevTools by executing the `POST /open-debugger` request.\n * This endpoint is handled through `@react-native/dev-middleware`.\n */\nexport async function openJsInspector(metroBaseUrl: string, app: MetroInspectorProxyApp) {\n  if (!app.reactNative?.logicalDeviceId) {\n    debug('Failed to open React Native DevTools, target is missing device ID');\n    return false;\n  }\n\n  const url = new URL('/open-debugger', metroBaseUrl);\n  url.searchParams.set('appId', app.description);\n  url.searchParams.set('device', app.reactNative.logicalDeviceId);\n  url.searchParams.set('target', app.id);\n\n  // Request to open the React Native DevTools, but limit it to 1s\n  // This is a workaround as this endpoint might not respond on some devices\n  const response = await fetch(url, {\n    method: 'POST',\n    signal: AbortSignal.timeout(1000),\n  }).catch((error) => {\n    // Only swallow timeout errors\n    if (error.name === 'TimeoutError') {\n      return null;\n    }\n\n    throw error;\n  });\n\n  if (!response) {\n    debug(`No response received from the React Native DevTools.`);\n  } else if (response.ok === false) {\n    debug('Failed to open React Native DevTools, received response:', response.status);\n  }\n\n  return response?.ok ?? true;\n}\n\nexport async function queryInspectorAppAsync(\n  metroServerOrigin: string,\n  appId: string\n): Promise<MetroInspectorProxyApp | null> {\n  const apps = await queryAllInspectorAppsAsync(metroServerOrigin);\n  return apps.find((app) => app.description === appId) ?? null;\n}\n\nexport async function queryAllInspectorAppsAsync(\n  metroServerOrigin: string\n): Promise<MetroInspectorProxyApp[]> {\n  const resp = await fetch(`${metroServerOrigin}/json/list`);\n  const apps: MetroInspectorProxyApp[] = transformApps(await resp.json());\n  const results: MetroInspectorProxyApp[] = [];\n  for (const app of apps) {\n    // Only use targets with better reloading support\n    if (!pageIsSupported(app)) {\n      continue;\n    }\n    // Hide targets that are marked as hidden from the inspector, e.g. instances from expo-dev-menu and expo-dev-launcher.\n    if (await appShouldBeIgnoredAsync(app)) {\n      continue;\n    }\n    results.push(app);\n  }\n  return results;\n}\n\nexport async function promptInspectorAppAsync(apps: MetroInspectorProxyApp[]) {\n  if (apps.length === 1) {\n    return apps[0];\n  }\n\n  // Check if multiple devices are connected with the same device names\n  // In this case, append the actual app id (device ID + page number) to the prompt\n  const hasDuplicateNames = apps.some(\n    (app, index) => index !== apps.findIndex((other) => app.deviceName === other.deviceName)\n  );\n\n  const choices = apps.map((app) => {\n    const name = app.deviceName ?? 'Unknown device';\n    return {\n      title: hasDuplicateNames ? chalk`${name}{dim  - ${app.id}}` : name,\n      value: app.id,\n      app,\n    };\n  });\n\n  const value = await selectAsync(chalk`Debug target {dim (Hermes only)}`, choices);\n\n  return choices.find((item) => item.value === value)?.app;\n}\n\n// The description of `React Native Experimental (Improved Chrome Reloads)` target is `don't use` from metro.\n// This function tries to transform the unmeaningful description to appId\nfunction transformApps(apps: MetroInspectorProxyApp[]): MetroInspectorProxyApp[] {\n  const deviceIdToAppId: Record<string, string> = {};\n\n  for (const app of apps) {\n    if (app.description !== \"don't use\") {\n      const deviceId = app.reactNative?.logicalDeviceId ?? app.id.split('-')[0];\n      const appId = app.description;\n      deviceIdToAppId[deviceId] = appId;\n    }\n  }\n\n  return apps.map((app) => {\n    if (app.description === \"don't use\") {\n      const deviceId = app.reactNative?.logicalDeviceId ?? app.id.split('-')[0];\n      app.description = deviceIdToAppId[deviceId] ?? app.description;\n    }\n    return app;\n  });\n}\n\nconst HIDE_FROM_INSPECTOR_ENV = 'globalThis.__expo_hide_from_inspector__';\n\nasync function appShouldBeIgnoredAsync(app: MetroInspectorProxyApp): Promise<boolean> {\n  const hideFromInspector = await evaluateJsFromCdpAsync(\n    app.webSocketDebuggerUrl,\n    HIDE_FROM_INSPECTOR_ENV\n  );\n  debug(\n    `[appShouldBeIgnoredAsync] webSocketDebuggerUrl[${app.webSocketDebuggerUrl}] hideFromInspector[${hideFromInspector}]`\n  );\n  return hideFromInspector !== undefined;\n}\n"], "names": ["openJsInspector", "queryInspectorAppAsync", "queryAllInspectorAppsAsync", "promptInspectorAppAsync", "debug", "require", "metroBaseUrl", "app", "reactNative", "logicalDeviceId", "url", "URL", "searchParams", "set", "description", "id", "response", "fetch", "method", "signal", "AbortSignal", "timeout", "catch", "error", "name", "ok", "status", "metroServerOrigin", "appId", "apps", "find", "resp", "transformApps", "json", "results", "pageIsSupported", "appShouldBeIgnoredAsync", "push", "choices", "length", "hasDuplicateNames", "some", "index", "findIndex", "other", "deviceName", "map", "title", "chalk", "value", "selectAsync", "item", "deviceIdToAppId", "deviceId", "split", "HIDE_FROM_INSPECTOR_ENV", "hideFromInspector", "evaluateJsFromCdpAsync", "webSocketDebuggerUrl", "undefined"], "mappings": "AAAA;;;;;;;;;;;IA6CsBA,eAAe,MAAfA,eAAe;IAkCfC,sBAAsB,MAAtBA,sBAAsB;IAQtBC,0BAA0B,MAA1BA,0BAA0B;IAoB1BC,uBAAuB,MAAvBA,uBAAuB;;;8DA1G3B,OAAO;;;;;;2BAEc,aAAa;yBACxB,2BAA2B;iCACvB,uCAAuC;;;;;;AAEvE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,oDAAoD,CACrD,AAAsB,AAAC;AAoCjB,eAAeL,eAAe,CAACM,YAAoB,EAAEC,GAA2B,EAAE;QAClFA,GAAe;IAApB,IAAI,CAACA,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAiB,GAAhCD,KAAAA,CAAgC,GAAhCA,GAAe,CAAEE,eAAe,CAAA,EAAE;QACrCL,KAAK,CAAC,mEAAmE,CAAC,CAAC;QAC3E,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAMM,GAAG,GAAG,IAAIC,GAAG,CAAC,gBAAgB,EAAEL,YAAY,CAAC,AAAC;IACpDI,GAAG,CAACE,YAAY,CAACC,GAAG,CAAC,OAAO,EAAEN,GAAG,CAACO,WAAW,CAAC,CAAC;IAC/CJ,GAAG,CAACE,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAEN,GAAG,CAACC,WAAW,CAACC,eAAe,CAAC,CAAC;IAChEC,GAAG,CAACE,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAEN,GAAG,CAACQ,EAAE,CAAC,CAAC;IAEvC,gEAAgE;IAChE,0EAA0E;IAC1E,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACP,GAAG,EAAE;QAChCQ,MAAM,EAAE,MAAM;QACdC,MAAM,EAAEC,WAAW,CAACC,OAAO,CAAC,IAAI,CAAC;KAClC,CAAC,CAACC,KAAK,CAAC,CAACC,KAAK,GAAK;QAClB,8BAA8B;QAC9B,IAAIA,KAAK,CAACC,IAAI,KAAK,cAAc,EAAE;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAMD,KAAK,CAAC;IACd,CAAC,CAAC,AAAC;IAEH,IAAI,CAACP,QAAQ,EAAE;QACbZ,KAAK,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC;IAChE,OAAO,IAAIY,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;QAChCrB,KAAK,CAAC,0DAA0D,EAAEY,QAAQ,CAACU,MAAM,CAAC,CAAC;IACrF,CAAC;IAED,OAAOV,CAAAA,QAAQ,QAAI,GAAZA,KAAAA,CAAY,GAAZA,QAAQ,CAAES,EAAE,CAAA,IAAI,IAAI,CAAC;AAC9B,CAAC;AAEM,eAAexB,sBAAsB,CAC1C0B,iBAAyB,EACzBC,KAAa,EAC2B;IACxC,MAAMC,IAAI,GAAG,MAAM3B,0BAA0B,CAACyB,iBAAiB,CAAC,AAAC;IACjE,OAAOE,IAAI,CAACC,IAAI,CAAC,CAACvB,GAAG,GAAKA,GAAG,CAACO,WAAW,KAAKc,KAAK,CAAC,IAAI,IAAI,CAAC;AAC/D,CAAC;AAEM,eAAe1B,0BAA0B,CAC9CyB,iBAAyB,EACU;IACnC,MAAMI,IAAI,GAAG,MAAMd,KAAK,CAAC,CAAC,EAAEU,iBAAiB,CAAC,UAAU,CAAC,CAAC,AAAC;IAC3D,MAAME,IAAI,GAA6BG,aAAa,CAAC,MAAMD,IAAI,CAACE,IAAI,EAAE,CAAC,AAAC;IACxE,MAAMC,OAAO,GAA6B,EAAE,AAAC;IAC7C,KAAK,MAAM3B,GAAG,IAAIsB,IAAI,CAAE;QACtB,iDAAiD;QACjD,IAAI,CAACM,IAAAA,gBAAe,gBAAA,EAAC5B,GAAG,CAAC,EAAE;YACzB,SAAS;QACX,CAAC;QACD,sHAAsH;QACtH,IAAI,MAAM6B,uBAAuB,CAAC7B,GAAG,CAAC,EAAE;YACtC,SAAS;QACX,CAAC;QACD2B,OAAO,CAACG,IAAI,CAAC9B,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,OAAO2B,OAAO,CAAC;AACjB,CAAC;AAEM,eAAe/B,uBAAuB,CAAC0B,IAA8B,EAAE;QAsBrES,GAA4C;IArBnD,IAAIT,IAAI,CAACU,MAAM,KAAK,CAAC,EAAE;QACrB,OAAOV,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC;IAED,qEAAqE;IACrE,iFAAiF;IACjF,MAAMW,iBAAiB,GAAGX,IAAI,CAACY,IAAI,CACjC,CAAClC,GAAG,EAAEmC,KAAK,GAAKA,KAAK,KAAKb,IAAI,CAACc,SAAS,CAAC,CAACC,KAAK,GAAKrC,GAAG,CAACsC,UAAU,KAAKD,KAAK,CAACC,UAAU,CAAC,CACzF,AAAC;IAEF,MAAMP,OAAO,GAAGT,IAAI,CAACiB,GAAG,CAAC,CAACvC,GAAG,GAAK;QAChC,MAAMiB,IAAI,GAAGjB,GAAG,CAACsC,UAAU,IAAI,gBAAgB,AAAC;QAChD,OAAO;YACLE,KAAK,EAAEP,iBAAiB,GAAGQ,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,EAAExB,IAAI,CAAC,QAAQ,EAAEjB,GAAG,CAACQ,EAAE,CAAC,CAAC,CAAC,GAAGS,IAAI;YAClEyB,KAAK,EAAE1C,GAAG,CAACQ,EAAE;YACbR,GAAG;SACJ,CAAC;IACJ,CAAC,CAAC,AAAC;IAEH,MAAM0C,KAAK,GAAG,MAAMC,IAAAA,QAAW,YAAA,EAACF,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,gCAAgC,CAAC,EAAEV,OAAO,CAAC,AAAC;IAElF,OAAOA,CAAAA,GAA4C,GAA5CA,OAAO,CAACR,IAAI,CAAC,CAACqB,IAAI,GAAKA,IAAI,CAACF,KAAK,KAAKA,KAAK,CAAC,SAAK,GAAjDX,KAAAA,CAAiD,GAAjDA,GAA4C,CAAE/B,GAAG,CAAC;AAC3D,CAAC;AAED,6GAA6G;AAC7G,yEAAyE;AACzE,SAASyB,aAAa,CAACH,IAA8B,EAA4B;IAC/E,MAAMuB,eAAe,GAA2B,EAAE,AAAC;IAEnD,KAAK,MAAM7C,GAAG,IAAIsB,IAAI,CAAE;QACtB,IAAItB,GAAG,CAACO,WAAW,KAAK,WAAW,EAAE;gBAClBP,GAAe;YAAhC,MAAM8C,QAAQ,GAAG9C,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAiB,GAAhCD,KAAAA,CAAgC,GAAhCA,GAAe,CAAEE,eAAe,CAAA,IAAIF,GAAG,CAACQ,EAAE,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,AAAC;YAC1E,MAAM1B,KAAK,GAAGrB,GAAG,CAACO,WAAW,AAAC;YAC9BsC,eAAe,CAACC,QAAQ,CAAC,GAAGzB,KAAK,CAAC;QACpC,CAAC;IACH,CAAC;IAED,OAAOC,IAAI,CAACiB,GAAG,CAAC,CAACvC,GAAG,GAAK;QACvB,IAAIA,GAAG,CAACO,WAAW,KAAK,WAAW,EAAE;gBAClBP,GAAe;YAAhC,MAAM8C,QAAQ,GAAG9C,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAiB,GAAhCD,KAAAA,CAAgC,GAAhCA,GAAe,CAAEE,eAAe,CAAA,IAAIF,GAAG,CAACQ,EAAE,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,AAAC;YAC1E/C,GAAG,CAACO,WAAW,GAAGsC,eAAe,CAACC,QAAQ,CAAC,IAAI9C,GAAG,CAACO,WAAW,CAAC;QACjE,CAAC;QACD,OAAOP,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAMgD,uBAAuB,GAAG,yCAAyC,AAAC;AAE1E,eAAenB,uBAAuB,CAAC7B,GAA2B,EAAoB;IACpF,MAAMiD,iBAAiB,GAAG,MAAMC,IAAAA,UAAsB,uBAAA,EACpDlD,GAAG,CAACmD,oBAAoB,EACxBH,uBAAuB,CACxB,AAAC;IACFnD,KAAK,CACH,CAAC,+CAA+C,EAAEG,GAAG,CAACmD,oBAAoB,CAAC,oBAAoB,EAAEF,iBAAiB,CAAC,CAAC,CAAC,CACtH,CAAC;IACF,OAAOA,iBAAiB,KAAKG,SAAS,CAAC;AACzC,CAAC"}