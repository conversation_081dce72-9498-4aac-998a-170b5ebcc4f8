{"version": 3, "sources": ["../../../src/utils/git.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { env } from './env';\nimport { isInteractive } from './interactive';\nimport { confirmAsync } from './prompts';\nimport * as Log from '../log';\n\nexport async function maybeBailOnGitStatusAsync(): Promise<boolean> {\n  if (env.EXPO_NO_GIT_STATUS) {\n    Log.warn(\n      'Git status is dirty but the command will continue because EXPO_NO_GIT_STATUS is enabled...'\n    );\n    return false;\n  }\n  const isGitStatusClean = await validateGitStatusAsync();\n\n  // Give people a chance to bail out if git working tree is dirty\n  if (!isGitStatusClean) {\n    if (!isInteractive()) {\n      Log.warn(\n        `Git status is dirty but the command will continue because the terminal is not interactive.`\n      );\n      return false;\n    }\n\n    Log.log();\n    const answer = await confirmAsync({\n      message: `Continue with uncommited changes?`,\n    });\n\n    if (!answer) {\n      return true;\n    }\n\n    Log.log();\n  }\n  return false;\n}\n\nexport async function validateGitStatusAsync(): Promise<boolean> {\n  let workingTreeStatus = 'unknown';\n  try {\n    const result = await spawnAsync('git', ['status', '--porcelain']);\n    workingTreeStatus = result.stdout === '' ? 'clean' : 'dirty';\n  } catch {\n    // Maybe git is not installed?\n    // Maybe this project is not using git?\n  }\n\n  if (workingTreeStatus === 'clean') {\n    return true;\n  } else if (workingTreeStatus === 'dirty') {\n    logWarning(\n      'Git branch has uncommited file changes',\n      `It's recommended to commit all changes before proceeding in case you want to revert generated changes.`\n    );\n  } else {\n    logWarning(\n      'No git repo found in current directory',\n      `Use git to track file changes before running commands that modify project files.`\n    );\n  }\n\n  return false;\n}\n\nfunction logWarning(warning: string, hint: string) {\n  Log.warn(chalk.bold`! ` + warning);\n  Log.log(chalk.gray`\\u203A ` + chalk.gray(hint));\n}\n"], "names": ["maybeBailOnGitStatusAsync", "validateGitStatusAsync", "env", "EXPO_NO_GIT_STATUS", "Log", "warn", "isGitStatusClean", "isInteractive", "log", "answer", "<PERSON><PERSON><PERSON>", "message", "workingTreeStatus", "result", "spawnAsync", "stdout", "logWarning", "warning", "hint", "chalk", "bold", "gray"], "mappings": "AAAA;;;;;;;;;;;IAQsBA,yBAAyB,MAAzBA,yBAAyB;IAgCzBC,sBAAsB,MAAtBA,sBAAsB;;;8DAxCrB,mBAAmB;;;;;;;8DACxB,OAAO;;;;;;qBAEL,OAAO;6BACG,eAAe;yBAChB,WAAW;2DACnB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,eAAeD,yBAAyB,GAAqB;IAClE,IAAIE,IAAG,IAAA,CAACC,kBAAkB,EAAE;QAC1BC,IAAG,CAACC,IAAI,CACN,4FAA4F,CAC7F,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAMC,gBAAgB,GAAG,MAAML,sBAAsB,EAAE,AAAC;IAExD,gEAAgE;IAChE,IAAI,CAACK,gBAAgB,EAAE;QACrB,IAAI,CAACC,IAAAA,YAAa,cAAA,GAAE,EAAE;YACpBH,IAAG,CAACC,IAAI,CACN,CAAC,0FAA0F,CAAC,CAC7F,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAEDD,IAAG,CAACI,GAAG,EAAE,CAAC;QACV,MAAMC,MAAM,GAAG,MAAMC,IAAAA,QAAY,aAAA,EAAC;YAChCC,OAAO,EAAE,CAAC,iCAAiC,CAAC;SAC7C,CAAC,AAAC;QAEH,IAAI,CAACF,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAEDL,IAAG,CAACI,GAAG,EAAE,CAAC;IACZ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,eAAeP,sBAAsB,GAAqB;IAC/D,IAAIW,iBAAiB,GAAG,SAAS,AAAC;IAClC,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,KAAK,EAAE;YAAC,QAAQ;YAAE,aAAa;SAAC,CAAC,AAAC;QAClEF,iBAAiB,GAAGC,MAAM,CAACE,MAAM,KAAK,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC;IAC/D,EAAE,OAAM;IACN,8BAA8B;IAC9B,uCAAuC;IACzC,CAAC;IAED,IAAIH,iBAAiB,KAAK,OAAO,EAAE;QACjC,OAAO,IAAI,CAAC;IACd,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;QACxCI,UAAU,CACR,wCAAwC,EACxC,CAAC,sGAAsG,CAAC,CACzG,CAAC;IACJ,OAAO;QACLA,UAAU,CACR,wCAAwC,EACxC,CAAC,gFAAgF,CAAC,CACnF,CAAC;IACJ,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAASA,UAAU,CAACC,OAAe,EAAEC,IAAY,EAAE;IACjDd,IAAG,CAACC,IAAI,CAACc,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,EAAE,CAAC,GAAGH,OAAO,CAAC,CAAC;IACnCb,IAAG,CAACI,GAAG,CAACW,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,OAAO,CAAC,GAAGF,MAAK,EAAA,QAAA,CAACE,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC;AAClD,CAAC"}