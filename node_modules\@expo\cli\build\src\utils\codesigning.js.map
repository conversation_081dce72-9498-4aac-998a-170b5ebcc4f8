{"version": 3, "sources": ["../../../src/utils/codesigning.ts"], "sourcesContent": ["import { GraphQLError } from '@0no-co/graphql.web';\nimport {\n  convertCertificatePEMToCertificate,\n  convertKeyPairToPEM,\n  convertCSRToCSRPEM,\n  generateKeyPair,\n  generateCSR,\n  convertPrivateKeyPEMToPrivateKey,\n  validateSelfSignedCertificate,\n  signBufferRSASHA256AndVerify,\n} from '@expo/code-signing-certificates';\nimport { ExpoConfig } from '@expo/config';\nimport { getExpoHomeDirectory } from '@expo/config/build/getUserState';\nimport JsonFile, { JSONObject } from '@expo/json-file';\nimport { CombinedError } from '@urql/core';\nimport { promises as fs } from 'fs';\nimport { pki as PKI } from 'node-forge';\nimport path from 'path';\nimport { Dictionary, parseDictionary } from 'structured-headers';\n\nimport { env } from './env';\nimport { CommandError } from './errors';\nimport { getExpoGoIntermediateCertificateAsync } from '../api/getExpoGoIntermediateCertificate';\nimport { getProjectDevelopmentCertificateAsync } from '../api/getProjectDevelopmentCertificate';\nimport { AppQuery } from '../api/graphql/queries/AppQuery';\nimport { ensureLoggedInAsync } from '../api/user/actions';\nimport { Actor } from '../api/user/user';\nimport { AppByIdQuery, Permission } from '../graphql/generated';\nimport * as Log from '../log';\nimport { learnMore } from '../utils/link';\n\nconst debug = require('debug')('expo:codesigning') as typeof console.log;\n\nexport type CodeSigningInfo = {\n  keyId: string;\n  privateKey: string;\n  certificateForPrivateKey: string;\n  /**\n   * Chain of certificates to serve in the manifest multipart body \"certificate_chain\" part.\n   * The leaf certificate must be the 0th element of the array, followed by any intermediate certificates\n   * necessary to evaluate the chain of trust ending in the implicitly trusted root certificate embedded in\n   * the client.\n   *\n   * An empty array indicates that there is no need to serve the certificate chain in the multipart response.\n   */\n  certificateChainForResponse: string[];\n  /**\n   * Scope key cached for the project when certificate is development Expo Go code signing.\n   * For project-specific code signing (keyId == the project's generated keyId) this is undefined.\n   */\n  scopeKey: string | null;\n};\n\ntype StoredDevelopmentExpoRootCodeSigningInfo = {\n  easProjectId: string | null;\n  scopeKey: string | null;\n  privateKey: string | null;\n  certificateChain: string[] | null;\n};\nconst DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME = 'development-code-signing-settings-2.json';\n\nexport function getDevelopmentCodeSigningDirectory(): string {\n  return path.join(getExpoHomeDirectory(), 'codesigning');\n}\n\nfunction getProjectDevelopmentCodeSigningInfoFile<T extends JSONObject>(defaults: T) {\n  function getFile(easProjectId: string): JsonFile<T> {\n    const filePath = path.join(\n      getDevelopmentCodeSigningDirectory(),\n      easProjectId,\n      DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME\n    );\n    return new JsonFile<T>(filePath);\n  }\n\n  async function readAsync(easProjectId: string): Promise<T> {\n    let projectSettings;\n    try {\n      projectSettings = await getFile(easProjectId).readAsync();\n    } catch {\n      projectSettings = await getFile(easProjectId).writeAsync(defaults, { ensureDir: true });\n    }\n    // Set defaults for any missing fields\n    return { ...defaults, ...projectSettings };\n  }\n\n  async function setAsync(easProjectId: string, json: Partial<T>): Promise<T> {\n    try {\n      return await getFile(easProjectId).mergeAsync(json, {\n        cantReadFileDefault: defaults,\n      });\n    } catch {\n      return await getFile(easProjectId).writeAsync(\n        {\n          ...defaults,\n          ...json,\n        },\n        { ensureDir: true }\n      );\n    }\n  }\n\n  return {\n    getFile,\n    readAsync,\n    setAsync,\n  };\n}\n\nexport const DevelopmentCodeSigningInfoFile =\n  getProjectDevelopmentCodeSigningInfoFile<StoredDevelopmentExpoRootCodeSigningInfo>({\n    easProjectId: null,\n    scopeKey: null,\n    privateKey: null,\n    certificateChain: null,\n  });\n\n/**\n * Get info necessary to generate a response `expo-signature` header given a project and incoming request `expo-expect-signature` header.\n * This only knows how to serve two code signing keyids:\n * - `expo-root` indicates that it should use a development certificate in the `expo-root` chain. See {@link getExpoRootDevelopmentCodeSigningInfoAsync}\n * - <developer's expo-updates keyid> indicates that it should sign with the configured certificate. See {@link getProjectCodeSigningCertificateAsync}\n */\nexport async function getCodeSigningInfoAsync(\n  exp: ExpoConfig,\n  expectSignatureHeader: string | null,\n  privateKeyPath: string | undefined\n): Promise<CodeSigningInfo | null> {\n  if (!expectSignatureHeader) {\n    return null;\n  }\n\n  let parsedExpectSignature: Dictionary;\n  try {\n    parsedExpectSignature = parseDictionary(expectSignatureHeader);\n  } catch {\n    throw new CommandError('Invalid value for expo-expect-signature header');\n  }\n\n  const expectedKeyIdOuter = parsedExpectSignature.get('keyid');\n  if (!expectedKeyIdOuter) {\n    throw new CommandError('keyid not present in expo-expect-signature header');\n  }\n\n  const expectedKeyId = expectedKeyIdOuter[0];\n  if (typeof expectedKeyId !== 'string') {\n    throw new CommandError(\n      `Invalid value for keyid in expo-expect-signature header: ${expectedKeyId}`\n    );\n  }\n\n  let expectedAlg: string | null = null;\n  const expectedAlgOuter = parsedExpectSignature.get('alg');\n  if (expectedAlgOuter) {\n    const expectedAlgTemp = expectedAlgOuter[0];\n    if (typeof expectedAlgTemp !== 'string') {\n      throw new CommandError('Invalid value for alg in expo-expect-signature header');\n    }\n    expectedAlg = expectedAlgTemp;\n  }\n\n  if (expectedKeyId === 'expo-root') {\n    return await getExpoRootDevelopmentCodeSigningInfoAsync(exp);\n  } else if (expectedKeyId === 'expo-go') {\n    throw new CommandError(\n      'Invalid certificate requested: cannot sign with embedded keyid=expo-go key'\n    );\n  } else {\n    return await getProjectCodeSigningCertificateAsync(\n      exp,\n      privateKeyPath,\n      expectedKeyId,\n      expectedAlg\n    );\n  }\n}\n\n/**\n * Get a development code signing certificate for the expo-root -> expo-go -> (development certificate) certificate chain.\n * This requires the user be logged in and online, otherwise try to use the cached development certificate.\n */\nasync function getExpoRootDevelopmentCodeSigningInfoAsync(\n  exp: ExpoConfig\n): Promise<CodeSigningInfo | null> {\n  const easProjectId = exp.extra?.eas?.projectId;\n  // can't check for scope key validity since scope key is derived on the server from projectId and we may be offline.\n  // we rely upon the client certificate check to validate the scope key\n  if (!easProjectId) {\n    debug(\n      `WARN: Expo Application Services (EAS) is not configured for your project. Configuring EAS enables a more secure development experience amongst many other benefits. ${learnMore(\n        'https://docs.expo.dev/eas/'\n      )}`\n    );\n    return null;\n  }\n\n  const developmentCodeSigningInfoFromFile =\n    await DevelopmentCodeSigningInfoFile.readAsync(easProjectId);\n  const validatedCodeSigningInfo = validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n    developmentCodeSigningInfoFromFile,\n    easProjectId\n  );\n\n  // 1. If online, ensure logged in, generate key pair and CSR, fetch and cache certificate chain for projectId\n  //    (overwriting existing dev cert in case projectId changed or it has expired)\n  if (!env.EXPO_OFFLINE) {\n    try {\n      return await fetchAndCacheNewDevelopmentCodeSigningInfoAsync(easProjectId);\n    } catch (e: any) {\n      if (validatedCodeSigningInfo) {\n        Log.warn(\n          'There was an error fetching the Expo development certificate, falling back to cached certificate'\n        );\n        return validatedCodeSigningInfo;\n      } else {\n        // need to return null here and say a message\n        throw e;\n      }\n    }\n  }\n\n  // 2. check for cached cert/private key matching projectId and scopeKey of project, if found and valid return private key and cert chain including expo-go cert\n  if (validatedCodeSigningInfo) {\n    return validatedCodeSigningInfo;\n  }\n\n  // 3. if offline, return null\n  Log.warn('Offline and no cached development certificate found, unable to sign manifest');\n  return null;\n}\n\n/**\n * Get the certificate configured for expo-updates for this project.\n */\nasync function getProjectCodeSigningCertificateAsync(\n  exp: ExpoConfig,\n  privateKeyPath: string | undefined,\n  expectedKeyId: string,\n  expectedAlg: string | null\n): Promise<CodeSigningInfo | null> {\n  const codeSigningCertificatePath = exp.updates?.codeSigningCertificate;\n  if (!codeSigningCertificatePath) {\n    return null;\n  }\n\n  if (!privateKeyPath) {\n    throw new CommandError(\n      'Must specify --private-key-path argument to sign development manifest for requested code signing key'\n    );\n  }\n\n  const codeSigningMetadata = exp.updates?.codeSigningMetadata;\n  if (!codeSigningMetadata) {\n    throw new CommandError(\n      'Must specify \"codeSigningMetadata\" under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  const { alg, keyid } = codeSigningMetadata;\n  if (!alg || !keyid) {\n    throw new CommandError(\n      'Must specify \"keyid\" and \"alg\" in the \"codeSigningMetadata\" field under the \"updates\" field of your app config file to use EAS code signing'\n    );\n  }\n\n  if (expectedKeyId !== keyid) {\n    throw new CommandError(`keyid mismatch: client=${expectedKeyId}, project=${keyid}`);\n  }\n\n  if (expectedAlg && expectedAlg !== alg) {\n    throw new CommandError(`\"alg\" field mismatch (client=${expectedAlg}, project=${alg})`);\n  }\n\n  const { privateKeyPEM, certificatePEM } =\n    await getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n      codeSigningCertificatePath,\n      privateKeyPath,\n    });\n\n  return {\n    keyId: keyid,\n    privateKey: privateKeyPEM,\n    certificateForPrivateKey: certificatePEM,\n    certificateChainForResponse: [],\n    scopeKey: null,\n  };\n}\n\nasync function readFileWithErrorAsync(path: string, errorMessage: string): Promise<string> {\n  try {\n    return await fs.readFile(path, 'utf8');\n  } catch {\n    throw new CommandError(errorMessage);\n  }\n}\n\nasync function getProjectPrivateKeyAndCertificateFromFilePathsAsync({\n  codeSigningCertificatePath,\n  privateKeyPath,\n}: {\n  codeSigningCertificatePath: string;\n  privateKeyPath: string;\n}): Promise<{ privateKeyPEM: string; certificatePEM: string }> {\n  const [codeSigningCertificatePEM, privateKeyPEM] = await Promise.all([\n    readFileWithErrorAsync(\n      codeSigningCertificatePath,\n      `Code signing certificate cannot be read from path: ${codeSigningCertificatePath}`\n    ),\n    readFileWithErrorAsync(\n      privateKeyPath,\n      `Code signing private key cannot be read from path: ${privateKeyPath}`\n    ),\n  ]);\n\n  const privateKey = convertPrivateKeyPEMToPrivateKey(privateKeyPEM);\n  const certificate = convertCertificatePEMToCertificate(codeSigningCertificatePEM);\n  validateSelfSignedCertificate(certificate, {\n    publicKey: certificate.publicKey as PKI.rsa.PublicKey,\n    privateKey,\n  });\n\n  return { privateKeyPEM, certificatePEM: codeSigningCertificatePEM };\n}\n\n/**\n * Validate that the cached code signing info is still valid for the current project and\n * that it hasn't expired. If invalid, return null.\n */\nfunction validateStoredDevelopmentExpoRootCertificateCodeSigningInfo(\n  codeSigningInfo: StoredDevelopmentExpoRootCodeSigningInfo,\n  easProjectId: string\n): CodeSigningInfo | null {\n  if (codeSigningInfo.easProjectId !== easProjectId) {\n    return null;\n  }\n\n  const {\n    privateKey: privateKeyPEM,\n    certificateChain: certificatePEMs,\n    scopeKey,\n  } = codeSigningInfo;\n  if (!privateKeyPEM || !certificatePEMs) {\n    return null;\n  }\n\n  const certificateChain = certificatePEMs.map((certificatePEM) =>\n    convertCertificatePEMToCertificate(certificatePEM)\n  );\n\n  // TODO(wschurman): maybe move to @expo/code-signing-certificates\n\n  // ensure all intermediate certificates are valid\n  for (const certificate of certificateChain) {\n    const now = new Date();\n    if (certificate.validity.notBefore > now || certificate.validity.notAfter < now) {\n      return null;\n    }\n  }\n\n  // TODO(wschurman): maybe do more validation, like validation of projectID and scopeKey within eas certificate extension\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: certificatePEMs,\n    certificateForPrivateKey: certificatePEMs[0],\n    privateKey: privateKeyPEM,\n    scopeKey,\n  };\n}\n\nfunction actorCanGetProjectDevelopmentCertificate(actor: Actor, app: AppByIdQuery['app']['byId']) {\n  const owningAccountId = app.ownerAccount.id;\n\n  const owningAccountIsActorPrimaryAccount =\n    actor.__typename === 'User' || actor.__typename === 'SSOUser'\n      ? actor.primaryAccount.id === owningAccountId\n      : false;\n  const userHasPublishPermissionForOwningAccount = !!actor.accounts\n    .find((account) => account.id === owningAccountId)\n    ?.users?.find((userPermission) => userPermission.actor.id === actor.id)\n    ?.permissions?.includes(Permission.Publish);\n  return owningAccountIsActorPrimaryAccount || userHasPublishPermissionForOwningAccount;\n}\n\nasync function fetchAndCacheNewDevelopmentCodeSigningInfoAsync(\n  easProjectId: string\n): Promise<CodeSigningInfo | null> {\n  const actor = await ensureLoggedInAsync();\n  let app: AppByIdQuery['app']['byId'];\n  try {\n    app = await AppQuery.byIdAsync(easProjectId);\n  } catch (e) {\n    if (e instanceof GraphQLError || e instanceof CombinedError) {\n      return null;\n    }\n    throw e;\n  }\n  if (!actorCanGetProjectDevelopmentCertificate(actor, app)) {\n    return null;\n  }\n\n  const keyPair = generateKeyPair();\n  const keyPairPEM = convertKeyPairToPEM(keyPair);\n  const csr = generateCSR(keyPair, `Development Certificate for ${easProjectId}`);\n  const csrPEM = convertCSRToCSRPEM(csr);\n  const [developmentSigningCertificate, expoGoIntermediateCertificate] = await Promise.all([\n    getProjectDevelopmentCertificateAsync(easProjectId, csrPEM),\n    getExpoGoIntermediateCertificateAsync(easProjectId),\n  ]);\n\n  await DevelopmentCodeSigningInfoFile.setAsync(easProjectId, {\n    easProjectId,\n    scopeKey: app.scopeKey,\n    privateKey: keyPairPEM.privateKeyPEM,\n    certificateChain: [developmentSigningCertificate, expoGoIntermediateCertificate],\n  });\n\n  return {\n    keyId: 'expo-go',\n    certificateChainForResponse: [developmentSigningCertificate, expoGoIntermediateCertificate],\n    certificateForPrivateKey: developmentSigningCertificate,\n    privateKey: keyPairPEM.privateKeyPEM,\n    scopeKey: app.scopeKey,\n  };\n}\n/**\n * Generate the `expo-signature` header for a manifest and code signing info.\n */\nexport function signManifestString(\n  stringifiedManifest: string,\n  codeSigningInfo: CodeSigningInfo\n): string {\n  const privateKey = convertPrivateKeyPEMToPrivateKey(codeSigningInfo.privateKey);\n  const certificate = convertCertificatePEMToCertificate(codeSigningInfo.certificateForPrivateKey);\n  return signBufferRSASHA256AndVerify(\n    privateKey,\n    certificate,\n    Buffer.from(stringifiedManifest, 'utf8')\n  );\n}\n"], "names": ["getDevelopmentCodeSigningDirectory", "DevelopmentCodeSigningInfoFile", "getCodeSigningInfoAsync", "signManifestString", "debug", "require", "DEVELOPMENT_CODE_SIGNING_SETTINGS_FILE_NAME", "path", "join", "getExpoHomeDirectory", "getProjectDevelopmentCodeSigningInfoFile", "defaults", "getFile", "easProjectId", "filePath", "JsonFile", "readAsync", "projectSettings", "writeAsync", "ensureDir", "setAsync", "json", "mergeAsync", "cantReadFileDefault", "<PERSON><PERSON>ey", "privateKey", "certificate<PERSON>hain", "exp", "expectSignatureHeader", "privateKeyPath", "parsedExpectSignature", "parseDictionary", "CommandError", "expectedKeyIdOuter", "get", "expectedKeyId", "expectedAlg", "expectedAlgOuter", "expectedAlgTemp", "getExpoRootDevelopmentCodeSigningInfoAsync", "getProjectCodeSigningCertificateAsync", "extra", "eas", "projectId", "learnMore", "developmentCodeSigningInfoFromFile", "validatedCodeSigningInfo", "validateStoredDevelopmentExpoRootCertificateCodeSigningInfo", "env", "EXPO_OFFLINE", "fetchAndCacheNewDevelopmentCodeSigningInfoAsync", "e", "Log", "warn", "codeSigningCertificatePath", "updates", "codeSigningCertificate", "codeSigningMetadata", "alg", "keyid", "privateKeyPEM", "certificatePEM", "getProjectPrivateKeyAndCertificateFromFilePathsAsync", "keyId", "certificateForPrivateKey", "certificateChainForResponse", "readFileWithErrorAsync", "errorMessage", "fs", "readFile", "codeSigningCertificatePEM", "Promise", "all", "convertPrivateKeyPEMToPrivateKey", "certificate", "convertCertificatePEMToCertificate", "validateSelfSignedCertificate", "public<PERSON>ey", "codeSigningInfo", "certificatePEMs", "map", "now", "Date", "validity", "notBefore", "notAfter", "actorCanGetProjectDevelopmentCertificate", "actor", "app", "owningAccountId", "ownerAccount", "id", "owningAccountIsActorPrimaryAccount", "__typename", "primaryAccount", "userHasPublishPermissionForOwningAccount", "accounts", "find", "account", "users", "userPermission", "permissions", "includes", "Permission", "Publish", "ensureLoggedInAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "GraphQLError", "CombinedError", "keyPair", "generateKeyPair", "keyPairPEM", "convertKeyPairToPEM", "csr", "generateCSR", "csrPEM", "convertCSRToCSRPEM", "developmentSigningCertificate", "expoGoIntermediateCertificate", "getProjectDevelopmentCertificateAsync", "getExpoGoIntermediateCertificateAsync", "stringifiedManifest", "signBufferRSASHA256AndVerify", "<PERSON><PERSON><PERSON>", "from"], "mappings": "AAAA;;;;;;;;;;;IA6DgBA,kCAAkC,MAAlCA,kCAAkC;IAgDrCC,8BAA8B,MAA9BA,8BAA8B;IAcrBC,uBAAuB,MAAvBA,uBAAuB;IAiT7BC,kBAAkB,MAAlBA,kBAAkB;;;yBA5aL,qBAAqB;;;;;;;yBAU3C,iCAAiC;;;;;;;yBAEH,iCAAiC;;;;;;;8DACjC,iBAAiB;;;;;;;yBACxB,YAAY;;;;;;;yBACX,IAAI;;;;;;;8DAElB,MAAM;;;;;;;yBACqB,oBAAoB;;;;;;qBAE5C,OAAO;wBACE,UAAU;kDACe,yCAAyC;kDACzC,yCAAyC;0BACtE,iCAAiC;yBACtB,qBAAqB;2BAEhB,sBAAsB;2DAC1C,QAAQ;sBACH,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,AAAsB,AAAC;AA4BzE,MAAMC,2CAA2C,GAAG,0CAA0C,AAAC;AAExF,SAASN,kCAAkC,GAAW;IAC3D,OAAOO,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,IAAAA,aAAoB,EAAA,qBAAA,GAAE,EAAE,aAAa,CAAC,CAAC;AAC1D,CAAC;AAED,SAASC,wCAAwC,CAAuBC,QAAW,EAAE;IACnF,SAASC,OAAO,CAACC,YAAoB,EAAe;QAClD,MAAMC,QAAQ,GAAGP,KAAI,EAAA,QAAA,CAACC,IAAI,CACxBR,kCAAkC,EAAE,EACpCa,YAAY,EACZP,2CAA2C,CAC5C,AAAC;QACF,OAAO,IAAIS,CAAAA,SAAQ,EAAA,CAAA,QAAA,CAAID,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED,eAAeE,SAAS,CAACH,YAAoB,EAAc;QACzD,IAAII,eAAe,AAAC;QACpB,IAAI;YACFA,eAAe,GAAG,MAAML,OAAO,CAACC,YAAY,CAAC,CAACG,SAAS,EAAE,CAAC;QAC5D,EAAE,OAAM;YACNC,eAAe,GAAG,MAAML,OAAO,CAACC,YAAY,CAAC,CAACK,UAAU,CAACP,QAAQ,EAAE;gBAAEQ,SAAS,EAAE,IAAI;aAAE,CAAC,CAAC;QAC1F,CAAC;QACD,sCAAsC;QACtC,OAAO;YAAE,GAAGR,QAAQ;YAAE,GAAGM,eAAe;SAAE,CAAC;IAC7C,CAAC;IAED,eAAeG,QAAQ,CAACP,YAAoB,EAAEQ,IAAgB,EAAc;QAC1E,IAAI;YACF,OAAO,MAAMT,OAAO,CAACC,YAAY,CAAC,CAACS,UAAU,CAACD,IAAI,EAAE;gBAClDE,mBAAmB,EAAEZ,QAAQ;aAC9B,CAAC,CAAC;QACL,EAAE,OAAM;YACN,OAAO,MAAMC,OAAO,CAACC,YAAY,CAAC,CAACK,UAAU,CAC3C;gBACE,GAAGP,QAAQ;gBACX,GAAGU,IAAI;aACR,EACD;gBAAEF,SAAS,EAAE,IAAI;aAAE,CACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO;QACLP,OAAO;QACPI,SAAS;QACTI,QAAQ;KACT,CAAC;AACJ,CAAC;AAEM,MAAMnB,8BAA8B,GACzCS,wCAAwC,CAA2C;IACjFG,YAAY,EAAE,IAAI;IAClBW,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,gBAAgB,EAAE,IAAI;CACvB,CAAC,AAAC;AAQE,eAAexB,uBAAuB,CAC3CyB,GAAe,EACfC,qBAAoC,EACpCC,cAAkC,EACD;IACjC,IAAI,CAACD,qBAAqB,EAAE;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAIE,qBAAqB,AAAY,AAAC;IACtC,IAAI;QACFA,qBAAqB,GAAGC,IAAAA,kBAAe,EAAA,gBAAA,EAACH,qBAAqB,CAAC,CAAC;IACjE,EAAE,OAAM;QACN,MAAM,IAAII,OAAY,aAAA,CAAC,gDAAgD,CAAC,CAAC;IAC3E,CAAC;IAED,MAAMC,kBAAkB,GAAGH,qBAAqB,CAACI,GAAG,CAAC,OAAO,CAAC,AAAC;IAC9D,IAAI,CAACD,kBAAkB,EAAE;QACvB,MAAM,IAAID,OAAY,aAAA,CAAC,mDAAmD,CAAC,CAAC;IAC9E,CAAC;IAED,MAAMG,aAAa,GAAGF,kBAAkB,CAAC,CAAC,CAAC,AAAC;IAC5C,IAAI,OAAOE,aAAa,KAAK,QAAQ,EAAE;QACrC,MAAM,IAAIH,OAAY,aAAA,CACpB,CAAC,yDAAyD,EAAEG,aAAa,CAAC,CAAC,CAC5E,CAAC;IACJ,CAAC;IAED,IAAIC,WAAW,GAAkB,IAAI,AAAC;IACtC,MAAMC,gBAAgB,GAAGP,qBAAqB,CAACI,GAAG,CAAC,KAAK,CAAC,AAAC;IAC1D,IAAIG,gBAAgB,EAAE;QACpB,MAAMC,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC,AAAC;QAC5C,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAIN,OAAY,aAAA,CAAC,uDAAuD,CAAC,CAAC;QAClF,CAAC;QACDI,WAAW,GAAGE,eAAe,CAAC;IAChC,CAAC;IAED,IAAIH,aAAa,KAAK,WAAW,EAAE;QACjC,OAAO,MAAMI,0CAA0C,CAACZ,GAAG,CAAC,CAAC;IAC/D,OAAO,IAAIQ,aAAa,KAAK,SAAS,EAAE;QACtC,MAAM,IAAIH,OAAY,aAAA,CACpB,4EAA4E,CAC7E,CAAC;IACJ,OAAO;QACL,OAAO,MAAMQ,qCAAqC,CAChDb,GAAG,EACHE,cAAc,EACdM,aAAa,EACbC,WAAW,CACZ,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;CAGC,GACD,eAAeG,0CAA0C,CACvDZ,GAAe,EACkB;QACZA,GAAS;IAA9B,MAAMd,YAAY,GAAGc,CAAAA,GAAS,GAATA,GAAG,CAACc,KAAK,SAAK,GAAdd,KAAAA,CAAc,GAAdA,QAAAA,GAAS,CAAEe,GAAG,SAAA,GAAdf,KAAAA,CAAc,QAAEgB,SAAS,AAAX,AAAY;IAC/C,oHAAoH;IACpH,sEAAsE;IACtE,IAAI,CAAC9B,YAAY,EAAE;QACjBT,KAAK,CACH,CAAC,oKAAoK,EAAEwC,IAAAA,KAAS,UAAA,EAC9K,4BAA4B,CAC7B,CAAC,CAAC,CACJ,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMC,kCAAkC,GACtC,MAAM5C,8BAA8B,CAACe,SAAS,CAACH,YAAY,CAAC,AAAC;IAC/D,MAAMiC,wBAAwB,GAAGC,2DAA2D,CAC1FF,kCAAkC,EAClChC,YAAY,CACb,AAAC;IAEF,6GAA6G;IAC7G,iFAAiF;IACjF,IAAI,CAACmC,IAAG,IAAA,CAACC,YAAY,EAAE;QACrB,IAAI;YACF,OAAO,MAAMC,+CAA+C,CAACrC,YAAY,CAAC,CAAC;QAC7E,EAAE,OAAOsC,CAAC,EAAO;YACf,IAAIL,wBAAwB,EAAE;gBAC5BM,IAAG,CAACC,IAAI,CACN,kGAAkG,CACnG,CAAC;gBACF,OAAOP,wBAAwB,CAAC;YAClC,OAAO;gBACL,6CAA6C;gBAC7C,MAAMK,CAAC,CAAC;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,+JAA+J;IAC/J,IAAIL,wBAAwB,EAAE;QAC5B,OAAOA,wBAAwB,CAAC;IAClC,CAAC;IAED,6BAA6B;IAC7BM,IAAG,CAACC,IAAI,CAAC,8EAA8E,CAAC,CAAC;IACzF,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;CAEC,GACD,eAAeb,qCAAqC,CAClDb,GAAe,EACfE,cAAkC,EAClCM,aAAqB,EACrBC,WAA0B,EACO;QACET,GAAW,EAWlBA,IAAW;IAXvC,MAAM2B,0BAA0B,GAAG3B,CAAAA,GAAW,GAAXA,GAAG,CAAC4B,OAAO,SAAwB,GAAnC5B,KAAAA,CAAmC,GAAnCA,GAAW,CAAE6B,sBAAsB,AAAC;IACvE,IAAI,CAACF,0BAA0B,EAAE;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAACzB,cAAc,EAAE;QACnB,MAAM,IAAIG,OAAY,aAAA,CACpB,sGAAsG,CACvG,CAAC;IACJ,CAAC;IAED,MAAMyB,mBAAmB,GAAG9B,CAAAA,IAAW,GAAXA,GAAG,CAAC4B,OAAO,SAAqB,GAAhC5B,KAAAA,CAAgC,GAAhCA,IAAW,CAAE8B,mBAAmB,AAAC;IAC7D,IAAI,CAACA,mBAAmB,EAAE;QACxB,MAAM,IAAIzB,OAAY,aAAA,CACpB,8GAA8G,CAC/G,CAAC;IACJ,CAAC;IAED,MAAM,EAAE0B,GAAG,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGF,mBAAmB,AAAC;IAC3C,IAAI,CAACC,GAAG,IAAI,CAACC,KAAK,EAAE;QAClB,MAAM,IAAI3B,OAAY,aAAA,CACpB,6IAA6I,CAC9I,CAAC;IACJ,CAAC;IAED,IAAIG,aAAa,KAAKwB,KAAK,EAAE;QAC3B,MAAM,IAAI3B,OAAY,aAAA,CAAC,CAAC,uBAAuB,EAAEG,aAAa,CAAC,UAAU,EAAEwB,KAAK,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC;IAED,IAAIvB,WAAW,IAAIA,WAAW,KAAKsB,GAAG,EAAE;QACtC,MAAM,IAAI1B,OAAY,aAAA,CAAC,CAAC,6BAA6B,EAAEI,WAAW,CAAC,UAAU,EAAEsB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,MAAM,EAAEE,aAAa,CAAA,EAAEC,cAAc,CAAA,EAAE,GACrC,MAAMC,oDAAoD,CAAC;QACzDR,0BAA0B;QAC1BzB,cAAc;KACf,CAAC,AAAC;IAEL,OAAO;QACLkC,KAAK,EAAEJ,KAAK;QACZlC,UAAU,EAAEmC,aAAa;QACzBI,wBAAwB,EAAEH,cAAc;QACxCI,2BAA2B,EAAE,EAAE;QAC/BzC,QAAQ,EAAE,IAAI;KACf,CAAC;AACJ,CAAC;AAED,eAAe0C,sBAAsB,CAAC3D,IAAY,EAAE4D,YAAoB,EAAmB;IACzF,IAAI;QACF,OAAO,MAAMC,GAAE,EAAA,SAAA,CAACC,QAAQ,CAAC9D,IAAI,EAAE,MAAM,CAAC,CAAC;IACzC,EAAE,OAAM;QACN,MAAM,IAAIyB,OAAY,aAAA,CAACmC,YAAY,CAAC,CAAC;IACvC,CAAC;AACH,CAAC;AAED,eAAeL,oDAAoD,CAAC,EAClER,0BAA0B,CAAA,EAC1BzB,cAAc,CAAA,EAIf,EAA8D;IAC7D,MAAM,CAACyC,yBAAyB,EAAEV,aAAa,CAAC,GAAG,MAAMW,OAAO,CAACC,GAAG,CAAC;QACnEN,sBAAsB,CACpBZ,0BAA0B,EAC1B,CAAC,mDAAmD,EAAEA,0BAA0B,CAAC,CAAC,CACnF;QACDY,sBAAsB,CACpBrC,cAAc,EACd,CAAC,mDAAmD,EAAEA,cAAc,CAAC,CAAC,CACvE;KACF,CAAC,AAAC;IAEH,MAAMJ,UAAU,GAAGgD,IAAAA,wBAAgC,EAAA,iCAAA,EAACb,aAAa,CAAC,AAAC;IACnE,MAAMc,WAAW,GAAGC,IAAAA,wBAAkC,EAAA,mCAAA,EAACL,yBAAyB,CAAC,AAAC;IAClFM,IAAAA,wBAA6B,EAAA,8BAAA,EAACF,WAAW,EAAE;QACzCG,SAAS,EAAEH,WAAW,CAACG,SAAS;QAChCpD,UAAU;KACX,CAAC,CAAC;IAEH,OAAO;QAAEmC,aAAa;QAAEC,cAAc,EAAES,yBAAyB;KAAE,CAAC;AACtE,CAAC;AAED;;;CAGC,GACD,SAASvB,2DAA2D,CAClE+B,eAAyD,EACzDjE,YAAoB,EACI;IACxB,IAAIiE,eAAe,CAACjE,YAAY,KAAKA,YAAY,EAAE;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,EACJY,UAAU,EAAEmC,aAAa,CAAA,EACzBlC,gBAAgB,EAAEqD,eAAe,CAAA,EACjCvD,QAAQ,CAAA,IACT,GAAGsD,eAAe,AAAC;IACpB,IAAI,CAAClB,aAAa,IAAI,CAACmB,eAAe,EAAE;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMrD,gBAAgB,GAAGqD,eAAe,CAACC,GAAG,CAAC,CAACnB,cAAc,GAC1Dc,IAAAA,wBAAkC,EAAA,mCAAA,EAACd,cAAc,CAAC,CACnD,AAAC;IAEF,iEAAiE;IAEjE,iDAAiD;IACjD,KAAK,MAAMa,WAAW,IAAIhD,gBAAgB,CAAE;QAC1C,MAAMuD,GAAG,GAAG,IAAIC,IAAI,EAAE,AAAC;QACvB,IAAIR,WAAW,CAACS,QAAQ,CAACC,SAAS,GAAGH,GAAG,IAAIP,WAAW,CAACS,QAAQ,CAACE,QAAQ,GAAGJ,GAAG,EAAE;YAC/E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,wHAAwH;IAExH,OAAO;QACLlB,KAAK,EAAE,SAAS;QAChBE,2BAA2B,EAAEc,eAAe;QAC5Cf,wBAAwB,EAAEe,eAAe,CAAC,CAAC,CAAC;QAC5CtD,UAAU,EAAEmC,aAAa;QACzBpC,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAAS8D,wCAAwC,CAACC,KAAY,EAAEC,GAAgC,EAAE;QAO7CD,GACC;IAPpD,MAAME,eAAe,GAAGD,GAAG,CAACE,YAAY,CAACC,EAAE,AAAC;IAE5C,MAAMC,kCAAkC,GACtCL,KAAK,CAACM,UAAU,KAAK,MAAM,IAAIN,KAAK,CAACM,UAAU,KAAK,SAAS,GACzDN,KAAK,CAACO,cAAc,CAACH,EAAE,KAAKF,eAAe,GAC3C,KAAK,AAAC;IACZ,MAAMM,wCAAwC,GAAG,CAAC,EAACR,QAAAA,CAAAA,GACC,GADDA,KAAK,CAACS,QAAQ,CAC9DC,IAAI,CAAC,CAACC,OAAO,GAAKA,OAAO,CAACP,EAAE,KAAKF,eAAe,CAAC,SAC3C,GAF0CF,KAAAA,CAE1C,GAF0CA,QAAAA,GACC,CAChDY,KAAK,SAAA,GAF0CZ,KAAAA,CAE1C,GAF0CA,KAExCU,IAAI,CAAC,CAACG,cAAc,GAAKA,cAAc,CAACb,KAAK,CAACI,EAAE,KAAKJ,KAAK,CAACI,EAAE,CAAC,SAC1D,GAHoCJ,KAAAA,CAGpC,GAHoCA,aAG/Cc,WAAW,SAAA,GAHoCd,KAAAA,CAGpC,GAHoCA,KAGlCe,QAAQ,CAACC,UAAU,WAAA,CAACC,OAAO,CAAC,CAAA,AAAC;IAC9C,OAAOZ,kCAAkC,IAAIG,wCAAwC,CAAC;AACxF,CAAC;AAED,eAAe7C,+CAA+C,CAC5DrC,YAAoB,EACa;IACjC,MAAM0E,KAAK,GAAG,MAAMkB,IAAAA,QAAmB,oBAAA,GAAE,AAAC;IAC1C,IAAIjB,GAAG,AAA6B,AAAC;IACrC,IAAI;QACFA,GAAG,GAAG,MAAMkB,SAAQ,SAAA,CAACC,SAAS,CAAC9F,YAAY,CAAC,CAAC;IAC/C,EAAE,OAAOsC,CAAC,EAAE;QACV,IAAIA,CAAC,YAAYyD,WAAY,EAAA,aAAA,IAAIzD,CAAC,YAAY0D,KAAa,EAAA,cAAA,EAAE;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM1D,CAAC,CAAC;IACV,CAAC;IACD,IAAI,CAACmC,wCAAwC,CAACC,KAAK,EAAEC,GAAG,CAAC,EAAE;QACzD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMsB,OAAO,GAAGC,IAAAA,wBAAe,EAAA,gBAAA,GAAE,AAAC;IAClC,MAAMC,UAAU,GAAGC,IAAAA,wBAAmB,EAAA,oBAAA,EAACH,OAAO,CAAC,AAAC;IAChD,MAAMI,GAAG,GAAGC,IAAAA,wBAAW,EAAA,YAAA,EAACL,OAAO,EAAE,CAAC,4BAA4B,EAAEjG,YAAY,CAAC,CAAC,CAAC,AAAC;IAChF,MAAMuG,MAAM,GAAGC,IAAAA,wBAAkB,EAAA,mBAAA,EAACH,GAAG,CAAC,AAAC;IACvC,MAAM,CAACI,6BAA6B,EAAEC,6BAA6B,CAAC,GAAG,MAAMhD,OAAO,CAACC,GAAG,CAAC;QACvFgD,IAAAA,iCAAqC,sCAAA,EAAC3G,YAAY,EAAEuG,MAAM,CAAC;QAC3DK,IAAAA,iCAAqC,sCAAA,EAAC5G,YAAY,CAAC;KACpD,CAAC,AAAC;IAEH,MAAMZ,8BAA8B,CAACmB,QAAQ,CAACP,YAAY,EAAE;QAC1DA,YAAY;QACZW,QAAQ,EAAEgE,GAAG,CAAChE,QAAQ;QACtBC,UAAU,EAAEuF,UAAU,CAACpD,aAAa;QACpClC,gBAAgB,EAAE;YAAC4F,6BAA6B;YAAEC,6BAA6B;SAAC;KACjF,CAAC,CAAC;IAEH,OAAO;QACLxD,KAAK,EAAE,SAAS;QAChBE,2BAA2B,EAAE;YAACqD,6BAA6B;YAAEC,6BAA6B;SAAC;QAC3FvD,wBAAwB,EAAEsD,6BAA6B;QACvD7F,UAAU,EAAEuF,UAAU,CAACpD,aAAa;QACpCpC,QAAQ,EAAEgE,GAAG,CAAChE,QAAQ;KACvB,CAAC;AACJ,CAAC;AAIM,SAASrB,kBAAkB,CAChCuH,mBAA2B,EAC3B5C,eAAgC,EACxB;IACR,MAAMrD,UAAU,GAAGgD,IAAAA,wBAAgC,EAAA,iCAAA,EAACK,eAAe,CAACrD,UAAU,CAAC,AAAC;IAChF,MAAMiD,WAAW,GAAGC,IAAAA,wBAAkC,EAAA,mCAAA,EAACG,eAAe,CAACd,wBAAwB,CAAC,AAAC;IACjG,OAAO2D,IAAAA,wBAA4B,EAAA,6BAAA,EACjClG,UAAU,EACViD,WAAW,EACXkD,MAAM,CAACC,IAAI,CAACH,mBAAmB,EAAE,MAAM,CAAC,CACzC,CAAC;AACJ,CAAC"}