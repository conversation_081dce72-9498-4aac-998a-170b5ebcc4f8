import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity,
  RefreshControl,
  SafeAreaView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { storeService } from '../../services/store';
import { Order, OrderStatus } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Icon from 'react-native-vector-icons/MaterialIcons';

const OrdersScreen = () => {
  const navigation = useNavigation<any>();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await storeService.getOrders();
      setOrders(data);
    } catch (err) {
      setError('Failed to load orders');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return COLORS.accent;
      case OrderStatus.CONFIRMED:
        return '#2196F3';
      case OrderStatus.PREPARING:
        return '#FF9800';
      case OrderStatus.READY_FOR_PICKUP:
        return '#4CAF50';
      case OrderStatus.PICKED_UP:
        return '#9C27B0';
      case OrderStatus.IN_TRANSIT:
        return '#607D8B';
      case OrderStatus.DELIVERED:
        return COLORS.success;
      case OrderStatus.CANCELLED:
        return COLORS.error;
      default:
        return COLORS.secondary;
    }
  };

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return 'schedule';
      case OrderStatus.CONFIRMED:
        return 'check-circle';
      case OrderStatus.PREPARING:
        return 'restaurant';
      case OrderStatus.READY_FOR_PICKUP:
        return 'done-all';
      case OrderStatus.PICKED_UP:
        return 'local-shipping';
      case OrderStatus.IN_TRANSIT:
        return 'directions-car';
      case OrderStatus.DELIVERED:
        return 'check-circle-outline';
      case OrderStatus.CANCELLED:
        return 'cancel';
      default:
        return 'help';
    }
  };

  const handleOrderPress = (order: Order) => {
    navigation.navigate('OrderDetail', { order });
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <Card style={styles.orderCard}>
      <TouchableOpacity 
        style={styles.orderContent}
        onPress={() => handleOrderPress(item)}
      >
        <View style={styles.orderHeader}>
          <Text style={styles.orderId}>Order #{item.id.slice(-6)}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Icon 
              name={getStatusIcon(item.status)} 
              size={16} 
              color={getStatusColor(item.status)} 
            />
            <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
              {item.status.replace('_', ' ')}
            </Text>
          </View>
        </View>

        <View style={styles.orderInfo}>
          <Text style={styles.customerName}>{item.customer.name}</Text>
          <Text style={styles.customerAddress}>{item.customer.address}</Text>
          <Text style={styles.orderDate}>
            {new Date(item.createdAt).toLocaleDateString()} at {new Date(item.createdAt).toLocaleTimeString()}
          </Text>
        </View>

        <View style={styles.orderFooter}>
          <Text style={styles.itemCount}>
            {item.items.length} item{item.items.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.orderTotal}>${item.total.toFixed(2)}</Text>
          <Icon name="chevron-right" size={24} color={COLORS.secondary} />
        </View>
      </TouchableOpacity>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Orders</Text>
        </View>
        
        {error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <Button 
              title="Try Again" 
              onPress={fetchOrders}
              variant="outline"
              size="small"
              style={styles.retryButton}
            />
          </Card>
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={fetchOrders} />
            }
            ListEmptyComponent={
              !isLoading ? (
                <View style={styles.emptyContainer}>
                  <Icon name="receipt" size={48} color={COLORS.secondary + '40'} />
                  <Text style={styles.emptyText}>No orders found</Text>
                </View>
              ) : null
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  header: {
    marginBottom: SPACING.md,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.secondary,
  },
  listContent: {
    paddingBottom: SPACING.lg,
  },
  orderCard: {
    marginBottom: SPACING.sm,
  },
  orderContent: {
    padding: 0,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  orderId: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.md,
    color: COLORS.secondary,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
  },
  statusText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.sm,
    marginLeft: SPACING.xs,
    textTransform: 'capitalize',
  },
  orderInfo: {
    marginBottom: SPACING.sm,
  },
  customerName: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  customerAddress: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.text,
    opacity: 0.7,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    opacity: 0.6,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemCount: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.text,
    opacity: 0.7,
  },
  orderTotal: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.accent,
  },
  errorCard: {
    backgroundColor: COLORS.error + '10',
    borderColor: COLORS.error,
    borderWidth: 1,
    marginBottom: SPACING.md,
  },
  errorText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.error,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  retryButton: {
    alignSelf: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
    marginTop: SPACING.md,
    textAlign: 'center',
  },
});

export default OrdersScreen;
