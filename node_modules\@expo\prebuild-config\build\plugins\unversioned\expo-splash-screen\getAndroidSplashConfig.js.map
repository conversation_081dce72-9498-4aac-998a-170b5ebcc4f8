{"version": 3, "file": "getAndroidSplashConfig.js", "names": ["defaultResizeMode", "getAndroidSplashConfig", "config", "props", "splash", "xxxhdpi", "image", "xxhdpi", "xhdpi", "hdpi", "mdpi", "backgroundColor", "resizeMode", "imageWidth", "dark", "drawable", "android", "getAndroidDarkSplashConfig", "lightTheme"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/getAndroidSplashConfig.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nexport type SplashScreenConfig = {\n  xxxhdpi?: string;\n  xxhdpi?: string;\n  xhdpi?: string;\n  hdpi?: string;\n  mdpi?: string;\n  image?: string;\n  backgroundColor?: string;\n  resizeMode: 'contain' | 'cover' | 'native';\n  drawable?: {\n    icon: string;\n    darkIcon?: string;\n  };\n  dark?: {\n    backgroundColor?: string;\n    xxxhdpi?: string;\n    xxhdpi?: string;\n    xhdpi?: string;\n    hdpi?: string;\n    mdpi?: string;\n    image?: string;\n    resizeMode?: 'contain' | 'cover' | 'native';\n  };\n};\n\nexport type AndroidSplashConfig = {\n  imageWidth?: number;\n} & SplashScreenConfig;\n\nconst defaultResizeMode = 'contain';\n\nexport function getAndroidSplashConfig(\n  config: Pick<ExpoConfig, 'splash' | 'android'>,\n  props?: AndroidSplashConfig | null\n): AndroidSplashConfig | null {\n  // Respect the splash screen object, don't mix and match across different splash screen objects\n  // in case the user wants the top level splash to apply to every platform except android.\n  if (props) {\n    const splash = props;\n    return {\n      xxxhdpi: splash.xxxhdpi ?? splash.image,\n      xxhdpi: splash.xxhdpi ?? splash.image,\n      xhdpi: splash.xhdpi ?? splash.image,\n      hdpi: splash.hdpi ?? splash.image,\n      mdpi: splash.mdpi ?? splash.image,\n      backgroundColor: splash.backgroundColor,\n      resizeMode: splash.resizeMode ?? defaultResizeMode,\n      image: splash.image,\n      imageWidth: splash.imageWidth ?? 100,\n      dark: splash.dark,\n      drawable: splash.drawable,\n    };\n  }\n\n  if (config.android?.splash) {\n    const splash = config.android?.splash;\n    return {\n      xxxhdpi: splash.xxxhdpi ?? splash.image,\n      xxhdpi: splash.xxhdpi ?? splash.image,\n      xhdpi: splash.xhdpi ?? splash.image,\n      hdpi: splash.hdpi ?? splash.image,\n      mdpi: splash.mdpi ?? splash.image,\n      backgroundColor: splash.backgroundColor,\n      image: splash.image,\n      resizeMode: splash.resizeMode ?? defaultResizeMode,\n      imageWidth: 200,\n      dark: splash.dark,\n    };\n  }\n\n  if (config.splash) {\n    const splash = config.splash;\n    return {\n      xxxhdpi: splash.image,\n      xxhdpi: splash.image,\n      xhdpi: splash.image,\n      hdpi: splash.image,\n      mdpi: splash.image,\n      image: splash.image,\n      backgroundColor: splash.backgroundColor,\n      resizeMode: splash.resizeMode ?? defaultResizeMode,\n      imageWidth: 200,\n      dark: splash.dark,\n    };\n  }\n\n  return null;\n}\n\nexport function getAndroidDarkSplashConfig(\n  config: Pick<ExpoConfig, 'splash' | 'android'>,\n  props?: AndroidSplashConfig | null\n): SplashScreenConfig | null {\n  if (props?.dark) {\n    const splash = props.dark;\n    const lightTheme = getAndroidSplashConfig(config, props);\n    return {\n      xxxhdpi: splash.xxxhdpi ?? splash.image,\n      xxhdpi: splash.xxhdpi ?? splash.image,\n      xhdpi: splash.xhdpi ?? splash.image,\n      hdpi: splash.hdpi ?? splash.image,\n      mdpi: splash.mdpi ?? splash.image,\n      image: splash.image,\n      backgroundColor: splash.backgroundColor,\n      resizeMode: lightTheme?.resizeMode ?? defaultResizeMode,\n      drawable: props.drawable,\n    };\n  }\n\n  // Respect the splash screen object, don't mix and match across different splash screen objects\n  // in case the user wants the top level splash to apply to every platform except android.\n  if (config.android?.splash?.dark) {\n    const splash = config.android?.splash?.dark;\n    const lightTheme = getAndroidSplashConfig(config, props);\n    return {\n      xxxhdpi: splash.xxxhdpi ?? splash.image,\n      xxhdpi: splash.xxhdpi ?? splash.image,\n      xhdpi: splash.xhdpi ?? splash.image,\n      hdpi: splash.hdpi ?? splash.image,\n      mdpi: splash.mdpi ?? splash.image,\n      image: splash.image,\n      backgroundColor: splash.backgroundColor,\n      // Can't support dark resizeMode because the resize mode is hardcoded into the MainActivity.java\n      resizeMode: lightTheme?.resizeMode ?? defaultResizeMode,\n    };\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;AA+BA,MAAMA,iBAAiB,GAAG,SAAS;AAE5B,SAASC,sBAAsBA,CACpCC,MAA8C,EAC9CC,KAAkC,EACN;EAC5B;EACA;EACA,IAAIA,KAAK,EAAE;IACT,MAAMC,MAAM,GAAGD,KAAK;IACpB,OAAO;MACLE,OAAO,EAAED,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,KAAK;MACvCC,MAAM,EAAEH,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACE,KAAK;MACrCE,KAAK,EAAEJ,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACE,KAAK;MACnCG,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACE,KAAK;MACjCI,IAAI,EAAEN,MAAM,CAACM,IAAI,IAAIN,MAAM,CAACE,KAAK;MACjCK,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvCC,UAAU,EAAER,MAAM,CAACQ,UAAU,IAAIZ,iBAAiB;MAClDM,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBO,UAAU,EAAET,MAAM,CAACS,UAAU,IAAI,GAAG;MACpCC,IAAI,EAAEV,MAAM,CAACU,IAAI;MACjBC,QAAQ,EAAEX,MAAM,CAACW;IACnB,CAAC;EACH;EAEA,IAAIb,MAAM,CAACc,OAAO,EAAEZ,MAAM,EAAE;IAC1B,MAAMA,MAAM,GAAGF,MAAM,CAACc,OAAO,EAAEZ,MAAM;IACrC,OAAO;MACLC,OAAO,EAAED,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,KAAK;MACvCC,MAAM,EAAEH,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACE,KAAK;MACrCE,KAAK,EAAEJ,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACE,KAAK;MACnCG,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACE,KAAK;MACjCI,IAAI,EAAEN,MAAM,CAACM,IAAI,IAAIN,MAAM,CAACE,KAAK;MACjCK,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvCL,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBM,UAAU,EAAER,MAAM,CAACQ,UAAU,IAAIZ,iBAAiB;MAClDa,UAAU,EAAE,GAAG;MACfC,IAAI,EAAEV,MAAM,CAACU;IACf,CAAC;EACH;EAEA,IAAIZ,MAAM,CAACE,MAAM,EAAE;IACjB,MAAMA,MAAM,GAAGF,MAAM,CAACE,MAAM;IAC5B,OAAO;MACLC,OAAO,EAAED,MAAM,CAACE,KAAK;MACrBC,MAAM,EAAEH,MAAM,CAACE,KAAK;MACpBE,KAAK,EAAEJ,MAAM,CAACE,KAAK;MACnBG,IAAI,EAAEL,MAAM,CAACE,KAAK;MAClBI,IAAI,EAAEN,MAAM,CAACE,KAAK;MAClBA,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBK,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvCC,UAAU,EAAER,MAAM,CAACQ,UAAU,IAAIZ,iBAAiB;MAClDa,UAAU,EAAE,GAAG;MACfC,IAAI,EAAEV,MAAM,CAACU;IACf,CAAC;EACH;EAEA,OAAO,IAAI;AACb;AAEO,SAASG,0BAA0BA,CACxCf,MAA8C,EAC9CC,KAAkC,EACP;EAC3B,IAAIA,KAAK,EAAEW,IAAI,EAAE;IACf,MAAMV,MAAM,GAAGD,KAAK,CAACW,IAAI;IACzB,MAAMI,UAAU,GAAGjB,sBAAsB,CAACC,MAAM,EAAEC,KAAK,CAAC;IACxD,OAAO;MACLE,OAAO,EAAED,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,KAAK;MACvCC,MAAM,EAAEH,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACE,KAAK;MACrCE,KAAK,EAAEJ,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACE,KAAK;MACnCG,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACE,KAAK;MACjCI,IAAI,EAAEN,MAAM,CAACM,IAAI,IAAIN,MAAM,CAACE,KAAK;MACjCA,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBK,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvCC,UAAU,EAAEM,UAAU,EAAEN,UAAU,IAAIZ,iBAAiB;MACvDe,QAAQ,EAAEZ,KAAK,CAACY;IAClB,CAAC;EACH;;EAEA;EACA;EACA,IAAIb,MAAM,CAACc,OAAO,EAAEZ,MAAM,EAAEU,IAAI,EAAE;IAChC,MAAMV,MAAM,GAAGF,MAAM,CAACc,OAAO,EAAEZ,MAAM,EAAEU,IAAI;IAC3C,MAAMI,UAAU,GAAGjB,sBAAsB,CAACC,MAAM,EAAEC,KAAK,CAAC;IACxD,OAAO;MACLE,OAAO,EAAED,MAAM,CAACC,OAAO,IAAID,MAAM,CAACE,KAAK;MACvCC,MAAM,EAAEH,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACE,KAAK;MACrCE,KAAK,EAAEJ,MAAM,CAACI,KAAK,IAAIJ,MAAM,CAACE,KAAK;MACnCG,IAAI,EAAEL,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACE,KAAK;MACjCI,IAAI,EAAEN,MAAM,CAACM,IAAI,IAAIN,MAAM,CAACE,KAAK;MACjCA,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBK,eAAe,EAAEP,MAAM,CAACO,eAAe;MACvC;MACAC,UAAU,EAAEM,UAAU,EAAEN,UAAU,IAAIZ;IACxC,CAAC;EACH;EAEA,OAAO,IAAI;AACb", "ignoreList": []}