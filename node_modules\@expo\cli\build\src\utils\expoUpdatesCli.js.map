{"version": 3, "sources": ["../../../src/utils/expoUpdatesCli.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport resolveFrom, { silent as silentResolveFrom } from 'resolve-from';\n\nexport class ExpoUpdatesCLIModuleNotFoundError extends Error {}\nexport class ExpoUpdatesCLIInvalidCommandError extends Error {}\nexport class ExpoUpdatesCLICommandFailedError extends <PERSON>rror {}\n\nexport async function expoUpdatesCommandAsync(projectDir: string, args: string[]): Promise<string> {\n  let expoUpdatesCli;\n  try {\n    expoUpdatesCli =\n      silentResolveFrom(projectDir, 'expo-updates/bin/cli') ??\n      resolveFrom(projectDir, 'expo-updates/bin/cli.js');\n  } catch (e: any) {\n    if (e.code === 'MODULE_NOT_FOUND') {\n      throw new ExpoUpdatesCLIModuleNotFoundError(`The \\`expo-updates\\` package was not found. `);\n    }\n    throw e;\n  }\n\n  try {\n    return (\n      await spawnAsync(expoUpdatesCli, args, {\n        stdio: 'pipe',\n        env: { ...process.env },\n      })\n    ).stdout;\n  } catch (e: any) {\n    if (e.stderr && typeof e.stderr === 'string') {\n      if (e.stderr.includes('Invalid command')) {\n        throw new ExpoUpdatesCLIInvalidCommandError(\n          `The command specified by ${args} was not valid in the \\`expo-updates\\` CLI.`\n        );\n      } else {\n        throw new ExpoUpdatesCLICommandFailedError(e.stderr);\n      }\n    }\n\n    throw e;\n  }\n}\n"], "names": ["ExpoUpdatesCLIModuleNotFoundError", "ExpoUpdatesCLIInvalidCommandError", "ExpoUpdatesCLICommandFailedError", "expoUpdatesCommandAsync", "Error", "projectDir", "args", "expoUpdatesCli", "silentResolveFrom", "resolveFrom", "e", "code", "spawnAsync", "stdio", "env", "process", "stdout", "stderr", "includes"], "mappings": "AAAA;;;;;;;;;;;IAGaA,iCAAiC,MAAjCA,iCAAiC;IACjCC,iCAAiC,MAAjCA,iCAAiC;IACjCC,gCAAgC,MAAhCA,gCAAgC;IAEvBC,uBAAuB,MAAvBA,uBAAuB;;;8DAPtB,mBAAmB;;;;;;;+DACe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhE,MAAMH,iCAAiC,SAASI,KAAK;CAAG;AACxD,MAAMH,iCAAiC,SAASG,KAAK;CAAG;AACxD,MAAMF,gCAAgC,SAASE,KAAK;CAAG;AAEvD,eAAeD,uBAAuB,CAACE,UAAkB,EAAEC,IAAc,EAAmB;IACjG,IAAIC,cAAc,AAAC;IACnB,IAAI;QACFA,cAAc,GACZC,IAAAA,YAAiB,EAAA,OAAA,EAACH,UAAU,EAAE,sBAAsB,CAAC,IACrDI,IAAAA,YAAW,EAAA,QAAA,EAACJ,UAAU,EAAE,yBAAyB,CAAC,CAAC;IACvD,EAAE,OAAOK,CAAC,EAAO;QACf,IAAIA,CAAC,CAACC,IAAI,KAAK,kBAAkB,EAAE;YACjC,MAAM,IAAIX,iCAAiC,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;QAC9F,CAAC;QACD,MAAMU,CAAC,CAAC;IACV,CAAC;IAED,IAAI;QACF,OAAO,CACL,MAAME,IAAAA,WAAU,EAAA,QAAA,EAACL,cAAc,EAAED,IAAI,EAAE;YACrCO,KAAK,EAAE,MAAM;YACbC,GAAG,EAAE;gBAAE,GAAGC,OAAO,CAACD,GAAG;aAAE;SACxB,CAAC,CACH,CAACE,MAAM,CAAC;IACX,EAAE,OAAON,EAAC,EAAO;QACf,IAAIA,EAAC,CAACO,MAAM,IAAI,OAAOP,EAAC,CAACO,MAAM,KAAK,QAAQ,EAAE;YAC5C,IAAIP,EAAC,CAACO,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;gBACxC,MAAM,IAAIjB,iCAAiC,CACzC,CAAC,yBAAyB,EAAEK,IAAI,CAAC,2CAA2C,CAAC,CAC9E,CAAC;YACJ,OAAO;gBACL,MAAM,IAAIJ,gCAAgC,CAACQ,EAAC,CAACO,MAAM,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,MAAMP,EAAC,CAAC;IACV,CAAC;AACH,CAAC"}