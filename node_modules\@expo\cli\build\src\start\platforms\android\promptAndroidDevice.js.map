{"version": 3, "sources": ["../../../../../src/start/platforms/android/promptAndroidDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { Device, logUnauthorized } from './adb';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { createSelectionFilter, promptAsync } from '../../../utils/prompts';\n\nexport async function promptForDeviceAsync(devices: Device[]): Promise<Device> {\n  // TODO: provide an option to add or download more simulators\n\n  const { value } = await promptAsync({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a device/emulator',\n    choices: devices.map((item) => formatDeviceChoice(item)),\n    suggest: createSelectionFilter(),\n  });\n\n  const device = devices.find(({ name }) => name === value);\n\n  if (device?.isAuthorized === false) {\n    logUnauthorized(device);\n    throw new AbortCommandError();\n  }\n\n  return device!;\n}\n\n/**\n * Format the device for prompt list.\n * @internal - Exposed for testing.\n */\nexport function formatDeviceChoice(device: Device): { title: string; value: string } {\n  const symbol = getDeviceChoiceSymbol(device);\n  const name = getDeviceChoiceName(device);\n  const type = chalk.dim(device.isAuthorized ? device.type : 'unauthorized');\n\n  return {\n    value: device.name,\n    title: `${symbol}${name} (${type})`,\n  };\n}\n\n/** Get the styled symbol of the device, based on ADB connection type (usb vs network) */\nfunction getDeviceChoiceSymbol(device: Device) {\n  if (device.type === 'device' && device.connectionType === 'Network') {\n    return '🌐 ';\n  }\n\n  if (device.type === 'device') {\n    return '🔌 ';\n  }\n\n  return '';\n}\n\n/** Get the styled name of the device, based on device state */\nfunction getDeviceChoiceName(device: Device) {\n  // Use no style changes for a disconnected device that is available to be opened.\n  if (!device.isBooted) {\n    return device.name;\n  }\n\n  // A device that is connected and ready to be used should be bolded to match iOS.\n  if (device.isAuthorized) {\n    return chalk.bold(device.name);\n  }\n\n  // Devices that are unauthorized and connected cannot be used, but they are connected so gray them out.\n  return chalk.bold(chalk.gray(device.name));\n}\n"], "names": ["promptForDeviceAsync", "formatDeviceChoice", "devices", "value", "promptAsync", "type", "name", "limit", "message", "choices", "map", "item", "suggest", "createSelectionFilter", "device", "find", "isAuthorized", "logUnauthorized", "AbortCommandError", "symbol", "getDeviceChoiceSymbol", "getDeviceChoiceName", "chalk", "dim", "title", "connectionType", "isBooted", "bold", "gray"], "mappings": "AAAA;;;;;;;;;;;IAMsBA,oBAAoB,MAApBA,oBAAoB;IA0B1BC,kBAAkB,MAAlBA,kBAAkB;;;8DAhChB,OAAO;;;;;;qBAEe,OAAO;wBACb,uBAAuB;yBACN,wBAAwB;;;;;;AAEpE,eAAeD,oBAAoB,CAACE,OAAiB,EAAmB;IAC7E,6DAA6D;IAE7D,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAW,YAAA,EAAC;QAClCC,IAAI,EAAE,cAAc;QACpBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,0BAA0B;QACnCC,OAAO,EAAEP,OAAO,CAACQ,GAAG,CAAC,CAACC,IAAI,GAAKV,kBAAkB,CAACU,IAAI,CAAC,CAAC;QACxDC,OAAO,EAAEC,IAAAA,QAAqB,sBAAA,GAAE;KACjC,CAAC,AAAC;IAEH,MAAMC,MAAM,GAAGZ,OAAO,CAACa,IAAI,CAAC,CAAC,EAAET,IAAI,CAAA,EAAE,GAAKA,IAAI,KAAKH,KAAK,CAAC,AAAC;IAE1D,IAAIW,CAAAA,MAAM,QAAc,GAApBA,KAAAA,CAAoB,GAApBA,MAAM,CAAEE,YAAY,CAAA,KAAK,KAAK,EAAE;QAClCC,IAAAA,IAAe,gBAAA,EAACH,MAAM,CAAC,CAAC;QACxB,MAAM,IAAII,OAAiB,kBAAA,EAAE,CAAC;IAChC,CAAC;IAED,OAAOJ,MAAM,CAAE;AACjB,CAAC;AAMM,SAASb,kBAAkB,CAACa,MAAc,EAAoC;IACnF,MAAMK,MAAM,GAAGC,qBAAqB,CAACN,MAAM,CAAC,AAAC;IAC7C,MAAMR,IAAI,GAAGe,mBAAmB,CAACP,MAAM,CAAC,AAAC;IACzC,MAAMT,IAAI,GAAGiB,MAAK,EAAA,QAAA,CAACC,GAAG,CAACT,MAAM,CAACE,YAAY,GAAGF,MAAM,CAACT,IAAI,GAAG,cAAc,CAAC,AAAC;IAE3E,OAAO;QACLF,KAAK,EAAEW,MAAM,CAACR,IAAI;QAClBkB,KAAK,EAAE,CAAC,EAAEL,MAAM,CAAC,EAAEb,IAAI,CAAC,EAAE,EAAED,IAAI,CAAC,CAAC,CAAC;KACpC,CAAC;AACJ,CAAC;AAED,uFAAuF,GACvF,SAASe,qBAAqB,CAACN,MAAc,EAAE;IAC7C,IAAIA,MAAM,CAACT,IAAI,KAAK,QAAQ,IAAIS,MAAM,CAACW,cAAc,KAAK,SAAS,EAAE;QACnE,OAAO,eAAI,CAAC;IACd,CAAC;IAED,IAAIX,MAAM,CAACT,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,eAAI,CAAC;IACd,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,6DAA6D,GAC7D,SAASgB,mBAAmB,CAACP,MAAc,EAAE;IAC3C,iFAAiF;IACjF,IAAI,CAACA,MAAM,CAACY,QAAQ,EAAE;QACpB,OAAOZ,MAAM,CAACR,IAAI,CAAC;IACrB,CAAC;IAED,iFAAiF;IACjF,IAAIQ,MAAM,CAACE,YAAY,EAAE;QACvB,OAAOM,MAAK,EAAA,QAAA,CAACK,IAAI,CAACb,MAAM,CAACR,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,uGAAuG;IACvG,OAAOgB,MAAK,EAAA,QAAA,CAACK,IAAI,CAACL,MAAK,EAAA,QAAA,CAACM,IAAI,CAACd,MAAM,CAACR,IAAI,CAAC,CAAC,CAAC;AAC7C,CAAC"}