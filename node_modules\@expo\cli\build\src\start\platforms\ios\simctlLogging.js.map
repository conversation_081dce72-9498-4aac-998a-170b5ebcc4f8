{"version": 3, "sources": ["../../../../../src/start/platforms/ios/simctlLogging.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { ChildProcessWithoutNullStreams, spawn } from 'child_process';\nimport { EOL } from 'os';\nimport path from 'path';\nimport wrapAnsi from 'wrap-ansi';\n\nimport { Device, getContainerPathAsync } from './simctl';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nexport type SimControlLog = {\n  /**\n   * 258753568922927108\n   */\n  traceID: number;\n  /**\n   *\n   * \"Connection 1: done\",\n   */\n  eventMessage: string;\n  /**\n   * \"logEvent\" | \"activityCreateEvent\",\n   */\n  eventType: 'logEvent' | 'activityCreateEvent';\n  source: null | {\n    /**\n     * 'RCTDefaultLogFunction_block_invoke' | '__TCC_CRASHING_DUE_TO_PRIVACY_VIOLATION__'\n     */\n    symbol: string;\n    line: number;\n    /**\n     * 'TCC' | 'Security' | 'CFNetwork' | 'libnetwork.dylib' | 'myapp'\n     *\n     * TCC is apple sys, it means \"Transparency, Consent, and Control\"\n     */\n    image: string;\n    /**\n     * 'RCTLog.mm' | ''\n     */\n    file: string;\n  };\n  /**\n   * \"Connection %llu: done\"\n   */\n  formatString: string;\n  /**\n   * 0\n   */\n  activityIdentifier: number;\n  subsystem:\n    | ''\n    | 'com.apple.network'\n    | 'com.facebook.react.log'\n    | 'com.apple.TCC'\n    | 'com.apple.CoreTelephony'\n    | 'com.apple.WebKit'\n    | 'com.apple.runningboard'\n    | string;\n  category: '' | 'access' | 'connection' | 'plugin';\n  /**\n   * \"2021-03-15 15:36:28.004331-0700\"\n   */\n  timestamp: string;\n  /**\n   * 706567072091713\n   */\n  machTimestamp: number;\n  /**\n   * \"Default\"\n   */\n  messageType: 'Default' | 'Error';\n  /**\n   * 15192\n   */\n  processID: number;\n};\n\ntype ProcessResolver =\n  | {\n      pid: string;\n    }\n  | {\n      appId: string;\n    };\n\nexport class SimulatorLogStreamer {\n  private childProcess: ChildProcessWithoutNullStreams | null = null;\n\n  static cache: SimulatorLogStreamer[] = [];\n\n  static getStreamer = (device: Pick<Device, 'udid'>, resolver: ProcessResolver) => {\n    return (\n      SimulatorLogStreamer.cache.find((streamer) => streamer.device.udid === device.udid) ??\n      new SimulatorLogStreamer(device, resolver)\n    );\n  };\n\n  constructor(\n    public device: Pick<Device, 'udid'>,\n    public resolver: ProcessResolver\n  ) {}\n\n  isAttached() {\n    return !!this.childProcess;\n  }\n\n  async resolvePidAsync() {\n    if ('pid' in this.resolver) {\n      return this.resolver.pid;\n    }\n    return getImageNameFromBundleIdentifierAsync(this.device.udid, this.resolver.appId);\n  }\n\n  async attachAsync() {\n    await this.detachAsync();\n\n    const pid = await this.resolvePidAsync();\n\n    if (!pid) {\n      throw new CommandError(`Could not find pid for ${this.device.udid}`);\n    }\n\n    // xcrun simctl spawn booted log stream --process --style json\n    this.childProcess = spawn('xcrun', [\n      'simctl',\n      'spawn',\n      this.device.udid,\n      'log',\n      'stream',\n      '--process',\n      pid,\n      // ndjson provides a better format than json.\n      '--style',\n      'ndjson',\n      // Provide the source so we can filter logs better\n      '--source',\n      // log, activity, trace -- activity was related to layouts, trace didn't work, so that leaves log.\n      // Passing nothing combines all three, but we don't use activity.\n      '--type',\n      'log',\n      // backtrace doesn't seem very useful in basic cases.\n      // TODO: Maybe we can format as a stack trace for native errors.\n      '--no-backtrace',\n    ]);\n\n    this.childProcess.stdout.on('data', (data: Buffer) => {\n      // Sometimes more than one chunk comes at a time, here we split by system newline,\n      // then trim and filter.\n      const strings = data\n        .toString()\n        .split(EOL)\n        .map((value) => value.trim())\n        // This filters out the first log which says something like:\n        // Filtering the log data using \"process BEGINSWITH[cd] \"my-app\" AND type == 1024\"\n        .filter((value) => value.startsWith('{'));\n\n      strings.forEach((str) => {\n        const simLog = parseMessageJson(str);\n        if (!simLog) {\n          return;\n        }\n        onMessage(simLog);\n      });\n    });\n\n    this.childProcess.on('error', ({ message }) => {\n      Log.debug('[simctl error]:', message);\n    });\n\n    this.off = installExitHooks(() => {\n      this.detachAsync.bind(this);\n    });\n  }\n\n  private off: (() => void) | null = null;\n\n  detachAsync() {\n    this.off?.();\n    this.off = null;\n    if (this.childProcess) {\n      return new Promise<void>((resolve) => {\n        this.childProcess?.on('close', resolve);\n        this.childProcess?.kill();\n        this.childProcess = null;\n      });\n    }\n    return Promise.resolve();\n  }\n}\n\nfunction parseMessageJson(data: string) {\n  const stringData = data.toString();\n  try {\n    return JSON.parse(stringData) as SimControlLog;\n  } catch {\n    Log.debug('Failed to parse simctl JSON message:\\n' + stringData);\n  }\n  return null;\n}\n\n// There are a lot of networking logs in RN that aren't relevant to the user.\nfunction isNetworkLog(simLog: SimControlLog): boolean {\n  return (\n    simLog.subsystem === 'com.apple.network' ||\n    simLog.category === 'connection' ||\n    simLog.source?.image === 'CFNetwork'\n  );\n}\n\nfunction isReactLog(simLog: SimControlLog): boolean {\n  return simLog.subsystem === 'com.facebook.react.log' && simLog.source?.file === 'RCTLog.mm';\n}\n\n// It's not clear what these are but they aren't very useful.\n// (The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated)\n// We can add them later if need.\nfunction isCoreTelephonyLog(simLog: SimControlLog): boolean {\n  // [CoreTelephony] Updating selectors failed with: Error Domain=NSCocoaErrorDomain Code=4099\n  // \"The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated.\" UserInfo={NSDebugDescription=The connection to service on pid 0 named com.apple.commcenter.coretelephony.xpc was invalidated.}\n  return simLog.subsystem === 'com.apple.CoreTelephony';\n}\n\n// https://stackoverflow.com/a/65313219/4047926\nfunction isWebKitLog(simLog: SimControlLog): boolean {\n  // [WebKit] 0x1143ca500 - ProcessAssertion: Failed to acquire RBS Background assertion 'WebProcess Background Assertion' for process with PID 27084, error: Error Domain=RBSAssertionErrorDomain Code=3 \"Target is not running or required target\n  // entitlement is missing\" UserInfo={RBSAssertionAttribute=<RBSDomainAttribute| domain:\"com.apple.webkit\" name:\"Background\" sourceEnvironment:\"(null)\">, NSLocalizedFailureReason=Target is not running or required target entitlement is missing}\n  return simLog.subsystem === 'com.apple.WebKit';\n}\n\n// Similar to WebKit logs\nfunction isRunningBoardServicesLog(simLog: SimControlLog): boolean {\n  // [RunningBoardServices] Error acquiring assertion: <Error Domain=RBSAssertionErrorDomain Code=3 \"Target is not running or required target entitlement is missing\" UserInfo={RBSAssertionAttribute=<RBSDomainAttribute| domain:\"com.apple.webkit\"\n  // name:\"Background\" sourceEnvironment:\"(null)\">, NSLocalizedFailureReason=Target is not running or required target entitlement is missing}>\n  return simLog.subsystem === 'com.apple.runningboard';\n}\n\nfunction formatMessage(simLog: SimControlLog): string {\n  // TODO: Maybe change \"TCC\" to \"Consent\" or \"System\".\n  const category = chalk.gray(`[${simLog.source?.image ?? simLog.subsystem}]`);\n  const message = simLog.eventMessage;\n  return wrapAnsi(category + ' ' + message, process.stdout.columns || 80);\n}\n\nexport function onMessage(simLog: SimControlLog) {\n  let hasLogged = false;\n\n  if (simLog.messageType === 'Error') {\n    if (\n      // Hide all networking errors which are mostly useless.\n      !isNetworkLog(simLog) &&\n      // Showing React errors will result in duplicate messages.\n      !isReactLog(simLog) &&\n      !isCoreTelephonyLog(simLog) &&\n      !isWebKitLog(simLog) &&\n      !isRunningBoardServicesLog(simLog)\n    ) {\n      hasLogged = true;\n      // Sim: This app has crashed because it attempted to access privacy-sensitive data without a usage description.  The app's Info.plist must contain an NSCameraUsageDescription key with a string value explaining to the user how the app uses this data.\n      Log.error(formatMessage(simLog));\n    }\n  } else if (simLog.eventMessage) {\n    // If the source has a file (i.e. not a system log).\n    if (\n      simLog.source?.file ||\n      simLog.eventMessage.includes('Terminating app due to uncaught exception')\n    ) {\n      hasLogged = true;\n      Log.log(formatMessage(simLog));\n    }\n  }\n\n  if (!hasLogged) {\n    Log.debug(formatMessage(simLog));\n  } else {\n    // console.log('DATA:', JSON.stringify(simLog));\n  }\n}\n\n/**\n *\n * @param udid\n * @param bundleIdentifier\n * @returns Image name like `Exponent` and `null` when the app is not installed on the provided simulator.\n */\nasync function getImageNameFromBundleIdentifierAsync(\n  udid: string,\n  bundleIdentifier: string\n): Promise<string | null> {\n  const containerPath = await getContainerPathAsync({ udid }, { appId: bundleIdentifier });\n\n  if (containerPath) {\n    return getImageNameFromContainerPath(containerPath);\n  }\n  return null;\n}\n\nfunction getImageNameFromContainerPath(binaryPath: string): string {\n  return path.basename(binaryPath).split('.')[0];\n}\n"], "names": ["SimulatorLogStreamer", "onMessage", "cache", "getStreamer", "device", "resolver", "find", "streamer", "udid", "constructor", "childProcess", "off", "isAttached", "resolvePidAsync", "pid", "getImageNameFromBundleIdentifierAsync", "appId", "attachAsync", "detachAsync", "CommandError", "spawn", "stdout", "on", "data", "strings", "toString", "split", "EOL", "map", "value", "trim", "filter", "startsWith", "for<PERSON>ach", "str", "simLog", "parseMessageJson", "message", "Log", "debug", "installExitHooks", "bind", "Promise", "resolve", "kill", "stringData", "JSON", "parse", "isNetworkLog", "subsystem", "category", "source", "image", "isReactLog", "file", "isCoreTelephonyLog", "isWebKitLog", "isRunningBoardServicesLog", "formatMessage", "chalk", "gray", "eventMessage", "wrapAnsi", "process", "columns", "<PERSON><PERSON><PERSON>", "messageType", "error", "includes", "log", "bundleIdentifier", "containerPath", "getContainerPathAsync", "getImageNameFromContainerPath", "binaryPath", "path", "basename"], "mappings": "AAAA;;;;;;;;;;;IAsFaA,oBAAoB,MAApBA,oBAAoB;IA8JjBC,SAAS,MAATA,SAAS;;;8DApPP,OAAO;;;;;;;yBAC6B,eAAe;;;;;;;yBACjD,IAAI;;;;;;;8DACP,MAAM;;;;;;;8DACF,WAAW;;;;;;wBAEc,UAAU;2DACnC,cAAc;wBACN,uBAAuB;sBACnB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6E/C,MAAMD,oBAAoB;IAG/B,OAAOE,KAAK,GAA2B,EAAE,CAAC;IAE1C,OAAOC,WAAW,GAAG,CAACC,MAA4B,EAAEC,QAAyB,GAAK;QAChF,OACEL,oBAAoB,CAACE,KAAK,CAACI,IAAI,CAAC,CAACC,QAAQ,GAAKA,QAAQ,CAACH,MAAM,CAACI,IAAI,KAAKJ,MAAM,CAACI,IAAI,CAAC,IACnF,IAAIR,oBAAoB,CAACI,MAAM,EAAEC,QAAQ,CAAC,CAC1C;IACJ,CAAC,CAAC;IAEFI,YACSL,MAA4B,EAC5BC,QAAyB,CAChC;QAFOD,cAAAA,MAA4B,CAAA;QAC5BC,gBAAAA,QAAyB,CAAA;aAb1BK,YAAY,GAA0C,IAAI;aAwF1DC,GAAG,GAAwB,IAAI;IA1EpC;IAEHC,UAAU,GAAG;QACX,OAAO,CAAC,CAAC,IAAI,CAACF,YAAY,CAAC;IAC7B;UAEMG,eAAe,GAAG;QACtB,IAAI,KAAK,IAAI,IAAI,CAACR,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAACA,QAAQ,CAACS,GAAG,CAAC;QAC3B,CAAC;QACD,OAAOC,qCAAqC,CAAC,IAAI,CAACX,MAAM,CAACI,IAAI,EAAE,IAAI,CAACH,QAAQ,CAACW,KAAK,CAAC,CAAC;IACtF;UAEMC,WAAW,GAAG;QAClB,MAAM,IAAI,CAACC,WAAW,EAAE,CAAC;QAEzB,MAAMJ,GAAG,GAAG,MAAM,IAAI,CAACD,eAAe,EAAE,AAAC;QAEzC,IAAI,CAACC,GAAG,EAAE;YACR,MAAM,IAAIK,OAAY,aAAA,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACf,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC;QAED,8DAA8D;QAC9D,IAAI,CAACE,YAAY,GAAGU,IAAAA,aAAK,EAAA,MAAA,EAAC,OAAO,EAAE;YACjC,QAAQ;YACR,OAAO;YACP,IAAI,CAAChB,MAAM,CAACI,IAAI;YAChB,KAAK;YACL,QAAQ;YACR,WAAW;YACXM,GAAG;YACH,6CAA6C;YAC7C,SAAS;YACT,QAAQ;YACR,kDAAkD;YAClD,UAAU;YACV,kGAAkG;YAClG,iEAAiE;YACjE,QAAQ;YACR,KAAK;YACL,qDAAqD;YACrD,gEAAgE;YAChE,gBAAgB;SACjB,CAAC,CAAC;QAEH,IAAI,CAACJ,YAAY,CAACW,MAAM,CAACC,EAAE,CAAC,MAAM,EAAE,CAACC,IAAY,GAAK;YACpD,kFAAkF;YAClF,wBAAwB;YACxB,MAAMC,OAAO,GAAGD,IAAI,CACjBE,QAAQ,EAAE,CACVC,KAAK,CAACC,GAAG,EAAA,IAAA,CAAC,CACVC,GAAG,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,EAAE,CAAC,AAC7B,4DAA4D;YAC5D,kFAAkF;aACjFC,MAAM,CAAC,CAACF,KAAK,GAAKA,KAAK,CAACG,UAAU,CAAC,GAAG,CAAC,CAAC,AAAC;YAE5CR,OAAO,CAACS,OAAO,CAAC,CAACC,GAAG,GAAK;gBACvB,MAAMC,MAAM,GAAGC,gBAAgB,CAACF,GAAG,CAAC,AAAC;gBACrC,IAAI,CAACC,MAAM,EAAE;oBACX,OAAO;gBACT,CAAC;gBACDlC,SAAS,CAACkC,MAAM,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAACzB,YAAY,CAACY,EAAE,CAAC,OAAO,EAAE,CAAC,EAAEe,OAAO,CAAA,EAAE,GAAK;YAC7CC,IAAG,CAACC,KAAK,CAAC,iBAAiB,EAAEF,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC1B,GAAG,GAAG6B,IAAAA,KAAgB,iBAAA,EAAC,IAAM;YAChC,IAAI,CAACtB,WAAW,CAACuB,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL;IAIAvB,WAAW,GAAG;YACZ,IAAI,AAAI,EAAR,GAAQ;QAAR,CAAA,GAAQ,GAAR,CAAA,IAAI,GAAJ,IAAI,EAACP,GAAG,SAAI,GAAZ,KAAA,CAAY,GAAZ,GAAQ,CAAR,IAAY,CAAZ,IAAI,CAAQ,CAAC;QACb,IAAI,CAACA,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,IAAI,CAACD,YAAY,EAAE;YACrB,OAAO,IAAIgC,OAAO,CAAO,CAACC,OAAO,GAAK;oBACpC,GAAiB,EACjB,IAAiB;gBADjB,CAAA,GAAiB,GAAjB,IAAI,CAACjC,YAAY,SAAI,GAArB,KAAA,CAAqB,GAArB,GAAiB,CAAEY,EAAE,CAAC,OAAO,EAAEqB,OAAO,CAAC,CAAC;gBACxC,CAAA,IAAiB,GAAjB,IAAI,CAACjC,YAAY,SAAM,GAAvB,KAAA,CAAuB,GAAvB,IAAiB,CAAEkC,IAAI,EAAE,CAAC;gBAC1B,IAAI,CAAClC,YAAY,GAAG,IAAI,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAOgC,OAAO,CAACC,OAAO,EAAE,CAAC;IAC3B;CACD;AAED,SAASP,gBAAgB,CAACb,IAAY,EAAE;IACtC,MAAMsB,UAAU,GAAGtB,IAAI,CAACE,QAAQ,EAAE,AAAC;IACnC,IAAI;QACF,OAAOqB,IAAI,CAACC,KAAK,CAACF,UAAU,CAAC,CAAkB;IACjD,EAAE,OAAM;QACNP,IAAG,CAACC,KAAK,CAAC,wCAAwC,GAAGM,UAAU,CAAC,CAAC;IACnE,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,6EAA6E;AAC7E,SAASG,YAAY,CAACb,MAAqB,EAAW;QAIlDA,GAAa;IAHf,OACEA,MAAM,CAACc,SAAS,KAAK,mBAAmB,IACxCd,MAAM,CAACe,QAAQ,KAAK,YAAY,IAChCf,CAAAA,CAAAA,GAAa,GAAbA,MAAM,CAACgB,MAAM,SAAO,GAApBhB,KAAAA,CAAoB,GAApBA,GAAa,CAAEiB,KAAK,CAAA,KAAK,WAAW,CACpC;AACJ,CAAC;AAED,SAASC,UAAU,CAAClB,MAAqB,EAAW;QACMA,GAAa;IAArE,OAAOA,MAAM,CAACc,SAAS,KAAK,wBAAwB,IAAId,CAAAA,CAAAA,GAAa,GAAbA,MAAM,CAACgB,MAAM,SAAM,GAAnBhB,KAAAA,CAAmB,GAAnBA,GAAa,CAAEmB,IAAI,CAAA,KAAK,WAAW,CAAC;AAC9F,CAAC;AAED,6DAA6D;AAC7D,oGAAoG;AACpG,iCAAiC;AACjC,SAASC,kBAAkB,CAACpB,MAAqB,EAAW;IAC1D,4FAA4F;IAC5F,oOAAoO;IACpO,OAAOA,MAAM,CAACc,SAAS,KAAK,yBAAyB,CAAC;AACxD,CAAC;AAED,+CAA+C;AAC/C,SAASO,WAAW,CAACrB,MAAqB,EAAW;IACnD,iPAAiP;IACjP,kPAAkP;IAClP,OAAOA,MAAM,CAACc,SAAS,KAAK,kBAAkB,CAAC;AACjD,CAAC;AAED,yBAAyB;AACzB,SAASQ,yBAAyB,CAACtB,MAAqB,EAAW;IACjE,kPAAkP;IAClP,4IAA4I;IAC5I,OAAOA,MAAM,CAACc,SAAS,KAAK,wBAAwB,CAAC;AACvD,CAAC;AAED,SAASS,aAAa,CAACvB,MAAqB,EAAU;QAEpBA,GAAa;IAD7C,qDAAqD;IACrD,MAAMe,QAAQ,GAAGS,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEzB,CAAAA,CAAAA,GAAa,GAAbA,MAAM,CAACgB,MAAM,SAAO,GAApBhB,KAAAA,CAAoB,GAApBA,GAAa,CAAEiB,KAAK,CAAA,IAAIjB,MAAM,CAACc,SAAS,CAAC,CAAC,CAAC,CAAC,AAAC;IAC7E,MAAMZ,OAAO,GAAGF,MAAM,CAAC0B,YAAY,AAAC;IACpC,OAAOC,IAAAA,SAAQ,EAAA,QAAA,EAACZ,QAAQ,GAAG,GAAG,GAAGb,OAAO,EAAE0B,OAAO,CAAC1C,MAAM,CAAC2C,OAAO,IAAI,EAAE,CAAC,CAAC;AAC1E,CAAC;AAEM,SAAS/D,SAAS,CAACkC,MAAqB,EAAE;IAC/C,IAAI8B,SAAS,GAAG,KAAK,AAAC;IAEtB,IAAI9B,MAAM,CAAC+B,WAAW,KAAK,OAAO,EAAE;QAClC,IACE,uDAAuD;QACvD,CAAClB,YAAY,CAACb,MAAM,CAAC,IACrB,0DAA0D;QAC1D,CAACkB,UAAU,CAAClB,MAAM,CAAC,IACnB,CAACoB,kBAAkB,CAACpB,MAAM,CAAC,IAC3B,CAACqB,WAAW,CAACrB,MAAM,CAAC,IACpB,CAACsB,yBAAyB,CAACtB,MAAM,CAAC,EAClC;YACA8B,SAAS,GAAG,IAAI,CAAC;YACjB,yPAAyP;YACzP3B,IAAG,CAAC6B,KAAK,CAACT,aAAa,CAACvB,MAAM,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,OAAO,IAAIA,MAAM,CAAC0B,YAAY,EAAE;YAG5B1B,GAAa;QAFf,oDAAoD;QACpD,IACEA,CAAAA,CAAAA,GAAa,GAAbA,MAAM,CAACgB,MAAM,SAAM,GAAnBhB,KAAAA,CAAmB,GAAnBA,GAAa,CAAEmB,IAAI,CAAA,IACnBnB,MAAM,CAAC0B,YAAY,CAACO,QAAQ,CAAC,2CAA2C,CAAC,EACzE;YACAH,SAAS,GAAG,IAAI,CAAC;YACjB3B,IAAG,CAAC+B,GAAG,CAACX,aAAa,CAACvB,MAAM,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,IAAI,CAAC8B,SAAS,EAAE;QACd3B,IAAG,CAACC,KAAK,CAACmB,aAAa,CAACvB,MAAM,CAAC,CAAC,CAAC;IACnC,OAAO;IACL,gDAAgD;IAClD,CAAC;AACH,CAAC;AAED;;;;;CAKC,GACD,eAAepB,qCAAqC,CAClDP,IAAY,EACZ8D,gBAAwB,EACA;IACxB,MAAMC,aAAa,GAAG,MAAMC,IAAAA,OAAqB,sBAAA,EAAC;QAAEhE,IAAI;KAAE,EAAE;QAAEQ,KAAK,EAAEsD,gBAAgB;KAAE,CAAC,AAAC;IAEzF,IAAIC,aAAa,EAAE;QACjB,OAAOE,6BAA6B,CAACF,aAAa,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAASE,6BAA6B,CAACC,UAAkB,EAAU;IACjE,OAAOC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACF,UAAU,CAAC,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC"}