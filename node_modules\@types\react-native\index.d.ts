///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// USING: these definitions are meant to be used with the TSC compiler target set to at least ES2015.
//
// USAGE EXAMPLES: check the RNTSExplorer project at https://github.com/bgrieder/RNTSExplorer
//
// CONTRIBUTING: please open pull requests
//
// CREDITS: This work is based on an original work made by Bernd <PERSON>dies: https://github.com/bparadie
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/// <reference path="modules/BatchedBridge.d.ts" />
/// <reference path="modules/Codegen.d.ts" />
/// <reference path="modules/Devtools.d.ts" />
/// <reference path="modules/globals.d.ts" />
/// <reference path="modules/LaunchScreen.d.ts" />

export * from "@react-native/virtualized-lists";
export * from "./Libraries/ActionSheetIOS/ActionSheetIOS";
export * from "./Libraries/Alert/Alert";
export * from "./Libraries/Animated/Animated";
export * from "./Libraries/Animated/Easing";
export * from "./Libraries/Animated/useAnimatedValue";
export * from "./Libraries/AppState/AppState";
export * from "./Libraries/BatchedBridge/NativeModules";
export * from "./Libraries/Components/AccessibilityInfo/AccessibilityInfo";
export * from "./Libraries/Components/ActivityIndicator/ActivityIndicator";
export * from "./Libraries/Components/Button";
export * from "./Libraries/Components/Clipboard/Clipboard";
export * from "./Libraries/Components/DrawerAndroid/DrawerLayoutAndroid";
export * from "./Libraries/Components/Keyboard/Keyboard";
export * from "./Libraries/Components/Keyboard/KeyboardAvoidingView";
export * from "./Libraries/Components/Pressable/Pressable";
export * from "./Libraries/Components/ProgressBarAndroid/ProgressBarAndroid";
export * from "./Libraries/Components/RefreshControl/RefreshControl";
export * from "./Libraries/Components/SafeAreaView/SafeAreaView";
export * from "./Libraries/Components/ScrollView/ScrollView";
export * from "./Libraries/Components/StatusBar/StatusBar";
export * from "./Libraries/Components/Switch/Switch";
export * from "./Libraries/Components/TextInput/InputAccessoryView";
export * from "./Libraries/Components/TextInput/TextInput";
export * from "./Libraries/Components/ToastAndroid/ToastAndroid";
export * from "./Libraries/Components/Touchable/Touchable";
export * from "./Libraries/Components/Touchable/TouchableHighlight";
export * from "./Libraries/Components/Touchable/TouchableNativeFeedback";
export * from "./Libraries/Components/Touchable/TouchableOpacity";
export * from "./Libraries/Components/Touchable/TouchableWithoutFeedback";
export * from "./Libraries/Components/View/View";
export * from "./Libraries/Components/View/ViewAccessibility";
export * from "./Libraries/Components/View/ViewPropTypes";
export * from "./Libraries/DevToolsSettings/DevToolsSettingsManager";
export * from "./Libraries/EventEmitter/NativeEventEmitter";
export * from "./Libraries/EventEmitter/RCTDeviceEventEmitter";
export * from "./Libraries/EventEmitter/RCTNativeAppEventEmitter";
export * from "./Libraries/Image/Image";
export * from "./Libraries/Image/ImageResizeMode";
export * from "./Libraries/Image/ImageSource";
export * from "./Libraries/Interaction/InteractionManager";
export * from "./Libraries/Interaction/PanResponder";
export * from "./Libraries/LayoutAnimation/LayoutAnimation";
export * from "./Libraries/Linking/Linking";
export * from "./Libraries/Lists/FlatList";
export * from "./Libraries/Lists/SectionList";
export * from "./Libraries/LogBox/LogBox";
export * from "./Libraries/Modal/Modal";
export * as Systrace from "./Libraries/Performance/Systrace";
export * from "./Libraries/PermissionsAndroid/PermissionsAndroid";
export * from "./Libraries/PushNotificationIOS/PushNotificationIOS";
export * from "./Libraries/ReactNative/AppRegistry";
export * from "./Libraries/ReactNative/I18nManager";
export * from "./Libraries/ReactNative/RendererProxy";
export * from "./Libraries/ReactNative/requireNativeComponent";
export * from "./Libraries/ReactNative/RootTag";
export * from "./Libraries/ReactNative/UIManager";
export * from "./Libraries/Settings/Settings";
export * from "./Libraries/Share/Share";
export * from "./Libraries/StyleSheet/PlatformColorValueTypes";
export * from "./Libraries/StyleSheet/PlatformColorValueTypesIOS";
export * from "./Libraries/StyleSheet/processColor";
export * from "./Libraries/StyleSheet/StyleSheet";
export * from "./Libraries/StyleSheet/StyleSheetTypes";
export * from "./Libraries/Text/Text";
export * from "./Libraries/TurboModule/RCTExport";
export * as TurboModuleRegistry from "./Libraries/TurboModule/TurboModuleRegistry";
export * from "./Libraries/Types/CoreEventTypes";
export * from "./Libraries/Utilities/Appearance";
export * from "./Libraries/Utilities/BackHandler";
export * from "./Libraries/Utilities/DevSettings";
export * from "./Libraries/Utilities/Dimensions";
export * from "./Libraries/Utilities/PixelRatio";
export * from "./Libraries/Utilities/Platform";
export * from "./Libraries/vendor/core/ErrorUtils";
export { EmitterSubscription, EventSubscription } from "./Libraries/vendor/emitter/EventEmitter";
export * from "./Libraries/Vibration/Vibration";
export * from "./Libraries/YellowBox/YellowBoxDeprecated";

export * from "./public/DeprecatedPropertiesAlias";
export * from "./public/Insets";
export * from "./public/ReactNativeRenderer";
export * from "./public/ReactNativeTypes";

import type { ErrorUtils } from "./Libraries/vendor/core/ErrorUtils";

declare global {
    interface NodeRequire {
        (id: string): any;
    }

    var require: NodeRequire;

    /**
     * Console polyfill
     * @see https://reactnative.dev/docs/javascript-environment#polyfills
     */
    interface Console {
        error(message?: any, ...optionalParams: any[]): void;
        info(message?: any, ...optionalParams: any[]): void;
        log(message?: any, ...optionalParams: any[]): void;
        warn(message?: any, ...optionalParams: any[]): void;
        trace(message?: any, ...optionalParams: any[]): void;
        debug(message?: any, ...optionalParams: any[]): void;
        table(...data: any[]): void;
        groupCollapsed(label?: string): void;
        groupEnd(): void;
        group(label?: string): void;
        /**
         * @deprecated Use LogBox.ignoreLogs(patterns) instead
         */
        ignoredYellowBox: string[];
    }

    var console: Console;

    /**
     * This contains the non-native `XMLHttpRequest` object, which you can use if you want to route network requests
     * through DevTools (to trace them):
     *
     *   global.XMLHttpRequest = global.originalXMLHttpRequest;
     *
     * @see https://github.com/facebook/react-native/issues/934
     */
    const originalXMLHttpRequest: any;

    const __BUNDLE_START_TIME__: number;
    const ErrorUtils: ErrorUtils;

    /**
     * This variable is set to true when react-native is running in Dev mode
     * @example
     * if (__DEV__) console.log('Running in dev mode')
     */
    const __DEV__: boolean;

    const HermesInternal: null | {};
}
