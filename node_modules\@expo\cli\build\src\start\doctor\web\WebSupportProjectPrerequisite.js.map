{"version": 3, "sources": ["../../../../../src/start/doctor/web/WebSupportProjectPrerequisite.ts"], "sourcesContent": ["import {\n  AppJSONConfig,\n  ExpoConfig,\n  getConfig,\n  getProjectConfigDescriptionWithPaths,\n  ProjectConfig,\n} from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { getPlatformBundlers } from '../../server/platformBundlers';\nimport { PrerequisiteCommandError, ProjectPrerequisite } from '../Prerequisite';\nimport { ensureDependenciesAsync } from '../dependencies/ensureDependenciesAsync';\nimport { ResolvedPackage } from '../dependencies/getMissingPackages';\n\nconst debug = require('debug')('expo:doctor:webSupport') as typeof console.log;\n\n/** Ensure the project has the required web support settings. */\nexport class WebSupportProjectPrerequisite extends ProjectPrerequisite {\n  /** Ensure a project that hasn't explicitly disabled web support has all the required packages for running in the browser. */\n  async assertImplementation(): Promise<void> {\n    if (env.EXPO_NO_WEB_SETUP) {\n      Log.warn('Skipping web setup: EXPO_NO_WEB_SETUP is enabled.');\n      return;\n    }\n    debug('Ensuring web support is setup');\n\n    const result = await this._shouldSetupWebSupportAsync();\n\n    // Ensure web packages are installed\n    await this._ensureWebDependenciesInstalledAsync({ exp: result.exp });\n  }\n\n  /** Exposed for testing. */\n  async _shouldSetupWebSupportAsync(): Promise<ProjectConfig> {\n    const config = getConfig(this.projectRoot);\n\n    // Detect if the 'web' string is purposefully missing from the platforms array.\n    if (isWebPlatformExcluded(config.rootConfig)) {\n      // Get exact config description with paths.\n      const configName = getProjectConfigDescriptionWithPaths(this.projectRoot, config);\n      throw new PrerequisiteCommandError(\n        'WEB_SUPPORT',\n        chalk`Skipping web setup: {bold \"web\"} is not included in the project ${configName} {bold \"platforms\"} array.`\n      );\n    }\n\n    return config;\n  }\n\n  /** Exposed for testing. */\n  async _ensureWebDependenciesInstalledAsync({ exp }: { exp: ExpoConfig }): Promise<boolean> {\n    const requiredPackages: ResolvedPackage[] = [\n      { file: 'react-dom/package.json', pkg: 'react-dom' },\n    ];\n    if (!env.EXPO_NO_REACT_NATIVE_WEB) {\n      // use react-native-web/package.json to skip node module cache issues when the user installs\n      // the package and attempts to resolve the module in the same process.\n      requiredPackages.push({ file: 'react-native-web/package.json', pkg: 'react-native-web' });\n    }\n\n    const bundler = getPlatformBundlers(this.projectRoot, exp).web;\n    // Only include webpack-config if bundler is webpack.\n    if (bundler === 'webpack') {\n      requiredPackages.push(\n        // `webpack` and `webpack-dev-server` should be installed in the `@expo/webpack-config`\n        {\n          file: '@expo/webpack-config/package.json',\n          pkg: '@expo/webpack-config',\n          dev: true,\n        }\n      );\n    } else if (bundler === 'metro') {\n      requiredPackages.push({\n        file: '@expo/metro-runtime/package.json',\n        pkg: '@expo/metro-runtime',\n      });\n    }\n\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        // This never seems to work when prompting, installing, and running -- instead just inform the user to run the install command and try again.\n        skipPrompt: true,\n        isProjectMutable: false,\n        exp,\n        installMessage: `It looks like you're trying to use web support but don't have the required dependencies installed.`,\n        warningMessage: chalk`If you're not using web, please ensure you remove the {bold \"web\"} string from the platforms array in the project Expo config.`,\n        requiredPackages,\n      });\n    } catch (error) {\n      // Reset the cached check so we can re-run the check if the user re-runs the command by pressing 'w' in the Terminal UI.\n      this.resetAssertion();\n      throw error;\n    }\n  }\n}\n\n/** Return `true` if the `web` platform is purposefully excluded from the project Expo config. */\nexport function isWebPlatformExcluded(rootConfig: AppJSONConfig): boolean {\n  // Detect if the 'web' string is purposefully missing from the platforms array.\n  const isWebExcluded =\n    Array.isArray(rootConfig?.expo?.platforms) &&\n    !!rootConfig.expo?.platforms.length &&\n    !rootConfig.expo?.platforms.includes('web');\n  return isWebExcluded;\n}\n"], "names": ["WebSupportProjectPrerequisite", "isWebPlatformExcluded", "debug", "require", "ProjectPrerequisite", "assertImplementation", "env", "EXPO_NO_WEB_SETUP", "Log", "warn", "result", "_shouldSetupWebSupportAsync", "_ensureWebDependenciesInstalledAsync", "exp", "config", "getConfig", "projectRoot", "rootConfig", "config<PERSON><PERSON>", "getProjectConfigDescriptionWithPaths", "PrerequisiteCommandError", "chalk", "requiredPackages", "file", "pkg", "EXPO_NO_REACT_NATIVE_WEB", "push", "bundler", "getPlatformBundlers", "web", "dev", "ensureDependenciesAsync", "skip<PERSON>rompt", "isProjectMutable", "installMessage", "warningMessage", "error", "resetAssertion", "isWebExcluded", "Array", "isArray", "expo", "platforms", "length", "includes"], "mappings": "AAAA;;;;;;;;;;;IAmBaA,6BAA6B,MAA7BA,6BAA6B;IAgF1BC,qBAAqB,MAArBA,qBAAqB;;;yBA7F9B,cAAc;;;;;;;8DACH,OAAO;;;;;;2DAEJ,cAAc;qBACf,oBAAoB;kCACJ,+BAA+B;8BACL,iBAAiB;yCACvC,yCAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGjF,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,AAAsB,AAAC;AAGxE,MAAMH,6BAA6B,SAASI,aAAmB,oBAAA;IACpE,2HAA2H,SACrHC,oBAAoB,GAAkB;QAC1C,IAAIC,IAAG,IAAA,CAACC,iBAAiB,EAAE;YACzBC,IAAG,CAACC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAC9D,OAAO;QACT,CAAC;QACDP,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAEvC,MAAMQ,MAAM,GAAG,MAAM,IAAI,CAACC,2BAA2B,EAAE,AAAC;QAExD,oCAAoC;QACpC,MAAM,IAAI,CAACC,oCAAoC,CAAC;YAAEC,GAAG,EAAEH,MAAM,CAACG,GAAG;SAAE,CAAC,CAAC;IACvE;IAEA,yBAAyB,SACnBF,2BAA2B,GAA2B;QAC1D,MAAMG,MAAM,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAACC,WAAW,CAAC,AAAC;QAE3C,+EAA+E;QAC/E,IAAIf,qBAAqB,CAACa,MAAM,CAACG,UAAU,CAAC,EAAE;YAC5C,2CAA2C;YAC3C,MAAMC,UAAU,GAAGC,IAAAA,OAAoC,EAAA,qCAAA,EAAC,IAAI,CAACH,WAAW,EAAEF,MAAM,CAAC,AAAC;YAClF,MAAM,IAAIM,aAAwB,yBAAA,CAChC,aAAa,EACbC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,gEAAgE,EAAEH,UAAU,CAAC,0BAA0B,CAAC,CAC/G,CAAC;QACJ,CAAC;QAED,OAAOJ,MAAM,CAAC;IAChB;IAEA,yBAAyB,SACnBF,oCAAoC,CAAC,EAAEC,GAAG,CAAA,EAAuB,EAAoB;QACzF,MAAMS,gBAAgB,GAAsB;YAC1C;gBAAEC,IAAI,EAAE,wBAAwB;gBAAEC,GAAG,EAAE,WAAW;aAAE;SACrD,AAAC;QACF,IAAI,CAAClB,IAAG,IAAA,CAACmB,wBAAwB,EAAE;YACjC,4FAA4F;YAC5F,sEAAsE;YACtEH,gBAAgB,CAACI,IAAI,CAAC;gBAAEH,IAAI,EAAE,+BAA+B;gBAAEC,GAAG,EAAE,kBAAkB;aAAE,CAAC,CAAC;QAC5F,CAAC;QAED,MAAMG,OAAO,GAAGC,IAAAA,iBAAmB,oBAAA,EAAC,IAAI,CAACZ,WAAW,EAAEH,GAAG,CAAC,CAACgB,GAAG,AAAC;QAC/D,qDAAqD;QACrD,IAAIF,OAAO,KAAK,SAAS,EAAE;YACzBL,gBAAgB,CAACI,IAAI,CACnB,uFAAuF;YACvF;gBACEH,IAAI,EAAE,mCAAmC;gBACzCC,GAAG,EAAE,sBAAsB;gBAC3BM,GAAG,EAAE,IAAI;aACV,CACF,CAAC;QACJ,OAAO,IAAIH,OAAO,KAAK,OAAO,EAAE;YAC9BL,gBAAgB,CAACI,IAAI,CAAC;gBACpBH,IAAI,EAAE,kCAAkC;gBACxCC,GAAG,EAAE,qBAAqB;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,IAAI;YACF,OAAO,MAAMO,IAAAA,wBAAuB,wBAAA,EAAC,IAAI,CAACf,WAAW,EAAE;gBACrD,6IAA6I;gBAC7IgB,UAAU,EAAE,IAAI;gBAChBC,gBAAgB,EAAE,KAAK;gBACvBpB,GAAG;gBACHqB,cAAc,EAAE,CAAC,kGAAkG,CAAC;gBACpHC,cAAc,EAAEd,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,8HAA8H,CAAC;gBACrJC,gBAAgB;aACjB,CAAC,CAAC;QACL,EAAE,OAAOc,KAAK,EAAE;YACd,wHAAwH;YACxH,IAAI,CAACC,cAAc,EAAE,CAAC;YACtB,MAAMD,KAAK,CAAC;QACd,CAAC;IACH;CACD;AAGM,SAASnC,qBAAqB,CAACgB,UAAyB,EAAW;QAGxDA,GAAgB,EAC5BA,IAAe,EAChBA,IAAe;IAJlB,+EAA+E;IAC/E,MAAMqB,aAAa,GACjBC,KAAK,CAACC,OAAO,CAACvB,UAAU,QAAM,GAAhBA,KAAAA,CAAgB,GAAhBA,CAAAA,GAAgB,GAAhBA,UAAU,CAAEwB,IAAI,SAAA,GAAhBxB,KAAAA,CAAgB,GAAhBA,GAAgB,CAAEyB,SAAS,AAAX,CAAY,IAC1C,CAAC,EAACzB,CAAAA,IAAe,GAAfA,UAAU,CAACwB,IAAI,SAAW,GAA1BxB,KAAAA,CAA0B,GAA1BA,IAAe,CAAEyB,SAAS,CAACC,MAAM,CAAA,IACnC,CAAC1B,CAAAA,CAAAA,IAAe,GAAfA,UAAU,CAACwB,IAAI,SAAW,GAA1BxB,KAAAA,CAA0B,GAA1BA,IAAe,CAAEyB,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAA,AAAC;IAC9C,OAAON,aAAa,CAAC;AACvB,CAAC"}