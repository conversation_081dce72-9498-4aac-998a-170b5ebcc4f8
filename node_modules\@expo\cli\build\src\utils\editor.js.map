{"version": 3, "sources": ["../../../src/utils/editor.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport editors from 'env-editor';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:editor') as typeof console.log;\n\n/** Guess what the default editor is and default to VSCode. */\nexport function guessEditor(): editors.Editor {\n  try {\n    const editor = env.EXPO_EDITOR;\n    if (editor) {\n      debug('Using $EXPO_EDITOR:', editor);\n      return editors.getEditor(editor);\n    }\n    debug('Falling back on $EDITOR:', editor);\n    return editors.defaultEditor();\n  } catch {\n    debug('Falling back on vscode');\n    return editors.getEditor('vscode');\n  }\n}\n\n/** Open a file path in a given editor. */\nexport async function openInEditorAsync(path: string, lineNumber?: number): Promise<boolean> {\n  const editor = guessEditor();\n  const fileReference = lineNumber ? `${path}:${lineNumber}` : path;\n\n  debug(`Opening ${fileReference} in ${editor?.name} (bin: ${editor?.binary}, id: ${editor?.id})`);\n\n  if (editor) {\n    try {\n      await spawnAsync(editor.binary, getEditorArguments(editor, path, lineNumber));\n      return true;\n    } catch (error: any) {\n      debug(\n        `Failed to open ${fileReference} in editor (path: ${path}, binary: ${editor.binary}):`,\n        error\n      );\n    }\n  }\n\n  Log.error(\n    'Could not open editor, you can set it by defining the $EDITOR environment variable with the binary of your editor. (e.g. \"vscode\" or \"atom\")'\n  );\n  return false;\n}\n\nfunction getEditorArguments(editor: editors.Editor, path: string, lineNumber?: number): string[] {\n  if (!lineNumber) {\n    return [path];\n  }\n\n  switch (editor.id) {\n    case 'atom':\n    case 'sublime':\n      return [`${path}:${lineNumber}`];\n\n    case 'emacs':\n    case 'emacsforosx':\n    case 'nano':\n    case 'neovim':\n    case 'vim':\n      return [`+${lineNumber}`, path];\n\n    case 'android-studio':\n    case 'intellij':\n    case 'textmate':\n    case 'webstorm':\n    case 'xcode':\n      return [`--line=${lineNumber}`, path];\n\n    case 'vscode':\n    case 'vscodium':\n      return ['-g', `${path}:${lineNumber}`];\n\n    default:\n      return [path];\n  }\n}\n"], "names": ["guessEditor", "openInEditorAsync", "debug", "require", "editor", "env", "EXPO_EDITOR", "editors", "getEditor", "defaultEditor", "path", "lineNumber", "fileReference", "name", "binary", "id", "spawnAsync", "getEditorArguments", "error", "Log"], "mappings": "AAAA;;;;;;;;;;;IASgBA,WAAW,MAAXA,WAAW;IAgBLC,iBAAiB,MAAjBA,iBAAiB;;;8DAzBhB,mBAAmB;;;;;;;8DACtB,YAAY;;;;;;qBAEZ,OAAO;2DACN,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAGnE,SAASH,WAAW,GAAmB;IAC5C,IAAI;QACF,MAAMI,MAAM,GAAGC,IAAG,IAAA,CAACC,WAAW,AAAC;QAC/B,IAAIF,MAAM,EAAE;YACVF,KAAK,CAAC,qBAAqB,EAAEE,MAAM,CAAC,CAAC;YACrC,OAAOG,UAAO,EAAA,QAAA,CAACC,SAAS,CAACJ,MAAM,CAAC,CAAC;QACnC,CAAC;QACDF,KAAK,CAAC,0BAA0B,EAAEE,MAAM,CAAC,CAAC;QAC1C,OAAOG,UAAO,EAAA,QAAA,CAACE,aAAa,EAAE,CAAC;IACjC,EAAE,OAAM;QACNP,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAChC,OAAOK,UAAO,EAAA,QAAA,CAACC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;AACH,CAAC;AAGM,eAAeP,iBAAiB,CAACS,IAAY,EAAEC,UAAmB,EAAoB;IAC3F,MAAMP,MAAM,GAAGJ,WAAW,EAAE,AAAC;IAC7B,MAAMY,aAAa,GAAGD,UAAU,GAAG,CAAC,EAAED,IAAI,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,GAAGD,IAAI,AAAC;IAElER,KAAK,CAAC,CAAC,QAAQ,EAAEU,aAAa,CAAC,IAAI,EAAER,MAAM,QAAM,GAAZA,KAAAA,CAAY,GAAZA,MAAM,CAAES,IAAI,CAAC,OAAO,EAAET,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAEU,MAAM,CAAC,MAAM,EAAEV,MAAM,QAAI,GAAVA,KAAAA,CAAU,GAAVA,MAAM,CAAEW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjG,IAAIX,MAAM,EAAE;QACV,IAAI;YACF,MAAMY,IAAAA,WAAU,EAAA,QAAA,EAACZ,MAAM,CAACU,MAAM,EAAEG,kBAAkB,CAACb,MAAM,EAAEM,IAAI,EAAEC,UAAU,CAAC,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;QACd,EAAE,OAAOO,KAAK,EAAO;YACnBhB,KAAK,CACH,CAAC,eAAe,EAAEU,aAAa,CAAC,kBAAkB,EAAEF,IAAI,CAAC,UAAU,EAAEN,MAAM,CAACU,MAAM,CAAC,EAAE,CAAC,EACtFI,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;IAEDC,IAAG,CAACD,KAAK,CACP,8IAA8I,CAC/I,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAASD,kBAAkB,CAACb,MAAsB,EAAEM,IAAY,EAAEC,UAAmB,EAAY;IAC/F,IAAI,CAACA,UAAU,EAAE;QACf,OAAO;YAACD,IAAI;SAAC,CAAC;IAChB,CAAC;IAED,OAAQN,MAAM,CAACW,EAAE;QACf,KAAK,MAAM,CAAC;QACZ,KAAK,SAAS;YACZ,OAAO;gBAAC,CAAC,EAAEL,IAAI,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC;aAAC,CAAC;QAEnC,KAAK,OAAO,CAAC;QACb,KAAK,aAAa,CAAC;QACnB,KAAK,MAAM,CAAC;QACZ,KAAK,QAAQ,CAAC;QACd,KAAK,KAAK;YACR,OAAO;gBAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC;gBAAED,IAAI;aAAC,CAAC;QAElC,KAAK,gBAAgB,CAAC;QACtB,KAAK,UAAU,CAAC;QAChB,KAAK,UAAU,CAAC;QAChB,KAAK,UAAU,CAAC;QAChB,KAAK,OAAO;YACV,OAAO;gBAAC,CAAC,OAAO,EAAEC,UAAU,CAAC,CAAC;gBAAED,IAAI;aAAC,CAAC;QAExC,KAAK,QAAQ,CAAC;QACd,KAAK,UAAU;YACb,OAAO;gBAAC,IAAI;gBAAE,CAAC,EAAEA,IAAI,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC;aAAC,CAAC;QAEzC;YACE,OAAO;gBAACD,IAAI;aAAC,CAAC;KACjB;AACH,CAAC"}