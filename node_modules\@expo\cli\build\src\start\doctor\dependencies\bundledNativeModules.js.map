{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/bundledNativeModules.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport chalk from 'chalk';\nimport resolveFrom from 'resolve-from';\n\nimport { getNativeModuleVersionsAsync } from '../../../api/getNativeModuleVersions';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:doctor:dependencies:bundledNativeModules'\n) as typeof console.log;\n\nexport type BundledNativeModules = Record<string, string>;\n\n/**\n * Gets the bundledNativeModules.json for a given SDK version:\n * - Tries to fetch the data from the /sdks/:sdkVersion/native-modules API endpoint.\n * - If the data is missing on the server (it can happen for SDKs that are yet fully released)\n *    or there's a downtime, reads the local .json file from the \"expo\" package.\n * - For UNVERSIONED, returns the local .json file contents.\n */\nexport async function getVersionedNativeModulesAsync(\n  projectRoot: string,\n  sdkVersion: string,\n  options: {\n    skipRemoteVersions?: boolean;\n  } = {}\n): Promise<BundledNativeModules> {\n  if (sdkVersion !== 'UNVERSIONED' && !env.EXPO_OFFLINE && !options.skipRemoteVersions) {\n    try {\n      debug('Fetching bundled native modules from the server...');\n      return await getNativeModuleVersionsAsync(sdkVersion);\n    } catch (error: any) {\n      if (error instanceof CommandError && (error.code === 'OFFLINE' || error.code === 'API')) {\n        Log.warn(\n          chalk`Unable to reach well-known versions endpoint. Using local dependency map {bold expo/bundledNativeModules.json} for version validation`\n        );\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  debug('Fetching bundled native modules from the local JSON file...');\n  return await getBundledNativeModulesAsync(projectRoot);\n}\n\n/**\n * Get the legacy static `bundledNativeModules.json` file\n * that's shipped with the version of `expo` that the project has installed.\n */\nasync function getBundledNativeModulesAsync(projectRoot: string): Promise<BundledNativeModules> {\n  // TODO: Revisit now that this code is in the `expo` package.\n  const bundledNativeModulesPath = resolveFrom.silent(\n    projectRoot,\n    'expo/bundledNativeModules.json'\n  );\n  if (!bundledNativeModulesPath) {\n    Log.log();\n    throw new CommandError(\n      chalk`The dependency map {bold expo/bundledNativeModules.json} cannot be found, please ensure you have the package \"{bold expo}\" installed in your project.`\n    );\n  }\n  return await JsonFile.readAsync<BundledNativeModules>(bundledNativeModulesPath);\n}\n"], "names": ["getVersionedNativeModulesAsync", "debug", "require", "projectRoot", "sdkVersion", "options", "env", "EXPO_OFFLINE", "skipRemoteVersions", "getNativeModuleVersionsAsync", "error", "CommandError", "code", "Log", "warn", "chalk", "getBundledNativeModulesAsync", "bundledNativeModulesPath", "resolveFrom", "silent", "log", "JsonFile", "readAsync"], "mappings": "AAAA;;;;+BAsBsBA,gCAA8B;;aAA9BA,8BAA8B;;;8DAtB/B,iBAAiB;;;;;;;8DACpB,OAAO;;;;;;;8DACD,cAAc;;;;;;yCAEO,sCAAsC;2DAC9D,cAAc;qBACf,oBAAoB;wBACX,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,+CAA+C,CAChD,AAAsB,AAAC;AAWjB,eAAeF,8BAA8B,CAClDG,WAAmB,EACnBC,UAAkB,EAClBC,OAEC,GAAG,EAAE,EACyB;IAC/B,IAAID,UAAU,KAAK,aAAa,IAAI,CAACE,IAAG,IAAA,CAACC,YAAY,IAAI,CAACF,OAAO,CAACG,kBAAkB,EAAE;QACpF,IAAI;YACFP,KAAK,CAAC,oDAAoD,CAAC,CAAC;YAC5D,OAAO,MAAMQ,IAAAA,wBAA4B,6BAAA,EAACL,UAAU,CAAC,CAAC;QACxD,EAAE,OAAOM,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAYC,OAAY,aAAA,IAAI,CAACD,KAAK,CAACE,IAAI,KAAK,SAAS,IAAIF,KAAK,CAACE,IAAI,KAAK,KAAK,CAAC,EAAE;gBACvFC,IAAG,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,qIAAqI,CAAC,CAC7I,CAAC;YACJ,OAAO;gBACL,MAAML,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAEDT,KAAK,CAAC,6DAA6D,CAAC,CAAC;IACrE,OAAO,MAAMe,4BAA4B,CAACb,WAAW,CAAC,CAAC;AACzD,CAAC;AAED;;;CAGC,GACD,eAAea,4BAA4B,CAACb,WAAmB,EAAiC;IAC9F,6DAA6D;IAC7D,MAAMc,wBAAwB,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CACjDhB,WAAW,EACX,gCAAgC,CACjC,AAAC;IACF,IAAI,CAACc,wBAAwB,EAAE;QAC7BJ,IAAG,CAACO,GAAG,EAAE,CAAC;QACV,MAAM,IAAIT,OAAY,aAAA,CACpBI,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,qJAAqJ,CAAC,CAC7J,CAAC;IACJ,CAAC;IACD,OAAO,MAAMM,SAAQ,EAAA,QAAA,CAACC,SAAS,CAAuBL,wBAAwB,CAAC,CAAC;AAClF,CAAC"}