{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/LogBoxSymbolication.ts"], "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { parse, StackFrame as UpstreamStackFrame } from 'stacktrace-parser';\n\nexport type CodeFrame = {\n  content: string;\n  location?: {\n    row: number;\n    column: number;\n    [key: string]: any;\n  } | null;\n  fileName: string;\n\n  // TODO: When React switched to using call stack frames,\n  // we gained the ability to use the collapse flag, but\n  // it is not integrated into the LogBox UI.\n  collapse?: boolean;\n};\n\nexport type SymbolicatedStackTrace = {\n  stack: UpstreamStackFrame[];\n  codeFrame?: CodeFrame;\n};\n\nexport type StackFrame = UpstreamStackFrame & { collapse?: boolean };\n\nconst cache: Map<StackFrame[], Promise<SymbolicatedStackTrace>> = new Map();\n\n/**\n * Sanitize because sometimes, `symbolicateStackTrace` gives us invalid values.\n */\nconst sanitize = ({\n  stack: maybeStack,\n  codeFrame,\n}: SymbolicatedStackTrace): SymbolicatedStackTrace => {\n  if (!Array.isArray(maybeStack)) {\n    throw new Error('Expected stack to be an array.');\n  }\n  const stack: StackFrame[] = [];\n  for (const maybeFrame of maybeStack) {\n    let collapse = false;\n    if ('collapse' in maybeFrame) {\n      if (typeof maybeFrame.collapse !== 'boolean') {\n        throw new Error('Expected stack frame `collapse` to be a boolean.');\n      }\n      collapse = maybeFrame.collapse;\n    }\n    stack.push({\n      arguments: [],\n      column: maybeFrame.column,\n      file: maybeFrame.file,\n      lineNumber: maybeFrame.lineNumber,\n      methodName: maybeFrame.methodName,\n      collapse,\n    });\n  }\n  return { stack, codeFrame };\n};\n\nexport function deleteStack(stack: StackFrame[]): void {\n  cache.delete(stack);\n}\n\nexport function symbolicate(stack: StackFrame[]): Promise<SymbolicatedStackTrace> {\n  let promise = cache.get(stack);\n  if (promise == null) {\n    promise = symbolicateStackTrace(stack).then(sanitize);\n    cache.set(stack, promise);\n  }\n\n  return promise;\n}\n\nasync function symbolicateStackTrace(stack: UpstreamStackFrame[]): Promise<SymbolicatedStackTrace> {\n  const baseUrl =\n    typeof window === 'undefined'\n      ? process.env.EXPO_DEV_SERVER_ORIGIN\n      : window.location.protocol + '//' + window.location.host;\n\n  return fetch(baseUrl + '/symbolicate', {\n    method: 'POST',\n    body: JSON.stringify({ stack }),\n  }).then((res) => res.json());\n}\n\nexport function parseErrorStack(stack?: string): (UpstreamStackFrame & { collapse?: boolean })[] {\n  if (stack == null) {\n    return [];\n  }\n  if (Array.isArray(stack)) {\n    return stack;\n  }\n\n  return parse(stack).map((frame) => {\n    // frame.file will mostly look like `http://localhost:8081/index.bundle?platform=web&dev=true&hot=false`\n    return {\n      ...frame,\n      column: frame.column != null ? frame.column - 1 : null,\n    };\n  });\n}\n"], "names": ["deleteStack", "symbolicate", "parseError<PERSON>tack", "cache", "Map", "sanitize", "stack", "maybeStack", "codeFrame", "Array", "isArray", "Error", "<PERSON><PERSON><PERSON><PERSON>", "collapse", "push", "arguments", "column", "file", "lineNumber", "methodName", "delete", "promise", "get", "symbolicateStackTrace", "then", "set", "baseUrl", "window", "process", "env", "EXPO_DEV_SERVER_ORIGIN", "location", "protocol", "host", "fetch", "method", "body", "JSON", "stringify", "res", "json", "parse", "map", "frame"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;;;;;;;;IAyDgBA,WAAW,MAAXA,WAAW;IAIXC,WAAW,MAAXA,WAAW;IAsBXC,eAAe,MAAfA,eAAe;;;yBAnFyB,mBAAmB;;;;;;AAwB3E,MAAMC,KAAK,GAAuD,IAAIC,GAAG,EAAE,AAAC;AAE5E;;CAEC,GACD,MAAMC,QAAQ,GAAG,CAAC,EAChBC,KAAK,EAAEC,UAAU,CAAA,EACjBC,SAAS,CAAA,EACc,GAA6B;IACpD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;QAC9B,MAAM,IAAII,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IACD,MAAML,KAAK,GAAiB,EAAE,AAAC;IAC/B,KAAK,MAAMM,UAAU,IAAIL,UAAU,CAAE;QACnC,IAAIM,QAAQ,GAAG,KAAK,AAAC;QACrB,IAAI,UAAU,IAAID,UAAU,EAAE;YAC5B,IAAI,OAAOA,UAAU,CAACC,QAAQ,KAAK,SAAS,EAAE;gBAC5C,MAAM,IAAIF,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;YACDE,QAAQ,GAAGD,UAAU,CAACC,QAAQ,CAAC;QACjC,CAAC;QACDP,KAAK,CAACQ,IAAI,CAAC;YACTC,SAAS,EAAE,EAAE;YACbC,MAAM,EAAEJ,UAAU,CAACI,MAAM;YACzBC,IAAI,EAAEL,UAAU,CAACK,IAAI;YACrBC,UAAU,EAAEN,UAAU,CAACM,UAAU;YACjCC,UAAU,EAAEP,UAAU,CAACO,UAAU;YACjCN,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IACD,OAAO;QAAEP,KAAK;QAAEE,SAAS;KAAE,CAAC;AAC9B,CAAC,AAAC;AAEK,SAASR,WAAW,CAACM,KAAmB,EAAQ;IACrDH,KAAK,CAACiB,MAAM,CAACd,KAAK,CAAC,CAAC;AACtB,CAAC;AAEM,SAASL,WAAW,CAACK,KAAmB,EAAmC;IAChF,IAAIe,OAAO,GAAGlB,KAAK,CAACmB,GAAG,CAAChB,KAAK,CAAC,AAAC;IAC/B,IAAIe,OAAO,IAAI,IAAI,EAAE;QACnBA,OAAO,GAAGE,qBAAqB,CAACjB,KAAK,CAAC,CAACkB,IAAI,CAACnB,QAAQ,CAAC,CAAC;QACtDF,KAAK,CAACsB,GAAG,CAACnB,KAAK,EAAEe,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,OAAOA,OAAO,CAAC;AACjB,CAAC;AAED,eAAeE,qBAAqB,CAACjB,KAA2B,EAAmC;IACjG,MAAMoB,OAAO,GACX,OAAOC,MAAM,KAAK,WAAW,GACzBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAClCH,MAAM,CAACI,QAAQ,CAACC,QAAQ,GAAG,IAAI,GAAGL,MAAM,CAACI,QAAQ,CAACE,IAAI,AAAC;IAE7D,OAAOC,KAAK,CAACR,OAAO,GAAG,cAAc,EAAE;QACrCS,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEhC,KAAK;SAAE,CAAC;KAChC,CAAC,CAACkB,IAAI,CAAC,CAACe,GAAG,GAAKA,GAAG,CAACC,IAAI,EAAE,CAAC,CAAC;AAC/B,CAAC;AAEM,SAAStC,eAAe,CAACI,KAAc,EAAmD;IAC/F,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;QACxB,OAAOA,KAAK,CAAC;IACf,CAAC;IAED,OAAOmC,IAAAA,iBAAK,EAAA,MAAA,EAACnC,KAAK,CAAC,CAACoC,GAAG,CAAC,CAACC,KAAK,GAAK;QACjC,wGAAwG;QACxG,OAAO;YACL,GAAGA,KAAK;YACR3B,MAAM,EAAE2B,KAAK,CAAC3B,MAAM,IAAI,IAAI,GAAG2B,KAAK,CAAC3B,MAAM,GAAG,CAAC,GAAG,IAAI;SACvD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}