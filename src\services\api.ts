import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const authApi = axios.create({
  baseURL: 'http://localhost:3000/api',
});

const ordersApi = axios.create({
  baseURL: 'http://localhost:3003/api/v1',
});

// Add token to requests
const setupInterceptors = async () => {
  const token = await AsyncStorage.getItem('token');
  
  if (token) {
    authApi.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    ordersApi.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }
};

// Setup response interceptor for token refresh
authApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('token');
      // Redirect to login
    }
    return Promise.reject(error);
  }
);

ordersApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('token');
      // Redirect to login
    }
    return Promise.reject(error);
  }
);

export { authApi, ordersApi, setupInterceptors };