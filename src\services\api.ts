import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const authApi = axios.create({
  baseURL: 'http://localhost:3000/api',
});

const ordersApi = axios.create({
  baseURL: 'http://localhost:3003/api/v1',
});

// Add token to requests
const setupInterceptors = () => {
  // Setup request interceptor to add token
  const requestInterceptor = async (config: any) => {
    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  };

  authApi.interceptors.request.use(requestInterceptor);
  ordersApi.interceptors.request.use(requestInterceptor);
};

// Setup response interceptor for token refresh
authApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('token');
      // Redirect to login
    }
    return Promise.reject(error);
  }
);

ordersApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('token');
      // Redirect to login
    }
    return Promise.reject(error);
  }
);

export { authApi, ordersApi, setupInterceptors };