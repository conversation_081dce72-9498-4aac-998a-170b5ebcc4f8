{"version": 3, "sources": ["../../../../src/utils/telemetry/Telemetry.ts"], "sourcesContent": ["import crypto from 'node:crypto';\n\nimport { FetchClient } from './clients/FetchClient';\nimport { FetchDetachedClient } from './clients/FetchDetachedClient';\nimport { TelemetryClient, TelemetryClientStrategy, TelemetryRecord } from './types';\nimport { createContext } from './utils/context';\nimport { getAnonymousId } from '../../api/user/UserSettings';\nimport { env } from '../env';\n\nconst debug = require('debug')('expo:telemetry') as typeof console.log;\n\ntype TelemetryOptions = {\n  /** A locally generated ID, untracable to an actual user */\n  anonymousId?: string;\n  /** A locally generated ID, per CLI invocation */\n  sessionId?: string;\n  /** The authenticated user ID, this is used to generate an untracable hash */\n  userId?: string;\n  /** The underlying telemetry strategy to use */\n  strategy?: TelemetryClientStrategy;\n};\n\ntype TelemetryActor = Required<Pick<TelemetryOptions, 'anonymousId' | 'sessionId'>> & {\n  /**\n   * Hashed version of the user ID, untracable to an actual user.\n   * If this value is set to `undefined`, telemetry is considered uninitialized and will wait until its set.\n   * If this value is set to `null`, telemetry is considered initialized without an authenticated user.\n   * If this value is set to a string, telemetry is considered initialized with an authenticated user.\n   */\n  userHash?: string | null;\n};\n\nexport class Telemetry {\n  private context = createContext();\n  private client: TelemetryClient = new FetchDetachedClient();\n  private actor: TelemetryActor;\n\n  /** A list of all events, recorded before the telemetry was fully initialized */\n  private earlyRecords: TelemetryRecord[] = [];\n\n  constructor({\n    anonymousId = getAnonymousId(),\n    sessionId = crypto.randomUUID(),\n    userId,\n    strategy = 'detached',\n  }: TelemetryOptions = {}) {\n    this.actor = { anonymousId, sessionId };\n    this.setStrategy(env.EXPO_NO_TELEMETRY_DETACH ? 'debug' : strategy);\n\n    if (userId) {\n      this.initialize({ userId });\n    }\n  }\n\n  get strategy() {\n    return this.client.strategy;\n  }\n\n  setStrategy(strategy: TelemetryOptions['strategy']) {\n    // Abort when client is already using the correct strategy\n    if (this.client.strategy === strategy) return;\n    // Abort when debugging the telemetry\n    if (env.EXPO_NO_TELEMETRY_DETACH && strategy !== 'debug') return;\n\n    debug('Switching strategy from %s to %s', this.client.strategy, strategy);\n\n    // Load and instantiate the correct client, based on strategy\n    const client = createClientFromStrategy(strategy);\n    // Replace the client, and re-record any pending records\n    this.client.abort().forEach((record) => client.record([record]));\n    this.client = client;\n\n    return this;\n  }\n\n  get isInitialized() {\n    return this.actor.userHash !== undefined;\n  }\n\n  initialize({ userId }: { userId: string | null }) {\n    this.actor.userHash = userId ? hashUserId(userId) : null;\n    this.flushEarlyRecords();\n  }\n\n  private flushEarlyRecords() {\n    if (this.earlyRecords.length) {\n      this.recordInternal(this.earlyRecords);\n      this.earlyRecords = [];\n    }\n  }\n\n  private recordInternal(records: TelemetryRecord[]) {\n    return this.client.record(\n      records.map((record) => ({\n        ...record,\n        type: 'track' as const,\n        sentAt: new Date(),\n        messageId: createMessageId(record),\n        anonymousId: this.actor.anonymousId,\n        userHash: this.actor.userHash,\n        context: {\n          ...this.context,\n          sessionId: this.actor.sessionId,\n          client: { mode: this.client.strategy },\n        },\n      }))\n    );\n  }\n\n  record(record: TelemetryRecord | TelemetryRecord[]) {\n    const records = Array.isArray(record) ? record : [record];\n\n    debug('Recording %d event(s)', records.length);\n\n    if (!this.isInitialized) {\n      this.earlyRecords.push(...records);\n      return;\n    }\n\n    return this.recordInternal(records);\n  }\n\n  flush() {\n    debug('Flushing events...');\n    this.flushEarlyRecords();\n    return this.client.flush();\n  }\n\n  flushOnExit() {\n    this.setStrategy('detached');\n    this.flushEarlyRecords();\n    return this.client.flush();\n  }\n}\n\nfunction createClientFromStrategy(strategy: TelemetryOptions['strategy']) {\n  // When debugging, use the actual Rudderstack client, but lazy load it\n  if (env.EXPO_NO_TELEMETRY_DETACH || strategy === 'debug' || strategy === 'instant') {\n    return new FetchClient();\n  }\n\n  return new FetchDetachedClient();\n}\n\n/** Generate a unique message ID using a random hash and UUID */\nfunction createMessageId(record: TelemetryRecord) {\n  const uuid = crypto.randomUUID();\n  const md5 = crypto.createHash('md5').update(JSON.stringify(record)).digest('hex');\n\n  return `node-${md5}-${uuid}`;\n}\n\n/** Hash the user identifier to make it untracable */\nfunction hashUserId(userId: string) {\n  return crypto.createHash('sha256').update(userId).digest('hex');\n}\n"], "names": ["Telemetry", "debug", "require", "context", "createContext", "client", "FetchDetachedClient", "earlyRecords", "constructor", "anonymousId", "getAnonymousId", "sessionId", "crypto", "randomUUID", "userId", "strategy", "actor", "setStrategy", "env", "EXPO_NO_TELEMETRY_DETACH", "initialize", "createClientFromStrategy", "abort", "for<PERSON>ach", "record", "isInitialized", "userHash", "undefined", "hashUserId", "flush<PERSON><PERSON><PERSON><PERSON><PERSON>ord<PERSON>", "length", "recordInternal", "records", "map", "type", "sentAt", "Date", "messageId", "createMessageId", "mode", "Array", "isArray", "push", "flush", "flushOnExit", "FetchClient", "uuid", "md5", "createHash", "update", "JSON", "stringify", "digest"], "mappings": "AAAA;;;;+BAg<PERSON>a<PERSON>,WAAS;;aAATA,SAAS;;;8DAhCH,aAAa;;;;;;6BAEJ,uBAAuB;qCACf,+BAA+B;yBAErC,iBAAiB;8BAChB,6BAA6B;qBACxC,QAAQ;;;;;;AAE5B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,AAAsB,AAAC;AAuBhE,MAAMF,SAAS;IACpB,AAAQG,OAAO,GAAGC,IAAAA,QAAa,cAAA,GAAE,CAAC;IAClC,AAAQC,MAAM,GAAoB,IAAIC,oBAAmB,oBAAA,EAAE,CAAC;IAG5D,8EAA8E,GAC9E,AAAQC,YAAY,GAAsB,EAAE,CAAC;IAE7CC,YAAY,EACVC,WAAW,EAAGC,IAAAA,aAAc,eAAA,GAAE,CAAA,EAC9BC,SAAS,EAAGC,WAAM,EAAA,QAAA,CAACC,UAAU,EAAE,CAAA,EAC/BC,MAAM,CAAA,EACNC,QAAQ,EAAG,UAAU,CAAA,EACJ,GAAG,EAAE,CAAE;QACxB,IAAI,CAACC,KAAK,GAAG;YAAEP,WAAW;YAAEE,SAAS;SAAE,CAAC;QACxC,IAAI,CAACM,WAAW,CAACC,IAAG,IAAA,CAACC,wBAAwB,GAAG,OAAO,GAAGJ,QAAQ,CAAC,CAAC;QAEpE,IAAID,MAAM,EAAE;YACV,IAAI,CAACM,UAAU,CAAC;gBAAEN,MAAM;aAAE,CAAC,CAAC;QAC9B,CAAC;IACH;QAEIC,QAAQ,GAAG;QACb,OAAO,IAAI,CAACV,MAAM,CAACU,QAAQ,CAAC;IAC9B;IAEAE,WAAW,CAACF,QAAsC,EAAE;QAClD,0DAA0D;QAC1D,IAAI,IAAI,CAACV,MAAM,CAACU,QAAQ,KAAKA,QAAQ,EAAE,OAAO;QAC9C,qCAAqC;QACrC,IAAIG,IAAG,IAAA,CAACC,wBAAwB,IAAIJ,QAAQ,KAAK,OAAO,EAAE,OAAO;QAEjEd,KAAK,CAAC,kCAAkC,EAAE,IAAI,CAACI,MAAM,CAACU,QAAQ,EAAEA,QAAQ,CAAC,CAAC;QAE1E,6DAA6D;QAC7D,MAAMV,MAAM,GAAGgB,wBAAwB,CAACN,QAAQ,CAAC,AAAC;QAClD,wDAAwD;QACxD,IAAI,CAACV,MAAM,CAACiB,KAAK,EAAE,CAACC,OAAO,CAAC,CAACC,MAAM,GAAKnB,MAAM,CAACmB,MAAM,CAAC;gBAACA,MAAM;aAAC,CAAC,CAAC,CAAC;QACjE,IAAI,CAACnB,MAAM,GAAGA,MAAM,CAAC;QAErB,OAAO,IAAI,CAAC;IACd;QAEIoB,aAAa,GAAG;QAClB,OAAO,IAAI,CAACT,KAAK,CAACU,QAAQ,KAAKC,SAAS,CAAC;IAC3C;IAEAP,UAAU,CAAC,EAAEN,MAAM,CAAA,EAA6B,EAAE;QAChD,IAAI,CAACE,KAAK,CAACU,QAAQ,GAAGZ,MAAM,GAAGc,UAAU,CAACd,MAAM,CAAC,GAAG,IAAI,CAAC;QACzD,IAAI,CAACe,iBAAiB,EAAE,CAAC;IAC3B;IAEQA,iBAAiB,GAAG;QAC1B,IAAI,IAAI,CAACtB,YAAY,CAACuB,MAAM,EAAE;YAC5B,IAAI,CAACC,cAAc,CAAC,IAAI,CAACxB,YAAY,CAAC,CAAC;YACvC,IAAI,CAACA,YAAY,GAAG,EAAE,CAAC;QACzB,CAAC;IACH;IAEQwB,cAAc,CAACC,OAA0B,EAAE;QACjD,OAAO,IAAI,CAAC3B,MAAM,CAACmB,MAAM,CACvBQ,OAAO,CAACC,GAAG,CAAC,CAACT,MAAM,GAAK,CAAC;gBACvB,GAAGA,MAAM;gBACTU,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE,IAAIC,IAAI,EAAE;gBAClBC,SAAS,EAAEC,eAAe,CAACd,MAAM,CAAC;gBAClCf,WAAW,EAAE,IAAI,CAACO,KAAK,CAACP,WAAW;gBACnCiB,QAAQ,EAAE,IAAI,CAACV,KAAK,CAACU,QAAQ;gBAC7BvB,OAAO,EAAE;oBACP,GAAG,IAAI,CAACA,OAAO;oBACfQ,SAAS,EAAE,IAAI,CAACK,KAAK,CAACL,SAAS;oBAC/BN,MAAM,EAAE;wBAAEkC,IAAI,EAAE,IAAI,CAAClC,MAAM,CAACU,QAAQ;qBAAE;iBACvC;aACF,CAAC,CAAC,CACJ,CAAC;IACJ;IAEAS,MAAM,CAACA,MAA2C,EAAE;QAClD,MAAMQ,OAAO,GAAGQ,KAAK,CAACC,OAAO,CAACjB,MAAM,CAAC,GAAGA,MAAM,GAAG;YAACA,MAAM;SAAC,AAAC;QAE1DvB,KAAK,CAAC,uBAAuB,EAAE+B,OAAO,CAACF,MAAM,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAACL,aAAa,EAAE;YACvB,IAAI,CAAClB,YAAY,CAACmC,IAAI,IAAIV,OAAO,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QAED,OAAO,IAAI,CAACD,cAAc,CAACC,OAAO,CAAC,CAAC;IACtC;IAEAW,KAAK,GAAG;QACN1C,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC5B,IAAI,CAAC4B,iBAAiB,EAAE,CAAC;QACzB,OAAO,IAAI,CAACxB,MAAM,CAACsC,KAAK,EAAE,CAAC;IAC7B;IAEAC,WAAW,GAAG;QACZ,IAAI,CAAC3B,WAAW,CAAC,UAAU,CAAC,CAAC;QAC7B,IAAI,CAACY,iBAAiB,EAAE,CAAC;QACzB,OAAO,IAAI,CAACxB,MAAM,CAACsC,KAAK,EAAE,CAAC;IAC7B;CACD;AAED,SAAStB,wBAAwB,CAACN,QAAsC,EAAE;IACxE,sEAAsE;IACtE,IAAIG,IAAG,IAAA,CAACC,wBAAwB,IAAIJ,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,SAAS,EAAE;QAClF,OAAO,IAAI8B,YAAW,YAAA,EAAE,CAAC;IAC3B,CAAC;IAED,OAAO,IAAIvC,oBAAmB,oBAAA,EAAE,CAAC;AACnC,CAAC;AAED,8DAA8D,GAC9D,SAASgC,eAAe,CAACd,MAAuB,EAAE;IAChD,MAAMsB,IAAI,GAAGlC,WAAM,EAAA,QAAA,CAACC,UAAU,EAAE,AAAC;IACjC,MAAMkC,GAAG,GAAGnC,WAAM,EAAA,QAAA,CAACoC,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAACC,IAAI,CAACC,SAAS,CAAC3B,MAAM,CAAC,CAAC,CAAC4B,MAAM,CAAC,KAAK,CAAC,AAAC;IAElF,OAAO,CAAC,KAAK,EAAEL,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC;AAC/B,CAAC;AAED,mDAAmD,GACnD,SAASlB,UAAU,CAACd,MAAc,EAAE;IAClC,OAAOF,WAAM,EAAA,QAAA,CAACoC,UAAU,CAAC,QAAQ,CAAC,CAACC,MAAM,CAACnC,MAAM,CAAC,CAACsC,MAAM,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC"}