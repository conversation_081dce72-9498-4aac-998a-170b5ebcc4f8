{"version": 3, "sources": ["../../../../../src/start/server/type-generation/startTypescriptTypeGeneration.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport { Server } from 'metro';\nimport path from 'path';\n\nimport { removeExpoEnvDTS, writeExpoEnvDTS } from './expo-env';\nimport { setupTypedRoutes } from './routes';\nimport { forceRemovalTSConfig, forceUpdateTSConfig } from './tsconfig';\nimport { upsertGitIgnoreContents } from '../../../utils/mergeGitIgnorePaths';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\nimport { ServerLike } from '../BundlerDevServer';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nexport interface TypeScriptTypeGenerationOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  projectRoot: string;\n}\n\nconst debug = require('debug')('expo:typed-routes') as typeof console.log;\n\n/** Setup all requisite features for statically typed routes in Expo Router v2 / SDK +49. */\nexport async function startTypescriptTypeGenerationAsync({\n  metro,\n  projectRoot,\n  server,\n}: TypeScriptTypeGenerationOptions) {\n  const { exp } = getConfig(projectRoot);\n\n  // If typed routes are disabled, remove any files that were added.\n  if (!exp.experiments?.typedRoutes) {\n    debug('Removing typed routes side-effects (experiments.typedRoutes: false)');\n    await Promise.all([forceRemovalTSConfig(projectRoot), removeExpoEnvDTS(projectRoot)]);\n  } else {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    const typesDirectory = path.resolve(dotExpoDir, './types');\n    debug(\n      'Ensuring typed routes side-effects are setup (experiments.typedRoutes: true, typesDirectory: %s)',\n      typesDirectory\n    );\n\n    // Ensure the types directory exists.\n    await fs.mkdir(typesDirectory, { recursive: true });\n\n    await Promise.all([\n      upsertGitIgnoreContents(path.join(projectRoot, '.gitignore'), 'expo-env.d.ts'),\n      writeExpoEnvDTS(projectRoot),\n      forceUpdateTSConfig(projectRoot),\n      setupTypedRoutes({\n        metro,\n        server,\n        typesDirectory,\n        projectRoot,\n        routerDirectory: path.join(\n          projectRoot,\n          getRouterDirectoryModuleIdWithManifest(projectRoot, exp)\n        ),\n        plugin: exp?.extra?.router,\n      }),\n    ]);\n  }\n}\n"], "names": ["startTypescriptTypeGenerationAsync", "debug", "require", "metro", "projectRoot", "server", "exp", "getConfig", "experiments", "typedRoutes", "Promise", "all", "forceRemovalTSConfig", "removeExpoEnvDTS", "dotExpoDir", "ensureDotExpoProjectDirectoryInitialized", "typesDirectory", "path", "resolve", "fs", "mkdir", "recursive", "upsertGitIgnoreContents", "join", "writeExpoEnvDTS", "forceUpdateTSConfig", "setupTypedRoutes", "routerDirectory", "getRouterDirectoryModuleIdWithManifest", "plugin", "extra", "router"], "mappings": "AAAA;;;;+BAsBsBA,oCAAkC;;aAAlCA,kCAAkC;;;yBAtB9B,cAAc;;;;;;;8DACzB,aAAa;;;;;;;8DAEX,MAAM;;;;;;yBAE2B,YAAY;wBAC7B,UAAU;0BACe,YAAY;qCAC9B,oCAAoC;yBACnB,uBAAuB;wBAEzB,iBAAiB;;;;;;AAQxE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAGnE,eAAeF,kCAAkC,CAAC,EACvDG,KAAK,CAAA,EACLC,WAAW,CAAA,EACXC,MAAM,CAAA,EAC0B,EAAE;QAI7BC,GAAe;IAHpB,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAACH,WAAW,CAAC,AAAC;IAEvC,kEAAkE;IAClE,IAAI,CAACE,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACE,WAAW,SAAa,GAA5BF,KAAAA,CAA4B,GAA5BA,GAAe,CAAEG,WAAW,CAAA,EAAE;QACjCR,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAC7E,MAAMS,OAAO,CAACC,GAAG,CAAC;YAACC,IAAAA,SAAoB,qBAAA,EAACR,WAAW,CAAC;YAAES,IAAAA,QAAgB,iBAAA,EAACT,WAAW,CAAC;SAAC,CAAC,CAAC;IACxF,OAAO;YAwBOE,IAAU;QAvBtB,MAAMQ,UAAU,GAAGC,IAAAA,QAAwC,yCAAA,EAACX,WAAW,CAAC,AAAC;QACzE,MAAMY,cAAc,GAAGC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACJ,UAAU,EAAE,SAAS,CAAC,AAAC;QAC3Db,KAAK,CACH,kGAAkG,EAClGe,cAAc,CACf,CAAC;QAEF,qCAAqC;QACrC,MAAMG,SAAE,EAAA,QAAA,CAACC,KAAK,CAACJ,cAAc,EAAE;YAAEK,SAAS,EAAE,IAAI;SAAE,CAAC,CAAC;QAEpD,MAAMX,OAAO,CAACC,GAAG,CAAC;YAChBW,IAAAA,oBAAuB,wBAAA,EAACL,KAAI,EAAA,QAAA,CAACM,IAAI,CAACnB,WAAW,EAAE,YAAY,CAAC,EAAE,eAAe,CAAC;YAC9EoB,IAAAA,QAAe,gBAAA,EAACpB,WAAW,CAAC;YAC5BqB,IAAAA,SAAmB,oBAAA,EAACrB,WAAW,CAAC;YAChCsB,IAAAA,OAAgB,iBAAA,EAAC;gBACfvB,KAAK;gBACLE,MAAM;gBACNW,cAAc;gBACdZ,WAAW;gBACXuB,eAAe,EAAEV,KAAI,EAAA,QAAA,CAACM,IAAI,CACxBnB,WAAW,EACXwB,IAAAA,OAAsC,uCAAA,EAACxB,WAAW,EAAEE,GAAG,CAAC,CACzD;gBACDuB,MAAM,EAAEvB,GAAG,QAAO,GAAVA,KAAAA,CAAU,GAAVA,CAAAA,IAAU,GAAVA,GAAG,CAAEwB,KAAK,SAAA,GAAVxB,KAAAA,CAAU,GAAVA,IAAU,CAAEyB,MAAM,AAAR;aACnB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}