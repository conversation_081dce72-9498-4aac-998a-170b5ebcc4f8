{"version": 3, "sources": ["../../../../../src/start/platforms/ios/ApplePlatformManager.ts"], "sourcesContent": ["import { AppleAppIdResolver } from './AppleAppIdResolver';\nimport { AppleDeviceManager } from './AppleDeviceManager';\nimport { Device } from './simctl';\nimport { AppIdResolver } from '../AppIdResolver';\nimport { BaseOpenInCustomProps, PlatformManager } from '../PlatformManager';\n\n/** Manages launching apps on Apple simulators. */\nexport class ApplePlatformManager extends PlatformManager<Device> {\n  constructor(\n    protected projectRoot: string,\n    protected port: number,\n    options: {\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL. */\n      getExpoGoUrl: () => string;\n      /** Get redirect URL for native disambiguation. */\n      getRedirectUrl: () => string | null;\n      /** Dev Client */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n    }\n  ) {\n    super(projectRoot, {\n      platform: 'ios',\n      ...options,\n      resolveDeviceAsync: AppleDeviceManager.resolveAsync,\n    });\n  }\n\n  async openAsync(\n    options:\n      | { runtime: 'expo' | 'web' }\n      | { runtime: 'custom'; props?: Partial<BaseOpenInCustomProps> },\n    resolveSettings?: Partial<{ shouldPrompt?: boolean; device?: Device }>\n  ): Promise<{ url: string }> {\n    await AppleDeviceManager.assertSystemRequirementsAsync();\n    return super.openAsync(options, resolveSettings);\n  }\n\n  _getAppIdResolver(): AppIdResolver {\n    return new AppleAppIdResolver(this.projectRoot);\n  }\n\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props?: Partial<BaseOpenInCustomProps>\n  ): string {\n    return applicationId;\n  }\n}\n"], "names": ["ApplePlatformManager", "PlatformManager", "constructor", "projectRoot", "port", "options", "platform", "resolveDeviceAsync", "AppleDeviceManager", "resolveAsync", "openAsync", "resolveSettings", "assertSystemRequirementsAsync", "_getAppIdResolver", "AppleAppIdResolver", "_resolveAlternativeLaunchUrl", "applicationId", "props"], "mappings": "AAAA;;;;+BAOaA,sBAAoB;;aAApBA,oBAAoB;;oCAPE,sBAAsB;oCACtB,sBAAsB;iCAGF,oBAAoB;AAGpE,MAAMA,oBAAoB,SAASC,gBAAe,gBAAA;IACvDC,YACYC,WAAmB,EACnBC,IAAY,EACtBC,OASC,CACD;QACA,KAAK,CAACF,WAAW,EAAE;YACjBG,QAAQ,EAAE,KAAK;YACf,GAAGD,OAAO;YACVE,kBAAkB,EAAEC,mBAAkB,mBAAA,CAACC,YAAY;SACpD,CAAC,CAAC;QAjBON,mBAAAA,WAAmB,CAAA;QACnBC,YAAAA,IAAY,CAAA;IAiBxB;UAEMM,SAAS,CACbL,OAEiE,EACjEM,eAAsE,EAC5C;QAC1B,MAAMH,mBAAkB,mBAAA,CAACI,6BAA6B,EAAE,CAAC;QACzD,OAAO,KAAK,CAACF,SAAS,CAACL,OAAO,EAAEM,eAAe,CAAC,CAAC;IACnD;IAEAE,iBAAiB,GAAkB;QACjC,OAAO,IAAIC,mBAAkB,mBAAA,CAAC,IAAI,CAACX,WAAW,CAAC,CAAC;IAClD;IAEAY,4BAA4B,CAC1BC,aAAqB,EACrBC,KAAsC,EAC9B;QACR,OAAOD,aAAa,CAAC;IACvB;CACD"}