{"version": 3, "sources": ["../../../../../src/start/server/middleware/ExpoGoManifestHandlerMiddleware.ts"], "sourcesContent": ["import { ExpoUpdatesManifest } from '@expo/config';\nimport { Updates } from '@expo/config-plugins';\nimport accepts from 'accepts';\nimport crypto from 'crypto';\nimport FormData from 'form-data';\nimport { serializeDictionary, Dictionary } from 'structured-headers';\n\nimport { ManifestMiddleware, ManifestRequestInfo } from './ManifestMiddleware';\nimport { assertRuntimePlatform, parsePlatformHeader } from './resolvePlatform';\nimport { resolveRuntimeVersionWithExpoUpdatesAsync } from './resolveRuntimeVersionWithExpoUpdatesAsync';\nimport { ServerHeaders, ServerRequest } from './server.types';\nimport { getAnonymousIdAsync } from '../../../api/user/UserSettings';\nimport { ANONYMOUS_USERNAME } from '../../../api/user/user';\nimport {\n  CodeSigningInfo,\n  getCodeSigningInfoAsync,\n  signManifestString,\n} from '../../../utils/codesigning';\nimport { CommandError } from '../../../utils/errors';\nimport { stripPort } from '../../../utils/url';\n\nconst debug = require('debug')('expo:start:server:middleware:ExpoGoManifestHandlerMiddleware');\n\nexport enum ResponseContentType {\n  TEXT_PLAIN,\n  APPLICATION_JSON,\n  APPLICATION_EXPO_JSON,\n  MULTIPART_MIXED,\n}\n\ninterface ExpoGoManifestRequestInfo extends ManifestRequestInfo {\n  responseContentType: ResponseContentType;\n  expectSignature: string | null;\n}\n\nexport class ExpoGoManifestHandlerMiddleware extends ManifestMiddleware<ExpoGoManifestRequestInfo> {\n  public getParsedHeaders(req: ServerRequest): ExpoGoManifestRequestInfo {\n    let platform = parsePlatformHeader(req);\n\n    if (!platform) {\n      debug(\n        `No \"expo-platform\" header or \"platform\" query parameter specified. Falling back to \"ios\".`\n      );\n      platform = 'ios';\n    }\n\n    assertRuntimePlatform(platform);\n\n    // Expo Updates clients explicitly accept \"multipart/mixed\" responses while browsers implicitly\n    // accept them with \"accept: */*\". To make it easier to debug manifest responses by visiting their\n    // URLs in a browser, we denote the response as \"text/plain\" if the user agent appears not to be\n    // an Expo Updates client.\n    const accept = accepts(req);\n    const acceptedType = accept.types([\n      'unknown/unknown',\n      'multipart/mixed',\n      'application/json',\n      'application/expo+json',\n      'text/plain',\n    ]);\n\n    let responseContentType;\n    switch (acceptedType) {\n      case 'multipart/mixed':\n        responseContentType = ResponseContentType.MULTIPART_MIXED;\n        break;\n      case 'application/json':\n        responseContentType = ResponseContentType.APPLICATION_JSON;\n        break;\n      case 'application/expo+json':\n        responseContentType = ResponseContentType.APPLICATION_EXPO_JSON;\n        break;\n      default:\n        responseContentType = ResponseContentType.TEXT_PLAIN;\n        break;\n    }\n\n    const expectSignature = req.headers['expo-expect-signature'];\n\n    return {\n      responseContentType,\n      platform,\n      expectSignature: expectSignature ? String(expectSignature) : null,\n      hostname: stripPort(req.headers['host']),\n      protocol: req.headers['x-forwarded-proto'] as 'http' | 'https' | undefined,\n    };\n  }\n\n  private getDefaultResponseHeaders(): ServerHeaders {\n    const headers = new Map<string, number | string | readonly string[]>();\n    // set required headers for Expo Updates manifest specification\n    headers.set('expo-protocol-version', 0);\n    headers.set('expo-sfv-version', 0);\n    headers.set('cache-control', 'private, max-age=0');\n    return headers;\n  }\n\n  public async _getManifestResponseAsync(requestOptions: ExpoGoManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }> {\n    const { exp, hostUri, expoGoConfig, bundleUrl } =\n      await this._resolveProjectSettingsAsync(requestOptions);\n\n    const runtimeVersion =\n      (await resolveRuntimeVersionWithExpoUpdatesAsync({\n        projectRoot: this.projectRoot,\n        platform: requestOptions.platform,\n      })) ??\n      // if expo-updates can't determine runtime version, fall back to calculation from config-plugin.\n      // this happens when expo-updates is installed but runtimeVersion hasn't yet been configured or when\n      // expo-updates is not installed.\n      (await Updates.getRuntimeVersionAsync(\n        this.projectRoot,\n        { ...exp, runtimeVersion: exp.runtimeVersion ?? { policy: 'sdkVersion' } },\n        requestOptions.platform\n      ));\n    if (!runtimeVersion) {\n      throw new CommandError(\n        'MANIFEST_MIDDLEWARE',\n        `Unable to determine runtime version for platform '${requestOptions.platform}'`\n      );\n    }\n\n    const codeSigningInfo = await getCodeSigningInfoAsync(\n      exp,\n      requestOptions.expectSignature,\n      this.options.privateKeyPath\n    );\n\n    const easProjectId = exp.extra?.eas?.projectId as string | undefined | null;\n    const scopeKey = await ExpoGoManifestHandlerMiddleware.getScopeKeyAsync({\n      slug: exp.slug,\n      codeSigningInfo,\n    });\n\n    const expoUpdatesManifest: ExpoUpdatesManifest = {\n      id: crypto.randomUUID(),\n      createdAt: new Date().toISOString(),\n      runtimeVersion,\n      launchAsset: {\n        key: 'bundle',\n        contentType: 'application/javascript',\n        url: bundleUrl,\n      },\n      assets: [], // assets are not used in development\n      metadata: {}, // required for the client to detect that this is an expo-updates manifest\n      extra: {\n        eas: {\n          projectId: easProjectId ?? undefined,\n        },\n        expoClient: {\n          ...exp,\n          hostUri,\n        },\n        expoGo: expoGoConfig,\n        scopeKey,\n      },\n    };\n\n    const stringifiedManifest = JSON.stringify(expoUpdatesManifest);\n\n    let manifestPartHeaders: { 'expo-signature': string } | null = null;\n    let certificateChainBody: string | null = null;\n    if (codeSigningInfo) {\n      const signature = signManifestString(stringifiedManifest, codeSigningInfo);\n      manifestPartHeaders = {\n        'expo-signature': serializeDictionary(\n          convertToDictionaryItemsRepresentation({\n            keyid: codeSigningInfo.keyId,\n            sig: signature,\n            alg: 'rsa-v1_5-sha256',\n          })\n        ),\n      };\n      certificateChainBody = codeSigningInfo.certificateChainForResponse.join('\\n');\n    }\n\n    const headers = this.getDefaultResponseHeaders();\n\n    switch (requestOptions.responseContentType) {\n      case ResponseContentType.MULTIPART_MIXED: {\n        const form = this.getFormData({\n          stringifiedManifest,\n          manifestPartHeaders,\n          certificateChainBody,\n        });\n        headers.set('content-type', `multipart/mixed; boundary=${form.getBoundary()}`);\n        return {\n          body: form.getBuffer().toString(),\n          version: runtimeVersion,\n          headers,\n        };\n      }\n      case ResponseContentType.APPLICATION_EXPO_JSON:\n      case ResponseContentType.APPLICATION_JSON:\n      case ResponseContentType.TEXT_PLAIN: {\n        headers.set(\n          'content-type',\n          ExpoGoManifestHandlerMiddleware.getContentTypeForResponseContentType(\n            requestOptions.responseContentType\n          )\n        );\n        if (manifestPartHeaders) {\n          Object.entries(manifestPartHeaders).forEach(([key, value]) => {\n            headers.set(key, value);\n          });\n        }\n\n        return {\n          body: stringifiedManifest,\n          version: runtimeVersion,\n          headers,\n        };\n      }\n    }\n  }\n\n  private static getContentTypeForResponseContentType(\n    responseContentType: ResponseContentType\n  ): string {\n    switch (responseContentType) {\n      case ResponseContentType.MULTIPART_MIXED:\n        return 'multipart/mixed';\n      case ResponseContentType.APPLICATION_EXPO_JSON:\n        return 'application/expo+json';\n      case ResponseContentType.APPLICATION_JSON:\n        return 'application/json';\n      case ResponseContentType.TEXT_PLAIN:\n        return 'text/plain';\n    }\n  }\n\n  private getFormData({\n    stringifiedManifest,\n    manifestPartHeaders,\n    certificateChainBody,\n  }: {\n    stringifiedManifest: string;\n    manifestPartHeaders: { 'expo-signature': string } | null;\n    certificateChainBody: string | null;\n  }): FormData {\n    const form = new FormData();\n    form.append('manifest', stringifiedManifest, {\n      contentType: 'application/json',\n      header: {\n        ...manifestPartHeaders,\n      },\n    });\n    if (certificateChainBody && certificateChainBody.length > 0) {\n      form.append('certificate_chain', certificateChainBody, {\n        contentType: 'application/x-pem-file',\n      });\n    }\n    return form;\n  }\n\n  private static async getScopeKeyAsync({\n    slug,\n    codeSigningInfo,\n  }: {\n    slug: string;\n    codeSigningInfo: CodeSigningInfo | null;\n  }): Promise<string> {\n    const scopeKeyFromCodeSigningInfo = codeSigningInfo?.scopeKey;\n    if (scopeKeyFromCodeSigningInfo) {\n      return scopeKeyFromCodeSigningInfo;\n    }\n\n    // Log.warn(\n    //   env.EXPO_OFFLINE\n    //     ? 'Using anonymous scope key in manifest for offline mode.'\n    //     : 'Using anonymous scope key in manifest.'\n    // );\n    return await getAnonymousScopeKeyAsync(slug);\n  }\n}\n\nasync function getAnonymousScopeKeyAsync(slug: string): Promise<string> {\n  const userAnonymousIdentifier = await getAnonymousIdAsync();\n  return `@${ANONYMOUS_USERNAME}/${slug}-${userAnonymousIdentifier}`;\n}\n\nfunction convertToDictionaryItemsRepresentation(obj: { [key: string]: string }): Dictionary {\n  return new Map(\n    Object.entries(obj).map(([k, v]) => {\n      return [k, [v, new Map()]];\n    })\n  );\n}\n"], "names": ["ExpoGoManifestHandlerMiddleware", "debug", "require", "ResponseContentType", "TEXT_PLAIN", "APPLICATION_JSON", "APPLICATION_EXPO_JSON", "MULTIPART_MIXED", "ManifestMiddleware", "getParsedHeaders", "req", "platform", "parsePlatformHeader", "assertRuntimePlatform", "accept", "accepts", "acceptedType", "types", "responseContentType", "expectSignature", "headers", "String", "hostname", "stripPort", "protocol", "getDefaultResponseHeaders", "Map", "set", "_getManifestResponseAsync", "requestOptions", "exp", "hostUri", "expoGoConfig", "bundleUrl", "_resolveProjectSettingsAsync", "runtimeVersion", "resolveRuntimeVersionWithExpoUpdatesAsync", "projectRoot", "Updates", "getRuntimeVersionAsync", "policy", "CommandError", "codeSigningInfo", "getCodeSigningInfoAsync", "options", "privateKeyPath", "easProjectId", "extra", "eas", "projectId", "<PERSON><PERSON>ey", "getScopeKeyAsync", "slug", "expoUpdatesManifest", "id", "crypto", "randomUUID", "createdAt", "Date", "toISOString", "launchAsset", "key", "contentType", "url", "assets", "metadata", "undefined", "expoClient", "expoGo", "stringifiedManifest", "JSON", "stringify", "manifestPartHeaders", "certificateChainBody", "signature", "signManifestString", "serializeDictionary", "convertToDictionaryItemsRepresentation", "keyid", "keyId", "sig", "alg", "certificateChainForResponse", "join", "form", "getFormData", "getBoundary", "body", "<PERSON><PERSON><PERSON><PERSON>", "toString", "version", "getContentTypeForResponseContentType", "Object", "entries", "for<PERSON>ach", "value", "FormData", "append", "header", "length", "scopeKeyFromCodeSigningInfo", "getAnonymousScopeKeyAsync", "userAnonymousIdentifier", "getAnonymousIdAsync", "ANONYMOUS_USERNAME", "obj", "map", "k", "v"], "mappings": "AAAA;;;;;;;;;;;;IAmCaA,+BAA+B,MAA/BA,+BAA+B;;;yBAlCpB,sBAAsB;;;;;;;8DAC1B,SAAS;;;;;;;8DACV,QAAQ;;;;;;;8DACN,WAAW;;;;;;;yBACgB,oBAAoB;;;;;;oCAEZ,sBAAsB;iCACnB,mBAAmB;2DACpB,6CAA6C;8BAEnE,gCAAgC;sBACjC,wBAAwB;6BAKpD,4BAA4B;wBACN,uBAAuB;qBAC1B,oBAAoB;;;;;;AAE9C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,8DAA8D,CAAC,AAAC;IAExF,mBAKN;UALWC,mBAAmB;IAAnBA,mBAAmB,CAAnBA,mBAAmB,CAC7BC,YAAU,IAAVA,CAAU,IAAVA,YAAU;IADAD,mBAAmB,CAAnBA,mBAAmB,CAE7BE,kBAAgB,IAAhBA,CAAgB,IAAhBA,kBAAgB;IAFNF,mBAAmB,CAAnBA,mBAAmB,CAG7BG,uBAAqB,IAArBA,CAAqB,IAArBA,uBAAqB;IAHXH,mBAAmB,CAAnBA,mBAAmB,CAI7BI,iBAAe,IAAfA,CAAe,IAAfA,iBAAe;GAJLJ,mBAAmB,KAAnBA,mBAAmB;AAYxB,MAAMH,+BAA+B,SAASQ,mBAAkB,mBAAA;IAC9DC,gBAAgB,CAACC,GAAkB,EAA6B;QACrE,IAAIC,QAAQ,GAAGC,IAAAA,gBAAmB,oBAAA,EAACF,GAAG,CAAC,AAAC;QAExC,IAAI,CAACC,QAAQ,EAAE;YACbV,KAAK,CACH,CAAC,yFAAyF,CAAC,CAC5F,CAAC;YACFU,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAEDE,IAAAA,gBAAqB,sBAAA,EAACF,QAAQ,CAAC,CAAC;QAEhC,+FAA+F;QAC/F,kGAAkG;QAClG,gGAAgG;QAChG,0BAA0B;QAC1B,MAAMG,MAAM,GAAGC,IAAAA,QAAO,EAAA,QAAA,EAACL,GAAG,CAAC,AAAC;QAC5B,MAAMM,YAAY,GAAGF,MAAM,CAACG,KAAK,CAAC;YAChC,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB;YAClB,uBAAuB;YACvB,YAAY;SACb,CAAC,AAAC;QAEH,IAAIC,mBAAmB,AAAC;QACxB,OAAQF,YAAY;YAClB,KAAK,iBAAiB;gBACpBE,mBAAmB,GArCzBX,CAAe,AAqCgD,CAAC;gBAC1D,MAAM;YACR,KAAK,kBAAkB;gBACrBW,mBAAmB,GA1CzBb,CAAgB,AA0CgD,CAAC;gBAC3D,MAAM;YACR,KAAK,uBAAuB;gBAC1Ba,mBAAmB,GA5CzBZ,CAAqB,AA4CgD,CAAC;gBAChE,MAAM;YACR;gBACEY,mBAAmB,GAjDzBd,CAAU,AAiDgD,CAAC;gBACrD,MAAM;SACT;QAED,MAAMe,eAAe,GAAGT,GAAG,CAACU,OAAO,CAAC,uBAAuB,CAAC,AAAC;QAE7D,OAAO;YACLF,mBAAmB;YACnBP,QAAQ;YACRQ,eAAe,EAAEA,eAAe,GAAGE,MAAM,CAACF,eAAe,CAAC,GAAG,IAAI;YACjEG,QAAQ,EAAEC,IAAAA,IAAS,UAAA,EAACb,GAAG,CAACU,OAAO,CAAC,MAAM,CAAC,CAAC;YACxCI,QAAQ,EAAEd,GAAG,CAACU,OAAO,CAAC,mBAAmB,CAAC;SAC3C,CAAC;IACJ;IAEQK,yBAAyB,GAAkB;QACjD,MAAML,OAAO,GAAG,IAAIM,GAAG,EAA+C,AAAC;QACvE,+DAA+D;QAC/DN,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;QACxCP,OAAO,CAACO,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACnCP,OAAO,CAACO,GAAG,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;QACnD,OAAOP,OAAO,CAAC;IACjB;UAEaQ,yBAAyB,CAACC,cAAyC,EAI7E;YA8BoBC,GAAS;QA7B9B,MAAM,EAAEA,GAAG,CAAA,EAAEC,OAAO,CAAA,EAAEC,YAAY,CAAA,EAAEC,SAAS,CAAA,EAAE,GAC7C,MAAM,IAAI,CAACC,4BAA4B,CAACL,cAAc,CAAC,AAAC;QAE1D,MAAMM,cAAc,GAClB,AAAC,MAAMC,IAAAA,0CAAyC,0CAAA,EAAC;YAC/CC,WAAW,EAAE,IAAI,CAACA,WAAW;YAC7B1B,QAAQ,EAAEkB,cAAc,CAAClB,QAAQ;SAClC,CAAC,IACF,gGAAgG;QAChG,oGAAoG;QACpG,iCAAiC;QACjC,CAAC,MAAM2B,cAAO,EAAA,QAAA,CAACC,sBAAsB,CACnC,IAAI,CAACF,WAAW,EAChB;YAAE,GAAGP,GAAG;YAAEK,cAAc,EAAEL,GAAG,CAACK,cAAc,IAAI;gBAAEK,MAAM,EAAE,YAAY;aAAE;SAAE,EAC1EX,cAAc,CAAClB,QAAQ,CACxB,CAAC,AAAC;QACL,IAAI,CAACwB,cAAc,EAAE;YACnB,MAAM,IAAIM,OAAY,aAAA,CACpB,qBAAqB,EACrB,CAAC,kDAAkD,EAAEZ,cAAc,CAAClB,QAAQ,CAAC,CAAC,CAAC,CAChF,CAAC;QACJ,CAAC;QAED,MAAM+B,eAAe,GAAG,MAAMC,IAAAA,YAAuB,wBAAA,EACnDb,GAAG,EACHD,cAAc,CAACV,eAAe,EAC9B,IAAI,CAACyB,OAAO,CAACC,cAAc,CAC5B,AAAC;QAEF,MAAMC,YAAY,GAAGhB,CAAAA,GAAS,GAATA,GAAG,CAACiB,KAAK,SAAK,GAAdjB,KAAAA,CAAc,GAAdA,QAAAA,GAAS,CAAEkB,GAAG,SAAA,GAAdlB,KAAAA,CAAc,QAAEmB,SAAS,AAAX,AAAwC,AAAC;QAC5E,MAAMC,QAAQ,GAAG,MAAMlD,+BAA+B,CAACmD,gBAAgB,CAAC;YACtEC,IAAI,EAAEtB,GAAG,CAACsB,IAAI;YACdV,eAAe;SAChB,CAAC,AAAC;QAEH,MAAMW,mBAAmB,GAAwB;YAC/CC,EAAE,EAAEC,OAAM,EAAA,QAAA,CAACC,UAAU,EAAE;YACvBC,SAAS,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YACnCxB,cAAc;YACdyB,WAAW,EAAE;gBACXC,GAAG,EAAE,QAAQ;gBACbC,WAAW,EAAE,wBAAwB;gBACrCC,GAAG,EAAE9B,SAAS;aACf;YACD+B,MAAM,EAAE,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZlB,KAAK,EAAE;gBACLC,GAAG,EAAE;oBACHC,SAAS,EAAEH,YAAY,IAAIoB,SAAS;iBACrC;gBACDC,UAAU,EAAE;oBACV,GAAGrC,GAAG;oBACNC,OAAO;iBACR;gBACDqC,MAAM,EAAEpC,YAAY;gBACpBkB,QAAQ;aACT;SACF,AAAC;QAEF,MAAMmB,mBAAmB,GAAGC,IAAI,CAACC,SAAS,CAAClB,mBAAmB,CAAC,AAAC;QAEhE,IAAImB,mBAAmB,GAAwC,IAAI,AAAC;QACpE,IAAIC,oBAAoB,GAAkB,IAAI,AAAC;QAC/C,IAAI/B,eAAe,EAAE;YACnB,MAAMgC,SAAS,GAAGC,IAAAA,YAAkB,mBAAA,EAACN,mBAAmB,EAAE3B,eAAe,CAAC,AAAC;YAC3E8B,mBAAmB,GAAG;gBACpB,gBAAgB,EAAEI,IAAAA,kBAAmB,EAAA,oBAAA,EACnCC,sCAAsC,CAAC;oBACrCC,KAAK,EAAEpC,eAAe,CAACqC,KAAK;oBAC5BC,GAAG,EAAEN,SAAS;oBACdO,GAAG,EAAE,iBAAiB;iBACvB,CAAC,CACH;aACF,CAAC;YACFR,oBAAoB,GAAG/B,eAAe,CAACwC,2BAA2B,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,CAAC;QAED,MAAM/D,OAAO,GAAG,IAAI,CAACK,yBAAyB,EAAE,AAAC;QAEjD,OAAQI,cAAc,CAACX,mBAAmB;YACxC,KA3JJX,CAAe;gBA2J+B;oBACxC,MAAM6E,IAAI,GAAG,IAAI,CAACC,WAAW,CAAC;wBAC5BhB,mBAAmB;wBACnBG,mBAAmB;wBACnBC,oBAAoB;qBACrB,CAAC,AAAC;oBACHrD,OAAO,CAACO,GAAG,CAAC,cAAc,EAAE,CAAC,0BAA0B,EAAEyD,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC/E,OAAO;wBACLC,IAAI,EAAEH,IAAI,CAACI,SAAS,EAAE,CAACC,QAAQ,EAAE;wBACjCC,OAAO,EAAEvD,cAAc;wBACvBf,OAAO;qBACR,CAAC;gBACJ,CAAC;YACD,KAzKJd,CAAqB,CAyK8B;YAC/C,KA3KJD,CAAgB,CA2K8B;YAC1C,KA7KJD,CAAU;gBA6K+B;oBACnCgB,OAAO,CAACO,GAAG,CACT,cAAc,EACd3B,+BAA+B,CAAC2F,oCAAoC,CAClE9D,cAAc,CAACX,mBAAmB,CACnC,CACF,CAAC;oBACF,IAAIsD,mBAAmB,EAAE;wBACvBoB,MAAM,CAACC,OAAO,CAACrB,mBAAmB,CAAC,CAACsB,OAAO,CAAC,CAAC,CAACjC,GAAG,EAAEkC,KAAK,CAAC,GAAK;4BAC5D3E,OAAO,CAACO,GAAG,CAACkC,GAAG,EAAEkC,KAAK,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC;oBACL,CAAC;oBAED,OAAO;wBACLR,IAAI,EAAElB,mBAAmB;wBACzBqB,OAAO,EAAEvD,cAAc;wBACvBf,OAAO;qBACR,CAAC;gBACJ,CAAC;SACF;IACH;WAEeuE,oCAAoC,CACjDzE,mBAAwC,EAChC;QACR,OAAQA,mBAAmB;YACzB,KApMJX,CAAe;gBAqMT,OAAO,iBAAiB,CAAC;YAC3B,KAvMJD,CAAqB;gBAwMf,OAAO,uBAAuB,CAAC;YACjC,KA1MJD,CAAgB;gBA2MV,OAAO,kBAAkB,CAAC;YAC5B,KA7MJD,CAAU;gBA8MJ,OAAO,YAAY,CAAC;SACvB;IACH;IAEQiF,WAAW,CAAC,EAClBhB,mBAAmB,CAAA,EACnBG,mBAAmB,CAAA,EACnBC,oBAAoB,CAAA,EAKrB,EAAY;QACX,MAAMW,IAAI,GAAG,IAAIY,CAAAA,SAAQ,EAAA,CAAA,QAAA,EAAE,AAAC;QAC5BZ,IAAI,CAACa,MAAM,CAAC,UAAU,EAAE5B,mBAAmB,EAAE;YAC3CP,WAAW,EAAE,kBAAkB;YAC/BoC,MAAM,EAAE;gBACN,GAAG1B,mBAAmB;aACvB;SACF,CAAC,CAAC;QACH,IAAIC,oBAAoB,IAAIA,oBAAoB,CAAC0B,MAAM,GAAG,CAAC,EAAE;YAC3Df,IAAI,CAACa,MAAM,CAAC,mBAAmB,EAAExB,oBAAoB,EAAE;gBACrDX,WAAW,EAAE,wBAAwB;aACtC,CAAC,CAAC;QACL,CAAC;QACD,OAAOsB,IAAI,CAAC;IACd;iBAEqBjC,gBAAgB,CAAC,EACpCC,IAAI,CAAA,EACJV,eAAe,CAAA,EAIhB,EAAmB;QAClB,MAAM0D,2BAA2B,GAAG1D,eAAe,QAAU,GAAzBA,KAAAA,CAAyB,GAAzBA,eAAe,CAAEQ,QAAQ,AAAC;QAC9D,IAAIkD,2BAA2B,EAAE;YAC/B,OAAOA,2BAA2B,CAAC;QACrC,CAAC;QAED,YAAY;QACZ,qBAAqB;QACrB,kEAAkE;QAClE,iDAAiD;QACjD,KAAK;QACL,OAAO,MAAMC,yBAAyB,CAACjD,IAAI,CAAC,CAAC;IAC/C;CACD;AAED,eAAeiD,yBAAyB,CAACjD,IAAY,EAAmB;IACtE,MAAMkD,uBAAuB,GAAG,MAAMC,IAAAA,aAAmB,oBAAA,GAAE,AAAC;IAC5D,OAAO,CAAC,CAAC,EAAEC,KAAkB,mBAAA,CAAC,CAAC,EAAEpD,IAAI,CAAC,CAAC,EAAEkD,uBAAuB,CAAC,CAAC,CAAC;AACrE,CAAC;AAED,SAASzB,sCAAsC,CAAC4B,GAA8B,EAAc;IAC1F,OAAO,IAAI/E,GAAG,CACZkE,MAAM,CAACC,OAAO,CAACY,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAK;QAClC,OAAO;YAACD,CAAC;YAAE;gBAACC,CAAC;gBAAE,IAAIlF,GAAG,EAAE;aAAC;SAAC,CAAC;IAC7B,CAAC,CAAC,CACH,CAAC;AACJ,CAAC"}