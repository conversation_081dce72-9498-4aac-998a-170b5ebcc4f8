{"version": 3, "sources": ["../../../src/start/resolveOptions.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { canResolveDevClient, hasDirectDevClientDependency } from './detectDevClient';\nimport { Log } from '../log';\nimport { envIsWebcontainer } from '../utils/env';\nimport { AbortCommandError, CommandError } from '../utils/errors';\nimport { resolvePortAsync } from '../utils/port';\n\nexport type Options = {\n  privateKeyPath: string | null;\n  android: boolean;\n  web: boolean;\n  ios: boolean;\n  offline: boolean;\n  clear: boolean;\n  dev: boolean;\n  https: boolean;\n  maxWorkers: number;\n  port: number;\n  /** Should instruct the bundler to create minified bundles. */\n  minify: boolean;\n  devClient: boolean;\n  scheme: string | null;\n  host: 'localhost' | 'lan' | 'tunnel';\n};\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  if (args['--dev-client'] && args['--go']) {\n    throw new CommandError('BAD_ARGS', 'Cannot use both --dev-client and --go together.');\n  }\n  const host = resolveHostType({\n    host: args['--host'],\n    offline: args['--offline'],\n    lan: args['--lan'],\n    localhost: args['--localhost'],\n    tunnel: args['--tunnel'],\n  });\n\n  // User can force the default target by passing either `--dev-client` or `--go`. They can also\n  // swap between them during development by pressing `s`.\n  const isUserDefinedDevClient =\n    !!args['--dev-client'] || (args['--go'] == null ? false : !args['--go']);\n\n  // If the user didn't specify `--dev-client` or `--go` we check if they have the dev client package\n  // in their package.json.\n  const isAutoDevClient =\n    args['--dev-client'] == null &&\n    args['--go'] == null &&\n    hasDirectDevClientDependency(projectRoot);\n\n  const isDevClient = isAutoDevClient || isUserDefinedDevClient;\n\n  const scheme = await resolveSchemeAsync(projectRoot, {\n    scheme: args['--scheme'],\n    devClient: isDevClient,\n  });\n\n  return {\n    privateKeyPath: args['--private-key-path'] ?? null,\n\n    android: !!args['--android'],\n    web: !!args['--web'],\n    ios: !!args['--ios'],\n    offline: !!args['--offline'],\n\n    clear: !!args['--clear'],\n    dev: !args['--no-dev'],\n    https: !!args['--https'],\n    maxWorkers: args['--max-workers'],\n    port: args['--port'],\n    minify: !!args['--minify'],\n\n    devClient: isDevClient,\n\n    scheme,\n    host,\n  };\n}\n\nexport async function resolveSchemeAsync(\n  projectRoot: string,\n  options: { scheme?: string; devClient?: boolean }\n): Promise<string | null> {\n  if (typeof options.scheme === 'string') {\n    // Use the custom scheme\n    return options.scheme ?? null;\n  }\n\n  if (options.devClient || canResolveDevClient(projectRoot)) {\n    const { getOptionalDevClientSchemeAsync } =\n      require('../utils/scheme') as typeof import('../utils/scheme');\n    // Attempt to find the scheme or warn the user how to setup a custom scheme\n    const resolvedScheme = await getOptionalDevClientSchemeAsync(projectRoot);\n    if (!resolvedScheme.scheme) {\n      if (resolvedScheme.resolution === 'shared') {\n        // This can happen if one of the native projects has no URI schemes defined in it.\n        // Normally, this should never happen.\n        Log.warn(\n          chalk`Could not find a shared URI scheme for the dev client between the local {bold /ios} and {bold /android} directories. App launches (QR code, interstitial, terminal keys) may not work as expected. You can configure a custom scheme using the {bold --scheme} option, or by running {bold npx expo prebuild} to regenerate the native directories with URI schemes.`\n        );\n      } else if (['ios', 'android'].includes(resolvedScheme.resolution)) {\n        Log.warn(\n          chalk`The {bold /${resolvedScheme.resolution}} project does not contain any URI schemes. Expo CLI will not be able to use links to launch the project. You can configure a custom URI scheme using the {bold --scheme} option.`\n        );\n      }\n    }\n    return resolvedScheme.scheme;\n  } else {\n    // Ensure this is reset when users don't use `--scheme`, `--dev-client` and don't have the `expo-dev-client` package installed.\n    return null;\n  }\n}\n\n/** Resolve and assert host type options. */\nexport function resolveHostType(options: {\n  host?: string;\n  offline?: boolean;\n  lan?: boolean;\n  localhost?: boolean;\n  tunnel?: boolean;\n}): 'lan' | 'tunnel' | 'localhost' {\n  if (\n    [options.offline, options.host, options.lan, options.localhost, options.tunnel].filter((i) => i)\n      .length > 1\n  ) {\n    throw new CommandError(\n      'BAD_ARGS',\n      'Specify at most one of: --offline, --host, --tunnel, --lan, --localhost'\n    );\n  }\n\n  if (options.offline) {\n    // Force `lan` in offline mode.\n    return 'lan';\n  } else if (options.host) {\n    assert.match(options.host, /^(lan|tunnel|localhost)$/);\n    return options.host as 'lan' | 'tunnel' | 'localhost';\n  } else if (options.tunnel) {\n    return 'tunnel';\n  } else if (options.lan) {\n    return 'lan';\n  } else if (options.localhost) {\n    return 'localhost';\n  }\n\n  // If no option is provided, and we are running in Stackblitz, enable tunnel by default\n  if (envIsWebcontainer()) {\n    return 'tunnel';\n  }\n\n  return 'lan';\n}\n\n/** Resolve the port options for all supported bundlers. */\nexport async function resolvePortsAsync(\n  projectRoot: string,\n  options: Partial<Pick<Options, 'port' | 'devClient'>>,\n  settings: { webOnly?: boolean }\n) {\n  const multiBundlerSettings: { webpackPort?: number; metroPort?: number } = {};\n\n  if (settings.webOnly) {\n    const webpackPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      // Default web port\n      fallbackPort: 19006,\n    });\n    if (!webpackPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.webpackPort = webpackPort;\n  } else {\n    const fallbackPort = process.env.RCT_METRO_PORT\n      ? parseInt(process.env.RCT_METRO_PORT, 10)\n      : 8081;\n    const metroPort = await resolvePortAsync(projectRoot, {\n      defaultPort: options.port,\n      fallbackPort,\n    });\n    if (!metroPort) {\n      throw new AbortCommandError();\n    }\n    multiBundlerSettings.metroPort = metroPort;\n  }\n\n  return multiBundlerSettings;\n}\n"], "names": ["resolveOptionsAsync", "resolveSchemeAsync", "resolveHostType", "resolvePortsAsync", "projectRoot", "args", "CommandError", "host", "offline", "lan", "localhost", "tunnel", "isUserDefinedDevClient", "isAutoDevClient", "hasDirectDevClientDependency", "isDevClient", "scheme", "devClient", "privateKeyPath", "android", "web", "ios", "clear", "dev", "https", "maxWorkers", "port", "minify", "options", "canResolveDevClient", "getOptionalDevClientSchemeAsync", "require", "resolvedScheme", "resolution", "Log", "warn", "chalk", "includes", "filter", "i", "length", "assert", "match", "envIsWebcontainer", "settings", "multiBundlerSettings", "webOnly", "webpackPort", "resolvePortAsync", "defaultPort", "fallback<PERSON>ort", "AbortCommandError", "process", "env", "RCT_METRO_PORT", "parseInt", "metroPort"], "mappings": "AAAA;;;;;;;;;;;IA2BsBA,mBAAmB,MAAnBA,mBAAmB;IAqDnBC,kBAAkB,MAAlBA,kBAAkB;IAmCxBC,eAAe,MAAfA,eAAe;IAwCTC,iBAAiB,MAAjBA,iBAAiB;;;8DA3JpB,QAAQ;;;;;;;8DACT,OAAO;;;;;;iCAEyC,mBAAmB;qBACjE,QAAQ;qBACM,cAAc;wBACA,iBAAiB;sBAChC,eAAe;;;;;;AAoBzC,eAAeH,mBAAmB,CAACI,WAAmB,EAAEC,IAAS,EAAoB;IAC1F,IAAIA,IAAI,CAAC,cAAc,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;QACxC,MAAM,IAAIC,OAAY,aAAA,CAAC,UAAU,EAAE,iDAAiD,CAAC,CAAC;IACxF,CAAC;IACD,MAAMC,IAAI,GAAGL,eAAe,CAAC;QAC3BK,IAAI,EAAEF,IAAI,CAAC,QAAQ,CAAC;QACpBG,OAAO,EAAEH,IAAI,CAAC,WAAW,CAAC;QAC1BI,GAAG,EAAEJ,IAAI,CAAC,OAAO,CAAC;QAClBK,SAAS,EAAEL,IAAI,CAAC,aAAa,CAAC;QAC9BM,MAAM,EAAEN,IAAI,CAAC,UAAU,CAAC;KACzB,CAAC,AAAC;IAEH,8FAA8F;IAC9F,wDAAwD;IACxD,MAAMO,sBAAsB,GAC1B,CAAC,CAACP,IAAI,CAAC,cAAc,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,CAACA,IAAI,CAAC,MAAM,CAAC,CAAC,AAAC;IAE3E,mGAAmG;IACnG,yBAAyB;IACzB,MAAMQ,eAAe,GACnBR,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,IAC5BA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IACpBS,IAAAA,gBAA4B,6BAAA,EAACV,WAAW,CAAC,AAAC;IAE5C,MAAMW,WAAW,GAAGF,eAAe,IAAID,sBAAsB,AAAC;IAE9D,MAAMI,MAAM,GAAG,MAAMf,kBAAkB,CAACG,WAAW,EAAE;QACnDY,MAAM,EAAEX,IAAI,CAAC,UAAU,CAAC;QACxBY,SAAS,EAAEF,WAAW;KACvB,CAAC,AAAC;IAEH,OAAO;QACLG,cAAc,EAAEb,IAAI,CAAC,oBAAoB,CAAC,IAAI,IAAI;QAElDc,OAAO,EAAE,CAAC,CAACd,IAAI,CAAC,WAAW,CAAC;QAC5Be,GAAG,EAAE,CAAC,CAACf,IAAI,CAAC,OAAO,CAAC;QACpBgB,GAAG,EAAE,CAAC,CAAChB,IAAI,CAAC,OAAO,CAAC;QACpBG,OAAO,EAAE,CAAC,CAACH,IAAI,CAAC,WAAW,CAAC;QAE5BiB,KAAK,EAAE,CAAC,CAACjB,IAAI,CAAC,SAAS,CAAC;QACxBkB,GAAG,EAAE,CAAClB,IAAI,CAAC,UAAU,CAAC;QACtBmB,KAAK,EAAE,CAAC,CAACnB,IAAI,CAAC,SAAS,CAAC;QACxBoB,UAAU,EAAEpB,IAAI,CAAC,eAAe,CAAC;QACjCqB,IAAI,EAAErB,IAAI,CAAC,QAAQ,CAAC;QACpBsB,MAAM,EAAE,CAAC,CAACtB,IAAI,CAAC,UAAU,CAAC;QAE1BY,SAAS,EAAEF,WAAW;QAEtBC,MAAM;QACNT,IAAI;KACL,CAAC;AACJ,CAAC;AAEM,eAAeN,kBAAkB,CACtCG,WAAmB,EACnBwB,OAAiD,EACzB;IACxB,IAAI,OAAOA,OAAO,CAACZ,MAAM,KAAK,QAAQ,EAAE;QACtC,wBAAwB;QACxB,OAAOY,OAAO,CAACZ,MAAM,IAAI,IAAI,CAAC;IAChC,CAAC;IAED,IAAIY,OAAO,CAACX,SAAS,IAAIY,IAAAA,gBAAmB,oBAAA,EAACzB,WAAW,CAAC,EAAE;QACzD,MAAM,EAAE0B,+BAA+B,CAAA,EAAE,GACvCC,OAAO,CAAC,iBAAiB,CAAC,AAAoC,AAAC;QACjE,2EAA2E;QAC3E,MAAMC,cAAc,GAAG,MAAMF,+BAA+B,CAAC1B,WAAW,CAAC,AAAC;QAC1E,IAAI,CAAC4B,cAAc,CAAChB,MAAM,EAAE;YAC1B,IAAIgB,cAAc,CAACC,UAAU,KAAK,QAAQ,EAAE;gBAC1C,kFAAkF;gBAClF,sCAAsC;gBACtCC,IAAG,IAAA,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,oWAAoW,CAAC,CAC5W,CAAC;YACJ,OAAO,IAAI;gBAAC,KAAK;gBAAE,SAAS;aAAC,CAACC,QAAQ,CAACL,cAAc,CAACC,UAAU,CAAC,EAAE;gBACjEC,IAAG,IAAA,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,WAAW,EAAEJ,cAAc,CAACC,UAAU,CAAC,iLAAiL,CAAC,CAChO,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAOD,cAAc,CAAChB,MAAM,CAAC;IAC/B,OAAO;QACL,+HAA+H;QAC/H,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAGM,SAASd,eAAe,CAAC0B,OAM/B,EAAkC;IACjC,IACE;QAACA,OAAO,CAACpB,OAAO;QAAEoB,OAAO,CAACrB,IAAI;QAAEqB,OAAO,CAACnB,GAAG;QAAEmB,OAAO,CAAClB,SAAS;QAAEkB,OAAO,CAACjB,MAAM;KAAC,CAAC2B,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAAC,CAC7FC,MAAM,GAAG,CAAC,EACb;QACA,MAAM,IAAIlC,OAAY,aAAA,CACpB,UAAU,EACV,yEAAyE,CAC1E,CAAC;IACJ,CAAC;IAED,IAAIsB,OAAO,CAACpB,OAAO,EAAE;QACnB,+BAA+B;QAC/B,OAAO,KAAK,CAAC;IACf,OAAO,IAAIoB,OAAO,CAACrB,IAAI,EAAE;QACvBkC,OAAM,EAAA,QAAA,CAACC,KAAK,CAACd,OAAO,CAACrB,IAAI,6BAA6B,CAAC;QACvD,OAAOqB,OAAO,CAACrB,IAAI,CAAmC;IACxD,OAAO,IAAIqB,OAAO,CAACjB,MAAM,EAAE;QACzB,OAAO,QAAQ,CAAC;IAClB,OAAO,IAAIiB,OAAO,CAACnB,GAAG,EAAE;QACtB,OAAO,KAAK,CAAC;IACf,OAAO,IAAImB,OAAO,CAAClB,SAAS,EAAE;QAC5B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,uFAAuF;IACvF,IAAIiC,IAAAA,IAAiB,kBAAA,GAAE,EAAE;QACvB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAGM,eAAexC,iBAAiB,CACrCC,WAAmB,EACnBwB,OAAqD,EACrDgB,QAA+B,EAC/B;IACA,MAAMC,oBAAoB,GAAiD,EAAE,AAAC;IAE9E,IAAID,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,WAAW,GAAG,MAAMC,IAAAA,KAAgB,iBAAA,EAAC5C,WAAW,EAAE;YACtD6C,WAAW,EAAErB,OAAO,CAACF,IAAI;YACzB,mBAAmB;YACnBwB,YAAY,EAAE,KAAK;SACpB,CAAC,AAAC;QACH,IAAI,CAACH,WAAW,EAAE;YAChB,MAAM,IAAII,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QACDN,oBAAoB,CAACE,WAAW,GAAGA,WAAW,CAAC;IACjD,OAAO;QACL,MAAMG,YAAY,GAAGE,OAAO,CAACC,GAAG,CAACC,cAAc,GAC3CC,QAAQ,CAACH,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE,EAAE,CAAC,GACxC,IAAI,AAAC;QACT,MAAME,SAAS,GAAG,MAAMR,IAAAA,KAAgB,iBAAA,EAAC5C,WAAW,EAAE;YACpD6C,WAAW,EAAErB,OAAO,CAACF,IAAI;YACzBwB,YAAY;SACb,CAAC,AAAC;QACH,IAAI,CAACM,SAAS,EAAE;YACd,MAAM,IAAIL,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QACDN,oBAAoB,CAACW,SAAS,GAAGA,SAAS,CAAC;IAC7C,CAAC;IAED,OAAOX,oBAAoB,CAAC;AAC9B,CAAC"}