{"version": 3, "sources": ["../../../../src/start/interface/commandsTable.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport qrcode from 'qrcode-terminal';\nimport wrapAnsi from 'wrap-ansi';\n\nimport * as Log from '../../log';\n\nexport const BLT = '\\u203A';\n\nexport type StartOptions = {\n  isWebSocketsEnabled?: boolean;\n  devClient?: boolean;\n  reset?: boolean;\n  nonPersistent?: boolean;\n  maxWorkers?: number;\n  platforms?: ExpoConfig['platforms'];\n};\n\nexport const printHelp = (): void => {\n  logCommandsTable([{ key: '?', msg: 'show all commands' }]);\n};\n\n/** Print the world famous 'Expo QR Code'. */\nexport function printQRCode(url: string) {\n  qrcode.generate(url, { small: true }, (code) => Log.log(code));\n}\n\nexport const getTerminalColumns = () => process.stdout.columns || 80;\nexport const printItem = (text: string): string =>\n  `${BLT} ` + wrapAnsi(text, getTerminalColumns()).trimStart();\n\nexport function printUsage(\n  options: Pick<StartOptions, 'devClient' | 'isWebSocketsEnabled' | 'platforms'>,\n  { verbose }: { verbose: boolean }\n) {\n  const isMac = process.platform === 'darwin';\n\n  const { platforms = ['ios', 'android', 'web'] } = options;\n\n  const isAndroidDisabled = !platforms.includes('android');\n  const isIosDisabled = !platforms.includes('ios');\n  const isWebDisable = !platforms.includes('web');\n\n  const switchMsg = `switch to ${options.devClient === false ? 'development build' : 'Expo Go'}`;\n  const target = options.devClient === false ? `Expo Go` : 'development build';\n\n  Log.log();\n  Log.log(printItem(chalk`Using {cyan ${target}}`));\n\n  if (verbose) {\n    logCommandsTable([\n      { key: 's', msg: switchMsg },\n      {},\n      { key: 'a', msg: 'open Android', disabled: isAndroidDisabled },\n      { key: 'shift+a', msg: 'select an Android device or emulator', disabled: isAndroidDisabled },\n      isMac && { key: 'i', msg: 'open iOS simulator', disabled: isIosDisabled },\n      isMac && { key: 'shift+i', msg: 'select an iOS simulator', disabled: isIosDisabled },\n      { key: 'w', msg: 'open web', disabled: isWebDisable },\n      {},\n      { key: 'r', msg: 'reload app' },\n      !!options.isWebSocketsEnabled && { key: 'j', msg: 'open debugger' },\n      !!options.isWebSocketsEnabled && { key: 'm', msg: 'toggle menu' },\n      !!options.isWebSocketsEnabled && { key: 'shift+m', msg: 'more tools' },\n      { key: 'o', msg: 'open project code in your editor' },\n      { key: 'c', msg: 'show project QR' },\n      {},\n    ]);\n  } else {\n    logCommandsTable([\n      { key: 's', msg: switchMsg },\n      {},\n      { key: 'a', msg: 'open Android', disabled: isAndroidDisabled },\n      isMac && { key: 'i', msg: 'open iOS simulator', disabled: isIosDisabled },\n      { key: 'w', msg: 'open web', disabled: isWebDisable },\n      {},\n      { key: 'j', msg: 'open debugger' },\n      { key: 'r', msg: 'reload app' },\n      !!options.isWebSocketsEnabled && { key: 'm', msg: 'toggle menu' },\n      !!options.isWebSocketsEnabled && { key: 'shift+m', msg: 'more tools' },\n      { key: 'o', msg: 'open project code in your editor' },\n      {},\n    ]);\n  }\n}\n\nfunction logCommandsTable(\n  ui: (false | { key?: string; msg?: string; status?: string; disabled?: boolean })[]\n) {\n  Log.log(\n    ui\n      .filter(Boolean)\n      // @ts-ignore: filter doesn't work\n      .map(({ key, msg, status, disabled }) => {\n        if (!key) return '';\n        let view = `${BLT} `;\n        if (key.length === 1) view += 'Press ';\n        view += chalk`{bold ${key}} {dim │} `;\n        view += msg;\n        if (status) {\n          view += ` ${chalk.dim(`(${chalk.italic(status)})`)}`;\n        }\n        if (disabled) {\n          view = chalk.dim(view);\n        }\n        return view;\n      })\n      .join('\\n')\n  );\n}\n"], "names": ["BLT", "printHelp", "printQRCode", "getTerminalColumns", "printItem", "printUsage", "logCommandsTable", "key", "msg", "url", "qrcode", "generate", "small", "code", "Log", "log", "process", "stdout", "columns", "text", "wrapAnsi", "trimStart", "options", "verbose", "isMac", "platform", "platforms", "isAndroidDisabled", "includes", "isIosDisabled", "isWebDisable", "switchMsg", "devClient", "target", "chalk", "disabled", "isWebSocketsEnabled", "ui", "filter", "Boolean", "map", "status", "view", "length", "dim", "italic", "join"], "mappings": "AAAA;;;;;;;;;;;IAOaA,GAAG,MAAHA,GAAG;IAWHC,SAAS,MAATA,SAAS;IAKNC,WAAW,MAAXA,WAAW;IAIdC,kBAAkB,MAAlBA,kBAAkB;IAClBC,SAAS,MAATA,SAAS;IAGNC,UAAU,MAAVA,UAAU;;;8DA9BR,OAAO;;;;;;;8DACN,iBAAiB;;;;;;;8DACf,WAAW;;;;;;2DAEX,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzB,MAAML,GAAG,GAAG,GAAQ,AAAC;AAWrB,MAAMC,SAAS,GAAG,IAAY;IACnCK,gBAAgB,CAAC;QAAC;YAAEC,GAAG,EAAE,GAAG;YAAEC,GAAG,EAAE,mBAAmB;SAAE;KAAC,CAAC,CAAC;AAC7D,CAAC,AAAC;AAGK,SAASN,WAAW,CAACO,GAAW,EAAE;IACvCC,eAAM,EAAA,QAAA,CAACC,QAAQ,CAACF,GAAG,EAAE;QAAEG,KAAK,EAAE,IAAI;KAAE,EAAE,CAACC,IAAI,GAAKC,IAAG,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC;AACjE,CAAC;AAEM,MAAMV,kBAAkB,GAAG,IAAMa,OAAO,CAACC,MAAM,CAACC,OAAO,IAAI,EAAE,AAAC;AAC9D,MAAMd,SAAS,GAAG,CAACe,IAAY,GACpC,CAAC,EAAEnB,GAAG,CAAC,CAAC,CAAC,GAAGoB,IAAAA,SAAQ,EAAA,QAAA,EAACD,IAAI,EAAEhB,kBAAkB,EAAE,CAAC,CAACkB,SAAS,EAAE,AAAC;AAExD,SAAShB,UAAU,CACxBiB,OAA8E,EAC9E,EAAEC,OAAO,CAAA,EAAwB,EACjC;IACA,MAAMC,KAAK,GAAGR,OAAO,CAACS,QAAQ,KAAK,QAAQ,AAAC;IAE5C,MAAM,EAAEC,SAAS,EAAG;QAAC,KAAK;QAAE,SAAS;QAAE,KAAK;KAAC,CAAA,EAAE,GAAGJ,OAAO,AAAC;IAE1D,MAAMK,iBAAiB,GAAG,CAACD,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,AAAC;IACzD,MAAMC,aAAa,GAAG,CAACH,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,AAAC;IACjD,MAAME,YAAY,GAAG,CAACJ,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,AAAC;IAEhD,MAAMG,SAAS,GAAG,CAAC,UAAU,EAAET,OAAO,CAACU,SAAS,KAAK,KAAK,GAAG,mBAAmB,GAAG,SAAS,CAAC,CAAC,AAAC;IAC/F,MAAMC,MAAM,GAAGX,OAAO,CAACU,SAAS,KAAK,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,mBAAmB,AAAC;IAE7ElB,IAAG,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,CAACC,GAAG,CAACX,SAAS,CAAC8B,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,YAAY,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAElD,IAAIV,OAAO,EAAE;QACXjB,gBAAgB,CAAC;YACf;gBAAEC,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAEuB,SAAS;aAAE;YAC5B,EAAE;YACF;gBAAExB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,cAAc;gBAAE2B,QAAQ,EAAER,iBAAiB;aAAE;YAC9D;gBAAEpB,GAAG,EAAE,SAAS;gBAAEC,GAAG,EAAE,sCAAsC;gBAAE2B,QAAQ,EAAER,iBAAiB;aAAE;YAC5FH,KAAK,IAAI;gBAAEjB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,oBAAoB;gBAAE2B,QAAQ,EAAEN,aAAa;aAAE;YACzEL,KAAK,IAAI;gBAAEjB,GAAG,EAAE,SAAS;gBAAEC,GAAG,EAAE,yBAAyB;gBAAE2B,QAAQ,EAAEN,aAAa;aAAE;YACpF;gBAAEtB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,UAAU;gBAAE2B,QAAQ,EAAEL,YAAY;aAAE;YACrD,EAAE;YACF;gBAAEvB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,YAAY;aAAE;YAC/B,CAAC,CAACc,OAAO,CAACc,mBAAmB,IAAI;gBAAE7B,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,eAAe;aAAE;YACnE,CAAC,CAACc,OAAO,CAACc,mBAAmB,IAAI;gBAAE7B,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,aAAa;aAAE;YACjE,CAAC,CAACc,OAAO,CAACc,mBAAmB,IAAI;gBAAE7B,GAAG,EAAE,SAAS;gBAAEC,GAAG,EAAE,YAAY;aAAE;YACtE;gBAAED,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,kCAAkC;aAAE;YACrD;gBAAED,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,iBAAiB;aAAE;YACpC,EAAE;SACH,CAAC,CAAC;IACL,OAAO;QACLF,gBAAgB,CAAC;YACf;gBAAEC,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAEuB,SAAS;aAAE;YAC5B,EAAE;YACF;gBAAExB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,cAAc;gBAAE2B,QAAQ,EAAER,iBAAiB;aAAE;YAC9DH,KAAK,IAAI;gBAAEjB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,oBAAoB;gBAAE2B,QAAQ,EAAEN,aAAa;aAAE;YACzE;gBAAEtB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,UAAU;gBAAE2B,QAAQ,EAAEL,YAAY;aAAE;YACrD,EAAE;YACF;gBAAEvB,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,eAAe;aAAE;YAClC;gBAAED,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,YAAY;aAAE;YAC/B,CAAC,CAACc,OAAO,CAACc,mBAAmB,IAAI;gBAAE7B,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,aAAa;aAAE;YACjE,CAAC,CAACc,OAAO,CAACc,mBAAmB,IAAI;gBAAE7B,GAAG,EAAE,SAAS;gBAAEC,GAAG,EAAE,YAAY;aAAE;YACtE;gBAAED,GAAG,EAAE,GAAG;gBAAEC,GAAG,EAAE,kCAAkC;aAAE;YACrD,EAAE;SACH,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAASF,gBAAgB,CACvB+B,EAAmF,EACnF;IACAvB,IAAG,CAACC,GAAG,CACLsB,EAAE,CACCC,MAAM,CAACC,OAAO,CAAC,AAChB,kCAAkC;KACjCC,GAAG,CAAC,CAAC,EAAEjC,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAEiC,MAAM,CAAA,EAAEN,QAAQ,CAAA,EAAE,GAAK;QACvC,IAAI,CAAC5B,GAAG,EAAE,OAAO,EAAE,CAAC;QACpB,IAAImC,IAAI,GAAG,CAAC,EAAE1C,GAAG,CAAC,CAAC,CAAC,AAAC;QACrB,IAAIO,GAAG,CAACoC,MAAM,KAAK,CAAC,EAAED,IAAI,IAAI,QAAQ,CAAC;QACvCA,IAAI,IAAIR,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,MAAM,EAAE3B,GAAG,CAAC,UAAU,CAAC,CAAC;QACtCmC,IAAI,IAAIlC,GAAG,CAAC;QACZ,IAAIiC,MAAM,EAAE;YACVC,IAAI,IAAI,CAAC,CAAC,EAAER,MAAK,EAAA,QAAA,CAACU,GAAG,CAAC,CAAC,CAAC,EAAEV,MAAK,EAAA,QAAA,CAACW,MAAM,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC;QACD,IAAIN,QAAQ,EAAE;YACZO,IAAI,GAAGR,MAAK,EAAA,QAAA,CAACU,GAAG,CAACF,IAAI,CAAC,CAAC;QACzB,CAAC;QACD,OAAOA,IAAI,CAAC;IACd,CAAC,CAAC,CACDI,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;AACJ,CAAC"}