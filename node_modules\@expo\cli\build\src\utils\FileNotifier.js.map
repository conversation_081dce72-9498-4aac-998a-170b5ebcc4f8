{"version": 3, "sources": ["../../../src/utils/FileNotifier.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { watchFile } from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { memoize } from './fn';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:fileNotifier') as typeof console.log;\n\n/** Observes and reports file changes. */\nexport class FileNotifier {\n  static instances: FileNotifier[] = [];\n\n  static stopAll() {\n    for (const instance of FileNotifier.instances) {\n      instance.stopObserving();\n    }\n  }\n\n  private unsubscribe: (() => void) | null = null;\n\n  constructor(\n    /** Project root to resolve the module IDs relative to. */\n    private projectRoot: string,\n    /** List of module IDs sorted by priority. Only the first file that exists will be observed. */\n    private moduleIds: string[],\n    private settings: {\n      /** An additional warning message to add to the notice. */\n      additionalWarning?: string;\n    } = {}\n  ) {\n    FileNotifier.instances.push(this);\n  }\n\n  /** Get the file in the project. */\n  private resolveFilePath(): string | null {\n    for (const moduleId of this.moduleIds) {\n      const filePath = resolveFrom.silent(this.projectRoot, moduleId);\n      if (filePath) {\n        return filePath;\n      }\n    }\n    return null;\n  }\n\n  public startObserving(callback?: (cur: any, prev: any) => void) {\n    const configPath = this.resolveFilePath();\n    if (configPath) {\n      debug(`Observing ${configPath}`);\n      return this.watchFile(configPath, callback);\n    }\n    return configPath;\n  }\n\n  public stopObserving() {\n    this.unsubscribe?.();\n  }\n\n  /** Watch the file and warn to reload the CLI if it changes. */\n  public watchFile = memoize(this.startWatchingFile.bind(this));\n\n  private startWatchingFile(filePath: string, callback?: (cur: any, prev: any) => void): string {\n    const configName = path.relative(this.projectRoot, filePath);\n    const listener = (cur: any, prev: any) => {\n      if (prev.size || cur.size) {\n        Log.log(\n          `\\u203A Detected a change in ${chalk.bold(\n            configName\n          )}. Restart the server to see the new results.` + (this.settings.additionalWarning || '')\n        );\n      }\n    };\n\n    const watcher = watchFile(filePath, callback ?? listener);\n\n    this.unsubscribe = () => {\n      watcher.unref();\n    };\n\n    return filePath;\n  }\n}\n"], "names": ["FileNotifier", "debug", "require", "instances", "stopAll", "instance", "stopObserving", "constructor", "projectRoot", "moduleIds", "settings", "unsubscribe", "watchFile", "memoize", "startWatchingFile", "bind", "push", "resolve<PERSON><PERSON><PERSON><PERSON>", "moduleId", "filePath", "resolveFrom", "silent", "startObserving", "callback", "config<PERSON><PERSON>", "config<PERSON><PERSON>", "path", "relative", "listener", "cur", "prev", "size", "Log", "log", "chalk", "bold", "additionalWarning", "watcher", "unref"], "mappings": "AAAA;;;;+BAWaA,cAAY;;aAAZA,YAAY;;;8DAXP,OAAO;;;;;;;yBACC,IAAI;;;;;;;8DACb,MAAM;;;;;;;8DACC,cAAc;;;;;;oBAEd,MAAM;2DACT,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAGzE,MAAMF,YAAY;IACvB,OAAOG,SAAS,GAAmB,EAAE,CAAC;WAE/BC,OAAO,GAAG;QACf,KAAK,MAAMC,QAAQ,IAAIL,YAAY,CAACG,SAAS,CAAE;YAC7CE,QAAQ,CAACC,aAAa,EAAE,CAAC;QAC3B,CAAC;IACH;IAIAC,YAEUC,WAAmB,EAEnBC,SAAmB,EACnBC,QAGP,GAAG,EAAE,CACN;QAPQF,mBAAAA,WAAmB,CAAA;QAEnBC,iBAAAA,SAAmB,CAAA;QACnBC,gBAAAA,QAGP,CAAA;aAVKC,WAAW,GAAwB,IAAI;aAwCxCC,SAAS,GAAGC,IAAAA,GAAO,QAAA,EAAC,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QA5B3Df,YAAY,CAACG,SAAS,CAACa,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC;IAEA,iCAAiC,GACzBC,eAAe,GAAkB;QACvC,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACT,SAAS,CAAE;YACrC,MAAMU,QAAQ,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAAC,IAAI,CAACb,WAAW,EAAEU,QAAQ,CAAC,AAAC;YAChE,IAAIC,QAAQ,EAAE;gBACZ,OAAOA,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd;IAEOG,cAAc,CAACC,QAAwC,EAAE;QAC9D,MAAMC,UAAU,GAAG,IAAI,CAACP,eAAe,EAAE,AAAC;QAC1C,IAAIO,UAAU,EAAE;YACdvB,KAAK,CAAC,CAAC,UAAU,EAAEuB,UAAU,CAAC,CAAC,CAAC,CAAC;YACjC,OAAO,IAAI,CAACZ,SAAS,CAACY,UAAU,EAAED,QAAQ,CAAC,CAAC;QAC9C,CAAC;QACD,OAAOC,UAAU,CAAC;IACpB;IAEOlB,aAAa,GAAG;YACrB,IAAI,AAAY,EAAhB,GAAgB;QAAhB,CAAA,GAAgB,GAAhB,CAAA,IAAI,GAAJ,IAAI,EAACK,WAAW,SAAI,GAApB,KAAA,CAAoB,GAApB,GAAgB,CAAhB,IAAoB,CAApB,IAAI,CAAgB,CAAC;IACvB;IAKQG,iBAAiB,CAACK,QAAgB,EAAEI,QAAwC,EAAU;QAC5F,MAAME,UAAU,GAAGC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAAC,IAAI,CAACnB,WAAW,EAAEW,QAAQ,CAAC,AAAC;QAC7D,MAAMS,QAAQ,GAAG,CAACC,GAAQ,EAAEC,IAAS,GAAK;YACxC,IAAIA,IAAI,CAACC,IAAI,IAAIF,GAAG,CAACE,IAAI,EAAE;gBACzBC,IAAG,CAACC,GAAG,CACL,CAAC,4BAA4B,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CACvCV,UAAU,CACX,CAAC,4CAA4C,CAAC,GAAG,CAAC,IAAI,CAACf,QAAQ,CAAC0B,iBAAiB,IAAI,EAAE,CAAC,CAC1F,CAAC;YACJ,CAAC;QACH,CAAC,AAAC;QAEF,MAAMC,OAAO,GAAGzB,IAAAA,GAAS,EAAA,UAAA,EAACO,QAAQ,EAAEI,QAAQ,IAAIK,QAAQ,CAAC,AAAC;QAE1D,IAAI,CAACjB,WAAW,GAAG,IAAM;YACvB0B,OAAO,CAACC,KAAK,EAAE,CAAC;QAClB,CAAC,CAAC;QAEF,OAAOnB,QAAQ,CAAC;IAClB;CACD"}