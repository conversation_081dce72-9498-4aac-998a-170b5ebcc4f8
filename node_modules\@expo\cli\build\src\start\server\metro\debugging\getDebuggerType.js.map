{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/getDebuggerType.ts"], "sourcesContent": ["/** Known compatible debuggers that require specific workarounds */\nexport type DebuggerType = 'chrome' | 'vscode' | 'unknown';\n\n// Patterns to test against user agents\nconst CHROME_USER_AGENT = /chrome/i;\nconst VSCODE_USER_AGENT = /vscode/i;\n\n/**\n * Determine the debugger type based on the known user agent.\n */\nexport function getDebuggerType(userAgent?: string | null): DebuggerType {\n  if (userAgent && CHROME_USER_AGENT.test(userAgent)) return 'chrome';\n  if (userAgent && VSCODE_USER_AGENT.test(userAgent)) return 'vscode';\n  return 'unknown';\n}\n"], "names": ["getDebuggerType", "CHROME_USER_AGENT", "VSCODE_USER_AGENT", "userAgent", "test"], "mappings": "AAAA,iEAAiE,GACjE;;;;+BASgBA,iBAAe;;aAAfA,eAAe;;AAP/B,uCAAuC;AACvC,MAAMC,iBAAiB,YAAY,AAAC;AACpC,MAAMC,iBAAiB,YAAY,AAAC;AAK7B,SAASF,eAAe,CAACG,SAAyB,EAAgB;IACvE,IAAIA,SAAS,IAAIF,iBAAiB,CAACG,IAAI,CAACD,SAAS,CAAC,EAAE,OAAO,QAAQ,CAAC;IACpE,IAAIA,SAAS,IAAID,iBAAiB,CAACE,IAAI,CAACD,SAAS,CAAC,EAAE,OAAO,QAAQ,CAAC;IACpE,OAAO,SAAS,CAAC;AACnB,CAAC"}