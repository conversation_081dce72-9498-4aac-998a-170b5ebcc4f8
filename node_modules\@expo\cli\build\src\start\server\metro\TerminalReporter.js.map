{"version": 3, "sources": ["../../../../../src/start/server/metro/TerminalReporter.ts"], "sourcesContent": ["// This file represents an abstraction on the metro TerminalReporter.\n// We use this abstraction to safely extend the TerminalReporter for our own custom logging.\nimport chalk from 'chalk';\nimport UpstreamTerminalReporter from 'metro/src/lib/TerminalReporter';\nimport { Terminal } from 'metro-core';\nimport type { WatcherStatus } from 'metro-file-map';\nimport util from 'util';\n\nimport {\n  BundleDetails,\n  BundleProgressUpdate,\n  TerminalReportableEvent,\n  TerminalReporterInterface,\n} from './TerminalReporter.types';\nimport { stripAnsi } from '../../../utils/ansi';\n\nconst debug = require('debug')('expo:metro:logger') as typeof console.log;\n\n/**\n * A standard way to log a warning to the terminal. This should not be called\n * from some arbitrary Metro logic, only from the reporters. Instead of\n * calling this, add a new type of ReportableEvent instead, and implement a\n * proper handler in the reporter(s).\n */\nexport function logWarning(terminal: Terminal, format: string, ...args: any[]): void {\n  const str = util.format(format, ...args);\n  terminal.log('%s: %s', chalk.yellow('warning'), str);\n}\n\n/**\n * Similar to `logWarning`, but for messages that require the user to act.\n */\nexport function logError(terminal: Terminal, format: string, ...args: any[]): void {\n  terminal.log(\n    '%s: %s',\n    chalk.red('error'),\n    // Syntax errors may have colors applied for displaying code frames\n    // in various places outside of where Metro is currently running.\n    // If the current terminal does not support color, we'll strip the colors\n    // here.\n    util.format(chalk.supportsColor ? format : stripAnsi(format), ...args)\n  );\n}\n\nconst XTerminalReporter = UpstreamTerminalReporter as unknown as TerminalReporterInterface;\n\n/** Extended TerminalReporter class but with proper types and extra functionality to avoid using the `_log` method directly in subclasses. */\nexport class TerminalReporter extends XTerminalReporter implements TerminalReporterInterface {\n  /**\n   * A cache of { [buildID]: BundleDetails } which can be used to\n   * add more contextual logs. BundleDetails is currently only sent with `bundle_build_started`\n   * so we need to cache the details in order to print the platform info with other event types.\n   */\n  _bundleDetails: Map<string, BundleDetails> = new Map();\n\n  /** Keep track of how long a bundle takes to complete */\n  _bundleTimers: Map<string, bigint> = new Map();\n\n  /** Keep track of bundle processes that should not be logged. */\n  _hiddenBundleEvents: Set<string> = new Set();\n\n  _log(event: TerminalReportableEvent): void {\n    switch (event.type) {\n      case 'transform_cache_reset':\n        return this.transformCacheReset();\n      case 'dep_graph_loading':\n        return this.dependencyGraphLoading(event.hasReducedPerformance);\n      case 'client_log':\n        if (this.shouldFilterClientLog(event)) {\n          return;\n        }\n        break;\n    }\n    return super._log(event);\n  }\n\n  /** Gives subclasses an easy interface for filtering out logs. Return `true` to skip. */\n  shouldFilterClientLog(event: TerminalReportableEvent): boolean {\n    return false;\n  }\n\n  /** Gives subclasses an easy interface for filtering out bundle events, specifically for source maps. Return `true` to skip. */\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return false;\n  }\n\n  /** Cache has been reset. */\n  transformCacheReset(): void {}\n\n  /** One of the first logs that will be printed. */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {}\n\n  /**\n   * Custom log event representing the end of the bundling.\n   *\n   * @param event event object.\n   * @param duration duration of the build in milliseconds.\n   */\n  bundleBuildEnded(event: TerminalReportableEvent, duration: bigint | number): void {}\n\n  // Add a custom format to logs that come from the worker threads.\n  // `| <contents>`\n  _logWorkerChunk(origin: 'stdout' | 'stderr', chunk: string): void {\n    const lines = chunk.split('\\n');\n    if (lines.length >= 1 && lines[lines.length - 1] === '') {\n      lines.splice(lines.length - 1, 1);\n    }\n\n    const originTag = origin === 'stdout' ? chalk.dim('|') : chalk.yellow('|');\n    lines.forEach((line: string) => {\n      this.terminal.log(originTag, line);\n    });\n  }\n\n  _logWatcherStatus(status: WatcherStatus) {\n    // Metro logs this warning twice. This helps reduce the noise.\n\n    if (status.type === 'watchman_warning') {\n      return;\n    }\n    return super._logWatcherStatus(status);\n  }\n\n  /**\n   * This function is exclusively concerned with updating the internal state.\n   * No logging or status updates should be done at this point.\n   */\n  _updateState(\n    event: TerminalReportableEvent & { bundleDetails?: BundleDetails; buildID?: string }\n  ) {\n    // Append the buildID to the bundleDetails.\n    if (event.bundleDetails) {\n      event.bundleDetails.buildID = event.buildID;\n    }\n\n    const buildID = event.bundleDetails?.buildID ?? event.buildID;\n\n    if (buildID && !this._hiddenBundleEvents.has(buildID)) {\n      if (this.shouldFilterBundleEvent(event)) {\n        debug('skipping bundle events for', buildID, event);\n        this._hiddenBundleEvents.add(buildID);\n      } else {\n        super._updateState(event);\n      }\n    } else {\n      super._updateState(event);\n    }\n\n    switch (event.type) {\n      case 'bundle_build_done':\n      case 'bundle_build_failed': {\n        const startTime = this._bundleTimers.get(event.buildID);\n        // Observed a bug in Metro where the `bundle_build_done` is invoked twice during a static bundle\n        // i.e. `expo export`.\n        if (startTime == null) {\n          break;\n        }\n\n        this.bundleBuildEnded(event, startTime ? process.hrtime.bigint() - startTime : 0);\n        this._bundleTimers.delete(event.buildID);\n        break;\n      }\n      case 'bundle_build_started':\n        this._bundleDetails.set(event.buildID, event.bundleDetails);\n        this._bundleTimers.set(event.buildID, process.hrtime.bigint());\n        break;\n    }\n  }\n\n  /**\n   * We use Math.pow(ratio, 2) to as a conservative measure of progress because\n   * we know the `totalCount` is going to progressively increase as well. We\n   * also prevent the ratio from going backwards.\n   */\n  _updateBundleProgress(options: BundleProgressUpdate) {\n    super._updateBundleProgress(options);\n\n    const currentProgress = this._activeBundles.get(options.buildID);\n    if (!currentProgress) {\n      return;\n    }\n\n    // Fix an issue where the transformer is faster than the resolver,\n    // locking the progress bar at 100% after transforming the first and only resolved file (1/1).\n    if (currentProgress.ratio === 1 && options.totalFileCount === 1) {\n      Object.assign(currentProgress, { ...currentProgress, ratio: 0 });\n    }\n  }\n}\n"], "names": ["logWarning", "logError", "TerminalReporter", "debug", "require", "terminal", "format", "args", "str", "util", "log", "chalk", "yellow", "red", "supportsColor", "stripAnsi", "XTerminalReporter", "UpstreamTerminalReporter", "_bundleDetails", "Map", "_bundleTimers", "_hiddenBundleEvents", "Set", "_log", "event", "type", "transformCacheReset", "dependencyGraphLoading", "hasReducedPerformance", "shouldFilterClientLog", "shouldFilterBundleEvent", "bundleBuildEnded", "duration", "_logWorkerChunk", "origin", "chunk", "lines", "split", "length", "splice", "originTag", "dim", "for<PERSON>ach", "line", "_logWatcherStatus", "status", "_updateState", "bundleDetails", "buildID", "has", "add", "startTime", "get", "process", "hrtime", "bigint", "delete", "set", "_updateBundleProgress", "options", "currentProgress", "_activeBundles", "ratio", "totalFileCount", "Object", "assign"], "mappings": "AAAA,qEAAqE;AACrE,4FAA4F;AAC5F;;;;;;;;;;;IAsBgBA,UAAU,MAAVA,UAAU;IAQVC,QAAQ,MAARA,QAAQ;IAeXC,gBAAgB,MAAhBA,gBAAgB;;;8DA7CX,OAAO;;;;;;;8DACY,gCAAgC;;;;;;;8DAGpD,MAAM;;;;;;sBAQG,qBAAqB;;;;;;AAE/C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAQnE,SAASJ,UAAU,CAACK,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAI,AAAO,EAAQ;IACnF,MAAMC,GAAG,GAAGC,KAAI,EAAA,QAAA,CAACH,MAAM,CAACA,MAAM,KAAKC,IAAI,CAAC,AAAC;IACzCF,QAAQ,CAACK,GAAG,CAAC,QAAQ,EAAEC,MAAK,EAAA,QAAA,CAACC,MAAM,CAAC,SAAS,CAAC,EAAEJ,GAAG,CAAC,CAAC;AACvD,CAAC;AAKM,SAASP,QAAQ,CAACI,QAAkB,EAAEC,MAAc,EAAE,GAAGC,IAAI,AAAO,EAAQ;IACjFF,QAAQ,CAACK,GAAG,CACV,QAAQ,EACRC,MAAK,EAAA,QAAA,CAACE,GAAG,CAAC,OAAO,CAAC,EAClB,mEAAmE;IACnE,iEAAiE;IACjE,yEAAyE;IACzE,QAAQ;IACRJ,KAAI,EAAA,QAAA,CAACH,MAAM,CAACK,MAAK,EAAA,QAAA,CAACG,aAAa,GAAGR,MAAM,GAAGS,IAAAA,KAAS,UAAA,EAACT,MAAM,CAAC,KAAKC,IAAI,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,MAAMS,iBAAiB,GAAGC,iBAAwB,EAAA,QAAA,AAAwC,AAAC;AAGpF,MAAMf,gBAAgB,SAASc,iBAAiB;IACrD;;;;GAIC,GACDE,cAAc,GAA+B,IAAIC,GAAG,EAAE,CAAC;IAEvD,sDAAsD,GACtDC,aAAa,GAAwB,IAAID,GAAG,EAAE,CAAC;IAE/C,8DAA8D,GAC9DE,mBAAmB,GAAgB,IAAIC,GAAG,EAAE,CAAC;IAE7CC,IAAI,CAACC,KAA8B,EAAQ;QACzC,OAAQA,KAAK,CAACC,IAAI;YAChB,KAAK,uBAAuB;gBAC1B,OAAO,IAAI,CAACC,mBAAmB,EAAE,CAAC;YACpC,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAACC,sBAAsB,CAACH,KAAK,CAACI,qBAAqB,CAAC,CAAC;YAClE,KAAK,YAAY;gBACf,IAAI,IAAI,CAACC,qBAAqB,CAACL,KAAK,CAAC,EAAE;oBACrC,OAAO;gBACT,CAAC;gBACD,MAAM;SACT;QACD,OAAO,KAAK,CAACD,IAAI,CAACC,KAAK,CAAC,CAAC;IAC3B;IAEA,sFAAsF,GACtFK,qBAAqB,CAACL,KAA8B,EAAW;QAC7D,OAAO,KAAK,CAAC;IACf;IAEA,6HAA6H,GAC7HM,uBAAuB,CAACN,KAA8B,EAAW;QAC/D,OAAO,KAAK,CAAC;IACf;IAEA,0BAA0B,GAC1BE,mBAAmB,GAAS,CAAC;IAE7B,gDAAgD,GAChDC,sBAAsB,CAACC,qBAA8B,EAAQ,CAAC;IAE9D;;;;;GAKC,GACDG,gBAAgB,CAACP,KAA8B,EAAEQ,QAAyB,EAAQ,CAAC;IAEnF,iEAAiE;IACjE,iBAAiB;IACjBC,eAAe,CAACC,MAA2B,EAAEC,KAAa,EAAQ;QAChE,MAAMC,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,IAAI,CAAC,AAAC;QAChC,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,IAAIF,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACvDF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,CAAC;QAED,MAAME,SAAS,GAAGN,MAAM,KAAK,QAAQ,GAAGvB,MAAK,EAAA,QAAA,CAAC8B,GAAG,CAAC,GAAG,CAAC,GAAG9B,MAAK,EAAA,QAAA,CAACC,MAAM,CAAC,GAAG,CAAC,AAAC;QAC3EwB,KAAK,CAACM,OAAO,CAAC,CAACC,IAAY,GAAK;YAC9B,IAAI,CAACtC,QAAQ,CAACK,GAAG,CAAC8B,SAAS,EAAEG,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL;IAEAC,iBAAiB,CAACC,MAAqB,EAAE;QACvC,8DAA8D;QAE9D,IAAIA,MAAM,CAACpB,IAAI,KAAK,kBAAkB,EAAE;YACtC,OAAO;QACT,CAAC;QACD,OAAO,KAAK,CAACmB,iBAAiB,CAACC,MAAM,CAAC,CAAC;IACzC;IAEA;;;GAGC,GACDC,YAAY,CACVtB,KAAoF,EACpF;YAMgBA,GAAmB;QALnC,2CAA2C;QAC3C,IAAIA,KAAK,CAACuB,aAAa,EAAE;YACvBvB,KAAK,CAACuB,aAAa,CAACC,OAAO,GAAGxB,KAAK,CAACwB,OAAO,CAAC;QAC9C,CAAC;QAED,MAAMA,OAAO,GAAGxB,CAAAA,CAAAA,GAAmB,GAAnBA,KAAK,CAACuB,aAAa,SAAS,GAA5BvB,KAAAA,CAA4B,GAA5BA,GAAmB,CAAEwB,OAAO,CAAA,IAAIxB,KAAK,CAACwB,OAAO,AAAC;QAE9D,IAAIA,OAAO,IAAI,CAAC,IAAI,CAAC3B,mBAAmB,CAAC4B,GAAG,CAACD,OAAO,CAAC,EAAE;YACrD,IAAI,IAAI,CAAClB,uBAAuB,CAACN,KAAK,CAAC,EAAE;gBACvCrB,KAAK,CAAC,4BAA4B,EAAE6C,OAAO,EAAExB,KAAK,CAAC,CAAC;gBACpD,IAAI,CAACH,mBAAmB,CAAC6B,GAAG,CAACF,OAAO,CAAC,CAAC;YACxC,OAAO;gBACL,KAAK,CAACF,YAAY,CAACtB,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,OAAO;YACL,KAAK,CAACsB,YAAY,CAACtB,KAAK,CAAC,CAAC;QAC5B,CAAC;QAED,OAAQA,KAAK,CAACC,IAAI;YAChB,KAAK,mBAAmB,CAAC;YACzB,KAAK,qBAAqB;gBAAE;oBAC1B,MAAM0B,SAAS,GAAG,IAAI,CAAC/B,aAAa,CAACgC,GAAG,CAAC5B,KAAK,CAACwB,OAAO,CAAC,AAAC;oBACxD,gGAAgG;oBAChG,sBAAsB;oBACtB,IAAIG,SAAS,IAAI,IAAI,EAAE;wBACrB,MAAM;oBACR,CAAC;oBAED,IAAI,CAACpB,gBAAgB,CAACP,KAAK,EAAE2B,SAAS,GAAGE,OAAO,CAACC,MAAM,CAACC,MAAM,EAAE,GAAGJ,SAAS,GAAG,CAAC,CAAC,CAAC;oBAClF,IAAI,CAAC/B,aAAa,CAACoC,MAAM,CAAChC,KAAK,CAACwB,OAAO,CAAC,CAAC;oBACzC,MAAM;gBACR,CAAC;YACD,KAAK,sBAAsB;gBACzB,IAAI,CAAC9B,cAAc,CAACuC,GAAG,CAACjC,KAAK,CAACwB,OAAO,EAAExB,KAAK,CAACuB,aAAa,CAAC,CAAC;gBAC5D,IAAI,CAAC3B,aAAa,CAACqC,GAAG,CAACjC,KAAK,CAACwB,OAAO,EAAEK,OAAO,CAACC,MAAM,CAACC,MAAM,EAAE,CAAC,CAAC;gBAC/D,MAAM;SACT;IACH;IAEA;;;;GAIC,GACDG,qBAAqB,CAACC,OAA6B,EAAE;QACnD,KAAK,CAACD,qBAAqB,CAACC,OAAO,CAAC,CAAC;QAErC,MAAMC,eAAe,GAAG,IAAI,CAACC,cAAc,CAACT,GAAG,CAACO,OAAO,CAACX,OAAO,CAAC,AAAC;QACjE,IAAI,CAACY,eAAe,EAAE;YACpB,OAAO;QACT,CAAC;QAED,kEAAkE;QAClE,8FAA8F;QAC9F,IAAIA,eAAe,CAACE,KAAK,KAAK,CAAC,IAAIH,OAAO,CAACI,cAAc,KAAK,CAAC,EAAE;YAC/DC,MAAM,CAACC,MAAM,CAACL,eAAe,EAAE;gBAAE,GAAGA,eAAe;gBAAEE,KAAK,EAAE,CAAC;aAAE,CAAC,CAAC;QACnE,CAAC;IACH;CACD"}