{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/RudderClient.ts"], "sourcesContent": ["import RudderAnalytics from '@expo/rudder-sdk-node';\n\nimport { TelemetryClient, TelemetryClientStrategy, TelemetryRecordInternal } from '../types';\nimport { TELEMETRY_ENDPOINT, TELEMETRY_TARGET } from '../utils/constants';\n\nexport class RudderClient implements TelemetryClient {\n  /** This client should only be used in debug mode, or in the detached script */\n  readonly strategy: TelemetryClientStrategy = 'debug';\n  /** The RudderStack SDK instance */\n  private rudderstack: RudderAnalytics;\n\n  constructor(sdk?: RudderAnalytics) {\n    if (!sdk) {\n      sdk = new RudderAnalytics(TELEMETRY_TARGET, TELEMETRY_ENDPOINT, {\n        flushInterval: 300,\n      });\n    }\n\n    this.rudderstack = sdk;\n  }\n\n  abort(): TelemetryRecordInternal[] {\n    throw new Error('Cannot abort Rudderstack client records');\n  }\n\n  async record(records: TelemetryRecordInternal[]) {\n    await Promise.all(records.map((record) => this.rudderstack.track(record)));\n  }\n\n  async flush() {\n    await this.rudderstack.flush();\n  }\n}\n"], "names": ["RudderClient", "strategy", "constructor", "sdk", "RudderAnalytics", "TELEMETRY_TARGET", "TELEMETRY_ENDPOINT", "flushInterval", "rudderstack", "abort", "Error", "record", "records", "Promise", "all", "map", "track", "flush"], "mappings": "AAAA;;;;+BAKa<PERSON>,cAAY;;aAAZA,YAAY;;;8DALG,uBAAuB;;;;;;2BAGE,oBAAoB;;;;;;AAElE,MAAMA,YAAY;IACvB,6EAA6E,GAC7E,AAASC,QAAQ,GAA4B,OAAO,CAAC;IAIrDC,YAAYC,GAAqB,CAAE;QACjC,IAAI,CAACA,GAAG,EAAE;YACRA,GAAG,GAAG,IAAIC,CAAAA,cAAe,EAAA,CAAA,QAAA,CAACC,UAAgB,iBAAA,EAAEC,UAAkB,mBAAA,EAAE;gBAC9DC,aAAa,EAAE,GAAG;aACnB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAACC,WAAW,GAAGL,GAAG,CAAC;IACzB;IAEAM,KAAK,GAA8B;QACjC,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC7D;UAEMC,MAAM,CAACC,OAAkC,EAAE;QAC/C,MAAMC,OAAO,CAACC,GAAG,CAACF,OAAO,CAACG,GAAG,CAAC,CAACJ,MAAM,GAAK,IAAI,CAACH,WAAW,CAACQ,KAAK,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7E;UAEMM,KAAK,GAAG;QACZ,MAAM,IAAI,CAACT,WAAW,CAACS,KAAK,EAAE,CAAC;IACjC;CACD"}