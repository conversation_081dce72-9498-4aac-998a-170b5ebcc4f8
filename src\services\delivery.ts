import { ordersApi } from './api';
import { Order } from '../types';

interface DeliveryStats {
  totalDeliveries: number;
  earnings: number;
  rating: number;
}

export const deliveryService = {
  async getAvailableOrders(): Promise<Order[]> {
    const response = await ordersApi.get<Order[]>('/orders/delivery/available');
    return response.data;
  },
  
  async acceptOrder(id: string): Promise<Order> {
    const response = await ordersApi.post<Order>(`/orders/${id}/accept`);
    return response.data;
  },
  
  async getMyOrders(): Promise<Order[]> {
    const response = await ordersApi.get<Order[]>('/orders/delivery/my-orders');
    return response.data;
  },
  
  async updateOrderStatus(id: string, status: string): Promise<Order> {
    const response = await ordersApi.put<Order>(`/orders/${id}/status`, { status });
    return response.data;
  },
  
  async getStats(): Promise<DeliveryStats> {
    const response = await ordersApi.get<DeliveryStats>('/delivery/stats');
    return response.data;
  }
};