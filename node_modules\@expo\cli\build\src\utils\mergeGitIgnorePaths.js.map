{"version": 3, "sources": ["../../../src/utils/mergeGitIgnorePaths.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport fs from 'fs';\n\nimport { Log } from '../log';\n\ntype MergeResults = {\n  contents: string;\n  didClear: boolean;\n  didMerge: boolean;\n};\n\nconst generatedHeaderPrefix = `# @generated expo-cli`;\nexport const generatedFooterComment = `# @end expo-cli`;\n\n/**\n * Merge two gitignore files together and add a generated header.\n *\n * @param targetGitIgnorePath\n * @param sourceGitIgnorePath\n *\n * @returns `null` if one of the gitignore files doesn't exist. Otherwise, returns the merged contents.\n */\nexport function mergeGitIgnorePaths(\n  targetGitIgnorePath: string,\n  sourceGitIgnorePath: string\n): null | MergeResults {\n  if (!fs.existsSync(targetGitIgnorePath)) {\n    // No gitignore in the project already, no need to merge anything into anything. I guess they\n    // are not using git :O\n    return null;\n  }\n\n  if (!fs.existsSync(sourceGitIgnorePath)) {\n    // Maybe we don't have a gitignore in the template project\n    return null;\n  }\n\n  const targetGitIgnore = fs.readFileSync(targetGitIgnorePath).toString();\n  const sourceGitIgnore = fs.readFileSync(sourceGitIgnorePath).toString();\n  const merged = mergeGitIgnoreContents(targetGitIgnore, sourceGitIgnore);\n  // Only rewrite the file if it was modified.\n  if (merged.contents) {\n    fs.writeFileSync(targetGitIgnorePath, merged.contents);\n  }\n\n  return merged;\n}\n\n/**\n * Get line indexes for the generated section of a gitignore.\n *\n * @param gitIgnore\n */\nfunction getGeneratedSectionIndexes(gitIgnore: string): {\n  contents: string[];\n  start: number;\n  end: number;\n} {\n  const contents = gitIgnore.split('\\n');\n  const start = contents.findIndex((line) => line.startsWith(generatedHeaderPrefix));\n  const end = contents.findIndex((line) => line.startsWith(generatedFooterComment));\n\n  return { contents, start, end };\n}\n\n/**\n * Removes the generated section from a gitignore, returns null when nothing can be removed.\n * This sways heavily towards not removing lines unless it's certain that modifications were not made to the gitignore manually.\n *\n * @param gitIgnore\n */\nexport function removeGeneratedGitIgnoreContents(gitIgnore: string): string | null {\n  const { contents, start, end } = getGeneratedSectionIndexes(gitIgnore);\n  if (start > -1 && end > -1 && start < end) {\n    contents.splice(start, end - start + 1);\n    // TODO: We could in theory check that the contents we're removing match the hash used in the header,\n    // this would ensure that we don't accidentally remove lines that someone added or removed from the generated section.\n    return contents.join('\\n');\n  }\n  return null;\n}\n\n/**\n * Merge the contents of two gitignores together and add a generated header.\n *\n * @param targetGitIgnore contents of the existing gitignore\n * @param sourceGitIgnore contents of the extra gitignore\n */\nexport function mergeGitIgnoreContents(\n  targetGitIgnore: string,\n  sourceGitIgnore: string\n): MergeResults {\n  const header = createGeneratedHeaderComment(sourceGitIgnore);\n  if (!targetGitIgnore.includes(header)) {\n    // Ensure the old generated gitignore contents are removed.\n    const sanitizedTarget = removeGeneratedGitIgnoreContents(targetGitIgnore);\n    return {\n      contents: [\n        sanitizedTarget ?? targetGitIgnore,\n        header,\n        `# The following patterns were generated by expo-cli`,\n        ``,\n        sourceGitIgnore,\n        generatedFooterComment,\n      ].join('\\n'),\n      didMerge: true,\n      didClear: !!sanitizedTarget,\n    };\n  }\n  return { contents: targetGitIgnore, didClear: false, didMerge: false };\n}\n\n/**\n * Adds the contents into an existing gitignore \"generated by expo-cli section\"\n * If no section exists, it will be created (hence the name upsert)\n */\nexport function upsertGitIgnoreContents(\n  targetGitIgnorePath: string,\n  contents: string\n): MergeResults | null {\n  const targetGitIgnore = fs.readFileSync(targetGitIgnorePath, {\n    encoding: 'utf-8',\n    flag: 'a+',\n  });\n\n  if (targetGitIgnore.match(new RegExp(`^${contents}[\\\\n\\\\r\\\\s]*$`, 'm'))) {\n    return null;\n  }\n\n  // If there is an existing section, update it with the new content\n  if (targetGitIgnore.includes(generatedHeaderPrefix)) {\n    const indexes = getGeneratedSectionIndexes(targetGitIgnore);\n\n    contents = `${indexes.contents.slice(indexes.start + 3, indexes.end).join('\\n')}\\n${contents}`;\n  }\n\n  const merged = mergeGitIgnoreContents(targetGitIgnore, contents);\n\n  if (merged.contents) {\n    fs.writeFileSync(targetGitIgnorePath, merged.contents);\n  }\n  return merged;\n}\n\nexport function createGeneratedHeaderComment(gitIgnore: string): string {\n  const hashKey = createGitIgnoreHash(getSanitizedGitIgnoreLines(gitIgnore).join('\\n'));\n\n  return `${generatedHeaderPrefix} ${hashKey}`;\n}\n\n/**\n * Normalize the contents of a gitignore to ensure that minor changes like new lines or sort order don't cause a regeneration.\n */\nexport function getSanitizedGitIgnoreLines(gitIgnore: string): string[] {\n  // filter, trim, and sort the lines.\n  return gitIgnore\n    .split('\\n')\n    .filter((v) => {\n      const line = v.trim();\n      // Strip comments\n      if (line.startsWith('#')) {\n        return false;\n      }\n      return !!line;\n    })\n    .sort();\n}\n\nexport function createGitIgnoreHash(gitIgnore: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  const hash = crypto.createHash('sha1').update(gitIgnore).digest('hex');\n  return `sync-${hash}`;\n}\n\nexport function removeFromGitIgnore(targetGitIgnorePath: string, contents: string) {\n  try {\n    if (!fs.existsSync(targetGitIgnorePath)) {\n      return;\n    }\n\n    let targetGitIgnore = fs.readFileSync(targetGitIgnorePath, 'utf-8');\n\n    if (!targetGitIgnore.includes(contents)) {\n      return null;\n    }\n\n    targetGitIgnore = targetGitIgnore.replace(`${contents}\\n`, '');\n\n    const indexes = getGeneratedSectionIndexes(targetGitIgnore);\n\n    if (indexes.start === indexes.end - 3) {\n      targetGitIgnore = targetGitIgnore.replace(\n        new RegExp(`^${generatedHeaderPrefix}((.|\\n)*)${generatedFooterComment}$`, 'm'),\n        ''\n      );\n    }\n\n    return fs.writeFileSync(targetGitIgnorePath, targetGitIgnore);\n  } catch (error) {\n    Log.error(`Failed to read/write to .gitignore path: ${targetGitIgnorePath}`);\n    throw error;\n  }\n}\n"], "names": ["generatedFooterComment", "mergeGitIgnorePaths", "removeGeneratedGitIgnoreContents", "mergeGitIgnoreContents", "upsertGitIgnoreContents", "createGeneratedHeaderComment", "getSanitizedGitIgnoreLines", "createGitIgnoreHash", "removeFromGitIgnore", "generatedHeaderPrefix", "targetGitIgnorePath", "sourceGitIgnorePath", "fs", "existsSync", "targetGitIgnore", "readFileSync", "toString", "sourceGitIgnore", "merged", "contents", "writeFileSync", "getGeneratedSectionIndexes", "gitIgnore", "split", "start", "findIndex", "line", "startsWith", "end", "splice", "join", "header", "includes", "sanitizedTarget", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "encoding", "flag", "match", "RegExp", "indexes", "slice", "hash<PERSON><PERSON>", "filter", "v", "trim", "sort", "hash", "crypto", "createHash", "update", "digest", "replace", "error", "Log"], "mappings": "AAAA;;;;;;;;;;;IAYaA,sBAAsB,MAAtBA,sBAAsB;IAUnBC,mBAAmB,MAAnBA,mBAAmB;IAiDnBC,gCAAgC,MAAhCA,gCAAgC;IAiBhCC,sBAAsB,MAAtBA,sBAAsB;IA4BtBC,uBAAuB,MAAvBA,uBAAuB;IA4BvBC,4BAA4B,MAA5BA,4BAA4B;IAS5BC,0BAA0B,MAA1BA,0BAA0B;IAe1BC,mBAAmB,MAAnBA,mBAAmB;IAMnBC,mBAAmB,MAAnBA,mBAAmB;;;8DA9KhB,QAAQ;;;;;;;8DACZ,IAAI;;;;;;qBAEC,QAAQ;;;;;;AAQ5B,MAAMC,qBAAqB,GAAG,CAAC,qBAAqB,CAAC,AAAC;AAC/C,MAAMT,sBAAsB,GAAG,CAAC,eAAe,CAAC,AAAC;AAUjD,SAASC,mBAAmB,CACjCS,mBAA2B,EAC3BC,mBAA2B,EACN;IACrB,IAAI,CAACC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACH,mBAAmB,CAAC,EAAE;QACvC,6FAA6F;QAC7F,uBAAuB;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAACE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,mBAAmB,CAAC,EAAE;QACvC,0DAA0D;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMG,eAAe,GAAGF,GAAE,EAAA,QAAA,CAACG,YAAY,CAACL,mBAAmB,CAAC,CAACM,QAAQ,EAAE,AAAC;IACxE,MAAMC,eAAe,GAAGL,GAAE,EAAA,QAAA,CAACG,YAAY,CAACJ,mBAAmB,CAAC,CAACK,QAAQ,EAAE,AAAC;IACxE,MAAME,MAAM,GAAGf,sBAAsB,CAACW,eAAe,EAAEG,eAAe,CAAC,AAAC;IACxE,4CAA4C;IAC5C,IAAIC,MAAM,CAACC,QAAQ,EAAE;QACnBP,GAAE,EAAA,QAAA,CAACQ,aAAa,CAACV,mBAAmB,EAAEQ,MAAM,CAACC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED,OAAOD,MAAM,CAAC;AAChB,CAAC;AAED;;;;CAIC,GACD,SAASG,0BAA0B,CAACC,SAAiB,EAInD;IACA,MAAMH,QAAQ,GAAGG,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC,AAAC;IACvC,MAAMC,KAAK,GAAGL,QAAQ,CAACM,SAAS,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACC,UAAU,CAAClB,qBAAqB,CAAC,CAAC,AAAC;IACnF,MAAMmB,GAAG,GAAGT,QAAQ,CAACM,SAAS,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACC,UAAU,CAAC3B,sBAAsB,CAAC,CAAC,AAAC;IAElF,OAAO;QAAEmB,QAAQ;QAAEK,KAAK;QAAEI,GAAG;KAAE,CAAC;AAClC,CAAC;AAQM,SAAS1B,gCAAgC,CAACoB,SAAiB,EAAiB;IACjF,MAAM,EAAEH,QAAQ,CAAA,EAAEK,KAAK,CAAA,EAAEI,GAAG,CAAA,EAAE,GAAGP,0BAA0B,CAACC,SAAS,CAAC,AAAC;IACvE,IAAIE,KAAK,GAAG,CAAC,CAAC,IAAII,GAAG,GAAG,CAAC,CAAC,IAAIJ,KAAK,GAAGI,GAAG,EAAE;QACzCT,QAAQ,CAACU,MAAM,CAACL,KAAK,EAAEI,GAAG,GAAGJ,KAAK,GAAG,CAAC,CAAC,CAAC;QACxC,qGAAqG;QACrG,sHAAsH;QACtH,OAAOL,QAAQ,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAQM,SAAS3B,sBAAsB,CACpCW,eAAuB,EACvBG,eAAuB,EACT;IACd,MAAMc,MAAM,GAAG1B,4BAA4B,CAACY,eAAe,CAAC,AAAC;IAC7D,IAAI,CAACH,eAAe,CAACkB,QAAQ,CAACD,MAAM,CAAC,EAAE;QACrC,2DAA2D;QAC3D,MAAME,eAAe,GAAG/B,gCAAgC,CAACY,eAAe,CAAC,AAAC;QAC1E,OAAO;YACLK,QAAQ,EAAE;gBACRc,eAAe,IAAInB,eAAe;gBAClCiB,MAAM;gBACN,CAAC,mDAAmD,CAAC;gBACrD,CAAC,CAAC;gBACFd,eAAe;gBACfjB,sBAAsB;aACvB,CAAC8B,IAAI,CAAC,IAAI,CAAC;YACZI,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,CAAC,CAACF,eAAe;SAC5B,CAAC;IACJ,CAAC;IACD,OAAO;QAAEd,QAAQ,EAAEL,eAAe;QAAEqB,QAAQ,EAAE,KAAK;QAAED,QAAQ,EAAE,KAAK;KAAE,CAAC;AACzE,CAAC;AAMM,SAAS9B,uBAAuB,CACrCM,mBAA2B,EAC3BS,QAAgB,EACK;IACrB,MAAML,eAAe,GAAGF,GAAE,EAAA,QAAA,CAACG,YAAY,CAACL,mBAAmB,EAAE;QAC3D0B,QAAQ,EAAE,OAAO;QACjBC,IAAI,EAAE,IAAI;KACX,CAAC,AAAC;IAEH,IAAIvB,eAAe,CAACwB,KAAK,CAAC,IAAIC,MAAM,CAAC,CAAC,CAAC,EAAEpB,QAAQ,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kEAAkE;IAClE,IAAIL,eAAe,CAACkB,QAAQ,CAACvB,qBAAqB,CAAC,EAAE;QACnD,MAAM+B,OAAO,GAAGnB,0BAA0B,CAACP,eAAe,CAAC,AAAC;QAE5DK,QAAQ,GAAG,CAAC,EAAEqB,OAAO,CAACrB,QAAQ,CAACsB,KAAK,CAACD,OAAO,CAAChB,KAAK,GAAG,CAAC,EAAEgB,OAAO,CAACZ,GAAG,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAEX,QAAQ,CAAC,CAAC,CAAC;IACjG,CAAC;IAED,MAAMD,MAAM,GAAGf,sBAAsB,CAACW,eAAe,EAAEK,QAAQ,CAAC,AAAC;IAEjE,IAAID,MAAM,CAACC,QAAQ,EAAE;QACnBP,GAAE,EAAA,QAAA,CAACQ,aAAa,CAACV,mBAAmB,EAAEQ,MAAM,CAACC,QAAQ,CAAC,CAAC;IACzD,CAAC;IACD,OAAOD,MAAM,CAAC;AAChB,CAAC;AAEM,SAASb,4BAA4B,CAACiB,SAAiB,EAAU;IACtE,MAAMoB,OAAO,GAAGnC,mBAAmB,CAACD,0BAA0B,CAACgB,SAAS,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC,AAAC;IAEtF,OAAO,CAAC,EAAErB,qBAAqB,CAAC,CAAC,EAAEiC,OAAO,CAAC,CAAC,CAAC;AAC/C,CAAC;AAKM,SAASpC,0BAA0B,CAACgB,SAAiB,EAAY;IACtE,oCAAoC;IACpC,OAAOA,SAAS,CACbC,KAAK,CAAC,IAAI,CAAC,CACXoB,MAAM,CAAC,CAACC,CAAC,GAAK;QACb,MAAMlB,IAAI,GAAGkB,CAAC,CAACC,IAAI,EAAE,AAAC;QACtB,iBAAiB;QACjB,IAAInB,IAAI,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,CAAC,CAACD,IAAI,CAAC;IAChB,CAAC,CAAC,CACDoB,IAAI,EAAE,CAAC;AACZ,CAAC;AAEM,SAASvC,mBAAmB,CAACe,SAAiB,EAAU;IAC7D,0DAA0D;IAC1D,MAAMyB,IAAI,GAAGC,OAAM,EAAA,QAAA,CAACC,UAAU,CAAC,MAAM,CAAC,CAACC,MAAM,CAAC5B,SAAS,CAAC,CAAC6B,MAAM,CAAC,KAAK,CAAC,AAAC;IACvE,OAAO,CAAC,KAAK,EAAEJ,IAAI,CAAC,CAAC,CAAC;AACxB,CAAC;AAEM,SAASvC,mBAAmB,CAACE,mBAA2B,EAAES,QAAgB,EAAE;IACjF,IAAI;QACF,IAAI,CAACP,GAAE,EAAA,QAAA,CAACC,UAAU,CAACH,mBAAmB,CAAC,EAAE;YACvC,OAAO;QACT,CAAC;QAED,IAAII,eAAe,GAAGF,GAAE,EAAA,QAAA,CAACG,YAAY,CAACL,mBAAmB,EAAE,OAAO,CAAC,AAAC;QAEpE,IAAI,CAACI,eAAe,CAACkB,QAAQ,CAACb,QAAQ,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAEDL,eAAe,GAAGA,eAAe,CAACsC,OAAO,CAAC,CAAC,EAAEjC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAE/D,MAAMqB,OAAO,GAAGnB,0BAA0B,CAACP,eAAe,CAAC,AAAC;QAE5D,IAAI0B,OAAO,CAAChB,KAAK,KAAKgB,OAAO,CAACZ,GAAG,GAAG,CAAC,EAAE;YACrCd,eAAe,GAAGA,eAAe,CAACsC,OAAO,CACvC,IAAIb,MAAM,CAAC,CAAC,CAAC,EAAE9B,qBAAqB,CAAC,SAAS,EAAET,sBAAsB,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAC/E,EAAE,CACH,CAAC;QACJ,CAAC;QAED,OAAOY,GAAE,EAAA,QAAA,CAACQ,aAAa,CAACV,mBAAmB,EAAEI,eAAe,CAAC,CAAC;IAChE,EAAE,OAAOuC,KAAK,EAAE;QACdC,IAAG,IAAA,CAACD,KAAK,CAAC,CAAC,yCAAyC,EAAE3C,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC7E,MAAM2C,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}