{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/flushFetchDetached.ts"], "sourcesContent": ["import fs from 'node:fs';\n\nimport { FetchClient } from './FetchClient';\nimport type { TelemetryRecordInternal } from '../types';\n\nconst telemetryFile = process.argv[2];\n\nflush()\n  .catch(() => fs.promises.unlink(telemetryFile))\n  .finally(() => process.exit(0));\n\nasync function flush() {\n  if (!telemetryFile) return;\n\n  let json: string;\n  let data: { records: TelemetryRecordInternal[] };\n\n  try {\n    json = await fs.promises.readFile(telemetryFile, 'utf8');\n    data = JSON.parse(json) as any;\n  } catch (error: any) {\n    if (error.code === 'ENOENT') return;\n    throw error;\n  }\n\n  if (data.records.length) {\n    const client = new FetchClient();\n    await client.record(data.records);\n    await client.flush();\n  }\n\n  await fs.promises.unlink(telemetryFile);\n}\n"], "names": ["telemetryFile", "process", "argv", "flush", "catch", "fs", "promises", "unlink", "finally", "exit", "json", "data", "readFile", "JSON", "parse", "error", "code", "records", "length", "client", "FetchClient", "record"], "mappings": "AAAA;;;;;8DAAe,SAAS;;;;;;6BAEI,eAAe;;;;;;AAG3C,MAAMA,aAAa,GAAGC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,AAAC;AAEtCC,KAAK,EAAE,CACJC,KAAK,CAAC,IAAMC,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,MAAM,CAACP,aAAa,CAAC,CAAC,CAC9CQ,OAAO,CAAC,IAAMP,OAAO,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAElC,eAAeN,KAAK,GAAG;IACrB,IAAI,CAACH,aAAa,EAAE,OAAO;IAE3B,IAAIU,IAAI,AAAQ,AAAC;IACjB,IAAIC,IAAI,AAAwC,AAAC;IAEjD,IAAI;QACFD,IAAI,GAAG,MAAML,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACM,QAAQ,CAACZ,aAAa,EAAE,MAAM,CAAC,CAAC;QACzDW,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,AAAO,CAAC;IACjC,EAAE,OAAOK,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAO;QACpC,MAAMD,KAAK,CAAC;IACd,CAAC;IAED,IAAIJ,IAAI,CAACM,OAAO,CAACC,MAAM,EAAE;QACvB,MAAMC,MAAM,GAAG,IAAIC,YAAW,YAAA,EAAE,AAAC;QACjC,MAAMD,MAAM,CAACE,MAAM,CAACV,IAAI,CAACM,OAAO,CAAC,CAAC;QAClC,MAAME,MAAM,CAAChB,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,MAAME,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,MAAM,CAACP,aAAa,CAAC,CAAC;AAC1C,CAAC"}