{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/createHandlersFactory.ts"], "sourcesContent": ["import type { CreateCustomMessageHandlerFn } from '@react-native/dev-middleware';\n\nimport { NetworkResponseHandler } from './messageHandlers/NetworkResponse';\nimport { VscodeDebuggerGetPossibleBreakpointsHandler } from './messageHandlers/VscodeDebuggerGetPossibleBreakpoints';\nimport { VscodeDebuggerSetBreakpointByUrlHandler } from './messageHandlers/VscodeDebuggerSetBreakpointByUrl';\nimport { VscodeRuntimeCallFunctionOnHandler } from './messageHandlers/VscodeRuntimeCallFunctionOn';\nimport { VscodeRuntimeEvaluateHandler } from './messageHandlers/VscodeRuntimeEvaluate';\nimport { VscodeRuntimeGetPropertiesHandler } from './messageHandlers/VscodeRuntimeGetProperties';\nimport { pageIsSupported } from './pageIsSupported';\n\nconst debug = require('debug')('expo:metro:debugging:messageHandlers') as typeof console.log;\n\nexport function createHandlersFactory(): CreateCustomMessageHandlerFn {\n  return (connection) => {\n    debug('Initializing for connection: ', connection.page.title);\n\n    if (!pageIsSupported(connection.page)) {\n      debug('Aborted, unsupported page capabiltiies:', connection.page.capabilities);\n      return null;\n    }\n\n    const handlers = [\n      // Generic handlers\n      new NetworkResponseHandler(connection),\n      // Vscode-specific handlers\n      new VscodeDebuggerGetPossibleBreakpointsHandler(connection),\n      new VscodeDebuggerSetBreakpointByUrlHandler(connection),\n      new VscodeRuntimeGetPropertiesHandler(connection),\n      new VscodeRuntimeCallFunctionOnHandler(connection),\n      new VscodeRuntimeEvaluateHandler(connection),\n    ].filter((middleware) => middleware.isEnabled());\n\n    if (!handlers.length) {\n      debug('Aborted, all handlers are disabled');\n      return null;\n    }\n\n    debug(\n      'Initialized with handlers: ',\n      handlers.map((middleware) => middleware.constructor.name).join(', ')\n    );\n\n    return {\n      handleDeviceMessage: (message: any) =>\n        withMessageDebug(\n          'device',\n          message,\n          handlers.some((middleware) => middleware.handleDeviceMessage?.(message))\n        ),\n      handleDebuggerMessage: (message: any) =>\n        withMessageDebug(\n          'debugger',\n          message,\n          handlers.some((middleware) => middleware.handleDebuggerMessage?.(message))\n        ),\n    };\n  };\n}\n\nfunction withMessageDebug(type: 'device' | 'debugger', message: any, result?: null | boolean) {\n  const status = result ? 'handled' : 'ignored';\n  const prefix = type === 'device' ? '(debugger) <- (device)' : '(debugger) -> (device)';\n\n  try {\n    debug(`%s = %s:`, prefix, status, JSON.stringify(message));\n  } catch {\n    debug(`%s = %s:`, prefix, status, 'message not serializable');\n  }\n\n  return result || undefined;\n}\n"], "names": ["createHandlersFactory", "debug", "require", "connection", "page", "title", "pageIsSupported", "capabilities", "handlers", "NetworkResponseHandler", "VscodeDebuggerGetPossibleBreakpointsHandler", "VscodeDebuggerSetBreakpointByUrlHandler", "VscodeRuntimeGetPropertiesHandler", "VscodeRuntimeCallFunctionOnHandler", "VscodeRuntimeEvaluateHandler", "filter", "middleware", "isEnabled", "length", "map", "constructor", "name", "join", "handleDeviceMessage", "message", "withMessageDebug", "some", "handleDebuggerMessage", "type", "result", "status", "prefix", "JSON", "stringify", "undefined"], "mappings": "AAAA;;;;+BAYg<PERSON>,uBAAqB;;aAArBA,qBAAqB;;iCAVE,mCAAmC;sDACd,wDAAwD;kDAC5D,oDAAoD;6CACzD,+CAA+C;uCACrD,yCAAyC;4CACpC,8CAA8C;iCAChE,mBAAmB;AAEnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAEtF,SAASF,qBAAqB,GAAiC;IACpE,OAAO,CAACG,UAAU,GAAK;QACrBF,KAAK,CAAC,+BAA+B,EAAEE,UAAU,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;QAE9D,IAAI,CAACC,IAAAA,gBAAe,gBAAA,EAACH,UAAU,CAACC,IAAI,CAAC,EAAE;YACrCH,KAAK,CAAC,yCAAyC,EAAEE,UAAU,CAACC,IAAI,CAACG,YAAY,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAMC,QAAQ,GAAG;YACf,mBAAmB;YACnB,IAAIC,gBAAsB,uBAAA,CAACN,UAAU,CAAC;YACtC,2BAA2B;YAC3B,IAAIO,qCAA2C,4CAAA,CAACP,UAAU,CAAC;YAC3D,IAAIQ,iCAAuC,wCAAA,CAACR,UAAU,CAAC;YACvD,IAAIS,2BAAiC,kCAAA,CAACT,UAAU,CAAC;YACjD,IAAIU,4BAAkC,mCAAA,CAACV,UAAU,CAAC;YAClD,IAAIW,sBAA4B,6BAAA,CAACX,UAAU,CAAC;SAC7C,CAACY,MAAM,CAAC,CAACC,UAAU,GAAKA,UAAU,CAACC,SAAS,EAAE,CAAC,AAAC;QAEjD,IAAI,CAACT,QAAQ,CAACU,MAAM,EAAE;YACpBjB,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;QAEDA,KAAK,CACH,6BAA6B,EAC7BO,QAAQ,CAACW,GAAG,CAAC,CAACH,UAAU,GAAKA,UAAU,CAACI,WAAW,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACrE,CAAC;QAEF,OAAO;YACLC,mBAAmB,EAAE,CAACC,OAAY;gBAChCC,OAAAA,gBAAgB,CACd,QAAQ,EACRD,OAAO,EACPhB,QAAQ,CAACkB,IAAI,CAAC,CAACV,UAAU;oBAAKA,OAAAA,UAAU,CAACO,mBAAmB,QAAW,GAAzCP,KAAAA,CAAyC,GAAzCA,UAAU,CAACO,mBAAmB,CAAGC,OAAO,CAAC,CAAA;iBAAA,CAAC,CACzE,CAAA;aAAA;YACHG,qBAAqB,EAAE,CAACH,OAAY;gBAClCC,OAAAA,gBAAgB,CACd,UAAU,EACVD,OAAO,EACPhB,QAAQ,CAACkB,IAAI,CAAC,CAACV,UAAU;oBAAKA,OAAAA,UAAU,CAACW,qBAAqB,QAAW,GAA3CX,KAAAA,CAA2C,GAA3CA,UAAU,CAACW,qBAAqB,CAAGH,OAAO,CAAC,CAAA;iBAAA,CAAC,CAC3E,CAAA;aAAA;SACJ,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAASC,gBAAgB,CAACG,IAA2B,EAAEJ,OAAY,EAAEK,MAAuB,EAAE;IAC5F,MAAMC,MAAM,GAAGD,MAAM,GAAG,SAAS,GAAG,SAAS,AAAC;IAC9C,MAAME,MAAM,GAAGH,IAAI,KAAK,QAAQ,GAAG,wBAAwB,GAAG,wBAAwB,AAAC;IAEvF,IAAI;QACF3B,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE8B,MAAM,EAAED,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACT,OAAO,CAAC,CAAC,CAAC;IAC7D,EAAE,OAAM;QACNvB,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE8B,MAAM,EAAED,MAAM,EAAE,0BAA0B,CAAC,CAAC;IAChE,CAAC;IAED,OAAOD,MAAM,IAAIK,SAAS,CAAC;AAC7B,CAAC"}