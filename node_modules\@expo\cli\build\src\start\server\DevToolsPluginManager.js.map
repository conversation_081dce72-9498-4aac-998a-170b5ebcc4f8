{"version": 3, "sources": ["../../../../src/start/server/DevToolsPluginManager.ts"], "sourcesContent": ["import type { ModuleDescriptorDevTools } from 'expo-modules-autolinking/exports';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nconst debug = require('debug')('expo:start:server:devtools');\n\nexport const DevToolsPluginEndpoint = '/_expo/plugins';\n\ninterface AutolinkingPlugin {\n  packageName: string;\n  packageRoot: string;\n  webpageRoot: string;\n}\n\nexport interface DevToolsPlugin extends AutolinkingPlugin {\n  webpageEndpoint: string;\n}\n\nexport default class DevToolsPluginManager {\n  private plugins: DevToolsPlugin[] | null = null;\n\n  constructor(private projectRoot: string) {}\n\n  public async queryPluginsAsync(): Promise<DevToolsPlugin[]> {\n    if (this.plugins) {\n      return this.plugins;\n    }\n    const plugins = (await this.queryAutolinkedPluginsAsync(this.projectRoot)).map((plugin) => ({\n      ...plugin,\n      webpageEndpoint: `${DevToolsPluginEndpoint}/${plugin.packageName}`,\n    }));\n    this.plugins = plugins;\n    return this.plugins;\n  }\n\n  public async queryPluginWebpageRootAsync(pluginName: string): Promise<string | null> {\n    const plugins = await this.queryPluginsAsync();\n    const plugin = plugins.find((p) => p.packageName === pluginName);\n    return plugin?.webpageRoot ?? null;\n  }\n\n  private async queryAutolinkedPluginsAsync(projectRoot: string): Promise<AutolinkingPlugin[]> {\n    const expoPackagePath = resolveFrom.silent(projectRoot, 'expo/package.json');\n    if (!expoPackagePath) {\n      return [];\n    }\n    const resolvedPath = resolveFrom.silent(\n      path.dirname(expoPackagePath),\n      'expo-modules-autolinking/exports'\n    );\n    if (!resolvedPath) {\n      return [];\n    }\n    const autolinkingModule = require(\n      resolvedPath\n    ) as typeof import('expo-modules-autolinking/exports');\n    if (!autolinkingModule.queryAutolinkingModulesFromProjectAsync) {\n      throw new Error(\n        'Missing exported `queryAutolinkingModulesFromProjectAsync()` function from `expo-modules-autolinking`'\n      );\n    }\n    const plugins = (await autolinkingModule.queryAutolinkingModulesFromProjectAsync(projectRoot, {\n      platform: 'devtools',\n      onlyProjectDeps: false,\n    })) as ModuleDescriptorDevTools[];\n    debug('Found autolinked plugins', this.plugins);\n    return plugins;\n  }\n}\n"], "names": ["DevToolsPluginEndpoint", "DevToolsPluginManager", "debug", "require", "constructor", "projectRoot", "plugins", "queryPluginsAsync", "queryAutolinkedPluginsAsync", "map", "plugin", "webpageEndpoint", "packageName", "queryPluginWebpageRootAsync", "pluginName", "find", "p", "webpageRoot", "expoPackagePath", "resolveFrom", "silent", "<PERSON><PERSON><PERSON>", "path", "dirname", "autolinkingModule", "queryAutolinkingModulesFromProjectAsync", "Error", "platform", "onlyProjectDeps"], "mappings": "AAAA;;;;;;;;;;;IAMaA,sBAAsB,MAAtBA,sBAAsB;IAYnC,OAkDC,MAlDoBC,qBAAqB;;;8DAjBzB,MAAM;;;;;;;8DACC,cAAc;;;;;;;;;;;AAEtC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAC;AAEtD,MAAMH,sBAAsB,GAAG,gBAAgB,AAAC;AAYxC,MAAMC,qBAAqB;IAGxCG,YAAoBC,WAAmB,CAAE;QAArBA,mBAAAA,WAAmB,CAAA;aAF/BC,OAAO,GAA4B,IAAI;IAEL;UAE7BC,iBAAiB,GAA8B;QAC1D,IAAI,IAAI,CAACD,OAAO,EAAE;YAChB,OAAO,IAAI,CAACA,OAAO,CAAC;QACtB,CAAC;QACD,MAAMA,OAAO,GAAG,CAAC,MAAM,IAAI,CAACE,2BAA2B,CAAC,IAAI,CAACH,WAAW,CAAC,CAAC,CAACI,GAAG,CAAC,CAACC,MAAM,GAAK,CAAC;gBAC1F,GAAGA,MAAM;gBACTC,eAAe,EAAE,CAAC,EAAEX,sBAAsB,CAAC,CAAC,EAAEU,MAAM,CAACE,WAAW,CAAC,CAAC;aACnE,CAAC,CAAC,AAAC;QACJ,IAAI,CAACN,OAAO,GAAGA,OAAO,CAAC;QACvB,OAAO,IAAI,CAACA,OAAO,CAAC;IACtB;UAEaO,2BAA2B,CAACC,UAAkB,EAA0B;QACnF,MAAMR,OAAO,GAAG,MAAM,IAAI,CAACC,iBAAiB,EAAE,AAAC;QAC/C,MAAMG,MAAM,GAAGJ,OAAO,CAACS,IAAI,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACJ,WAAW,KAAKE,UAAU,CAAC,AAAC;QACjE,OAAOJ,CAAAA,MAAM,QAAa,GAAnBA,KAAAA,CAAmB,GAAnBA,MAAM,CAAEO,WAAW,CAAA,IAAI,IAAI,CAAC;IACrC;UAEcT,2BAA2B,CAACH,WAAmB,EAAgC;QAC3F,MAAMa,eAAe,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACf,WAAW,EAAE,mBAAmB,CAAC,AAAC;QAC7E,IAAI,CAACa,eAAe,EAAE;YACpB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAMG,YAAY,GAAGF,YAAW,EAAA,QAAA,CAACC,MAAM,CACrCE,KAAI,EAAA,QAAA,CAACC,OAAO,CAACL,eAAe,CAAC,EAC7B,kCAAkC,CACnC,AAAC;QACF,IAAI,CAACG,YAAY,EAAE;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,MAAMG,iBAAiB,GAAGrB,OAAO,CAC/BkB,YAAY,CACb,AAAqD,AAAC;QACvD,IAAI,CAACG,iBAAiB,CAACC,uCAAuC,EAAE;YAC9D,MAAM,IAAIC,KAAK,CACb,uGAAuG,CACxG,CAAC;QACJ,CAAC;QACD,MAAMpB,OAAO,GAAI,MAAMkB,iBAAiB,CAACC,uCAAuC,CAACpB,WAAW,EAAE;YAC5FsB,QAAQ,EAAE,UAAU;YACpBC,eAAe,EAAE,KAAK;SACvB,CAAC,AAA+B,AAAC;QAClC1B,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACI,OAAO,CAAC,CAAC;QAChD,OAAOA,OAAO,CAAC;IACjB;CACD"}