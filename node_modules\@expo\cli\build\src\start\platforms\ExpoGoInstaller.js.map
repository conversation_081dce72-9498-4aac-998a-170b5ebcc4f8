{"version": 3, "sources": ["../../../../src/start/platforms/ExpoGoInstaller.ts"], "sourcesContent": ["import semver from 'semver';\n\nimport type { <PERSON><PERSON><PERSON>anager } from './DeviceManager';\nimport { getVersionsAsync } from '../../api/getVersions';\nimport * as Log from '../../log';\nimport { downloadExpoGoAsync } from '../../utils/downloadExpoGoAsync';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport { logNewSection } from '../../utils/ora';\nimport { confirmAsync } from '../../utils/prompts';\n\nconst debug = require('debug')('expo:utils:ExpoGoInstaller') as typeof console.log;\n\n/** Given a platform, appId, and sdkVersion, this module will ensure that Expo Go is up-to-date on the provided device. */\nexport class ExpoGoInstaller<IDevice> {\n  // Keep a list of [platform-deviceId] so we can prevent asking multiple times if a user wants to upgrade.\n  // This can prevent annoying interactions when they don't want to upgrade for whatever reason.\n  static cache: Record<string, boolean> = {};\n\n  constructor(\n    private platform: 'ios' | 'android',\n    // Ultimately this should be inlined since we know the platform.\n    private appId: string,\n    private sdkVersion: string\n  ) {}\n\n  /** Returns true if the installed app matching the previously provided `appId` is outdated. */\n  isInstalledClientVersionMismatched(\n    installedVersion: string | null,\n    expectedExpoGoVersion: string | null\n  ): boolean {\n    if (!installedVersion) {\n      return true;\n    }\n\n    debug(\n      `Expected Expo Go version: ${expectedExpoGoVersion}, installed version: ${installedVersion}`\n    );\n    return expectedExpoGoVersion ? !semver.eq(installedVersion, expectedExpoGoVersion) : true;\n  }\n\n  /** Returns the expected version of Expo Go given the project SDK Version. Exposed for testing. */\n  async getExpectedExpoGoClientVersionAsync(): Promise<string | null> {\n    const versions = await getVersionsAsync();\n    // Like `sdkVersions['44.0.0']['androidClientVersion'] = '1.0.0'`\n    const specificVersion =\n      versions?.sdkVersions?.[this.sdkVersion]?.[`${this.platform}ClientVersion`];\n    const latestVersion = versions[`${this.platform}Version`];\n    return specificVersion ?? latestVersion ?? null;\n  }\n\n  /** Returns a boolean indicating if Expo Go should be installed. Returns `true` if the app was uninstalled. */\n  async promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync(\n    deviceManager: DeviceManager<IDevice>,\n    { containerPath }: { containerPath?: string } = {}\n  ): Promise<boolean> {\n    const cacheId = `${this.platform}-${deviceManager.identifier}`;\n\n    if (ExpoGoInstaller.cache[cacheId]) {\n      debug('skipping subsequent upgrade check');\n      return false;\n    }\n    ExpoGoInstaller.cache[cacheId] = true;\n\n    const [installedExpoGoVersion, expectedExpoGoVersion] = await Promise.all([\n      deviceManager.getAppVersionAsync(this.appId, {\n        containerPath,\n      }),\n      this.getExpectedExpoGoClientVersionAsync(),\n    ]);\n\n    if (this.isInstalledClientVersionMismatched(installedExpoGoVersion, expectedExpoGoVersion)) {\n      if (this.sdkVersion === 'UNVERSIONED') {\n        // This should only happen in the expo/expo repo, e.g. `apps/test-suite`\n        Log.log(\n          `Skipping Expo Go upgrade check for UNVERSIONED project. Manually ensure the Expo Go app is built from source.`\n        );\n        return false;\n      }\n\n      // Only prompt once per device, per run.\n      const confirm = await confirmAsync({\n        initial: true,\n        message: `Expo Go ${expectedExpoGoVersion} is recommended for SDK ${this.sdkVersion} (${\n          deviceManager.name\n        } is using ${installedExpoGoVersion}). ${learnMore(\n          'https://docs.expo.dev/get-started/expo-go/#sdk-versions'\n        )}. Install the recommended Expo Go version?`,\n      });\n\n      if (confirm) {\n        // Don't need to uninstall to update on iOS.\n        if (this.platform !== 'ios') {\n          Log.log(`Uninstalling Expo Go from ${this.platform} device ${deviceManager.name}.`);\n          await deviceManager.uninstallAppAsync(this.appId);\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /** Check if a given device has Expo Go installed, if not then download and install it. */\n  async ensureAsync(deviceManager: DeviceManager<IDevice>): Promise<boolean> {\n    const isExpoGoInstalledAndIfSoContainerPathForIOS =\n      await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(this.appId);\n    let shouldInstall = !isExpoGoInstalledAndIfSoContainerPathForIOS;\n    if (env.EXPO_OFFLINE) {\n      if (isExpoGoInstalledAndIfSoContainerPathForIOS) {\n        Log.warn(`Skipping Expo Go version validation in offline mode`);\n        return false;\n      }\n      throw new CommandError(\n        'NO_EXPO_GO',\n        `Expo Go is not installed on device \"${deviceManager.name}\", while running in offline mode. Manually install Expo Go or run without --offline flag (or EXPO_OFFLINE environment variable).`\n      );\n    }\n\n    if (isExpoGoInstalledAndIfSoContainerPathForIOS) {\n      shouldInstall =\n        await this.promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync(\n          deviceManager,\n          {\n            // iOS optimization to prevent duplicate calls to `getContainerPathAsync`.\n            containerPath:\n              typeof isExpoGoInstalledAndIfSoContainerPathForIOS === 'string'\n                ? isExpoGoInstalledAndIfSoContainerPathForIOS\n                : undefined,\n          }\n        );\n    }\n\n    if (shouldInstall) {\n      // Download the Expo Go app from the Expo servers.\n      const binaryPath = await downloadExpoGoAsync(this.platform, { sdkVersion: this.sdkVersion });\n      // Install the app on the device.\n      const ora = logNewSection(`Installing Expo Go on ${deviceManager.name}`);\n      try {\n        await deviceManager.installAppAsync(binaryPath);\n      } finally {\n        ora.stop();\n      }\n      return true;\n    }\n    return false;\n  }\n}\n"], "names": ["ExpoGoInstaller", "debug", "require", "cache", "constructor", "platform", "appId", "sdkVersion", "isInstalledClientVersionMismatched", "installedVersion", "expectedExpoGoVersion", "semver", "eq", "getExpectedExpoGoClientVersionAsync", "versions", "getVersionsAsync", "specificVersion", "sdkVersions", "latestVersion", "promptForUninstallExpoGoIfInstalledClientVersionMismatchedAndReturnShouldInstallAsync", "deviceManager", "containerPath", "cacheId", "identifier", "installedExpoGoVersion", "Promise", "all", "getAppVersionAsync", "Log", "log", "confirm", "<PERSON><PERSON><PERSON>", "initial", "message", "name", "learnMore", "uninstallAppAsync", "ensureAsync", "isExpoGoInstalledAndIfSoContainerPathForIOS", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "shouldInstall", "env", "EXPO_OFFLINE", "warn", "CommandError", "undefined", "binaryPath", "downloadExpoGoAsync", "ora", "logNewSection", "installAppAsync", "stop"], "mappings": "AAAA;;;;+BAeaA,iBAAe;;aAAfA,eAAe;;;8DAfT,QAAQ;;;;;;6BAGM,uBAAuB;2DACnC,WAAW;qCACI,iCAAiC;qBACjD,iBAAiB;wBACR,oBAAoB;sBACvB,kBAAkB;qBACd,iBAAiB;yBAClB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAsB,AAAC;AAG5E,MAAMF,eAAe;IAC1B,yGAAyG;IACzG,8FAA8F;IAC9F,OAAOG,KAAK,GAA4B,EAAE,CAAC;IAE3CC,YACUC,QAA2B,EAE3BC,KAAa,EACbC,UAAkB,CAC1B;QAJQF,gBAAAA,QAA2B,CAAA;QAE3BC,aAAAA,KAAa,CAAA;QACbC,kBAAAA,UAAkB,CAAA;IACzB;IAEH,4FAA4F,GAC5FC,kCAAkC,CAChCC,gBAA+B,EAC/BC,qBAAoC,EAC3B;QACT,IAAI,CAACD,gBAAgB,EAAE;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QAEDR,KAAK,CACH,CAAC,0BAA0B,EAAES,qBAAqB,CAAC,qBAAqB,EAAED,gBAAgB,CAAC,CAAC,CAC7F,CAAC;QACF,OAAOC,qBAAqB,GAAG,CAACC,OAAM,EAAA,QAAA,CAACC,EAAE,CAACH,gBAAgB,EAAEC,qBAAqB,CAAC,GAAG,IAAI,CAAC;IAC5F;IAEA,gGAAgG,SAC1FG,mCAAmC,GAA2B;YAIhEC,GAAqB;QAHvB,MAAMA,QAAQ,GAAG,MAAMC,IAAAA,YAAgB,iBAAA,GAAE,AAAC;QAC1C,iEAAiE;QACjE,MAAMC,eAAe,GACnBF,QAAQ,QAAa,GAArBA,KAAAA,CAAqB,GAArBA,CAAAA,GAAqB,GAArBA,QAAQ,CAAEG,WAAW,SAAA,GAArBH,KAAAA,CAAqB,GAArBA,QAAAA,GAAqB,AAAE,CAAC,IAAI,CAACP,UAAU,CAAC,SAAnB,GAArBO,KAAAA,CAAqB,OAAqB,CAAC,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,aAAa,CAAC,CAAC,AAAtD,AAAuD;QAC9E,MAAMa,aAAa,GAAGJ,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACT,QAAQ,CAAC,OAAO,CAAC,CAAC,AAAC;QAC1D,OAAOW,eAAe,IAAIE,aAAa,IAAI,IAAI,CAAC;IAClD;IAEA,4GAA4G,SACtGC,qFAAqF,CACzFC,aAAqC,EACrC,EAAEC,aAAa,CAAA,EAA8B,GAAG,EAAE,EAChC;QAClB,MAAMC,OAAO,GAAG,CAAC,EAAE,IAAI,CAACjB,QAAQ,CAAC,CAAC,EAAEe,aAAa,CAACG,UAAU,CAAC,CAAC,AAAC;QAE/D,IAAIvB,eAAe,CAACG,KAAK,CAACmB,OAAO,CAAC,EAAE;YAClCrB,KAAK,CAAC,mCAAmC,CAAC,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QACDD,eAAe,CAACG,KAAK,CAACmB,OAAO,CAAC,GAAG,IAAI,CAAC;QAEtC,MAAM,CAACE,sBAAsB,EAAEd,qBAAqB,CAAC,GAAG,MAAMe,OAAO,CAACC,GAAG,CAAC;YACxEN,aAAa,CAACO,kBAAkB,CAAC,IAAI,CAACrB,KAAK,EAAE;gBAC3Ce,aAAa;aACd,CAAC;YACF,IAAI,CAACR,mCAAmC,EAAE;SAC3C,CAAC,AAAC;QAEH,IAAI,IAAI,CAACL,kCAAkC,CAACgB,sBAAsB,EAAEd,qBAAqB,CAAC,EAAE;YAC1F,IAAI,IAAI,CAACH,UAAU,KAAK,aAAa,EAAE;gBACrC,wEAAwE;gBACxEqB,IAAG,CAACC,GAAG,CACL,CAAC,6GAA6G,CAAC,CAChH,CAAC;gBACF,OAAO,KAAK,CAAC;YACf,CAAC;YAED,wCAAwC;YACxC,MAAMC,OAAO,GAAG,MAAMC,IAAAA,QAAY,aAAA,EAAC;gBACjCC,OAAO,EAAE,IAAI;gBACbC,OAAO,EAAE,CAAC,QAAQ,EAAEvB,qBAAqB,CAAC,wBAAwB,EAAE,IAAI,CAACH,UAAU,CAAC,EAAE,EACpFa,aAAa,CAACc,IAAI,CACnB,UAAU,EAAEV,sBAAsB,CAAC,GAAG,EAAEW,IAAAA,KAAS,UAAA,EAChD,yDAAyD,CAC1D,CAAC,0CAA0C,CAAC;aAC9C,CAAC,AAAC;YAEH,IAAIL,OAAO,EAAE;gBACX,4CAA4C;gBAC5C,IAAI,IAAI,CAACzB,QAAQ,KAAK,KAAK,EAAE;oBAC3BuB,IAAG,CAACC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAACxB,QAAQ,CAAC,QAAQ,EAAEe,aAAa,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpF,MAAMd,aAAa,CAACgB,iBAAiB,CAAC,IAAI,CAAC9B,KAAK,CAAC,CAAC;gBACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf;IAEA,wFAAwF,SAClF+B,WAAW,CAACjB,aAAqC,EAAoB;QACzE,MAAMkB,2CAA2C,GAC/C,MAAMlB,aAAa,CAACmB,mDAAmD,CAAC,IAAI,CAACjC,KAAK,CAAC,AAAC;QACtF,IAAIkC,aAAa,GAAG,CAACF,2CAA2C,AAAC;QACjE,IAAIG,IAAG,IAAA,CAACC,YAAY,EAAE;YACpB,IAAIJ,2CAA2C,EAAE;gBAC/CV,IAAG,CAACe,IAAI,CAAC,CAAC,mDAAmD,CAAC,CAAC,CAAC;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,IAAIC,OAAY,aAAA,CACpB,YAAY,EACZ,CAAC,oCAAoC,EAAExB,aAAa,CAACc,IAAI,CAAC,gIAAgI,CAAC,CAC5L,CAAC;QACJ,CAAC;QAED,IAAII,2CAA2C,EAAE;YAC/CE,aAAa,GACX,MAAM,IAAI,CAACrB,qFAAqF,CAC9FC,aAAa,EACb;gBACE,0EAA0E;gBAC1EC,aAAa,EACX,OAAOiB,2CAA2C,KAAK,QAAQ,GAC3DA,2CAA2C,GAC3CO,SAAS;aAChB,CACF,CAAC;QACN,CAAC;QAED,IAAIL,aAAa,EAAE;YACjB,kDAAkD;YAClD,MAAMM,UAAU,GAAG,MAAMC,IAAAA,oBAAmB,oBAAA,EAAC,IAAI,CAAC1C,QAAQ,EAAE;gBAAEE,UAAU,EAAE,IAAI,CAACA,UAAU;aAAE,CAAC,AAAC;YAC7F,iCAAiC;YACjC,MAAMyC,GAAG,GAAGC,IAAAA,IAAa,cAAA,EAAC,CAAC,sBAAsB,EAAE7B,aAAa,CAACc,IAAI,CAAC,CAAC,CAAC,AAAC;YACzE,IAAI;gBACF,MAAMd,aAAa,CAAC8B,eAAe,CAACJ,UAAU,CAAC,CAAC;YAClD,SAAU;gBACRE,GAAG,CAACG,IAAI,EAAE,CAAC;YACb,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf;CACD"}