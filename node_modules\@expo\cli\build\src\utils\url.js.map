{"version": 3, "sources": ["../../../src/utils/url.ts"], "sourcesContent": ["import dns from 'dns';\nimport { URL } from 'url';\n\nimport { fetchAsync } from '../api/rest/client';\n\n/** Check if a server is available based on the URL. */\nexport function isUrlAvailableAsync(url: string): Promise<boolean> {\n  return new Promise<boolean>((resolve) => {\n    dns.lookup(url, (err) => {\n      resolve(!err);\n    });\n  });\n}\n\n/** Check if a request to the given URL is `ok` (status 200). */\nexport async function isUrlOk(url: string): Promise<boolean> {\n  try {\n    const res = await fetchAsync(url);\n    return res.ok;\n  } catch {\n    return false;\n  }\n}\n\n/** Determine if a string is a valid URL, can optionally ensure certain protocols (like `https` or `exp`) are adhered to. */\nexport function validateUrl(\n  urlString: string,\n  {\n    protocols,\n    requireProtocol,\n  }: {\n    /** Set of allowed protocols for the string to adhere to. @example ['exp', 'https'] */\n    protocols?: string[];\n    /** Ensure the URL has a protocol component (prefix before `://`). */\n    requireProtocol?: boolean;\n  } = {}\n) {\n  try {\n    const results = new URL(urlString);\n    if (!results.protocol && !requireProtocol) {\n      return true;\n    }\n    return protocols\n      ? results.protocol\n        ? protocols.map((x) => `${x.toLowerCase()}:`).includes(results.protocol)\n        : false\n      : true;\n  } catch {\n    return false;\n  }\n}\n\n/** Remove the port from a given `host` URL string. */\nexport function stripPort(host?: string): string | null {\n  return coerceUrl(host)?.hostname ?? null;\n}\n\nfunction coerceUrl(urlString?: string): URL | null {\n  if (!urlString) {\n    return null;\n  }\n  try {\n    return new URL('/', urlString);\n  } catch {\n    return new URL('/', `http://${urlString}`);\n  }\n}\n\n/** Strip a given extension from a URL string. */\nexport function stripExtension(url: string, extension: string): string {\n  return url.replace(new RegExp(`.${extension}$`), '');\n}\n"], "names": ["isUrlAvailableAsync", "isUrlOk", "validateUrl", "stripPort", "stripExtension", "url", "Promise", "resolve", "dns", "lookup", "err", "res", "fetchAsync", "ok", "urlString", "protocols", "requireProtocol", "results", "URL", "protocol", "map", "x", "toLowerCase", "includes", "host", "coerceUrl", "hostname", "extension", "replace", "RegExp"], "mappings": "AAAA;;;;;;;;;;;IAMgBA,mBAAmB,MAAnBA,mBAAmB;IASbC,OAAO,MAAPA,OAAO;IAUbC,WAAW,MAAXA,WAAW;IA4BXC,SAAS,MAATA,SAAS;IAgBTC,cAAc,MAAdA,cAAc;;;8DArEd,KAAK;;;;;;;yBACD,KAAK;;;;;;wBAEE,oBAAoB;;;;;;AAGxC,SAASJ,mBAAmB,CAACK,GAAW,EAAoB;IACjE,OAAO,IAAIC,OAAO,CAAU,CAACC,OAAO,GAAK;QACvCC,IAAG,EAAA,QAAA,CAACC,MAAM,CAACJ,GAAG,EAAE,CAACK,GAAG,GAAK;YACvBH,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAGM,eAAeT,OAAO,CAACI,GAAW,EAAoB;IAC3D,IAAI;QACF,MAAMM,GAAG,GAAG,MAAMC,IAAAA,OAAU,WAAA,EAACP,GAAG,CAAC,AAAC;QAClC,OAAOM,GAAG,CAACE,EAAE,CAAC;IAChB,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAGM,SAASX,WAAW,CACzBY,SAAiB,EACjB,EACEC,SAAS,CAAA,EACTC,eAAe,CAAA,EAMhB,GAAG,EAAE,EACN;IACA,IAAI;QACF,MAAMC,OAAO,GAAG,IAAIC,CAAAA,IAAG,EAAA,CAAA,IAAA,CAACJ,SAAS,CAAC,AAAC;QACnC,IAAI,CAACG,OAAO,CAACE,QAAQ,IAAI,CAACH,eAAe,EAAE;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAOD,SAAS,GACZE,OAAO,CAACE,QAAQ,GACdJ,SAAS,CAACK,GAAG,CAAC,CAACC,CAAC,GAAK,CAAC,EAAEA,CAAC,CAACC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACN,OAAO,CAACE,QAAQ,CAAC,GACtE,KAAK,GACP,IAAI,CAAC;IACX,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAGM,SAAShB,SAAS,CAACqB,IAAa,EAAiB;QAC/CC,GAAe;IAAtB,OAAOA,CAAAA,CAAAA,GAAe,GAAfA,SAAS,CAACD,IAAI,CAAC,SAAU,GAAzBC,KAAAA,CAAyB,GAAzBA,GAAe,CAAEC,QAAQ,CAAA,IAAI,IAAI,CAAC;AAC3C,CAAC;AAED,SAASD,SAAS,CAACX,SAAkB,EAAc;IACjD,IAAI,CAACA,SAAS,EAAE;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI;QACF,OAAO,IAAII,CAAAA,IAAG,EAAA,CAAA,IAAA,CAAC,GAAG,EAAEJ,SAAS,CAAC,CAAC;IACjC,EAAE,OAAM;QACN,OAAO,IAAII,CAAAA,IAAG,EAAA,CAAA,IAAA,CAAC,GAAG,EAAE,CAAC,OAAO,EAAEJ,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAGM,SAASV,cAAc,CAACC,GAAW,EAAEsB,SAAiB,EAAU;IACrE,OAAOtB,GAAG,CAACuB,OAAO,CAAC,IAAIC,MAAM,CAAC,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACvD,CAAC"}