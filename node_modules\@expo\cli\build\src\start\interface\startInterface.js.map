{"version": 3, "sources": ["../../../../src/start/interface/startInterface.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './KeyPressHandler';\nimport { BLT, printHelp, printUsage, StartOptions } from './commandsTable';\nimport { DevServerManagerActions } from './interactiveActions';\nimport * as Log from '../../log';\nimport { openInEditorAsync } from '../../utils/editor';\nimport { AbortCommandError } from '../../utils/errors';\nimport { getAllSpinners, ora } from '../../utils/ora';\nimport { getProgressBar, setProgressBar } from '../../utils/progress';\nimport { addInteractionListener, pauseInteractions } from '../../utils/prompts';\nimport { WebSupportProjectPrerequisite } from '../doctor/web/WebSupportProjectPrerequisite';\nimport { DevServerManager } from '../server/DevServerManager';\n\nconst debug = require('debug')('expo:start:interface:startInterface') as typeof console.log;\n\nconst CTRL_C = '\\u0003';\nconst CTRL_D = '\\u0004';\nconst CTRL_L = '\\u000C';\n\nconst PLATFORM_SETTINGS: Record<\n  string,\n  { name: string; key: 'android' | 'ios'; launchTarget: 'emulator' | 'simulator' }\n> = {\n  android: {\n    name: 'Android',\n    key: 'android',\n    launchTarget: 'emulator',\n  },\n  ios: {\n    name: 'iOS',\n    key: 'ios',\n    launchTarget: 'simulator',\n  },\n};\n\nexport async function startInterfaceAsync(\n  devServerManager: DevServerManager,\n  options: Pick<StartOptions, 'devClient' | 'platforms'>\n) {\n  const actions = new DevServerManagerActions(devServerManager, options);\n\n  const isWebSocketsEnabled = devServerManager.getDefaultDevServer()?.isTargetingNative();\n\n  const usageOptions = {\n    isWebSocketsEnabled,\n    devClient: devServerManager.options.devClient,\n    ...options,\n  };\n\n  actions.printDevServerInfo(usageOptions);\n\n  const onPressAsync = async (key: string) => {\n    // Auxillary commands all escape.\n    switch (key) {\n      case CTRL_C:\n      case CTRL_D: {\n        // Prevent terminal UI from accepting commands while the process is closing.\n        // Without this, fast typers will close the server then start typing their\n        // next command and have a bunch of unrelated things pop up.\n        pauseInteractions();\n\n        const spinners = getAllSpinners();\n        spinners.forEach((spinner) => {\n          spinner.fail();\n        });\n\n        const currentProgress = getProgressBar();\n        if (currentProgress) {\n          currentProgress.terminate();\n          setProgressBar(null);\n        }\n        const spinner = ora({ text: 'Stopping server', color: 'white' }).start();\n        try {\n          await devServerManager.stopAsync();\n          spinner.stopAndPersist({ text: 'Stopped server', symbol: `\\u203A` });\n          // @ts-ignore: Argument of type '\"SIGINT\"' is not assignable to parameter of type '\"disconnect\"'.\n          process.emit('SIGINT');\n\n          // TODO: Is this the right place to do this?\n          process.exit();\n        } catch (error) {\n          spinner.fail('Failed to stop server');\n          throw error;\n        }\n        break;\n      }\n      case CTRL_L:\n        return Log.clear();\n      case '?':\n        return printUsage(usageOptions, { verbose: true });\n    }\n\n    // Optionally enabled\n\n    if (isWebSocketsEnabled) {\n      switch (key) {\n        case 'm':\n          return actions.toggleDevMenu();\n        case 'M':\n          return actions.openMoreToolsAsync();\n      }\n    }\n\n    const { platforms = ['ios', 'android', 'web'] } = options;\n\n    if (['i', 'a'].includes(key.toLowerCase())) {\n      const platform = key.toLowerCase() === 'i' ? 'ios' : 'android';\n\n      const shouldPrompt = ['I', 'A'].includes(key);\n      if (shouldPrompt) {\n        Log.clear();\n      }\n\n      const server = devServerManager.getDefaultDevServer();\n      const settings = PLATFORM_SETTINGS[platform];\n\n      Log.log(`${BLT} Opening on ${settings.name}...`);\n\n      if (server.isTargetingNative() && !platforms.includes(settings.key)) {\n        Log.warn(\n          chalk`${settings.name} is disabled, enable it by adding {bold ${settings.key}} to the platforms array in your app.json or app.config.js`\n        );\n      } else {\n        try {\n          await server.openPlatformAsync(settings.launchTarget, { shouldPrompt });\n          printHelp();\n        } catch (error: any) {\n          if (!(error instanceof AbortCommandError)) {\n            Log.exception(error);\n          }\n        }\n      }\n      // Break out early.\n      return;\n    }\n\n    switch (key) {\n      case 's': {\n        Log.clear();\n        if (await devServerManager.toggleRuntimeMode()) {\n          usageOptions.devClient = devServerManager.options.devClient;\n          return actions.printDevServerInfo(usageOptions);\n        }\n        break;\n      }\n      case 'w': {\n        try {\n          await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n          if (!platforms.includes('web')) {\n            platforms.push('web');\n            options.platforms?.push('web');\n          }\n        } catch (e: any) {\n          Log.warn(e.message);\n          break;\n        }\n\n        const isDisabled = !platforms.includes('web');\n        if (isDisabled) {\n          debug('Web is disabled');\n          // Use warnings from the web support setup.\n          break;\n        }\n\n        // Ensure the Webpack dev server is running first\n        if (!devServerManager.getWebDevServer()) {\n          debug('Starting up webpack dev server');\n          await devServerManager.ensureWebDevServerRunningAsync();\n          // When this is the first time webpack is started, reprint the connection info.\n          actions.printDevServerInfo(usageOptions);\n        }\n\n        Log.log(`${BLT} Open in the web browser...`);\n        try {\n          await devServerManager.getWebDevServer()?.openPlatformAsync('desktop');\n          printHelp();\n        } catch (error: any) {\n          if (!(error instanceof AbortCommandError)) {\n            Log.exception(error);\n          }\n        }\n        break;\n      }\n      case 'c':\n        Log.clear();\n        return actions.printDevServerInfo(usageOptions);\n      case 'j':\n        return actions.openJsInspectorAsync();\n      case 'r':\n        return actions.reloadApp();\n      case 'o':\n        Log.log(`${BLT} Opening the editor...`);\n        return openInEditorAsync(devServerManager.projectRoot);\n    }\n  };\n\n  const keyPressHandler = new KeyPressHandler(onPressAsync);\n\n  const listener = keyPressHandler.createInteractionListener();\n\n  addInteractionListener(listener);\n\n  // Start observing...\n  keyPressHandler.startInterceptingKeyStrokes();\n}\n"], "names": ["startInterfaceAsync", "debug", "require", "CTRL_C", "CTRL_D", "CTRL_L", "PLATFORM_SETTINGS", "android", "name", "key", "launchTarget", "ios", "devServerManager", "options", "actions", "DevServerManagerActions", "isWebSocketsEnabled", "getDefaultDevServer", "isTargetingNative", "usageOptions", "devClient", "printDevServerInfo", "onPressAsync", "pauseInteractions", "spinners", "getAllSpinners", "for<PERSON>ach", "spinner", "fail", "currentProgress", "getProgressBar", "terminate", "setProgressBar", "ora", "text", "color", "start", "stopAsync", "stopAndPersist", "symbol", "process", "emit", "exit", "error", "Log", "clear", "printUsage", "verbose", "toggleDevMenu", "openMoreToolsAsync", "platforms", "includes", "toLowerCase", "platform", "should<PERSON>rompt", "server", "settings", "log", "BLT", "warn", "chalk", "openPlatformAsync", "printHelp", "AbortCommandError", "exception", "toggleRuntimeMode", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "push", "e", "message", "isDisabled", "getWebDevServer", "ensureWebDevServerRunningAsync", "openJsInspectorAsync", "reloadApp", "openInEditorAsync", "projectRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listener", "createInteractionListener", "addInteractionListener", "startInterceptingKeyStrokes"], "mappings": "AAAA;;;;+BA<PERSON><PERSON><PERSON>,qBAAmB;;aAAnBA,mBAAmB;;;8DApCvB,OAAO;;;;;;iCAEO,mBAAmB;+BACM,iBAAiB;oCAClC,sBAAsB;2DACzC,WAAW;wBACE,oBAAoB;wBACpB,oBAAoB;qBAClB,iBAAiB;0BACN,sBAAsB;yBACX,qBAAqB;+CACjC,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG3F,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC,AAAsB,AAAC;AAE5F,MAAMC,MAAM,GAAG,MAAQ,AAAC;AACxB,MAAMC,MAAM,GAAG,MAAQ,AAAC;AACxB,MAAMC,MAAM,GAAG,IAAQ,AAAC;AAExB,MAAMC,iBAAiB,GAGnB;IACFC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,GAAG,EAAE,SAAS;QACdC,YAAY,EAAE,UAAU;KACzB;IACDC,GAAG,EAAE;QACHH,IAAI,EAAE,KAAK;QACXC,GAAG,EAAE,KAAK;QACVC,YAAY,EAAE,WAAW;KAC1B;CACF,AAAC;AAEK,eAAeV,mBAAmB,CACvCY,gBAAkC,EAClCC,OAAsD,EACtD;QAG4BD,GAAsC;IAFlE,MAAME,OAAO,GAAG,IAAIC,mBAAuB,wBAAA,CAACH,gBAAgB,EAAEC,OAAO,CAAC,AAAC;IAEvE,MAAMG,mBAAmB,GAAGJ,CAAAA,GAAsC,GAAtCA,gBAAgB,CAACK,mBAAmB,EAAE,SAAmB,GAAzDL,KAAAA,CAAyD,GAAzDA,GAAsC,CAAEM,iBAAiB,EAAE,AAAC;IAExF,MAAMC,YAAY,GAAG;QACnBH,mBAAmB;QACnBI,SAAS,EAAER,gBAAgB,CAACC,OAAO,CAACO,SAAS;QAC7C,GAAGP,OAAO;KACX,AAAC;IAEFC,OAAO,CAACO,kBAAkB,CAACF,YAAY,CAAC,CAAC;IAEzC,MAAMG,YAAY,GAAG,OAAOb,GAAW,GAAK;QAC1C,iCAAiC;QACjC,OAAQA,GAAG;YACT,KAAKN,MAAM,CAAC;YACZ,KAAKC,MAAM;gBAAE;oBACX,4EAA4E;oBAC5E,0EAA0E;oBAC1E,4DAA4D;oBAC5DmB,IAAAA,QAAiB,kBAAA,GAAE,CAAC;oBAEpB,MAAMC,QAAQ,GAAGC,IAAAA,IAAc,eAAA,GAAE,AAAC;oBAClCD,QAAQ,CAACE,OAAO,CAAC,CAACC,OAAO,GAAK;wBAC5BA,OAAO,CAACC,IAAI,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC;oBAEH,MAAMC,eAAe,GAAGC,IAAAA,SAAc,eAAA,GAAE,AAAC;oBACzC,IAAID,eAAe,EAAE;wBACnBA,eAAe,CAACE,SAAS,EAAE,CAAC;wBAC5BC,IAAAA,SAAc,eAAA,EAAC,IAAI,CAAC,CAAC;oBACvB,CAAC;oBACD,MAAML,OAAO,GAAGM,IAAAA,IAAG,IAAA,EAAC;wBAAEC,IAAI,EAAE,iBAAiB;wBAAEC,KAAK,EAAE,OAAO;qBAAE,CAAC,CAACC,KAAK,EAAE,AAAC;oBACzE,IAAI;wBACF,MAAMxB,gBAAgB,CAACyB,SAAS,EAAE,CAAC;wBACnCV,OAAO,CAACW,cAAc,CAAC;4BAAEJ,IAAI,EAAE,gBAAgB;4BAAEK,MAAM,EAAE,CAAC,MAAM,CAAC;yBAAE,CAAC,CAAC;wBACrE,iGAAiG;wBACjGC,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAEvB,4CAA4C;wBAC5CD,OAAO,CAACE,IAAI,EAAE,CAAC;oBACjB,EAAE,OAAOC,KAAK,EAAE;wBACdhB,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAC,CAAC;wBACtC,MAAMe,KAAK,CAAC;oBACd,CAAC;oBACD,MAAM;gBACR,CAAC;YACD,KAAKtC,MAAM;gBACT,OAAOuC,IAAG,CAACC,KAAK,EAAE,CAAC;YACrB,KAAK,GAAG;gBACN,OAAOC,IAAAA,cAAU,WAAA,EAAC3B,YAAY,EAAE;oBAAE4B,OAAO,EAAE,IAAI;iBAAE,CAAC,CAAC;SACtD;QAED,qBAAqB;QAErB,IAAI/B,mBAAmB,EAAE;YACvB,OAAQP,GAAG;gBACT,KAAK,GAAG;oBACN,OAAOK,OAAO,CAACkC,aAAa,EAAE,CAAC;gBACjC,KAAK,GAAG;oBACN,OAAOlC,OAAO,CAACmC,kBAAkB,EAAE,CAAC;aACvC;QACH,CAAC;QAED,MAAM,EAAEC,SAAS,EAAG;YAAC,KAAK;YAAE,SAAS;YAAE,KAAK;SAAC,CAAA,EAAE,GAAGrC,OAAO,AAAC;QAE1D,IAAI;YAAC,GAAG;YAAE,GAAG;SAAC,CAACsC,QAAQ,CAAC1C,GAAG,CAAC2C,WAAW,EAAE,CAAC,EAAE;YAC1C,MAAMC,QAAQ,GAAG5C,GAAG,CAAC2C,WAAW,EAAE,KAAK,GAAG,GAAG,KAAK,GAAG,SAAS,AAAC;YAE/D,MAAME,YAAY,GAAG;gBAAC,GAAG;gBAAE,GAAG;aAAC,CAACH,QAAQ,CAAC1C,GAAG,CAAC,AAAC;YAC9C,IAAI6C,YAAY,EAAE;gBAChBV,IAAG,CAACC,KAAK,EAAE,CAAC;YACd,CAAC;YAED,MAAMU,MAAM,GAAG3C,gBAAgB,CAACK,mBAAmB,EAAE,AAAC;YACtD,MAAMuC,QAAQ,GAAGlD,iBAAiB,CAAC+C,QAAQ,CAAC,AAAC;YAE7CT,IAAG,CAACa,GAAG,CAAC,CAAC,EAAEC,cAAG,IAAA,CAAC,YAAY,EAAEF,QAAQ,CAAChD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAEjD,IAAI+C,MAAM,CAACrC,iBAAiB,EAAE,IAAI,CAACgC,SAAS,CAACC,QAAQ,CAACK,QAAQ,CAAC/C,GAAG,CAAC,EAAE;gBACnEmC,IAAG,CAACe,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,EAAEJ,QAAQ,CAAChD,IAAI,CAAC,wCAAwC,EAAEgD,QAAQ,CAAC/C,GAAG,CAAC,0DAA0D,CAAC,CACzI,CAAC;YACJ,OAAO;gBACL,IAAI;oBACF,MAAM8C,MAAM,CAACM,iBAAiB,CAACL,QAAQ,CAAC9C,YAAY,EAAE;wBAAE4C,YAAY;qBAAE,CAAC,CAAC;oBACxEQ,IAAAA,cAAS,UAAA,GAAE,CAAC;gBACd,EAAE,OAAOnB,MAAK,EAAO;oBACnB,IAAI,CAAC,CAACA,MAAK,YAAYoB,OAAiB,kBAAA,CAAC,EAAE;wBACzCnB,IAAG,CAACoB,SAAS,CAACrB,MAAK,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;YACD,mBAAmB;YACnB,OAAO;QACT,CAAC;QAED,OAAQlC,GAAG;YACT,KAAK,GAAG;gBAAE;oBACRmC,IAAG,CAACC,KAAK,EAAE,CAAC;oBACZ,IAAI,MAAMjC,gBAAgB,CAACqD,iBAAiB,EAAE,EAAE;wBAC9C9C,YAAY,CAACC,SAAS,GAAGR,gBAAgB,CAACC,OAAO,CAACO,SAAS,CAAC;wBAC5D,OAAON,OAAO,CAACO,kBAAkB,CAACF,YAAY,CAAC,CAAC;oBAClD,CAAC;oBACD,MAAM;gBACR,CAAC;YACD,KAAK,GAAG;gBAAE;oBACR,IAAI;wBACF,MAAMP,gBAAgB,CAACsD,8BAA8B,CAACC,8BAA6B,8BAAA,CAAC,CAAC;wBACrF,IAAI,CAACjB,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;gCAE9BtC,GAAiB;4BADjBqC,SAAS,CAACkB,IAAI,CAAC,KAAK,CAAC,CAAC;4BACtBvD,CAAAA,GAAiB,GAAjBA,OAAO,CAACqC,SAAS,SAAM,GAAvBrC,KAAAA,CAAuB,GAAvBA,GAAiB,CAAEuD,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC,CAAC;oBACH,EAAE,OAAOC,CAAC,EAAO;wBACfzB,IAAG,CAACe,IAAI,CAACU,CAAC,CAACC,OAAO,CAAC,CAAC;wBACpB,MAAM;oBACR,CAAC;oBAED,MAAMC,UAAU,GAAG,CAACrB,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC,AAAC;oBAC9C,IAAIoB,UAAU,EAAE;wBACdtE,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBAEzB,MAAM;oBACR,CAAC;oBAED,iDAAiD;oBACjD,IAAI,CAACW,gBAAgB,CAAC4D,eAAe,EAAE,EAAE;wBACvCvE,KAAK,CAAC,gCAAgC,CAAC,CAAC;wBACxC,MAAMW,gBAAgB,CAAC6D,8BAA8B,EAAE,CAAC;wBACxD,+EAA+E;wBAC/E3D,OAAO,CAACO,kBAAkB,CAACF,YAAY,CAAC,CAAC;oBAC3C,CAAC;oBAEDyB,IAAG,CAACa,GAAG,CAAC,CAAC,EAAEC,cAAG,IAAA,CAAC,2BAA2B,CAAC,CAAC,CAAC;oBAC7C,IAAI;4BACI9C,IAAkC;wBAAxC,OAAMA,CAAAA,IAAkC,GAAlCA,gBAAgB,CAAC4D,eAAe,EAAE,SAAmB,GAArD5D,KAAAA,CAAqD,GAArDA,IAAkC,CAAEiD,iBAAiB,CAAC,SAAS,CAAC,CAAA,CAAC;wBACvEC,IAAAA,cAAS,UAAA,GAAE,CAAC;oBACd,EAAE,OAAOnB,MAAK,EAAO;wBACnB,IAAI,CAAC,CAACA,MAAK,YAAYoB,OAAiB,kBAAA,CAAC,EAAE;4BACzCnB,IAAG,CAACoB,SAAS,CAACrB,MAAK,CAAC,CAAC;wBACvB,CAAC;oBACH,CAAC;oBACD,MAAM;gBACR,CAAC;YACD,KAAK,GAAG;gBACNC,IAAG,CAACC,KAAK,EAAE,CAAC;gBACZ,OAAO/B,OAAO,CAACO,kBAAkB,CAACF,YAAY,CAAC,CAAC;YAClD,KAAK,GAAG;gBACN,OAAOL,OAAO,CAAC4D,oBAAoB,EAAE,CAAC;YACxC,KAAK,GAAG;gBACN,OAAO5D,OAAO,CAAC6D,SAAS,EAAE,CAAC;YAC7B,KAAK,GAAG;gBACN/B,IAAG,CAACa,GAAG,CAAC,CAAC,EAAEC,cAAG,IAAA,CAAC,sBAAsB,CAAC,CAAC,CAAC;gBACxC,OAAOkB,IAAAA,OAAiB,kBAAA,EAAChE,gBAAgB,CAACiE,WAAW,CAAC,CAAC;SAC1D;IACH,CAAC,AAAC;IAEF,MAAMC,eAAe,GAAG,IAAIC,gBAAe,gBAAA,CAACzD,YAAY,CAAC,AAAC;IAE1D,MAAM0D,QAAQ,GAAGF,eAAe,CAACG,yBAAyB,EAAE,AAAC;IAE7DC,IAAAA,QAAsB,uBAAA,EAACF,QAAQ,CAAC,CAAC;IAEjC,qBAAqB;IACrBF,eAAe,CAACK,2BAA2B,EAAE,CAAC;AAChD,CAAC"}