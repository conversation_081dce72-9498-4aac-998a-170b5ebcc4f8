/**
 * @flow strict
 */

import { PureComponent } from 'react';

export type Color = number | string;

export type IconButtonProps<Glyphs: string> = {
  backgroundColor?: Color,
  borderRadius?: number,
  color?: Color,
  name: Glyphs,
  size?: number,
  ...
};

declare class IconButton<Glyphs: string> extends PureComponent<
  IconButtonProps<Glyphs>
> {}

export type IconProps<Glyphs: string> = {
  allowFontScaling?: boolean,
  color?: Color,
  name: Glyphs,
  size?: number,
  ...
};

export type ImageSource = {|
  uri: string,
  scale: number,
|};

declare class Icon<Glyphs: string> extends PureComponent<IconProps<Glyphs>> {
  static Button: Class<IconButton<Glyphs>>;

  static getFontFamily(): string;
  static getImageSource(
    name: Glyphs,
    size?: number,
    color?: Color
  ): Promise<ImageSource>;
  static getImageSourceSync(
    name: Glyphs,
    size?: number,
    color?: Color
  ): ImageSource;
  static getRawGlyphMap(): { [name: Glyphs]: number };
  static hasIcon(name: string): boolean;
  static loadFont(file?: string): Promise<void>;
}

export type { Icon };

declare export function createIconSet<GlyphMap: { [key: string]: number }>(
  glyphMap: GlyphMap,
  fontFamily: string,
  fontFile?: string
): Class<Icon<$Keys<GlyphMap>>>;

export type FontelloConfig = {
  glyphs: Array<{
    css: string,
    code: number,
  }>,
};

declare export function createIconSetFromFontello(
  config: FontelloConfig,
  fontFamily?: string,
  fontFile?: string
): Class<Icon<string>>;

export type IcoMoonConfig = {
  icons: Array<{
    properties: { name: string, code: number },
  }>,
};

declare export function createIconSetFromIcoMoon(
  config: IcoMoonConfig,
  fontFamily?: string,
  fontFile?: string
): Class<Icon<string>>;
