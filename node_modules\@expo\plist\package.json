{"name": "@expo/plist", "version": "0.3.5", "description": "Mac OS X Plist parser/builder for Node.js and browsers", "main": "build/index.js", "scripts": {"build": "expo-module tsc", "prepare": "yarn run clean && yarn run build", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "watch": "yarn run build --watch --preserveWatchOutput", "prepublishOnly": "expo-module prepublishOnly"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/plist"}, "keywords": ["plist"], "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/plist#readme", "files": ["build"], "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.2.3", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@types/base64-js": "^1.2.5", "expo-module-scripts": "^4.1.9"}, "publishConfig": {"access": "public"}, "gitHead": "7980ffdc39bf0ca82e62b59148a43ed755991ecb"}