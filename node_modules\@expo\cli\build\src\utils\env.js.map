{"version": 3, "sources": ["../../../src/utils/env.ts"], "sourcesContent": ["import { boolish, int, string } from 'getenv';\nimport process from 'node:process';\n\n// @expo/webpack-config -> expo-pwa -> @expo/image-utils: EXPO_IMAGE_UTILS_NO_SHARP\n\n// TODO: EXPO_CLI_USERNAME, EXPO_CLI_PASSWORD\n\nclass Env {\n  /** Enable profiling metrics */\n  get EXPO_PROFILE() {\n    return boolish('EXPO_PROFILE', false);\n  }\n\n  /** Enable debug logging */\n  get EXPO_DEBUG() {\n    return boolish('EXPO_DEBUG', false);\n  }\n\n  /** Disable all network requests */\n  get EXPO_OFFLINE() {\n    return boolish('EXPO_OFFLINE', false);\n  }\n\n  /** Enable the beta version of Expo (TODO: Should this just be in the beta version of expo releases?) */\n  get EXPO_BETA() {\n    return boolish('EXPO_BETA', false);\n  }\n\n  /** Enable staging API environment */\n  get EXPO_STAGING() {\n    return boolish('EXPO_STAGING', false);\n  }\n\n  /** Enable local API environment */\n  get EXPO_LOCAL() {\n    return boolish('EXPO_LOCAL', false);\n  }\n\n  /** Is running in non-interactive CI mode */\n  get CI() {\n    return boolish('CI', false);\n  }\n\n  /** Disable telemetry (analytics) */\n  get EXPO_NO_TELEMETRY() {\n    return boolish('EXPO_NO_TELEMETRY', false);\n  }\n\n  /** Disable detaching telemetry to separate process */\n  get EXPO_NO_TELEMETRY_DETACH() {\n    return boolish('EXPO_NO_TELEMETRY_DETACH', false);\n  }\n\n  /** local directory to the universe repo for testing locally */\n  get EXPO_UNIVERSE_DIR() {\n    return string('EXPO_UNIVERSE_DIR', '');\n  }\n\n  /** @deprecated Default Webpack host string */\n  get WEB_HOST() {\n    return string('WEB_HOST', '0.0.0.0');\n  }\n\n  /** Skip warning users about a dirty git status */\n  get EXPO_NO_GIT_STATUS() {\n    return boolish('EXPO_NO_GIT_STATUS', false);\n  }\n  /** Disable auto web setup */\n  get EXPO_NO_WEB_SETUP() {\n    return boolish('EXPO_NO_WEB_SETUP', false);\n  }\n  /** Disable auto TypeScript setup */\n  get EXPO_NO_TYPESCRIPT_SETUP() {\n    return boolish('EXPO_NO_TYPESCRIPT_SETUP', false);\n  }\n  /** Disable all API caches. Does not disable bundler caches. */\n  get EXPO_NO_CACHE() {\n    return boolish('EXPO_NO_CACHE', false);\n  }\n  /** Disable the app select redirect page. */\n  get EXPO_NO_REDIRECT_PAGE() {\n    return boolish('EXPO_NO_REDIRECT_PAGE', false);\n  }\n  /** The React Metro port that's baked into react-native scripts and tools. */\n  get RCT_METRO_PORT() {\n    return int('RCT_METRO_PORT', 0);\n  }\n  /** Skip validating the manifest during `export`. */\n  get EXPO_SKIP_MANIFEST_VALIDATION_TOKEN(): boolean {\n    return !!string('EXPO_SKIP_MANIFEST_VALIDATION_TOKEN', '');\n  }\n\n  /** Public folder path relative to the project root. Default to `public` */\n  get EXPO_PUBLIC_FOLDER(): string {\n    return string('EXPO_PUBLIC_FOLDER', 'public');\n  }\n\n  /** Higher priority `$EDIOTR` variable for indicating which editor to use when pressing `o` in the Terminal UI. */\n  get EXPO_EDITOR(): string {\n    return string('EXPO_EDITOR', '');\n  }\n\n  /**\n   * Overwrite the dev server URL, disregarding the `--port`, `--host`, `--tunnel`, `--lan`, `--localhost` arguments.\n   * This is useful for browser editors that require custom proxy URLs.\n   */\n  get EXPO_PACKAGER_PROXY_URL(): string {\n    return string('EXPO_PACKAGER_PROXY_URL', '');\n  }\n\n  /**\n   * **Experimental** - Disable using `exp.direct` as the hostname for\n   * `--tunnel` connections. This enables **https://** forwarding which\n   * can be used to test universal links on iOS.\n   *\n   * This may cause issues with `expo-linking` and Expo Go.\n   *\n   * Select the exact subdomain by passing a string value that is not one of: `true`, `false`, `1`, `0`.\n   */\n  get EXPO_TUNNEL_SUBDOMAIN(): string | boolean {\n    const subdomain = string('EXPO_TUNNEL_SUBDOMAIN', '');\n    if (['0', 'false', ''].includes(subdomain)) {\n      return false;\n    } else if (['1', 'true'].includes(subdomain)) {\n      return true;\n    }\n    return subdomain;\n  }\n\n  /**\n   * Force Expo CLI to use the [`resolver.resolverMainFields`](https://facebook.github.io/metro/docs/configuration/#resolvermainfields) from the project `metro.config.js` for all platforms.\n   *\n   * By default, Expo CLI will use `['browser', 'module', 'main']` (default for Webpack) for web and the user-defined main fields for other platforms.\n   */\n  get EXPO_METRO_NO_MAIN_FIELD_OVERRIDE(): boolean {\n    return boolish('EXPO_METRO_NO_MAIN_FIELD_OVERRIDE', false);\n  }\n\n  /**\n   * HTTP/HTTPS proxy to connect to for network requests. Configures [https-proxy-agent](https://www.npmjs.com/package/https-proxy-agent).\n   */\n  get HTTP_PROXY(): string {\n    return process.env.HTTP_PROXY || process.env.http_proxy || '';\n  }\n\n  /**\n   * Use the network inspector by overriding the metro inspector proxy with a custom version.\n   * @deprecated This has been replaced by `@react-native/dev-middleware` and is now unused.\n   */\n  get EXPO_NO_INSPECTOR_PROXY(): boolean {\n    return boolish('EXPO_NO_INSPECTOR_PROXY', false);\n  }\n\n  /** Disable lazy bundling in Metro bundler. */\n  get EXPO_NO_METRO_LAZY() {\n    return boolish('EXPO_NO_METRO_LAZY', false);\n  }\n\n  /** Enable the unstable inverse dependency stack trace for Metro bundling errors. */\n  get EXPO_METRO_UNSTABLE_ERRORS() {\n    return boolish('EXPO_METRO_UNSTABLE_ERRORS', false);\n  }\n\n  /** Enable the unstable fast resolver for Metro. */\n  get EXPO_USE_FAST_RESOLVER() {\n    return boolish('EXPO_USE_FAST_RESOLVER', false);\n  }\n\n  /** Disable Environment Variable injection in client bundles. */\n  get EXPO_NO_CLIENT_ENV_VARS(): boolean {\n    return boolish('EXPO_NO_CLIENT_ENV_VARS', false);\n  }\n\n  /** Set the default `user` that should be passed to `--user` with ADB commands. Used for installing APKs on Android devices with multiple profiles. Defaults to `0`. */\n  get EXPO_ADB_USER(): string {\n    return string('EXPO_ADB_USER', '0');\n  }\n\n  /** Used internally to enable E2E utilities. This behavior is not stable to external users. */\n  get __EXPO_E2E_TEST(): boolean {\n    return boolish('__EXPO_E2E_TEST', false);\n  }\n\n  /** Unstable: Force single-bundle exports in production. */\n  get EXPO_NO_BUNDLE_SPLITTING(): boolean {\n    return boolish('EXPO_NO_BUNDLE_SPLITTING', false);\n  }\n\n  /** Enable unstable/experimental Atlas to gather bundle information during development or export */\n  get EXPO_UNSTABLE_ATLAS() {\n    return boolish('EXPO_UNSTABLE_ATLAS', false);\n  }\n\n  /** Unstable: Enable tree shaking for Metro. */\n  get EXPO_UNSTABLE_TREE_SHAKING() {\n    return boolish('EXPO_UNSTABLE_TREE_SHAKING', false);\n  }\n\n  /** Unstable: Enable eager bundling where transformation runs uncached after the entire bundle has been created. This is required for production tree shaking and less optimized for development bundling. */\n  get EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH() {\n    return boolish('EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH', false);\n  }\n\n  /** Enable the use of Expo's custom metro require implementation. The custom require supports better debugging, tree shaking, and React Server Components. */\n  get EXPO_USE_METRO_REQUIRE() {\n    return boolish('EXPO_USE_METRO_REQUIRE', false);\n  }\n\n  /** Internal key used to pass eager bundle data from the CLI to the native run scripts during `npx expo run` commands. */\n  get __EXPO_EAGER_BUNDLE_OPTIONS() {\n    return string('__EXPO_EAGER_BUNDLE_OPTIONS', '');\n  }\n\n  /** Disable server deployment during production builds (during `expo export:embed`). This is useful for testing API routes and server components against a local server. */\n  get EXPO_NO_DEPLOY(): boolean {\n    return boolish('EXPO_NO_DEPLOY', false);\n  }\n\n  /** Enable hydration during development when rendering Expo Web */\n  get EXPO_WEB_DEV_HYDRATE(): boolean {\n    return boolish('EXPO_WEB_DEV_HYDRATE', false);\n  }\n\n  /** Enable experimental React Server Functions support. */\n  get EXPO_UNSTABLE_SERVER_FUNCTIONS(): boolean {\n    return boolish('EXPO_UNSTABLE_SERVER_FUNCTIONS', false);\n  }\n\n  /** Enable unstable/experimental mode where React Native Web isn't required to run Expo apps on web. */\n  get EXPO_NO_REACT_NATIVE_WEB(): boolean {\n    return boolish('EXPO_NO_REACT_NATIVE_WEB', false);\n  }\n\n  /** Enable unstable/experimental support for deploying the native server in `npx expo run` commands. */\n  get EXPO_UNSTABLE_DEPLOY_SERVER(): boolean {\n    return boolish('EXPO_UNSTABLE_DEPLOY_SERVER', false);\n  }\n\n  /** Is running in EAS Build. This is set by EAS: https://docs.expo.dev/eas/environment-variables/ */\n  get EAS_BUILD(): boolean {\n    return boolish('EAS_BUILD', false);\n  }\n\n  /** Disable the React Native Directory compatibility check for new architecture when installing packages */\n  get EXPO_NO_NEW_ARCH_COMPAT_CHECK(): boolean {\n    return boolish('EXPO_NO_NEW_ARCH_COMPAT_CHECK', false);\n  }\n\n  /** Disable the dependency validation when installing other dependencies and starting the project */\n  get EXPO_NO_DEPENDENCY_VALIDATION(): boolean {\n    // Default to disabling when running in a web container (stackblitz, bolt, etc).\n    const isWebContainer = process.versions.webcontainer != null;\n    return boolish('EXPO_NO_DEPENDENCY_VALIDATION', isWebContainer);\n  }\n\n  /** Force Expo CLI to run in webcontainer mode, this has impact on which URL Expo is using by default */\n  get EXPO_FORCE_WEBCONTAINER_ENV(): boolean {\n    return boolish('EXPO_FORCE_WEBCONTAINER_ENV', false);\n  }\n}\n\nexport const env = new Env();\n\nexport function envIsWebcontainer() {\n  // See: https://github.com/unjs/std-env/blob/4b1e03c4efce58249858efc2cc5f5eac727d0adb/src/providers.ts#L134-L143\n  return (\n    env.EXPO_FORCE_WEBCONTAINER_ENV ||\n    (process.env.SHELL === '/bin/jsh' && !!process.versions.webcontainer)\n  );\n}\n"], "names": ["env", "envIsWebcontainer", "Env", "EXPO_PROFILE", "boolish", "EXPO_DEBUG", "EXPO_OFFLINE", "EXPO_BETA", "EXPO_STAGING", "EXPO_LOCAL", "CI", "EXPO_NO_TELEMETRY", "EXPO_NO_TELEMETRY_DETACH", "EXPO_UNIVERSE_DIR", "string", "WEB_HOST", "EXPO_NO_GIT_STATUS", "EXPO_NO_WEB_SETUP", "EXPO_NO_TYPESCRIPT_SETUP", "EXPO_NO_CACHE", "EXPO_NO_REDIRECT_PAGE", "RCT_METRO_PORT", "int", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN", "EXPO_PUBLIC_FOLDER", "EXPO_EDITOR", "EXPO_PACKAGER_PROXY_URL", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "includes", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "HTTP_PROXY", "process", "http_proxy", "EXPO_NO_INSPECTOR_PROXY", "EXPO_NO_METRO_LAZY", "EXPO_METRO_UNSTABLE_ERRORS", "EXPO_USE_FAST_RESOLVER", "EXPO_NO_CLIENT_ENV_VARS", "EXPO_ADB_USER", "__EXPO_E2E_TEST", "EXPO_NO_BUNDLE_SPLITTING", "EXPO_UNSTABLE_ATLAS", "EXPO_UNSTABLE_TREE_SHAKING", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "EXPO_USE_METRO_REQUIRE", "__EXPO_EAGER_BUNDLE_OPTIONS", "EXPO_NO_DEPLOY", "EXPO_WEB_DEV_HYDRATE", "EXPO_UNSTABLE_SERVER_FUNCTIONS", "EXPO_NO_REACT_NATIVE_WEB", "EXPO_UNSTABLE_DEPLOY_SERVER", "EAS_BUILD", "EXPO_NO_NEW_ARCH_COMPAT_CHECK", "EXPO_NO_DEPENDENCY_VALIDATION", "isWebContainer", "versions", "webcontainer", "EXPO_FORCE_WEBCONTAINER_ENV", "SHELL"], "mappings": "AAAA;;;;;;;;;;;IAqQaA,GAAG,MAAHA,GAAG;IAEAC,iBAAiB,MAAjBA,iBAAiB;;;yBAvQI,QAAQ;;;;;;;8DACzB,cAAc;;;;;;;;;;;AAElC,mFAAmF;AAEnF,6CAA6C;AAE7C,MAAMC,GAAG;IACP,6BAA6B,OACzBC,YAAY,GAAG;QACjB,OAAOC,IAAAA,OAAO,EAAA,QAAA,EAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACxC;IAEA,yBAAyB,OACrBC,UAAU,GAAG;QACf,OAAOD,IAAAA,OAAO,EAAA,QAAA,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACtC;IAEA,iCAAiC,OAC7BE,YAAY,GAAG;QACjB,OAAOF,IAAAA,OAAO,EAAA,QAAA,EAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACxC;IAEA,sGAAsG,OAClGG,SAAS,GAAG;QACd,OAAOH,IAAAA,OAAO,EAAA,QAAA,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACrC;IAEA,mCAAmC,OAC/BI,YAAY,GAAG;QACjB,OAAOJ,IAAAA,OAAO,EAAA,QAAA,EAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IACxC;IAEA,iCAAiC,OAC7BK,UAAU,GAAG;QACf,OAAOL,IAAAA,OAAO,EAAA,QAAA,EAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACtC;IAEA,0CAA0C,OACtCM,EAAE,GAAG;QACP,OAAON,IAAAA,OAAO,EAAA,QAAA,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B;IAEA,kCAAkC,OAC9BO,iBAAiB,GAAG;QACtB,OAAOP,IAAAA,OAAO,EAAA,QAAA,EAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAC7C;IAEA,oDAAoD,OAChDQ,wBAAwB,GAAG;QAC7B,OAAOR,IAAAA,OAAO,EAAA,QAAA,EAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACpD;IAEA,6DAA6D,OACzDS,iBAAiB,GAAG;QACtB,OAAOC,IAAAA,OAAM,EAAA,OAAA,EAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;IACzC;IAEA,4CAA4C,OACxCC,QAAQ,GAAG;QACb,OAAOD,IAAAA,OAAM,EAAA,OAAA,EAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACvC;IAEA,gDAAgD,OAC5CE,kBAAkB,GAAG;QACvB,OAAOZ,IAAAA,OAAO,EAAA,QAAA,EAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IAC9C;IACA,2BAA2B,OACvBa,iBAAiB,GAAG;QACtB,OAAOb,IAAAA,OAAO,EAAA,QAAA,EAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAC7C;IACA,kCAAkC,OAC9Bc,wBAAwB,GAAG;QAC7B,OAAOd,IAAAA,OAAO,EAAA,QAAA,EAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACpD;IACA,6DAA6D,OACzDe,aAAa,GAAG;QAClB,OAAOf,IAAAA,OAAO,EAAA,QAAA,EAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACzC;IACA,0CAA0C,OACtCgB,qBAAqB,GAAG;QAC1B,OAAOhB,IAAAA,OAAO,EAAA,QAAA,EAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;IACjD;IACA,2EAA2E,OACvEiB,cAAc,GAAG;QACnB,OAAOC,IAAAA,OAAG,EAAA,IAAA,EAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;IAClC;IACA,kDAAkD,OAC9CC,mCAAmC,GAAY;QACjD,OAAO,CAAC,CAACT,IAAAA,OAAM,EAAA,OAAA,EAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;IAC7D;IAEA,yEAAyE,OACrEU,kBAAkB,GAAW;QAC/B,OAAOV,IAAAA,OAAM,EAAA,OAAA,EAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;IAChD;IAEA,gHAAgH,OAC5GW,WAAW,GAAW;QACxB,OAAOX,IAAAA,OAAM,EAAA,OAAA,EAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IACnC;IAEA;;;GAGC,OACGY,uBAAuB,GAAW;QACpC,OAAOZ,IAAAA,OAAM,EAAA,OAAA,EAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAC/C;IAEA;;;;;;;;GAQC,OACGa,qBAAqB,GAAqB;QAC5C,MAAMC,SAAS,GAAGd,IAAAA,OAAM,EAAA,OAAA,EAAC,uBAAuB,EAAE,EAAE,CAAC,AAAC;QACtD,IAAI;YAAC,GAAG;YAAE,OAAO;YAAE,EAAE;SAAC,CAACe,QAAQ,CAACD,SAAS,CAAC,EAAE;YAC1C,OAAO,KAAK,CAAC;QACf,OAAO,IAAI;YAAC,GAAG;YAAE,MAAM;SAAC,CAACC,QAAQ,CAACD,SAAS,CAAC,EAAE;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAOA,SAAS,CAAC;IACnB;IAEA;;;;GAIC,OACGE,iCAAiC,GAAY;QAC/C,OAAO1B,IAAAA,OAAO,EAAA,QAAA,EAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC7D;IAEA;;GAEC,OACG2B,UAAU,GAAW;QACvB,OAAOC,YAAO,EAAA,QAAA,CAAChC,GAAG,CAAC+B,UAAU,IAAIC,YAAO,EAAA,QAAA,CAAChC,GAAG,CAACiC,UAAU,IAAI,EAAE,CAAC;IAChE;IAEA;;;GAGC,OACGC,uBAAuB,GAAY;QACrC,OAAO9B,IAAAA,OAAO,EAAA,QAAA,EAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IACnD;IAEA,4CAA4C,OACxC+B,kBAAkB,GAAG;QACvB,OAAO/B,IAAAA,OAAO,EAAA,QAAA,EAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IAC9C;IAEA,kFAAkF,OAC9EgC,0BAA0B,GAAG;QAC/B,OAAOhC,IAAAA,OAAO,EAAA,QAAA,EAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACtD;IAEA,iDAAiD,OAC7CiC,sBAAsB,GAAG;QAC3B,OAAOjC,IAAAA,OAAO,EAAA,QAAA,EAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;IAClD;IAEA,8DAA8D,OAC1DkC,uBAAuB,GAAY;QACrC,OAAOlC,IAAAA,OAAO,EAAA,QAAA,EAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IACnD;IAEA,qKAAqK,OACjKmC,aAAa,GAAW;QAC1B,OAAOzB,IAAAA,OAAM,EAAA,OAAA,EAAC,eAAe,EAAE,GAAG,CAAC,CAAC;IACtC;IAEA,4FAA4F,OACxF0B,eAAe,GAAY;QAC7B,OAAOpC,IAAAA,OAAO,EAAA,QAAA,EAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAC3C;IAEA,yDAAyD,OACrDqC,wBAAwB,GAAY;QACtC,OAAOrC,IAAAA,OAAO,EAAA,QAAA,EAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACpD;IAEA,iGAAiG,OAC7FsC,mBAAmB,GAAG;QACxB,OAAOtC,IAAAA,OAAO,EAAA,QAAA,EAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;IAC/C;IAEA,6CAA6C,OACzCuC,0BAA0B,GAAG;QAC/B,OAAOvC,IAAAA,OAAO,EAAA,QAAA,EAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACtD;IAEA,2MAA2M,OACvMwC,kCAAkC,GAAG;QACvC,OAAOxC,IAAAA,OAAO,EAAA,QAAA,EAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;IAC9D;IAEA,2JAA2J,OACvJyC,sBAAsB,GAAG;QAC3B,OAAOzC,IAAAA,OAAO,EAAA,QAAA,EAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;IAClD;IAEA,uHAAuH,OACnH0C,2BAA2B,GAAG;QAChC,OAAOhC,IAAAA,OAAM,EAAA,OAAA,EAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;IACnD;IAEA,yKAAyK,OACrKiC,cAAc,GAAY;QAC5B,OAAO3C,IAAAA,OAAO,EAAA,QAAA,EAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC1C;IAEA,gEAAgE,OAC5D4C,oBAAoB,GAAY;QAClC,OAAO5C,IAAAA,OAAO,EAAA,QAAA,EAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;IAChD;IAEA,wDAAwD,OACpD6C,8BAA8B,GAAY;QAC5C,OAAO7C,IAAAA,OAAO,EAAA,QAAA,EAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;IAC1D;IAEA,qGAAqG,OACjG8C,wBAAwB,GAAY;QACtC,OAAO9C,IAAAA,OAAO,EAAA,QAAA,EAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACpD;IAEA,qGAAqG,OACjG+C,2BAA2B,GAAY;QACzC,OAAO/C,IAAAA,OAAO,EAAA,QAAA,EAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACvD;IAEA,kGAAkG,OAC9FgD,SAAS,GAAY;QACvB,OAAOhD,IAAAA,OAAO,EAAA,QAAA,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACrC;IAEA,yGAAyG,OACrGiD,6BAA6B,GAAY;QAC3C,OAAOjD,IAAAA,OAAO,EAAA,QAAA,EAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;IACzD;IAEA,kGAAkG,OAC9FkD,6BAA6B,GAAY;QAC3C,gFAAgF;QAChF,MAAMC,cAAc,GAAGvB,YAAO,EAAA,QAAA,CAACwB,QAAQ,CAACC,YAAY,IAAI,IAAI,AAAC;QAC7D,OAAOrD,IAAAA,OAAO,EAAA,QAAA,EAAC,+BAA+B,EAAEmD,cAAc,CAAC,CAAC;IAClE;IAEA,sGAAsG,OAClGG,2BAA2B,GAAY;QACzC,OAAOtD,IAAAA,OAAO,EAAA,QAAA,EAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;IACvD;CACD;AAEM,MAAMJ,GAAG,GAAG,IAAIE,GAAG,EAAE,AAAC;AAEtB,SAASD,iBAAiB,GAAG;IAClC,gHAAgH;IAChH,OACED,GAAG,CAAC0D,2BAA2B,IAC9B1B,YAAO,EAAA,QAAA,CAAChC,GAAG,CAAC2D,KAAK,KAAK,UAAU,IAAI,CAAC,CAAC3B,YAAO,EAAA,QAAA,CAACwB,QAAQ,CAACC,YAAY,AAAC,CACrE;AACJ,CAAC"}