{"version": 3, "sources": ["../../../../../src/start/platforms/ios/AppleAppIdResolver.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport plist from '@expo/plist';\nimport fs from 'fs';\n\nimport { AppIdResolver } from '../AppIdResolver';\n\nconst debug = require('debug')('expo:start:platforms:ios:AppleAppIdResolver') as typeof console.log;\n\n/** Resolves the iOS bundle identifier from the Expo config or native files. */\nexport class AppleAppIdResolver extends AppIdResolver {\n  constructor(projectRoot: string) {\n    super(projectRoot, 'ios', 'ios.bundleIdentifier');\n  }\n\n  /** @return `true` if the app has valid `*.pbxproj` file */\n  async hasNativeProjectAsync(): Promise<boolean> {\n    try {\n      // Never returns nullish values.\n      return !!IOSConfig.Paths.getAllPBXProjectPaths(this.projectRoot).length;\n    } catch (error: any) {\n      debug('Expected error checking for native project:', error.message);\n      return false;\n    }\n  }\n\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    // Check xcode project\n    try {\n      const bundleId = IOSConfig.BundleIdentifier.getBundleIdentifierFromPbxproj(this.projectRoot);\n      if (bundleId) {\n        return bundleId;\n      }\n    } catch (error: any) {\n      debug('Expected error resolving the bundle identifier from the pbxproj:', error);\n    }\n\n    // Check Info.plist\n    try {\n      const infoPlistPath = IOSConfig.Paths.getInfoPlistPath(this.projectRoot);\n      const data = await plist.parse(fs.readFileSync(infoPlistPath, 'utf8'));\n      if (data.CFBundleIdentifier && !data.CFBundleIdentifier.startsWith('$(')) {\n        return data.CFBundleIdentifier;\n      }\n    } catch (error) {\n      debug('Expected error resolving the bundle identifier from the project Info.plist:', error);\n    }\n\n    return null;\n  }\n}\n"], "names": ["AppleAppIdResolver", "debug", "require", "AppIdResolver", "constructor", "projectRoot", "hasNativeProjectAsync", "IOSConfig", "Paths", "getAllPBXProjectPaths", "length", "error", "message", "resolveAppIdFromNativeAsync", "bundleId", "BundleIdentifier", "getBundleIdentifierFromPbxproj", "infoPlistPath", "getInfoPlistPath", "data", "plist", "parse", "fs", "readFileSync", "CFBundleIdentifier", "startsWith"], "mappings": "AAAA;;;;+BA<PERSON>a<PERSON>,oBAAkB;;aAAlBA,kBAAkB;;;yBATL,sBAAsB;;;;;;;8DAC9B,aAAa;;;;;;;8DAChB,IAAI;;;;;;+BAEW,kBAAkB;;;;;;AAEhD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6CAA6C,CAAC,AAAsB,AAAC;AAG7F,MAAMF,kBAAkB,SAASG,cAAa,cAAA;IACnDC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,WAAW,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAC;IACpD;IAEA,yDAAyD,SACnDC,qBAAqB,GAAqB;QAC9C,IAAI;YACF,gCAAgC;YAChC,OAAO,CAAC,CAACC,cAAS,EAAA,UAAA,CAACC,KAAK,CAACC,qBAAqB,CAAC,IAAI,CAACJ,WAAW,CAAC,CAACK,MAAM,CAAC;QAC1E,EAAE,OAAOC,KAAK,EAAO;YACnBV,KAAK,CAAC,6CAA6C,EAAEU,KAAK,CAACC,OAAO,CAAC,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;IACH;UAEMC,2BAA2B,GAA2B;QAC1D,sBAAsB;QACtB,IAAI;YACF,MAAMC,QAAQ,GAAGP,cAAS,EAAA,UAAA,CAACQ,gBAAgB,CAACC,8BAA8B,CAAC,IAAI,CAACX,WAAW,CAAC,AAAC;YAC7F,IAAIS,QAAQ,EAAE;gBACZ,OAAOA,QAAQ,CAAC;YAClB,CAAC;QACH,EAAE,OAAOH,KAAK,EAAO;YACnBV,KAAK,CAAC,kEAAkE,EAAEU,KAAK,CAAC,CAAC;QACnF,CAAC;QAED,mBAAmB;QACnB,IAAI;YACF,MAAMM,aAAa,GAAGV,cAAS,EAAA,UAAA,CAACC,KAAK,CAACU,gBAAgB,CAAC,IAAI,CAACb,WAAW,CAAC,AAAC;YACzE,MAAMc,IAAI,GAAG,MAAMC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACC,GAAE,EAAA,QAAA,CAACC,YAAY,CAACN,aAAa,EAAE,MAAM,CAAC,CAAC,AAAC;YACvE,IAAIE,IAAI,CAACK,kBAAkB,IAAI,CAACL,IAAI,CAACK,kBAAkB,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACxE,OAAON,IAAI,CAACK,kBAAkB,CAAC;YACjC,CAAC;QACH,EAAE,OAAOb,MAAK,EAAE;YACdV,KAAK,CAAC,6EAA6E,EAAEU,MAAK,CAAC,CAAC;QAC9F,CAAC;QAED,OAAO,IAAI,CAAC;IACd;CACD"}