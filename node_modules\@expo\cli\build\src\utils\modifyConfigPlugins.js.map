{"version": 3, "sources": ["../../../src/utils/modifyConfigPlugins.ts"], "sourcesContent": ["import { modifyConfigAsync } from '@expo/config';\n\nimport { warnAboutConfigAndThrow } from './modifyConfigAsync';\nimport * as Log from '../log';\n\nexport async function attemptAddingPluginsAsync(\n  projectRoot: string,\n  plugins: string[]\n): Promise<void> {\n  if (!plugins.length) return;\n\n  const modification = await modifyConfigAsync(\n    projectRoot,\n    { plugins },\n    {\n      skipSDKVersionRequirement: true,\n      skipPlugins: true,\n    }\n  );\n  if (modification.type === 'success') {\n    Log.log(`\\u203A Added config plugin${plugins.length === 1 ? '' : 's'}: ${plugins.join(', ')}`);\n  } else {\n    const exactEdits = {\n      plugins,\n    };\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n"], "names": ["attemptAddingPluginsAsync", "projectRoot", "plugins", "length", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "type", "Log", "log", "join", "exactEdits", "warnAboutConfigAndThrow", "message"], "mappings": "AAAA;;;;+BAKs<PERSON>,2BAAyB;;aAAzBA,yBAAyB;;;yBALb,cAAc;;;;;;mCAER,qBAAqB;2DACxC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,eAAeA,yBAAyB,CAC7CC,WAAmB,EACnBC,OAAiB,EACF;IACf,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO;IAE5B,MAAMC,YAAY,GAAG,MAAMC,IAAAA,OAAiB,EAAA,kBAAA,EAC1CJ,WAAW,EACX;QAAEC,OAAO;KAAE,EACX;QACEI,yBAAyB,EAAE,IAAI;QAC/BC,WAAW,EAAE,IAAI;KAClB,CACF,AAAC;IACF,IAAIH,YAAY,CAACI,IAAI,KAAK,SAAS,EAAE;QACnCC,IAAG,CAACC,GAAG,CAAC,CAAC,0BAA0B,EAAER,OAAO,CAACC,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAED,OAAO,CAACS,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,OAAO;QACL,MAAMC,UAAU,GAAG;YACjBV,OAAO;SACR,AAAC;QACFW,IAAAA,kBAAuB,wBAAA,EAACT,YAAY,CAACI,IAAI,EAAEJ,YAAY,CAACU,OAAO,EAAGF,UAAU,CAAC,CAAC;IAChF,CAAC;AACH,CAAC"}