import { authApi } from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface LoginCredentials {
  email: string;
  password: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  role: 'STORE' | 'DELIVERY';
}

interface AuthResponse {
  access_token: string;
  user: User;
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await authApi.post<AuthResponse>('/auth/login', credentials);
    await AsyncStorage.setItem('token', response.data.access_token);
    await AsyncStorage.setItem('user', JSON.stringify(response.data.user));
    return response.data.user;
  },
  
  async logout(): Promise<void> {
    await authApi.delete('/auth/logout');
    await AsyncStorage.removeItem('token');
    await AsyncStorage.removeItem('user');
  },
  
  async getProfile(): Promise<User> {
    const response = await authApi.get<User>('/users/profile');
    return response.data;
  }
};