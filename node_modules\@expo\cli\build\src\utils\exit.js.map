{"version": 3, "sources": ["../../../src/utils/exit.ts"], "sourcesContent": ["import { ChildProcess } from 'node:child_process';\nimport process from 'node:process';\n\nimport { guardAsync } from './fn';\nimport { warn } from '../log';\n\nconst debug = require('debug')('expo:utils:exit') as typeof console.log;\n\ntype AsyncExitHook = (signal: NodeJS.Signals) => void | Promise<void>;\n\nconst PRE_EXIT_SIGNALS: NodeJS.Signals[] = ['SIGHUP', 'SIGINT', 'SIGTERM', 'SIGBREAK'];\n\n// We create a queue since Node.js throws an error if we try to append too many listeners:\n// (node:4405) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 SIGINT listeners added to [process]. Use emitter.setMaxListeners() to increase limit\nconst queue: AsyncExitHook[] = [];\n\nlet unsubscribe: (() => void) | null = null;\n\n/** Add functions that run before the process exits. Returns a function for removing the listeners. */\nexport function installExitHooks(asyncExitHook: AsyncExitHook): () => void {\n  // We need to instantiate the master listener the first time the queue is used.\n  if (!queue.length) {\n    // Track the master listener so we can remove it later.\n    unsubscribe = attachMasterListener();\n  }\n\n  queue.push(asyncExitHook);\n\n  return () => {\n    const index = queue.indexOf(asyncExitHook);\n    if (index >= 0) {\n      queue.splice(index, 1);\n    }\n    // Clean up the master listener if we don't need it anymore.\n    if (!queue.length) {\n      unsubscribe?.();\n    }\n  };\n}\n\n// Create a function that runs before the process exits and guards against running multiple times.\nfunction createExitHook(signal: NodeJS.Signals) {\n  return guardAsync(async () => {\n    debug(`pre-exit (signal: ${signal}, queue length: ${queue.length})`);\n\n    for (const [index, hookAsync] of Object.entries(queue)) {\n      try {\n        await hookAsync(signal);\n      } catch (error: any) {\n        debug(`Error in exit hook: %O (queue: ${index})`, error);\n      }\n    }\n\n    debug(`post-exit (code: ${process.exitCode ?? 0})`);\n\n    process.exit();\n  });\n}\n\nfunction attachMasterListener() {\n  const hooks: [NodeJS.Signals, () => any][] = [];\n  for (const signal of PRE_EXIT_SIGNALS) {\n    const hook = createExitHook(signal);\n    hooks.push([signal, hook]);\n    process.on(signal, hook);\n  }\n  return () => {\n    for (const [signal, hook] of hooks) {\n      process.removeListener(signal, hook);\n    }\n  };\n}\n\n/**\n * Monitor if the current process is exiting before the delay is reached.\n * If there are active resources, the process will be forced to exit after the delay is reached.\n *\n * @see https://nodejs.org/docs/latest-v18.x/api/process.html#processgetactiveresourcesinfo\n */\nexport function ensureProcessExitsAfterDelay(waitUntilExitMs = 10000, startedAtMs = Date.now()) {\n  // Create a list of the expected active resources before exiting.\n  // Note, the order is undeterministic\n  const expectedResources = [\n    process.stdout.isTTY ? 'TTYWrap' : 'PipeWrap',\n    process.stderr.isTTY ? 'TTYWrap' : 'PipeWrap',\n    process.stdin.isTTY ? 'TTYWrap' : 'PipeWrap',\n  ];\n  // Check active resources, besides the TTYWrap/PipeWrap (process.stdin, process.stdout, process.stderr)\n  // @ts-expect-error Added in v17.3.0, v16.14.0 but unavailable in v18 typings\n  const activeResources = process.getActiveResourcesInfo() as string[];\n  // Filter the active resource list by subtracting the expected resources, in undeterministic order\n  const unexpectedActiveResources = activeResources.filter((activeResource) => {\n    const index = expectedResources.indexOf(activeResource);\n    if (index >= 0) {\n      expectedResources.splice(index, 1);\n      return false;\n    }\n\n    return true;\n  });\n\n  const canExitProcess = !unexpectedActiveResources.length;\n  if (canExitProcess) {\n    return debug('no active resources detected, process can safely exit');\n  } else {\n    debug(\n      `process is trying to exit, but is stuck on unexpected active resources:`,\n      unexpectedActiveResources\n    );\n  }\n\n  // Check if the process needs to be force-closed\n  const elapsedTime = Date.now() - startedAtMs;\n  if (elapsedTime > waitUntilExitMs) {\n    debug('active handles detected past the exit delay, forcefully exiting:', activeResources);\n    tryWarnActiveProcesses();\n    return process.exit(0);\n  }\n\n  const timeoutId = setTimeout(() => {\n    // Ensure the timeout is cleared before checking the active resources\n    clearTimeout(timeoutId);\n    // Check if the process can exit\n    ensureProcessExitsAfterDelay(waitUntilExitMs, startedAtMs);\n  }, 100);\n}\n\n/**\n * Try to warn the user about unexpected active processes running in the background.\n * This uses the internal `process._getActiveHandles` method, within a try-catch block.\n * If active child processes are detected, the commands of these processes are logged.\n *\n * @example ```bash\n * Done writing bundle output\n * Detected 2 processes preventing Expo from exiting, forcefully exiting now.\n *   - node /Users/<USER>/../node_modules/nativewind/dist/metro/tailwind/v3/child.js\n *   - node /Users/<USER>/../node_modules/nativewind/dist/metro/tailwind/v3/child.js\n * ```\n */\nfunction tryWarnActiveProcesses() {\n  let activeProcesses: string[] = [];\n\n  try {\n    const children: ChildProcess[] = process\n      // @ts-expect-error - This is an internal method, not designed to be exposed. It's also our only way to get this info\n      ._getActiveHandles()\n      .filter((handle: any) => handle instanceof ChildProcess);\n\n    if (children.length) {\n      activeProcesses = children.map((child) => child.spawnargs.join(' '));\n    }\n  } catch (error) {\n    debug('failed to get active process information:', error);\n  }\n\n  if (!activeProcesses.length) {\n    warn('Something prevented Expo from exiting, forcefully exiting now.');\n  } else {\n    const singularOrPlural =\n      activeProcesses.length === 1 ? '1 process' : `${activeProcesses.length} processes`;\n\n    warn(`Detected ${singularOrPlural} preventing Expo from exiting, forcefully exiting now.`);\n    warn('  - ' + activeProcesses.join('\\n  - '));\n  }\n}\n"], "names": ["installExitHooks", "ensureProcessExitsAfterDelay", "debug", "require", "PRE_EXIT_SIGNALS", "queue", "unsubscribe", "asyncExitHook", "length", "attachMasterListener", "push", "index", "indexOf", "splice", "createExitHook", "signal", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Object", "entries", "error", "process", "exitCode", "exit", "hooks", "hook", "on", "removeListener", "waitUntilExitMs", "startedAtMs", "Date", "now", "expectedResources", "stdout", "isTTY", "stderr", "stdin", "activeResources", "getActiveResourcesInfo", "unexpectedActiveResources", "filter", "activeResource", "canExitProcess", "elapsedTime", "tryWarnActiveProcesses", "timeoutId", "setTimeout", "clearTimeout", "activeProcesses", "children", "_getActiveHandles", "handle", "ChildProcess", "map", "child", "spawnargs", "join", "warn", "singularOrPlural"], "mappings": "AAAA;;;;;;;;;;;IAmBgBA,gBAAgB,MAAhBA,gBAAgB;IA4DhBC,4BAA4B,MAA5BA,4BAA4B;;;yBA/Ef,oBAAoB;;;;;;;8DAC7B,cAAc;;;;;;oBAEP,MAAM;qBACZ,QAAQ;;;;;;AAE7B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,AAAsB,AAAC;AAIxE,MAAMC,gBAAgB,GAAqB;IAAC,QAAQ;IAAE,QAAQ;IAAE,SAAS;IAAE,UAAU;CAAC,AAAC;AAEvF,0FAA0F;AAC1F,+KAA+K;AAC/K,MAAMC,KAAK,GAAoB,EAAE,AAAC;AAElC,IAAIC,WAAW,GAAwB,IAAI,AAAC;AAGrC,SAASN,gBAAgB,CAACO,aAA4B,EAAc;IACzE,+EAA+E;IAC/E,IAAI,CAACF,KAAK,CAACG,MAAM,EAAE;QACjB,uDAAuD;QACvDF,WAAW,GAAGG,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAEDJ,KAAK,CAACK,IAAI,CAACH,aAAa,CAAC,CAAC;IAE1B,OAAO,IAAM;QACX,MAAMI,KAAK,GAAGN,KAAK,CAACO,OAAO,CAACL,aAAa,CAAC,AAAC;QAC3C,IAAII,KAAK,IAAI,CAAC,EAAE;YACdN,KAAK,CAACQ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,CAAC;QACD,4DAA4D;QAC5D,IAAI,CAACN,KAAK,CAACG,MAAM,EAAE;YACjBF,WAAW,QAAI,GAAfA,KAAAA,CAAe,GAAfA,WAAW,EAAI,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,kGAAkG;AAClG,SAASQ,cAAc,CAACC,MAAsB,EAAE;IAC9C,OAAOC,IAAAA,GAAU,WAAA,EAAC,UAAY;QAC5Bd,KAAK,CAAC,CAAC,kBAAkB,EAAEa,MAAM,CAAC,gBAAgB,EAAEV,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAErE,KAAK,MAAM,CAACG,KAAK,EAAEM,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACd,KAAK,CAAC,CAAE;YACtD,IAAI;gBACF,MAAMY,SAAS,CAACF,MAAM,CAAC,CAAC;YAC1B,EAAE,OAAOK,KAAK,EAAO;gBACnBlB,KAAK,CAAC,CAAC,+BAA+B,EAAES,KAAK,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAEDlB,KAAK,CAAC,CAAC,iBAAiB,EAAEmB,YAAO,EAAA,QAAA,CAACC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpDD,YAAO,EAAA,QAAA,CAACE,IAAI,EAAE,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAASd,oBAAoB,GAAG;IAC9B,MAAMe,KAAK,GAAkC,EAAE,AAAC;IAChD,KAAK,MAAMT,MAAM,IAAIX,gBAAgB,CAAE;QACrC,MAAMqB,IAAI,GAAGX,cAAc,CAACC,MAAM,CAAC,AAAC;QACpCS,KAAK,CAACd,IAAI,CAAC;YAACK,MAAM;YAAEU,IAAI;SAAC,CAAC,CAAC;QAC3BJ,YAAO,EAAA,QAAA,CAACK,EAAE,CAACX,MAAM,EAAEU,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,IAAM;QACX,KAAK,MAAM,CAACV,MAAM,EAAEU,IAAI,CAAC,IAAID,KAAK,CAAE;YAClCH,YAAO,EAAA,QAAA,CAACM,cAAc,CAACZ,MAAM,EAAEU,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAQM,SAASxB,4BAA4B,CAAC2B,eAAe,GAAG,KAAK,EAAEC,WAAW,GAAGC,IAAI,CAACC,GAAG,EAAE,EAAE;IAC9F,iEAAiE;IACjE,qCAAqC;IACrC,MAAMC,iBAAiB,GAAG;QACxBX,YAAO,EAAA,QAAA,CAACY,MAAM,CAACC,KAAK,GAAG,SAAS,GAAG,UAAU;QAC7Cb,YAAO,EAAA,QAAA,CAACc,MAAM,CAACD,KAAK,GAAG,SAAS,GAAG,UAAU;QAC7Cb,YAAO,EAAA,QAAA,CAACe,KAAK,CAACF,KAAK,GAAG,SAAS,GAAG,UAAU;KAC7C,AAAC;IACF,uGAAuG;IACvG,6EAA6E;IAC7E,MAAMG,eAAe,GAAGhB,YAAO,EAAA,QAAA,CAACiB,sBAAsB,EAAE,AAAY,AAAC;IACrE,kGAAkG;IAClG,MAAMC,yBAAyB,GAAGF,eAAe,CAACG,MAAM,CAAC,CAACC,cAAc,GAAK;QAC3E,MAAM9B,KAAK,GAAGqB,iBAAiB,CAACpB,OAAO,CAAC6B,cAAc,CAAC,AAAC;QACxD,IAAI9B,KAAK,IAAI,CAAC,EAAE;YACdqB,iBAAiB,CAACnB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,AAAC;IAEH,MAAM+B,cAAc,GAAG,CAACH,yBAAyB,CAAC/B,MAAM,AAAC;IACzD,IAAIkC,cAAc,EAAE;QAClB,OAAOxC,KAAK,CAAC,uDAAuD,CAAC,CAAC;IACxE,OAAO;QACLA,KAAK,CACH,CAAC,uEAAuE,CAAC,EACzEqC,yBAAyB,CAC1B,CAAC;IACJ,CAAC;IAED,gDAAgD;IAChD,MAAMI,WAAW,GAAGb,IAAI,CAACC,GAAG,EAAE,GAAGF,WAAW,AAAC;IAC7C,IAAIc,WAAW,GAAGf,eAAe,EAAE;QACjC1B,KAAK,CAAC,kEAAkE,EAAEmC,eAAe,CAAC,CAAC;QAC3FO,sBAAsB,EAAE,CAAC;QACzB,OAAOvB,YAAO,EAAA,QAAA,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAMsB,SAAS,GAAGC,UAAU,CAAC,IAAM;QACjC,qEAAqE;QACrEC,YAAY,CAACF,SAAS,CAAC,CAAC;QACxB,gCAAgC;QAChC5C,4BAA4B,CAAC2B,eAAe,EAAEC,WAAW,CAAC,CAAC;IAC7D,CAAC,EAAE,GAAG,CAAC,AAAC;AACV,CAAC;AAED;;;;;;;;;;;CAWC,GACD,SAASe,sBAAsB,GAAG;IAChC,IAAII,eAAe,GAAa,EAAE,AAAC;IAEnC,IAAI;QACF,MAAMC,QAAQ,GAAmB5B,YAAO,EAAA,QAAA,AACtC,qHAAqH;SACpH6B,iBAAiB,EAAE,CACnBV,MAAM,CAAC,CAACW,MAAW,GAAKA,MAAM,YAAYC,iBAAY,EAAA,aAAA,CAAC,AAAC;QAE3D,IAAIH,QAAQ,CAACzC,MAAM,EAAE;YACnBwC,eAAe,GAAGC,QAAQ,CAACI,GAAG,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,SAAS,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,EAAE,OAAOpC,KAAK,EAAE;QACdlB,KAAK,CAAC,2CAA2C,EAAEkB,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,CAAC4B,eAAe,CAACxC,MAAM,EAAE;QAC3BiD,IAAAA,IAAI,KAAA,EAAC,gEAAgE,CAAC,CAAC;IACzE,OAAO;QACL,MAAMC,gBAAgB,GACpBV,eAAe,CAACxC,MAAM,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,EAAEwC,eAAe,CAACxC,MAAM,CAAC,UAAU,CAAC,AAAC;QAErFiD,IAAAA,IAAI,KAAA,EAAC,CAAC,SAAS,EAAEC,gBAAgB,CAAC,sDAAsD,CAAC,CAAC,CAAC;QAC3FD,IAAAA,IAAI,KAAA,EAAC,MAAM,GAAGT,eAAe,CAACQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC;AACH,CAAC"}