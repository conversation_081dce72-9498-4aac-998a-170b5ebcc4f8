{"version": 3, "sources": ["../../../../../src/start/platforms/ios/AppleDeviceManager.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { assertSystemRequirementsAsync } from './assertSystemRequirements';\nimport { ensureSimulatorAppRunningAsync } from './ensureSimulatorAppRunning';\nimport {\n  getBestBootedSimulatorAsync,\n  getBestUnbootedSimulatorAsync,\n  getSelectableSimulatorsAsync,\n} from './getBestSimulator';\nimport { promptAppleDeviceAsync } from './promptAppleDevice';\nimport * as SimControl from './simctl';\nimport { delayAsync, waitForActionAsync } from '../../../utils/delay';\nimport { CommandError } from '../../../utils/errors';\nimport { parsePlistAsync } from '../../../utils/plist';\nimport { validateUrl } from '../../../utils/url';\nimport { DeviceManager } from '../DeviceManager';\nimport { ExpoGoInstaller } from '../ExpoGoInstaller';\nimport { BaseResolveDeviceProps } from '../PlatformManager';\n\nconst debug = require('debug')('expo:start:platforms:ios:AppleDeviceManager') as typeof console.log;\n\nconst EXPO_GO_BUNDLE_IDENTIFIER = 'host.exp.Exponent';\n\n/**\n * Ensure a simulator is booted and the Simulator app is opened.\n * This is where any timeout related error handling should live.\n */\nexport async function ensureSimulatorOpenAsync(\n  { udid, osType }: Partial<Pick<SimControl.Device, 'udid' | 'osType'>> = {},\n  tryAgain: boolean = true\n): Promise<SimControl.Device> {\n  // Use a default simulator if none was specified\n  if (!udid) {\n    // If a simulator is open, side step the entire booting sequence.\n    const simulatorOpenedByApp = await getBestBootedSimulatorAsync({ osType });\n    if (simulatorOpenedByApp) {\n      return simulatorOpenedByApp;\n    }\n\n    // Otherwise, find the best possible simulator from user defaults and continue\n    const bestUdid = await getBestUnbootedSimulatorAsync({ osType });\n    if (!bestUdid) {\n      throw new CommandError('No simulators found.');\n    }\n    udid = bestUdid;\n  }\n\n  const bootedDevice = await waitForActionAsync({\n    action: () => {\n      // Just for the type check.\n      assert(udid);\n      return SimControl.bootAsync({ udid });\n    },\n  });\n\n  if (!bootedDevice) {\n    // Give it a second chance, this might not be needed but it could potentially lead to a better UX on slower devices.\n    if (tryAgain) {\n      return await ensureSimulatorOpenAsync({ udid, osType }, false);\n    }\n    // TODO: We should eliminate all needs for a timeout error, it's bad UX to get an error about the simulator not starting while the user can clearly see it starting on their slow computer.\n    throw new CommandError(\n      'SIMULATOR_TIMEOUT',\n      `Simulator didn't boot fast enough. Try opening Simulator first, then running your app.`\n    );\n  }\n  return bootedDevice;\n}\nexport class AppleDeviceManager extends DeviceManager<SimControl.Device> {\n  static assertSystemRequirementsAsync = assertSystemRequirementsAsync;\n\n  static async resolveAsync({\n    device,\n    shouldPrompt,\n  }: BaseResolveDeviceProps<\n    Partial<Pick<SimControl.Device, 'udid' | 'osType'>>\n  > = {}): Promise<AppleDeviceManager> {\n    if (shouldPrompt) {\n      const devices = await getSelectableSimulatorsAsync(device);\n      device = await promptAppleDeviceAsync(devices, device?.osType);\n    }\n\n    const booted = await ensureSimulatorOpenAsync(device);\n    return new AppleDeviceManager(booted);\n  }\n\n  get name() {\n    return this.device.name;\n  }\n\n  get identifier(): string {\n    return this.device.udid;\n  }\n\n  async getAppVersionAsync(\n    appId: string,\n    { containerPath }: { containerPath?: string } = {}\n  ): Promise<string | null> {\n    return await SimControl.getInfoPlistValueAsync(this.device, {\n      appId,\n      key: 'CFBundleShortVersionString',\n      containerPath,\n    });\n  }\n\n  async startAsync(): Promise<SimControl.Device> {\n    return ensureSimulatorOpenAsync({ osType: this.device.osType, udid: this.device.udid });\n  }\n\n  async launchApplicationIdAsync(appId: string) {\n    try {\n      const result = await SimControl.openAppIdAsync(this.device, {\n        appId,\n      });\n      if (result.status === 0) {\n        await this.activateWindowAsync();\n      } else {\n        throw new CommandError(result.stderr);\n      }\n    } catch (error: any) {\n      let errorMessage = `Couldn't open iOS app with ID \"${appId}\" on device \"${this.name}\".`;\n      if (error instanceof CommandError && error.code === 'APP_NOT_INSTALLED') {\n        if (appId === EXPO_GO_BUNDLE_IDENTIFIER) {\n          errorMessage = `Couldn't open Expo Go app on device \"${this.name}\". Please install.`;\n        } else {\n          errorMessage += `\\nThe app might not be installed, try installing it with: ${chalk.bold(\n            `npx expo run:ios -d ${this.device.udid}`\n          )}`;\n        }\n      }\n      if (error.stderr) {\n        errorMessage += chalk.gray(`\\n${error.stderr}`);\n      } else if (error.message) {\n        errorMessage += chalk.gray(`\\n${error.message}`);\n      }\n      throw new CommandError(errorMessage);\n    }\n  }\n\n  async installAppAsync(filePath: string) {\n    await SimControl.installAsync(this.device, {\n      filePath,\n    });\n\n    await this.waitForAppInstalledAsync(await this.getApplicationIdFromBundle(filePath));\n  }\n\n  private async getApplicationIdFromBundle(filePath: string): Promise<string> {\n    debug('getApplicationIdFromBundle:', filePath);\n    const builtInfoPlistPath = path.join(filePath, 'Info.plist');\n    if (fs.existsSync(builtInfoPlistPath)) {\n      const { CFBundleIdentifier } = await parsePlistAsync(builtInfoPlistPath);\n      debug('getApplicationIdFromBundle: using built Info.plist', CFBundleIdentifier);\n      return CFBundleIdentifier;\n    }\n    debug('getApplicationIdFromBundle: no Info.plist found');\n    return EXPO_GO_BUNDLE_IDENTIFIER;\n  }\n\n  private async waitForAppInstalledAsync(applicationId: string): Promise<boolean> {\n    while (true) {\n      if (await this.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId)) {\n        return true;\n      }\n      await delayAsync(100);\n    }\n  }\n\n  async uninstallAppAsync(appId: string) {\n    await SimControl.uninstallAsync(this.device, {\n      appId,\n    });\n  }\n\n  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(appId: string) {\n    return (\n      (await SimControl.getContainerPathAsync(this.device, {\n        appId,\n      })) ?? false\n    );\n  }\n\n  async openUrlAsync(url: string, options: { appId?: string } = {}) {\n    // Non-compliant URLs will be treated as application identifiers.\n    if (!validateUrl(url, { requireProtocol: true })) {\n      return await this.launchApplicationIdAsync(url);\n    }\n\n    try {\n      await SimControl.openUrlAsync(this.device, { url, appId: options.appId });\n    } catch (error: any) {\n      // 194 means the device does not conform to a given URL, in this case we'll assume that the desired app is not installed.\n      if (error.status === 194) {\n        // An error was encountered processing the command (domain=NSOSStatusErrorDomain, code=-10814):\n        // The operation couldn’t be completed. (OSStatus error -10814.)\n        //\n        // This can be thrown when no app conforms to the URI scheme that we attempted to open.\n        throw new CommandError(\n          'APP_NOT_INSTALLED',\n          `Device ${this.device.name} (${this.device.udid}) has no app to handle the URI: ${url}`\n        );\n      }\n      throw error;\n    }\n  }\n\n  async activateWindowAsync() {\n    await ensureSimulatorAppRunningAsync(this.device);\n    // TODO: Focus the individual window\n    await osascript.execAsync(`tell application \"Simulator\" to activate`);\n  }\n\n  getExpoGoAppId(): string {\n    return EXPO_GO_BUNDLE_IDENTIFIER;\n  }\n\n  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {\n    const installer = new ExpoGoInstaller('ios', EXPO_GO_BUNDLE_IDENTIFIER, sdkVersion);\n    return installer.ensureAsync(this);\n  }\n}\n"], "names": ["ensureSimulatorOpenAsync", "AppleDeviceManager", "debug", "require", "EXPO_GO_BUNDLE_IDENTIFIER", "udid", "osType", "try<PERSON><PERSON>n", "simulatorOpenedByApp", "getBestBootedSimulatorAsync", "bestUdid", "getBestUnbootedSimulatorAsync", "CommandError", "bootedDevice", "waitForActionAsync", "action", "assert", "SimControl", "bootAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assertSystemRequirementsAsync", "resolveAsync", "device", "should<PERSON>rompt", "devices", "getSelectableSimulatorsAsync", "promptAppleDeviceAsync", "booted", "name", "identifier", "getAppVersionAsync", "appId", "containerPath", "getInfoPlistValueAsync", "key", "startAsync", "launchApplicationIdAsync", "result", "openAppIdAsync", "status", "activateWindowAsync", "stderr", "error", "errorMessage", "code", "chalk", "bold", "gray", "message", "installAppAsync", "filePath", "installAsync", "waitForAppInstalledAsync", "getApplicationIdFromBundle", "builtInfoPlistPath", "path", "join", "fs", "existsSync", "CFBundleIdentifier", "parsePlistAsync", "applicationId", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "delayAsync", "uninstallAppAsync", "uninstallAsync", "getContainerPathAsync", "openUrlAsync", "url", "options", "validateUrl", "requireProtocol", "ensureSimulatorAppRunningAsync", "osascript", "execAsync", "getExpoGoAppId", "ensureExpoGoAsync", "sdkVersion", "installer", "ExpoGoInstaller", "ensureAsync"], "mappings": "AAAA;;;;;;;;;;;IA+BsBA,wBAAwB,MAAxBA,wBAAwB;IAyCjCC,kBAAkB,MAAlBA,kBAAkB;;;+DAxEJ,iBAAiB;;;;;;;8DACzB,QAAQ;;;;;;;8DACT,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DACF,MAAM;;;;;;0CAEuB,4BAA4B;2CAC3B,6BAA6B;kCAKrE,oBAAoB;mCACY,qBAAqB;8DAChC,UAAU;uBACS,sBAAsB;wBACxC,uBAAuB;uBACpB,sBAAsB;qBAC1B,oBAAoB;+BAClB,kBAAkB;iCAChB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6CAA6C,CAAC,AAAsB,AAAC;AAEpG,MAAMC,yBAAyB,GAAG,mBAAmB,AAAC;AAM/C,eAAeJ,wBAAwB,CAC5C,EAAEK,IAAI,CAAA,EAAEC,MAAM,CAAA,EAAuD,GAAG,EAAE,EAC1EC,QAAiB,GAAG,IAAI,EACI;IAC5B,gDAAgD;IAChD,IAAI,CAACF,IAAI,EAAE;QACT,iEAAiE;QACjE,MAAMG,oBAAoB,GAAG,MAAMC,IAAAA,iBAA2B,4BAAA,EAAC;YAAEH,MAAM;SAAE,CAAC,AAAC;QAC3E,IAAIE,oBAAoB,EAAE;YACxB,OAAOA,oBAAoB,CAAC;QAC9B,CAAC;QAED,8EAA8E;QAC9E,MAAME,QAAQ,GAAG,MAAMC,IAAAA,iBAA6B,8BAAA,EAAC;YAAEL,MAAM;SAAE,CAAC,AAAC;QACjE,IAAI,CAACI,QAAQ,EAAE;YACb,MAAM,IAAIE,OAAY,aAAA,CAAC,sBAAsB,CAAC,CAAC;QACjD,CAAC;QACDP,IAAI,GAAGK,QAAQ,CAAC;IAClB,CAAC;IAED,MAAMG,YAAY,GAAG,MAAMC,IAAAA,MAAkB,mBAAA,EAAC;QAC5CC,MAAM,EAAE,IAAM;YACZ,2BAA2B;YAC3BC,IAAAA,OAAM,EAAA,QAAA,EAACX,IAAI,CAAC,CAAC;YACb,OAAOY,OAAU,CAACC,SAAS,CAAC;gBAAEb,IAAI;aAAE,CAAC,CAAC;QACxC,CAAC;KACF,CAAC,AAAC;IAEH,IAAI,CAACQ,YAAY,EAAE;QACjB,oHAAoH;QACpH,IAAIN,QAAQ,EAAE;YACZ,OAAO,MAAMP,wBAAwB,CAAC;gBAAEK,IAAI;gBAAEC,MAAM;aAAE,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;QACD,2LAA2L;QAC3L,MAAM,IAAIM,OAAY,aAAA,CACpB,mBAAmB,EACnB,CAAC,sFAAsF,CAAC,CACzF,CAAC;IACJ,CAAC;IACD,OAAOC,YAAY,CAAC;AACtB,CAAC;AACM,MAAMZ,kBAAkB,SAASkB,cAAa,cAAA;IACnD,OAAOC,6BAA6B,GAAGA,yBAA6B,8BAAA,CAAC;iBAExDC,YAAY,CAAC,EACxBC,MAAM,CAAA,EACNC,YAAY,CAAA,EAGb,GAAG,EAAE,EAA+B;QACnC,IAAIA,YAAY,EAAE;YAChB,MAAMC,OAAO,GAAG,MAAMC,IAAAA,iBAA4B,6BAAA,EAACH,MAAM,CAAC,AAAC;YAC3DA,MAAM,GAAG,MAAMI,IAAAA,kBAAsB,uBAAA,EAACF,OAAO,EAAEF,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAEhB,MAAM,CAAC,CAAC;QACjE,CAAC;QAED,MAAMqB,MAAM,GAAG,MAAM3B,wBAAwB,CAACsB,MAAM,CAAC,AAAC;QACtD,OAAO,IAAIrB,kBAAkB,CAAC0B,MAAM,CAAC,CAAC;IACxC;QAEIC,IAAI,GAAG;QACT,OAAO,IAAI,CAACN,MAAM,CAACM,IAAI,CAAC;IAC1B;QAEIC,UAAU,GAAW;QACvB,OAAO,IAAI,CAACP,MAAM,CAACjB,IAAI,CAAC;IAC1B;UAEMyB,kBAAkB,CACtBC,KAAa,EACb,EAAEC,aAAa,CAAA,EAA8B,GAAG,EAAE,EAC1B;QACxB,OAAO,MAAMf,OAAU,CAACgB,sBAAsB,CAAC,IAAI,CAACX,MAAM,EAAE;YAC1DS,KAAK;YACLG,GAAG,EAAE,4BAA4B;YACjCF,aAAa;SACd,CAAC,CAAC;IACL;UAEMG,UAAU,GAA+B;QAC7C,OAAOnC,wBAAwB,CAAC;YAAEM,MAAM,EAAE,IAAI,CAACgB,MAAM,CAAChB,MAAM;YAAED,IAAI,EAAE,IAAI,CAACiB,MAAM,CAACjB,IAAI;SAAE,CAAC,CAAC;IAC1F;UAEM+B,wBAAwB,CAACL,KAAa,EAAE;QAC5C,IAAI;YACF,MAAMM,MAAM,GAAG,MAAMpB,OAAU,CAACqB,cAAc,CAAC,IAAI,CAAChB,MAAM,EAAE;gBAC1DS,KAAK;aACN,CAAC,AAAC;YACH,IAAIM,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;gBACvB,MAAM,IAAI,CAACC,mBAAmB,EAAE,CAAC;YACnC,OAAO;gBACL,MAAM,IAAI5B,OAAY,aAAA,CAACyB,MAAM,CAACI,MAAM,CAAC,CAAC;YACxC,CAAC;QACH,EAAE,OAAOC,KAAK,EAAO;YACnB,IAAIC,YAAY,GAAG,CAAC,+BAA+B,EAAEZ,KAAK,CAAC,aAAa,EAAE,IAAI,CAACH,IAAI,CAAC,EAAE,CAAC,AAAC;YACxF,IAAIc,KAAK,YAAY9B,OAAY,aAAA,IAAI8B,KAAK,CAACE,IAAI,KAAK,mBAAmB,EAAE;gBACvE,IAAIb,KAAK,KAAK3B,yBAAyB,EAAE;oBACvCuC,YAAY,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACf,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACvF,OAAO;oBACLe,YAAY,IAAI,CAAC,0DAA0D,EAAEE,MAAK,EAAA,QAAA,CAACC,IAAI,CACrF,CAAC,oBAAoB,EAAE,IAAI,CAACxB,MAAM,CAACjB,IAAI,CAAC,CAAC,CAC1C,CAAC,CAAC,CAAC;gBACN,CAAC;YACH,CAAC;YACD,IAAIqC,KAAK,CAACD,MAAM,EAAE;gBAChBE,YAAY,IAAIE,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,CAAC,EAAE,EAAEL,KAAK,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC;YAClD,OAAO,IAAIC,KAAK,CAACM,OAAO,EAAE;gBACxBL,YAAY,IAAIE,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,CAAC,EAAE,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YACD,MAAM,IAAIpC,OAAY,aAAA,CAAC+B,YAAY,CAAC,CAAC;QACvC,CAAC;IACH;UAEMM,eAAe,CAACC,QAAgB,EAAE;QACtC,MAAMjC,OAAU,CAACkC,YAAY,CAAC,IAAI,CAAC7B,MAAM,EAAE;YACzC4B,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,IAAI,CAACE,wBAAwB,CAAC,MAAM,IAAI,CAACC,0BAA0B,CAACH,QAAQ,CAAC,CAAC,CAAC;IACvF;UAEcG,0BAA0B,CAACH,QAAgB,EAAmB;QAC1EhD,KAAK,CAAC,6BAA6B,EAAEgD,QAAQ,CAAC,CAAC;QAC/C,MAAMI,kBAAkB,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACN,QAAQ,EAAE,YAAY,CAAC,AAAC;QAC7D,IAAIO,GAAE,EAAA,QAAA,CAACC,UAAU,CAACJ,kBAAkB,CAAC,EAAE;YACrC,MAAM,EAAEK,kBAAkB,CAAA,EAAE,GAAG,MAAMC,IAAAA,MAAe,gBAAA,EAACN,kBAAkB,CAAC,AAAC;YACzEpD,KAAK,CAAC,oDAAoD,EAAEyD,kBAAkB,CAAC,CAAC;YAChF,OAAOA,kBAAkB,CAAC;QAC5B,CAAC;QACDzD,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACzD,OAAOE,yBAAyB,CAAC;IACnC;UAEcgD,wBAAwB,CAACS,aAAqB,EAAoB;QAC9E,MAAO,IAAI,CAAE;YACX,IAAI,MAAM,IAAI,CAACC,mDAAmD,CAACD,aAAa,CAAC,EAAE;gBACjF,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAME,IAAAA,MAAU,WAAA,EAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACH;UAEMC,iBAAiB,CAACjC,KAAa,EAAE;QACrC,MAAMd,OAAU,CAACgD,cAAc,CAAC,IAAI,CAAC3C,MAAM,EAAE;YAC3CS,KAAK;SACN,CAAC,CAAC;IACL;UAEM+B,mDAAmD,CAAC/B,KAAa,EAAE;QACvE,OACE,AAAC,MAAMd,OAAU,CAACiD,qBAAqB,CAAC,IAAI,CAAC5C,MAAM,EAAE;YACnDS,KAAK;SACN,CAAC,IAAK,KAAK,CACZ;IACJ;UAEMoC,YAAY,CAACC,GAAW,EAAEC,OAA2B,GAAG,EAAE,EAAE;QAChE,iEAAiE;QACjE,IAAI,CAACC,IAAAA,IAAW,YAAA,EAACF,GAAG,EAAE;YAAEG,eAAe,EAAE,IAAI;SAAE,CAAC,EAAE;YAChD,OAAO,MAAM,IAAI,CAACnC,wBAAwB,CAACgC,GAAG,CAAC,CAAC;QAClD,CAAC;QAED,IAAI;YACF,MAAMnD,OAAU,CAACkD,YAAY,CAAC,IAAI,CAAC7C,MAAM,EAAE;gBAAE8C,GAAG;gBAAErC,KAAK,EAAEsC,OAAO,CAACtC,KAAK;aAAE,CAAC,CAAC;QAC5E,EAAE,OAAOW,KAAK,EAAO;YACnB,yHAAyH;YACzH,IAAIA,KAAK,CAACH,MAAM,KAAK,GAAG,EAAE;gBACxB,+FAA+F;gBAC/F,gEAAgE;gBAChE,EAAE;gBACF,uFAAuF;gBACvF,MAAM,IAAI3B,OAAY,aAAA,CACpB,mBAAmB,EACnB,CAAC,OAAO,EAAE,IAAI,CAACU,MAAM,CAACM,IAAI,CAAC,EAAE,EAAE,IAAI,CAACN,MAAM,CAACjB,IAAI,CAAC,gCAAgC,EAAE+D,GAAG,CAAC,CAAC,CACxF,CAAC;YACJ,CAAC;YACD,MAAM1B,KAAK,CAAC;QACd,CAAC;IACH;UAEMF,mBAAmB,GAAG;QAC1B,MAAMgC,IAAAA,0BAA8B,+BAAA,EAAC,IAAI,CAAClD,MAAM,CAAC,CAAC;QAClD,oCAAoC;QACpC,MAAMmD,UAAS,EAAA,CAACC,SAAS,CAAC,CAAC,wCAAwC,CAAC,CAAC,CAAC;IACxE;IAEAC,cAAc,GAAW;QACvB,OAAOvE,yBAAyB,CAAC;IACnC;UAEMwE,iBAAiB,CAACC,UAAkB,EAAoB;QAC5D,MAAMC,SAAS,GAAG,IAAIC,gBAAe,gBAAA,CAAC,KAAK,EAAE3E,yBAAyB,EAAEyE,UAAU,CAAC,AAAC;QACpF,OAAOC,SAAS,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC;IACrC;CACD"}