{"version": 3, "sources": ["../../../../../src/start/server/middleware/DomComponentsMiddleware.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { createBundleUrlPath, ExpoMetroOptions } from './metroOptions';\nimport type { ServerRequest, ServerResponse } from './server.types';\nimport { Log } from '../../../log';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { memoize } from '../../../utils/fn';\nimport { fileURLToFilePath } from '../metro/createServerComponentsMiddleware';\n\nexport type PickPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport const DOM_COMPONENTS_BUNDLE_DIR = 'www.bundle';\n\nconst warnUnstable = memoize(() =>\n  Log.warn('Using experimental DOM Components API. Production exports may not work as expected.')\n);\n\nconst checkWebViewInstalled = memoize((projectRoot: string) => {\n  const webViewInstalled =\n    resolveFrom.silent(projectRoot, 'react-native-webview') ||\n    resolveFrom.silent(projectRoot, '@expo/dom-webview');\n  if (!webViewInstalled) {\n    throw new Error(\n      `To use DOM Components, you must install the 'react-native-webview' package. Run 'npx expo install react-native-webview' to install it.`\n    );\n  }\n});\n\ntype CreateDomComponentsMiddlewareOptions = {\n  /** The absolute metro or server root, used to calculate the relative dom entry path */\n  metroRoot: string;\n  /** The absolute project root, used to resolve the `expo/dom/entry.js` path */\n  projectRoot: string;\n};\n\nexport function createDomComponentsMiddleware(\n  { metroRoot, projectRoot }: CreateDomComponentsMiddlewareOptions,\n  instanceMetroOptions: PickPartial<ExpoMetroOptions, 'mainModuleName' | 'platform' | 'bytecode'>\n) {\n  return (req: ServerRequest, res: ServerResponse, next: (err?: Error) => void) => {\n    if (!req.url) return next();\n\n    const url = coerceUrl(req.url);\n\n    // Match `/_expo/@dom`.\n    // This URL can contain additional paths like `/_expo/@dom/foo.js?file=...` to help the Safari dev tools.\n    if (!url.pathname.startsWith('/_expo/@dom')) {\n      return next();\n    }\n\n    const file = url.searchParams.get('file');\n\n    if (!file || !file.startsWith('file://')) {\n      res.statusCode = 400;\n      res.statusMessage = 'Invalid file path: ' + file;\n      return res.end();\n    }\n\n    checkWebViewInstalled(projectRoot);\n    warnUnstable();\n\n    // Generate a unique entry file for the webview.\n    const generatedEntry = toPosixPath(file.startsWith('file://') ? fileURLToFilePath(file) : file);\n    const virtualEntry = toPosixPath(resolveFrom(projectRoot, 'expo/dom/entry.js'));\n    // The relative import path will be used like URI so it must be POSIX.\n    const relativeImport = './' + path.posix.relative(path.dirname(virtualEntry), generatedEntry);\n    // Create the script URL\n    const requestUrlBase = `http://${req.headers.host}`;\n    const metroUrl = new URL(\n      createBundleUrlPath({\n        ...instanceMetroOptions,\n        domRoot: encodeURI(relativeImport),\n        baseUrl: '/',\n        mainModuleName: path.relative(metroRoot, virtualEntry),\n        bytecode: false,\n        platform: 'web',\n        isExporting: false,\n        engine: 'hermes',\n        // Required for ensuring bundler errors are caught in the root entry / async boundary and can be recovered from automatically.\n        lazy: true,\n      }),\n      requestUrlBase\n    ).toString();\n\n    res.statusCode = 200;\n    // Return HTML file\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(\n      // Create the entry HTML file.\n      getDomComponentHtml(metroUrl, { title: path.basename(file) })\n    );\n  };\n}\n\nfunction coerceUrl(url: string) {\n  try {\n    return new URL(url);\n  } catch {\n    return new URL(url, 'https://localhost:0');\n  }\n}\n\nexport function getDomComponentHtml(src?: string, { title }: { title?: string } = {}) {\n  // This HTML is not optimized for `react-native-web` since DOM Components are meant for general React DOM web development.\n  return `\n<!DOCTYPE html>\n<html>\n    <head>\n        <meta charset=\"utf-8\" />\n        <meta httpEquiv=\"X-UA-Compatible\" content=\"IE=edge\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, user-scalable=no\">\n        ${title ? `<title>${title}</title>` : ''}\n        <style id=\"expo-dom-component-style\">\n        /* These styles make the body full-height */\n        html,\n        body {\n          -webkit-overflow-scrolling: touch; /* Enables smooth momentum scrolling */\n        }\n        /* These styles make the root element full-height */\n        #root {\n          display: flex;\n          flex: 1;\n        }\n        </style>\n    </head>\n    <body>\n    <noscript>DOM Components require <code>javaScriptEnabled</code></noscript>\n        <!-- Root element for the DOM component. -->\n        <div id=\"root\"></div>\n        ${src ? `<script crossorigin src=\"${src.replace(/^https?:/, '')}\"></script>` : ''}\n    </body>\n</html>`;\n}\n"], "names": ["DOM_COMPONENTS_BUNDLE_DIR", "createDomComponentsMiddleware", "getDomComponentHtml", "warnUnstable", "memoize", "Log", "warn", "checkWebViewInstalled", "projectRoot", "webViewInstalled", "resolveFrom", "silent", "Error", "metroRoot", "instanceMetroOptions", "req", "res", "next", "url", "coerceUrl", "pathname", "startsWith", "file", "searchParams", "get", "statusCode", "statusMessage", "end", "generatedEntry", "toPosixPath", "fileURLToFilePath", "virtualEntry", "relativeImport", "path", "posix", "relative", "dirname", "requestUrlBase", "headers", "host", "metroUrl", "URL", "createBundleUrlPath", "domRoot", "encodeURI", "baseUrl", "mainModuleName", "bytecode", "platform", "isExporting", "engine", "lazy", "toString", "<PERSON><PERSON><PERSON><PERSON>", "title", "basename", "src", "replace"], "mappings": "AAAA;;;;;;;;;;;IAYaA,yBAAyB,MAAzBA,yBAAyB;IAwBtBC,6BAA6B,MAA7BA,6BAA6B;IAoE7BC,mBAAmB,MAAnBA,mBAAmB;;;8DAxGlB,MAAM;;;;;;;8DACC,cAAc;;;;;;8BAEgB,gBAAgB;qBAElD,cAAc;0BACN,yBAAyB;oBAC7B,mBAAmB;kDACT,2CAA2C;;;;;;AAItE,MAAMF,yBAAyB,GAAG,YAAY,AAAC;AAEtD,MAAMG,YAAY,GAAGC,IAAAA,GAAO,QAAA,EAAC,IAC3BC,IAAG,IAAA,CAACC,IAAI,CAAC,qFAAqF,CAAC,CAChG,AAAC;AAEF,MAAMC,qBAAqB,GAAGH,IAAAA,GAAO,QAAA,EAAC,CAACI,WAAmB,GAAK;IAC7D,MAAMC,gBAAgB,GACpBC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,sBAAsB,CAAC,IACvDE,YAAW,EAAA,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,mBAAmB,CAAC,AAAC;IACvD,IAAI,CAACC,gBAAgB,EAAE;QACrB,MAAM,IAAIG,KAAK,CACb,CAAC,sIAAsI,CAAC,CACzI,CAAC;IACJ,CAAC;AACH,CAAC,CAAC,AAAC;AASI,SAASX,6BAA6B,CAC3C,EAAEY,SAAS,CAAA,EAAEL,WAAW,CAAA,EAAwC,EAChEM,oBAA+F,EAC/F;IACA,OAAO,CAACC,GAAkB,EAAEC,GAAmB,EAAEC,IAA2B,GAAK;QAC/E,IAAI,CAACF,GAAG,CAACG,GAAG,EAAE,OAAOD,IAAI,EAAE,CAAC;QAE5B,MAAMC,GAAG,GAAGC,SAAS,CAACJ,GAAG,CAACG,GAAG,CAAC,AAAC;QAE/B,uBAAuB;QACvB,yGAAyG;QACzG,IAAI,CAACA,GAAG,CAACE,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC,EAAE;YAC3C,OAAOJ,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAMK,IAAI,GAAGJ,GAAG,CAACK,YAAY,CAACC,GAAG,CAAC,MAAM,CAAC,AAAC;QAE1C,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACD,UAAU,CAAC,SAAS,CAAC,EAAE;YACxCL,GAAG,CAACS,UAAU,GAAG,GAAG,CAAC;YACrBT,GAAG,CAACU,aAAa,GAAG,qBAAqB,GAAGJ,IAAI,CAAC;YACjD,OAAON,GAAG,CAACW,GAAG,EAAE,CAAC;QACnB,CAAC;QAEDpB,qBAAqB,CAACC,WAAW,CAAC,CAAC;QACnCL,YAAY,EAAE,CAAC;QAEf,gDAAgD;QAChD,MAAMyB,cAAc,GAAGC,IAAAA,SAAW,YAAA,EAACP,IAAI,CAACD,UAAU,CAAC,SAAS,CAAC,GAAGS,IAAAA,iCAAiB,kBAAA,EAACR,IAAI,CAAC,GAAGA,IAAI,CAAC,AAAC;QAChG,MAAMS,YAAY,GAAGF,IAAAA,SAAW,YAAA,EAACnB,IAAAA,YAAW,EAAA,QAAA,EAACF,WAAW,EAAE,mBAAmB,CAAC,CAAC,AAAC;QAChF,sEAAsE;QACtE,MAAMwB,cAAc,GAAG,IAAI,GAAGC,KAAI,EAAA,QAAA,CAACC,KAAK,CAACC,QAAQ,CAACF,KAAI,EAAA,QAAA,CAACG,OAAO,CAACL,YAAY,CAAC,EAAEH,cAAc,CAAC,AAAC;QAC9F,wBAAwB;QACxB,MAAMS,cAAc,GAAG,CAAC,OAAO,EAAEtB,GAAG,CAACuB,OAAO,CAACC,IAAI,CAAC,CAAC,AAAC;QACpD,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CACtBC,IAAAA,aAAmB,oBAAA,EAAC;YAClB,GAAG5B,oBAAoB;YACvB6B,OAAO,EAAEC,SAAS,CAACZ,cAAc,CAAC;YAClCa,OAAO,EAAE,GAAG;YACZC,cAAc,EAAEb,KAAI,EAAA,QAAA,CAACE,QAAQ,CAACtB,SAAS,EAAEkB,YAAY,CAAC;YACtDgB,QAAQ,EAAE,KAAK;YACfC,QAAQ,EAAE,KAAK;YACfC,WAAW,EAAE,KAAK;YAClBC,MAAM,EAAE,QAAQ;YAChB,8HAA8H;YAC9HC,IAAI,EAAE,IAAI;SACX,CAAC,EACFd,cAAc,CACf,CAACe,QAAQ,EAAE,AAAC;QAEbpC,GAAG,CAACS,UAAU,GAAG,GAAG,CAAC;QACrB,mBAAmB;QACnBT,GAAG,CAACqC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE3CrC,GAAG,CAACW,GAAG,CACL,8BAA8B;QAC9BzB,mBAAmB,CAACsC,QAAQ,EAAE;YAAEc,KAAK,EAAErB,KAAI,EAAA,QAAA,CAACsB,QAAQ,CAACjC,IAAI,CAAC;SAAE,CAAC,CAC9D,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,SAASH,SAAS,CAACD,GAAW,EAAE;IAC9B,IAAI;QACF,OAAO,IAAIuB,GAAG,CAACvB,GAAG,CAAC,CAAC;IACtB,EAAE,OAAM;QACN,OAAO,IAAIuB,GAAG,CAACvB,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAEM,SAAShB,mBAAmB,CAACsD,GAAY,EAAE,EAAEF,KAAK,CAAA,EAAsB,GAAG,EAAE,EAAE;IACpF,0HAA0H;IAC1H,OAAO,CAAC;;;;;;;QAOF,EAAEA,KAAK,GAAG,CAAC,OAAO,EAAEA,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;;;;;;;;;;;;;;;;;;QAkBzC,EAAEE,GAAG,GAAG,CAAC,yBAAyB,EAAEA,GAAG,CAACC,OAAO,aAAa,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;;OAEnF,CAAC,CAAC;AACT,CAAC"}