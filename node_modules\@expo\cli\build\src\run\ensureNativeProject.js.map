{"version": 3, "sources": ["../../../src/run/ensureNativeProject.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { promptToClearMalformedNativeProjectsAsync } from '../prebuild/clearNativeFolder';\nimport { prebuildAsync } from '../prebuild/prebuildAsync';\nimport { profile } from '../utils/profile';\n\nexport async function ensureNativeProjectAsync(\n  projectRoot: string,\n  { platform, install }: { platform: ModPlatform; install?: boolean }\n) {\n  // If the user has an empty android folder then the project won't build, this can happen when they delete the prebuild files in git.\n  // Check to ensure most of the core files are in place, and prompt to remove the folder if they aren't.\n  await profile(promptToClearMalformedNativeProjectsAsync)(projectRoot, [platform]);\n\n  // If the project doesn't have native code, prebuild it...\n  if (!fs.existsSync(path.join(projectRoot, platform))) {\n    await prebuildAsync(projectRoot, {\n      install: !!install,\n      platforms: [platform],\n    });\n  } else {\n    return true;\n  }\n  return false;\n}\n"], "names": ["ensureNativeProjectAsync", "projectRoot", "platform", "install", "profile", "promptToClearMalformedNativeProjectsAsync", "fs", "existsSync", "path", "join", "prebuildAsync", "platforms"], "mappings": "AAAA;;;;+BAQsBA,0BAAwB;;aAAxBA,wBAAwB;;;8DAP/B,IAAI;;;;;;;8DACF,MAAM;;;;;;mCAEmC,+BAA+B;+BAC3D,2BAA2B;yBACjC,kBAAkB;;;;;;AAEnC,eAAeA,wBAAwB,CAC5CC,WAAmB,EACnB,EAAEC,QAAQ,CAAA,EAAEC,OAAO,CAAA,EAAgD,EACnE;IACA,oIAAoI;IACpI,uGAAuG;IACvG,MAAMC,IAAAA,QAAO,QAAA,EAACC,kBAAyC,0CAAA,CAAC,CAACJ,WAAW,EAAE;QAACC,QAAQ;KAAC,CAAC,CAAC;IAElF,0DAA0D;IAC1D,IAAI,CAACI,GAAE,EAAA,QAAA,CAACC,UAAU,CAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACR,WAAW,EAAEC,QAAQ,CAAC,CAAC,EAAE;QACpD,MAAMQ,IAAAA,cAAa,cAAA,EAACT,WAAW,EAAE;YAC/BE,OAAO,EAAE,CAAC,CAACA,OAAO;YAClBQ,SAAS,EAAE;gBAACT,QAAQ;aAAC;SACtB,CAAC,CAAC;IACL,OAAO;QACL,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}