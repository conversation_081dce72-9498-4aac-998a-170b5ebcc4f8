{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/createSocketMap.ts"], "sourcesContent": ["import type { WebSocket } from 'ws';\n\nconst debug = require('debug')('expo:metro:dev-server:socketmap') as typeof console.log;\n\nexport type SocketId = string;\nexport type SocketMap = Map<string, WebSocket>;\n\nexport function createSocketMap() {\n  const map: SocketMap = new Map();\n  const createId = createSocketIdFactory();\n\n  const registerSocket = (socket: WebSocket) => {\n    const id = createId();\n    map.set(id, socket);\n    return {\n      id,\n      terminate: () => {\n        map.delete(id);\n        socket.removeAllListeners();\n        socket.terminate();\n      },\n    };\n  };\n\n  const findSocket = (id: SocketId): WebSocket | null => {\n    const socket = map.get(id);\n    if (!socket) debug(`No connected socket found with ID: ${id}`);\n    return socket ?? null;\n  };\n\n  return { map, registerSocket, findSocket };\n}\n\nfunction createSocketIdFactory() {\n  let nextId = 0;\n  return () => `socket#${nextId++}`;\n}\n"], "names": ["createSocketMap", "debug", "require", "map", "Map", "createId", "createSocketIdFactory", "registerSocket", "socket", "id", "set", "terminate", "delete", "removeAllListeners", "findSocket", "get", "nextId"], "mappings": "AAAA;;;;+BAOg<PERSON>,iBAAe;;aAAfA,eAAe;;AAL/B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,AAAsB,AAAC;AAKjF,SAASF,eAAe,GAAG;IAChC,MAAMG,GAAG,GAAc,IAAIC,GAAG,EAAE,AAAC;IACjC,MAAMC,QAAQ,GAAGC,qBAAqB,EAAE,AAAC;IAEzC,MAAMC,cAAc,GAAG,CAACC,MAAiB,GAAK;QAC5C,MAAMC,EAAE,GAAGJ,QAAQ,EAAE,AAAC;QACtBF,GAAG,CAACO,GAAG,CAACD,EAAE,EAAED,MAAM,CAAC,CAAC;QACpB,OAAO;YACLC,EAAE;YACFE,SAAS,EAAE,IAAM;gBACfR,GAAG,CAACS,MAAM,CAACH,EAAE,CAAC,CAAC;gBACfD,MAAM,CAACK,kBAAkB,EAAE,CAAC;gBAC5BL,MAAM,CAACG,SAAS,EAAE,CAAC;YACrB,CAAC;SACF,CAAC;IACJ,CAAC,AAAC;IAEF,MAAMG,UAAU,GAAG,CAACL,EAAY,GAAuB;QACrD,MAAMD,MAAM,GAAGL,GAAG,CAACY,GAAG,CAACN,EAAE,CAAC,AAAC;QAC3B,IAAI,CAACD,MAAM,EAAEP,KAAK,CAAC,CAAC,mCAAmC,EAAEQ,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,OAAOD,MAAM,IAAI,IAAI,CAAC;IACxB,CAAC,AAAC;IAEF,OAAO;QAAEL,GAAG;QAAEI,cAAc;QAAEO,UAAU;KAAE,CAAC;AAC7C,CAAC;AAED,SAASR,qBAAqB,GAAG;IAC/B,IAAIU,MAAM,GAAG,CAAC,AAAC;IACf,OAAO,IAAM,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,CAAC,CAAC;AACpC,CAAC"}