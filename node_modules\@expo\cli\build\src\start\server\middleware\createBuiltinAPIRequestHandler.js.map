{"version": 3, "sources": ["../../../../../src/start/server/middleware/createBuiltinAPIRequestHandler.ts"], "sourcesContent": ["import { convertRequest, RequestHand<PERSON>, respond } from '@expo/server/build/vendor/http';\n\nimport type { ServerNext, ServerRequest, ServerResponse } from './server.types';\n\nexport function createBuiltinAPIRequestHandler(\n  matchRequest: (request: Request) => boolean,\n  handlers: Record<string, (req: Request) => Promise<Response>>\n): RequestHandler {\n  return createRequestHandler((req) => {\n    if (!matchRequest(req)) {\n      winterNext();\n    }\n    const handler = handlers[req.method];\n    if (!handler) {\n      notAllowed();\n    }\n    return handler(req);\n  });\n}\n\n/**\n * Returns a request handler for http that serves the response using Remix.\n */\nexport function createRequestHandler(\n  handleRequest: (request: Request) => Promise<Response>\n): RequestHandler {\n  return async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n    if (!req?.url || !req.method) {\n      return next();\n    }\n    // These headers (added by other middleware) break the browser preview of RSC.\n    res.removeHeader('X-Content-Type-Options');\n    res.removeHeader('Cache-Control');\n    res.removeHeader('Expires');\n    res.removeHeader('Surrogate-Control');\n\n    try {\n      const request = convertRequest(req, res);\n      const response = await handleRequest(request);\n      return await respond(res, response);\n    } catch (error: unknown) {\n      if (error instanceof Error) {\n        return await respond(\n          res,\n          new Response('Internal Server Error: ' + error.message, {\n            status: 500,\n            headers: {\n              'Content-Type': 'text/plain',\n            },\n          })\n        );\n      } else if (error instanceof Response) {\n        return await respond(res, error);\n      }\n      // http doesn't support async functions, so we have to pass along the\n      // error manually using next().\n      // @ts-expect-error\n      next(error);\n    }\n  };\n}\n\nfunction notAllowed(): never {\n  throw new Response('Method Not Allowed', {\n    status: 405,\n    headers: {\n      'Content-Type': 'text/plain',\n    },\n  });\n}\n\nexport function winterNext(): never {\n  // eslint-disable-next-line no-throw-literal\n  throw undefined;\n}\n"], "names": ["createBuiltinAPIRequestHandler", "createRequestHandler", "winterNext", "matchRequest", "handlers", "req", "handler", "method", "notAllowed", "handleRequest", "res", "next", "url", "removeHeader", "request", "convertRequest", "response", "respond", "error", "Error", "Response", "message", "status", "headers", "undefined"], "mappings": "AAAA;;;;;;;;;;;IAIgBA,8BAA8B,MAA9BA,8BAA8B;IAmB9BC,oBAAoB,MAApBA,oBAAoB;IAgDpBC,UAAU,MAAVA,UAAU;;;yBAvE8B,gCAAgC;;;;;;AAIjF,SAASF,8BAA8B,CAC5CG,YAA2C,EAC3CC,QAA6D,EAC7C;IAChB,OAAOH,oBAAoB,CAAC,CAACI,GAAG,GAAK;QACnC,IAAI,CAACF,YAAY,CAACE,GAAG,CAAC,EAAE;YACtBH,UAAU,EAAE,CAAC;QACf,CAAC;QACD,MAAMI,OAAO,GAAGF,QAAQ,CAACC,GAAG,CAACE,MAAM,CAAC,AAAC;QACrC,IAAI,CAACD,OAAO,EAAE;YACZE,UAAU,EAAE,CAAC;QACf,CAAC;QACD,OAAOF,OAAO,CAACD,GAAG,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;AACL,CAAC;AAKM,SAASJ,oBAAoB,CAClCQ,aAAsD,EACtC;IAChB,OAAO,OAAOJ,GAAkB,EAAEK,GAAmB,EAAEC,IAAgB,GAAK;QAC1E,IAAI,CAACN,CAAAA,GAAG,QAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEO,GAAG,CAAA,IAAI,CAACP,GAAG,CAACE,MAAM,EAAE;YAC5B,OAAOI,IAAI,EAAE,CAAC;QAChB,CAAC;QACD,8EAA8E;QAC9ED,GAAG,CAACG,YAAY,CAAC,wBAAwB,CAAC,CAAC;QAC3CH,GAAG,CAACG,YAAY,CAAC,eAAe,CAAC,CAAC;QAClCH,GAAG,CAACG,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5BH,GAAG,CAACG,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAEtC,IAAI;YACF,MAAMC,OAAO,GAAGC,IAAAA,KAAc,EAAA,eAAA,EAACV,GAAG,EAAEK,GAAG,CAAC,AAAC;YACzC,MAAMM,QAAQ,GAAG,MAAMP,aAAa,CAACK,OAAO,CAAC,AAAC;YAC9C,OAAO,MAAMG,IAAAA,KAAO,EAAA,QAAA,EAACP,GAAG,EAAEM,QAAQ,CAAC,CAAC;QACtC,EAAE,OAAOE,KAAK,EAAW;YACvB,IAAIA,KAAK,YAAYC,KAAK,EAAE;gBAC1B,OAAO,MAAMF,IAAAA,KAAO,EAAA,QAAA,EAClBP,GAAG,EACH,IAAIU,QAAQ,CAAC,yBAAyB,GAAGF,KAAK,CAACG,OAAO,EAAE;oBACtDC,MAAM,EAAE,GAAG;oBACXC,OAAO,EAAE;wBACP,cAAc,EAAE,YAAY;qBAC7B;iBACF,CAAC,CACH,CAAC;YACJ,OAAO,IAAIL,KAAK,YAAYE,QAAQ,EAAE;gBACpC,OAAO,MAAMH,IAAAA,KAAO,EAAA,QAAA,EAACP,GAAG,EAAEQ,KAAK,CAAC,CAAC;YACnC,CAAC;YACD,qEAAqE;YACrE,+BAA+B;YAC/B,mBAAmB;YACnBP,IAAI,CAACO,KAAK,CAAC,CAAC;QACd,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAASV,UAAU,GAAU;IAC3B,MAAM,IAAIY,QAAQ,CAAC,oBAAoB,EAAE;QACvCE,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;YACP,cAAc,EAAE,YAAY;SAC7B;KACF,CAAC,CAAC;AACL,CAAC;AAEM,SAASrB,UAAU,GAAU;IAClC,4CAA4C;IAC5C,MAAMsB,SAAS,CAAC;AAClB,CAAC"}