{"version": 3, "sources": ["../../../../../src/start/server/middleware/CreateFileMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\n\nconst debug = require('debug')('expo:start:server:middleware:createFile') as typeof console.log;\n\nexport type TouchFileBody = {\n  /** @deprecated */\n  path: string;\n  absolutePath?: string;\n  contents: string;\n};\n\n/**\n * Middleware for creating a file given a `POST` request with\n * `{ contents: string, path: string }` in the body.\n */\nexport class CreateFileMiddleware extends ExpoMiddleware {\n  constructor(protected projectRoot: string) {\n    super(projectRoot, ['/_expo/touch']);\n  }\n\n  protected resolvePath(inputPath: string): string {\n    return this.resolveExtension(path.join(this.projectRoot, inputPath));\n  }\n\n  protected resolveExtension(inputPath: string): string {\n    let resolvedPath = inputPath;\n    const extension = path.extname(inputPath);\n    if (extension === '.js') {\n      // Automatically convert JS files to TS files when added to a project\n      // with TypeScript.\n      const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json');\n      if (fs.existsSync(tsconfigPath)) {\n        resolvedPath = resolvedPath.replace(/\\.js$/, '.tsx');\n      }\n    }\n\n    return resolvedPath;\n  }\n\n  protected async parseRawBody(req: ServerRequest): Promise<TouchFileBody> {\n    const rawBody = await new Promise<string>((resolve, reject) => {\n      let body = '';\n      req.on('data', (chunk) => {\n        body += chunk.toString();\n      });\n      req.on('end', () => {\n        resolve(body);\n      });\n      req.on('error', (err) => {\n        reject(err);\n      });\n    });\n\n    const properties = JSON.parse(rawBody);\n    this.assertTouchFileBody(properties);\n\n    return properties;\n  }\n\n  private assertTouchFileBody(body: any): asserts body is TouchFileBody {\n    if (typeof body !== 'object' || body == null) {\n      throw new Error('Expected object');\n    }\n    if (typeof body.path !== 'string') {\n      throw new Error('Expected \"path\" in body to be string');\n    }\n    if (typeof body.contents !== 'string') {\n      throw new Error('Expected \"contents\" in body to be string');\n    }\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    if (req.method !== 'POST') {\n      res.statusCode = 405;\n      res.end('Method Not Allowed');\n      return;\n    }\n\n    let properties: TouchFileBody;\n\n    try {\n      properties = await this.parseRawBody(req);\n    } catch (e) {\n      debug('Error parsing request body', e);\n      res.statusCode = 400;\n      res.end('Bad Request');\n      return;\n    }\n\n    debug(`Requested: %O`, properties);\n\n    const resolvedPath = properties.absolutePath\n      ? this.resolveExtension(path.resolve(properties.absolutePath))\n      : this.resolvePath(properties.path);\n\n    if (fs.existsSync(resolvedPath)) {\n      res.statusCode = 409;\n      res.end('File already exists.');\n      return;\n    }\n\n    debug(`Resolved path:`, resolvedPath);\n\n    try {\n      await fs.promises.mkdir(path.dirname(resolvedPath), { recursive: true });\n      await fs.promises.writeFile(resolvedPath, properties.contents, 'utf8');\n    } catch (e) {\n      debug('Error writing file', e);\n      res.statusCode = 500;\n      res.end('Error writing file.');\n      return;\n    }\n\n    debug(`File created`);\n    res.statusCode = 200;\n    res.end('OK');\n  }\n}\n"], "names": ["CreateFileMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "<PERSON><PERSON><PERSON>", "inputPath", "resolveExtension", "path", "join", "<PERSON><PERSON><PERSON>", "extension", "extname", "tsconfigPath", "fs", "existsSync", "replace", "parseRawBody", "req", "rawBody", "Promise", "resolve", "reject", "body", "on", "chunk", "toString", "err", "properties", "JSON", "parse", "assertTouchFileBody", "Error", "contents", "handleRequestAsync", "res", "method", "statusCode", "end", "e", "absolutePath", "promises", "mkdir", "dirname", "recursive", "writeFile"], "mappings": "AAAA;;;;;CAKC,GACD;;;;+BAmBaA,sBAAoB;;aAApBA,oBAAoB;;;8DAnBlB,IAAI;;;;;;;8DACF,MAAM;;;;;;gCAEQ,kBAAkB;;;;;;AAGjD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yCAAyC,CAAC,AAAsB,AAAC;AAazF,MAAMF,oBAAoB,SAASG,eAAc,eAAA;IACtDC,YAAsBC,WAAmB,CAAE;QACzC,KAAK,CAACA,WAAW,EAAE;YAAC,cAAc;SAAC,CAAC,CAAC;QADjBA,mBAAAA,WAAmB,CAAA;IAEzC;IAEUC,WAAW,CAACC,SAAiB,EAAU;QAC/C,OAAO,IAAI,CAACC,gBAAgB,CAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACL,WAAW,EAAEE,SAAS,CAAC,CAAC,CAAC;IACvE;IAEUC,gBAAgB,CAACD,SAAiB,EAAU;QACpD,IAAII,YAAY,GAAGJ,SAAS,AAAC;QAC7B,MAAMK,SAAS,GAAGH,KAAI,EAAA,QAAA,CAACI,OAAO,CAACN,SAAS,CAAC,AAAC;QAC1C,IAAIK,SAAS,KAAK,KAAK,EAAE;YACvB,qEAAqE;YACrE,mBAAmB;YACnB,MAAME,YAAY,GAAGL,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACL,WAAW,EAAE,eAAe,CAAC,AAAC;YAClE,IAAIU,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,YAAY,CAAC,EAAE;gBAC/BH,YAAY,GAAGA,YAAY,CAACM,OAAO,UAAU,MAAM,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAON,YAAY,CAAC;IACtB;UAEgBO,YAAY,CAACC,GAAkB,EAA0B;QACvE,MAAMC,OAAO,GAAG,MAAM,IAAIC,OAAO,CAAS,CAACC,OAAO,EAAEC,MAAM,GAAK;YAC7D,IAAIC,IAAI,GAAG,EAAE,AAAC;YACdL,GAAG,CAACM,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;gBACxBF,IAAI,IAAIE,KAAK,CAACC,QAAQ,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;YACHR,GAAG,CAACM,EAAE,CAAC,KAAK,EAAE,IAAM;gBAClBH,OAAO,CAACE,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACHL,GAAG,CAACM,EAAE,CAAC,OAAO,EAAE,CAACG,GAAG,GAAK;gBACvBL,MAAM,CAACK,GAAG,CAAC,CAAC;YACd,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,AAAC;QAEH,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACX,OAAO,CAAC,AAAC;QACvC,IAAI,CAACY,mBAAmB,CAACH,UAAU,CAAC,CAAC;QAErC,OAAOA,UAAU,CAAC;IACpB;IAEQG,mBAAmB,CAACR,IAAS,EAAiC;QACpE,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAI,IAAI,EAAE;YAC5C,MAAM,IAAIS,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,OAAOT,IAAI,CAACf,IAAI,KAAK,QAAQ,EAAE;YACjC,MAAM,IAAIwB,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,OAAOT,IAAI,CAACU,QAAQ,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAID,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH;UAEME,kBAAkB,CAAChB,GAAkB,EAAEiB,GAAmB,EAAiB;QAC/E,IAAIjB,GAAG,CAACkB,MAAM,KAAK,MAAM,EAAE;YACzBD,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC;YACrBF,GAAG,CAACG,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAC9B,OAAO;QACT,CAAC;QAED,IAAIV,UAAU,AAAe,AAAC;QAE9B,IAAI;YACFA,UAAU,GAAG,MAAM,IAAI,CAACX,YAAY,CAACC,GAAG,CAAC,CAAC;QAC5C,EAAE,OAAOqB,CAAC,EAAE;YACVvC,KAAK,CAAC,4BAA4B,EAAEuC,CAAC,CAAC,CAAC;YACvCJ,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC;YACrBF,GAAG,CAACG,GAAG,CAAC,aAAa,CAAC,CAAC;YACvB,OAAO;QACT,CAAC;QAEDtC,KAAK,CAAC,CAAC,aAAa,CAAC,EAAE4B,UAAU,CAAC,CAAC;QAEnC,MAAMlB,YAAY,GAAGkB,UAAU,CAACY,YAAY,GACxC,IAAI,CAACjC,gBAAgB,CAACC,KAAI,EAAA,QAAA,CAACa,OAAO,CAACO,UAAU,CAACY,YAAY,CAAC,CAAC,GAC5D,IAAI,CAACnC,WAAW,CAACuB,UAAU,CAACpB,IAAI,CAAC,AAAC;QAEtC,IAAIM,GAAE,EAAA,QAAA,CAACC,UAAU,CAACL,YAAY,CAAC,EAAE;YAC/ByB,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC;YACrBF,GAAG,CAACG,GAAG,CAAC,sBAAsB,CAAC,CAAC;YAChC,OAAO;QACT,CAAC;QAEDtC,KAAK,CAAC,CAAC,cAAc,CAAC,EAAEU,YAAY,CAAC,CAAC;QAEtC,IAAI;YACF,MAAMI,GAAE,EAAA,QAAA,CAAC2B,QAAQ,CAACC,KAAK,CAAClC,KAAI,EAAA,QAAA,CAACmC,OAAO,CAACjC,YAAY,CAAC,EAAE;gBAAEkC,SAAS,EAAE,IAAI;aAAE,CAAC,CAAC;YACzE,MAAM9B,GAAE,EAAA,QAAA,CAAC2B,QAAQ,CAACI,SAAS,CAACnC,YAAY,EAAEkB,UAAU,CAACK,QAAQ,EAAE,MAAM,CAAC,CAAC;QACzE,EAAE,OAAOM,EAAC,EAAE;YACVvC,KAAK,CAAC,oBAAoB,EAAEuC,EAAC,CAAC,CAAC;YAC/BJ,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC;YACrBF,GAAG,CAACG,GAAG,CAAC,qBAAqB,CAAC,CAAC;YAC/B,OAAO;QACT,CAAC;QAEDtC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACtBmC,GAAG,CAACE,UAAU,GAAG,GAAG,CAAC;QACrBF,GAAG,CAACG,GAAG,CAAC,IAAI,CAAC,CAAC;IAChB;CACD"}