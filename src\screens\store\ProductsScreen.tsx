import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Image,
  RefreshControl,
  SafeAreaView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { storeService } from '../../services/store';
import { Product } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Icon from 'react-native-vector-icons/MaterialIcons';

const ProductsScreen = () => {
  const navigation = useNavigation();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await storeService.getProducts();
      setProducts(data);
    } catch (err) {
      setError('Failed to load products');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  const handleAddProduct = () => {
    navigation.navigate('ProductDetail', { isNew: true });
  };

  const handleEditProduct = (product: Product) => {
    navigation.navigate('ProductDetail', { product, isNew: false });
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <Card style={styles.productCard}>
      <TouchableOpacity 
        style={styles.productContent}
        onPress={() => handleEditProduct(item)}
      >
        <Image 
          source={{ uri: item.image || 'https://via.placeholder.com/100' }} 
          style={styles.productImage}
        />
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>${item.price.toFixed(2)}</Text>
          <Text style={styles.productStock}>Stock: {item.stock}</Text>
          <Text style={styles.productCategory}>{item.category}</Text>
        </View>
        <Icon name="chevron-right" size={24} color={COLORS.secondary} />
      </TouchableOpacity>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Products</Text>
          <Button 
            title="Add Product" 
            onPress={handleAddProduct}
            variant="primary"
            size="small"
            style={styles.addButton}
          />
        </View>
        
        {error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <Button 
              title="Try Again" 
              onPress={fetchProducts}
              variant="outline"
              size="small"
              style={styles.retryButton}
            />
          </Card>
        ) : (
          <FlatList
            data={products}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={fetchProducts} />
            }
            ListEmptyComponent={
              !isLoading ? (
                <View style={styles.emptyContainer}>
                  <Icon name="inventory" size={48} color={COLORS.secondary + '40'} />
                  <Text style={styles.emptyText}>No products found</Text>
                  <Button 
                    title="Add Your First Product" 
                    onPress={handleAddProduct}
                    variant="primary"
                    style={styles.emptyButton}
                  />
                </View>
              ) : null
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.secondary,
  },
  addButton: {
    minWidth: 120,
  },
  listContent: {
    paddingBottom: SPACING.lg,
  },
  productCard: {
    marginBottom: SPACING.sm,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: SPACING.md,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...FONTS.medium