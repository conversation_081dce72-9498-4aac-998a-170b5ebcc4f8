{"version": 3, "sources": ["../../../../../src/start/platforms/android/ADBServer.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport { execFileSync } from 'child_process';\n\nimport { assertSdkRoot } from './AndroidSdk';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { AbortCommandError, CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nconst debug = require('debug')('expo:start:platforms:android:adbServer') as typeof console.log;\n\nconst BEGINNING_OF_ADB_ERROR_MESSAGE = 'error: ';\n\n// This is a tricky class since it controls a system state (side-effects).\n// A more ideal solution would be to implement ADB in JS.\n// The main reason this is a class is to control the flow of testing.\n\nexport class ADBServer {\n  isRunning: boolean = false;\n  removeExitHook: () => void = () => {};\n\n  /** Returns the command line reference to ADB. */\n  getAdbExecutablePath(): string {\n    try {\n      const sdkRoot = assertSdkRoot();\n      if (sdkRoot) {\n        return `${sdkRoot}/platform-tools/adb`;\n      }\n    } catch (error: any) {\n      Log.warn(error.message);\n    }\n\n    Log.debug('Failed to resolve the Android SDK path, falling back to global adb executable');\n    return 'adb';\n  }\n\n  /** Start the ADB server. */\n  async startAsync(): Promise<boolean> {\n    if (this.isRunning) {\n      return false;\n    }\n    // clean up\n    this.removeExitHook = installExitHooks(() => {\n      if (this.isRunning) {\n        this.stopAsync();\n      }\n    });\n    const adb = this.getAdbExecutablePath();\n    const result = await this.resolveAdbPromise(spawnAsync(adb, ['start-server']));\n    const lines = result.stderr.trim().split(/\\r?\\n/);\n    const isStarted = lines.includes('* daemon started successfully');\n    this.isRunning = isStarted;\n    return isStarted;\n  }\n\n  /** Kill the ADB server. */\n  async stopAsync(): Promise<boolean> {\n    debug('Stopping ADB server');\n\n    if (!this.isRunning) {\n      debug('ADB server is not running');\n      return false;\n    }\n    this.removeExitHook();\n    try {\n      await this.runAsync(['kill-server']);\n      return true;\n    } catch (error: any) {\n      Log.error('Failed to stop ADB server: ' + error.message);\n      return false;\n    } finally {\n      debug('Stopped ADB server');\n      this.isRunning = false;\n    }\n  }\n\n  /** Execute an ADB command with given args. */\n  async runAsync(args: string[]): Promise<string> {\n    // TODO: Add a global package that installs adb to the path.\n    const adb = this.getAdbExecutablePath();\n\n    await this.startAsync();\n\n    debug([adb, ...args].join(' '));\n    const result = await this.resolveAdbPromise(spawnAsync(adb, args));\n    return result.output.join('\\n');\n  }\n\n  /** Get ADB file output. Useful for reading device state/settings. */\n  async getFileOutputAsync(args: string[]): Promise<string> {\n    // TODO: Add a global package that installs adb to the path.\n    const adb = this.getAdbExecutablePath();\n\n    await this.startAsync();\n\n    const results = await this.resolveAdbPromise(\n      execFileSync(adb, args, {\n        encoding: 'latin1',\n        stdio: 'pipe',\n      })\n    );\n    debug('[ADB] File output:\\n', results);\n    return results;\n  }\n\n  /** Formats error info. */\n  async resolveAdbPromise<T>(promise: T | Promise<T>): Promise<T> {\n    try {\n      return await promise;\n    } catch (error: any) {\n      // User pressed ctrl+c to cancel the process...\n      if (error.signal === 'SIGINT') {\n        throw new AbortCommandError();\n      }\n      if (error.status === 255 && error.stdout.includes('Bad user number')) {\n        const userNumber = error.stdout.match(/Bad user number: (.+)/)?.[1] ?? env.EXPO_ADB_USER;\n        throw new CommandError(\n          'EXPO_ADB_USER',\n          `Invalid ADB user number \"${userNumber}\" set with environment variable EXPO_ADB_USER. Run \"adb shell pm list users\" to see valid user numbers.`\n        );\n      }\n      // TODO: Support heap corruption for adb 29 (process exits with code -1073740940) (windows and linux)\n      let errorMessage = (error.stderr || error.stdout || error.message).trim();\n      if (errorMessage.startsWith(BEGINNING_OF_ADB_ERROR_MESSAGE)) {\n        errorMessage = errorMessage.substring(BEGINNING_OF_ADB_ERROR_MESSAGE.length);\n      }\n\n      error.message = errorMessage;\n      throw error;\n    }\n  }\n}\n"], "names": ["ADBServer", "debug", "require", "BEGINNING_OF_ADB_ERROR_MESSAGE", "isRunning", "removeExitHook", "getAdbExecutablePath", "sdkRoot", "assertSdkRoot", "error", "Log", "warn", "message", "startAsync", "installExitHooks", "stopAsync", "adb", "result", "resolveAdbPromise", "spawnAsync", "lines", "stderr", "trim", "split", "isStarted", "includes", "runAsync", "args", "join", "output", "getFileOutputAsync", "results", "execFileSync", "encoding", "stdio", "promise", "signal", "AbortCommandError", "status", "stdout", "userNumber", "match", "env", "EXPO_ADB_USER", "CommandError", "errorMessage", "startsWith", "substring", "length"], "mappings": "AAAA;;;;+BAiBaA,WAAS;;aAATA,SAAS;;;8DAjBC,mBAAmB;;;;;;;yBACb,eAAe;;;;;;4BAEd,cAAc;qBACxB,cAAc;qBACd,oBAAoB;wBACQ,uBAAuB;sBACtC,qBAAqB;;;;;;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wCAAwC,CAAC,AAAsB,AAAC;AAE/F,MAAMC,8BAA8B,GAAG,SAAS,AAAC;AAM1C,MAAMH,SAAS;IACpBI,SAAS,GAAY,KAAK,CAAC;IAC3BC,cAAc,GAAe,IAAM,CAAC,CAAC,CAAC;IAEtC,+CAA+C,GAC/CC,oBAAoB,GAAW;QAC7B,IAAI;YACF,MAAMC,OAAO,GAAGC,IAAAA,WAAa,cAAA,GAAE,AAAC;YAChC,IAAID,OAAO,EAAE;gBACX,OAAO,CAAC,EAAEA,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACzC,CAAC;QACH,EAAE,OAAOE,KAAK,EAAO;YACnBC,IAAG,IAAA,CAACC,IAAI,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC;QAC1B,CAAC;QAEDF,IAAG,IAAA,CAACT,KAAK,CAAC,+EAA+E,CAAC,CAAC;QAC3F,OAAO,KAAK,CAAC;IACf;IAEA,0BAA0B,SACpBY,UAAU,GAAqB;QACnC,IAAI,IAAI,CAACT,SAAS,EAAE;YAClB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,WAAW;QACX,IAAI,CAACC,cAAc,GAAGS,IAAAA,KAAgB,iBAAA,EAAC,IAAM;YAC3C,IAAI,IAAI,CAACV,SAAS,EAAE;gBAClB,IAAI,CAACW,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAMC,GAAG,GAAG,IAAI,CAACV,oBAAoB,EAAE,AAAC;QACxC,MAAMW,MAAM,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACC,IAAAA,WAAU,EAAA,QAAA,EAACH,GAAG,EAAE;YAAC,cAAc;SAAC,CAAC,CAAC,AAAC;QAC/E,MAAMI,KAAK,GAAGH,MAAM,CAACI,MAAM,CAACC,IAAI,EAAE,CAACC,KAAK,SAAS,AAAC;QAClD,MAAMC,SAAS,GAAGJ,KAAK,CAACK,QAAQ,CAAC,+BAA+B,CAAC,AAAC;QAClE,IAAI,CAACrB,SAAS,GAAGoB,SAAS,CAAC;QAC3B,OAAOA,SAAS,CAAC;IACnB;IAEA,yBAAyB,SACnBT,SAAS,GAAqB;QAClCd,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,CAACG,SAAS,EAAE;YACnBH,KAAK,CAAC,2BAA2B,CAAC,CAAC;YACnC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAACI,cAAc,EAAE,CAAC;QACtB,IAAI;YACF,MAAM,IAAI,CAACqB,QAAQ,CAAC;gBAAC,aAAa;aAAC,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,EAAE,OAAOjB,KAAK,EAAO;YACnBC,IAAG,IAAA,CAACD,KAAK,CAAC,6BAA6B,GAAGA,KAAK,CAACG,OAAO,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC,QAAS;YACRX,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC5B,IAAI,CAACG,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC;IACH;IAEA,4CAA4C,SACtCsB,QAAQ,CAACC,IAAc,EAAmB;QAC9C,4DAA4D;QAC5D,MAAMX,GAAG,GAAG,IAAI,CAACV,oBAAoB,EAAE,AAAC;QAExC,MAAM,IAAI,CAACO,UAAU,EAAE,CAAC;QAExBZ,KAAK,CAAC;YAACe,GAAG;eAAKW,IAAI;SAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAChC,MAAMX,MAAM,GAAG,MAAM,IAAI,CAACC,iBAAiB,CAACC,IAAAA,WAAU,EAAA,QAAA,EAACH,GAAG,EAAEW,IAAI,CAAC,CAAC,AAAC;QACnE,OAAOV,MAAM,CAACY,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC;IAEA,mEAAmE,SAC7DE,kBAAkB,CAACH,IAAc,EAAmB;QACxD,4DAA4D;QAC5D,MAAMX,GAAG,GAAG,IAAI,CAACV,oBAAoB,EAAE,AAAC;QAExC,MAAM,IAAI,CAACO,UAAU,EAAE,CAAC;QAExB,MAAMkB,OAAO,GAAG,MAAM,IAAI,CAACb,iBAAiB,CAC1Cc,IAAAA,aAAY,EAAA,aAAA,EAAChB,GAAG,EAAEW,IAAI,EAAE;YACtBM,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,MAAM;SACd,CAAC,CACH,AAAC;QACFjC,KAAK,CAAC,sBAAsB,EAAE8B,OAAO,CAAC,CAAC;QACvC,OAAOA,OAAO,CAAC;IACjB;IAEA,wBAAwB,SAClBb,iBAAiB,CAAIiB,OAAuB,EAAc;QAC9D,IAAI;YACF,OAAO,MAAMA,OAAO,CAAC;QACvB,EAAE,OAAO1B,KAAK,EAAO;YACnB,+CAA+C;YAC/C,IAAIA,KAAK,CAAC2B,MAAM,KAAK,QAAQ,EAAE;gBAC7B,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;YAChC,CAAC;YACD,IAAI5B,KAAK,CAAC6B,MAAM,KAAK,GAAG,IAAI7B,KAAK,CAAC8B,MAAM,CAACd,QAAQ,CAAC,iBAAiB,CAAC,EAAE;oBACjDhB,GAA2C;gBAA9D,MAAM+B,UAAU,GAAG/B,CAAAA,CAAAA,GAA2C,GAA3CA,KAAK,CAAC8B,MAAM,CAACE,KAAK,yBAAyB,SAAK,GAAhDhC,KAAAA,CAAgD,GAAhDA,GAA2C,AAAE,CAAC,CAAC,CAAC,CAAA,IAAIiC,IAAG,IAAA,CAACC,aAAa,AAAC;gBACzF,MAAM,IAAIC,OAAY,aAAA,CACpB,eAAe,EACf,CAAC,yBAAyB,EAAEJ,UAAU,CAAC,uGAAuG,CAAC,CAChJ,CAAC;YACJ,CAAC;YACD,qGAAqG;YACrG,IAAIK,YAAY,GAAG,CAACpC,KAAK,CAACY,MAAM,IAAIZ,KAAK,CAAC8B,MAAM,IAAI9B,KAAK,CAACG,OAAO,CAAC,CAACU,IAAI,EAAE,AAAC;YAC1E,IAAIuB,YAAY,CAACC,UAAU,CAAC3C,8BAA8B,CAAC,EAAE;gBAC3D0C,YAAY,GAAGA,YAAY,CAACE,SAAS,CAAC5C,8BAA8B,CAAC6C,MAAM,CAAC,CAAC;YAC/E,CAAC;YAEDvC,KAAK,CAACG,OAAO,GAAGiC,YAAY,CAAC;YAC7B,MAAMpC,KAAK,CAAC;QACd,CAAC;IACH;CACD"}