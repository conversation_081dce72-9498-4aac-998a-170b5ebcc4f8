{"version": 3, "file": "withAndroidSplashMainActivity.js", "names": ["_configPlugins", "data", "require", "_codeMod", "_generateCode", "withAndroidSplashMainActivity", "config", "isLegacyConfig", "withMainActivity", "modResults", "language", "withImports", "addImports", "contents", "replace", "init", "mergeContents", "src", "comment", "tag", "offset", "anchor", "newSrc", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashMainActivity.ts"], "sourcesContent": ["import { ConfigPlugin, withMainActivity } from '@expo/config-plugins';\nimport { addImports } from '@expo/config-plugins/build/android/codeMod';\nimport { mergeContents } from '@expo/config-plugins/build/utils/generateCode';\n\nexport const withAndroidSplashMainActivity: ConfigPlugin<{ isLegacyConfig: boolean }> = (\n  config,\n  { isLegacyConfig }\n) => {\n  if (isLegacyConfig) {\n    return config;\n  }\n  return withMainActivity(config, (config) => {\n    const { modResults } = config;\n    const { language } = modResults;\n\n    const withImports = addImports(\n      modResults.contents.replace(\n        /(\\/\\/ )?setTheme\\(R\\.style\\.AppTheme\\)/,\n        '// setTheme(R.style.AppTheme)'\n      ),\n      ['expo.modules.splashscreen.SplashScreenManager'],\n      language === 'java'\n    );\n\n    const init = mergeContents({\n      src: withImports,\n      comment: '    //',\n      tag: 'expo-splashscreen',\n      offset: 0,\n      anchor: /super\\.onCreate\\(null\\)/,\n      newSrc: '    SplashScreenManager.registerOnActivity(this)' + (language === 'java' ? ';' : ''),\n    });\n\n    return {\n      ...config,\n      modResults: {\n        ...modResults,\n        contents: init.contents,\n      },\n    };\n  });\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,cAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,aAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMI,6BAAwE,GAAGA,CACtFC,MAAM,EACN;EAAEC;AAAe,CAAC,KACf;EACH,IAAIA,cAAc,EAAE;IAClB,OAAOD,MAAM;EACf;EACA,OAAO,IAAAE,iCAAgB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC1C,MAAM;MAAEG;IAAW,CAAC,GAAGH,MAAM;IAC7B,MAAM;MAAEI;IAAS,CAAC,GAAGD,UAAU;IAE/B,MAAME,WAAW,GAAG,IAAAC,qBAAU,EAC5BH,UAAU,CAACI,QAAQ,CAACC,OAAO,CACzB,wCAAwC,EACxC,+BACF,CAAC,EACD,CAAC,+CAA+C,CAAC,EACjDJ,QAAQ,KAAK,MACf,CAAC;IAED,MAAMK,IAAI,GAAG,IAAAC,6BAAa,EAAC;MACzBC,GAAG,EAAEN,WAAW;MAChBO,OAAO,EAAE,QAAQ;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,yBAAyB;MACjCC,MAAM,EAAE,kDAAkD,IAAIZ,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE;IAC9F,CAAC,CAAC;IAEF,OAAO;MACL,GAAGJ,MAAM;MACTG,UAAU,EAAE;QACV,GAAGA,UAAU;QACbI,QAAQ,EAAEE,IAAI,CAACF;MACjB;IACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAACU,OAAA,CAAAlB,6BAAA,GAAAA,6BAAA", "ignoreList": []}