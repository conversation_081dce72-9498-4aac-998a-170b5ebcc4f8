{"version": 3, "sources": ["../../../src/run/startBundler.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport { startInterfaceAsync } from '../start/interface/startInterface';\nimport { BundlerStartOptions } from '../start/server/BundlerDevServer';\nimport { DevServerManager } from '../start/server/DevServerManager';\nimport { env } from '../utils/env';\nimport { isInteractive } from '../utils/interactive';\n\nexport async function startBundlerAsync(\n  projectRoot: string,\n  {\n    port,\n    headless,\n    scheme,\n  }: {\n    port: number;\n    headless?: boolean;\n    scheme?: string;\n  }\n): Promise<DevServerManager> {\n  const options: BundlerStartOptions = {\n    port,\n    headless,\n    devClient: true,\n    minify: false,\n\n    location: {\n      scheme,\n    },\n  };\n\n  const devServerManager = await DevServerManager.startMetroAsync(projectRoot, options);\n\n  // Present the Terminal UI.\n  if (!headless && isInteractive()) {\n    // Only read the config if we are going to use the results.\n    const { exp } = getConfig(projectRoot, {\n      // We don't need very many fields here, just use the lightest possible read.\n      skipSDKVersionRequirement: true,\n      skipPlugins: true,\n    });\n    await startInterfaceAsync(devServerManager, {\n      platforms: exp.platforms ?? [],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n\n    if (url) {\n      if (env.__EXPO_E2E_TEST) {\n        // Print the URL to stdout for tests\n        console.info(`[__EXPO_E2E_TEST:server] ${JSON.stringify({ url })}`);\n      }\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  if (!options.headless) {\n    await devServerManager.watchEnvironmentVariables();\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  return devServerManager;\n}\n"], "names": ["startBundlerAsync", "projectRoot", "port", "headless", "scheme", "options", "devClient", "minify", "location", "devServerManager", "DevServerManager", "startMetroAsync", "isInteractive", "exp", "getConfig", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "startInterfaceAsync", "platforms", "url", "getDefaultDevServer", "getDevServerUrl", "env", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "Log", "log", "chalk", "watchEnvironmentVariables", "bootstrapTypeScriptAsync"], "mappings": "AAAA;;;;+BAUsBA,mBAAiB;;aAAjBA,iBAAiB;;;yBAVb,cAAc;;;;;;;8DACtB,OAAO;;;;;;2DAEJ,QAAQ;gCACO,mCAAmC;kCAEtC,kCAAkC;qBAC/C,cAAc;6BACJ,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,eAAeA,iBAAiB,CACrCC,WAAmB,EACnB,EACEC,IAAI,CAAA,EACJC,QAAQ,CAAA,EACRC,MAAM,CAAA,EAKP,EAC0B;IAC3B,MAAMC,OAAO,GAAwB;QACnCH,IAAI;QACJC,QAAQ;QACRG,SAAS,EAAE,IAAI;QACfC,MAAM,EAAE,KAAK;QAEbC,QAAQ,EAAE;YACRJ,MAAM;SACP;KACF,AAAC;IAEF,MAAMK,gBAAgB,GAAG,MAAMC,iBAAgB,iBAAA,CAACC,eAAe,CAACV,WAAW,EAAEI,OAAO,CAAC,AAAC;IAEtF,2BAA2B;IAC3B,IAAI,CAACF,QAAQ,IAAIS,IAAAA,YAAa,cAAA,GAAE,EAAE;QAChC,2DAA2D;QAC3D,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAACb,WAAW,EAAE;YACrC,4EAA4E;YAC5Ec,yBAAyB,EAAE,IAAI;YAC/BC,WAAW,EAAE,IAAI;SAClB,CAAC,AAAC;QACH,MAAMC,IAAAA,eAAmB,oBAAA,EAACR,gBAAgB,EAAE;YAC1CS,SAAS,EAAEL,GAAG,CAACK,SAAS,IAAI,EAAE;SAC/B,CAAC,CAAC;IACL,OAAO;YAEOT,GAAsC;QADlD,uCAAuC;QACvC,MAAMU,GAAG,GAAGV,CAAAA,GAAsC,GAAtCA,gBAAgB,CAACW,mBAAmB,EAAE,SAAiB,GAAvDX,KAAAA,CAAuD,GAAvDA,GAAsC,CAAEY,eAAe,EAAE,AAAC;QAEtE,IAAIF,GAAG,EAAE;YACP,IAAIG,IAAG,IAAA,CAACC,eAAe,EAAE;gBACvB,oCAAoC;gBACpCC,OAAO,CAACC,IAAI,CAAC,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAAC;oBAAER,GAAG;iBAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,CAAC;YACDS,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sBAAsB,EAAEX,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,IAAI,CAACd,OAAO,CAACF,QAAQ,EAAE;QACrB,MAAMM,gBAAgB,CAACsB,yBAAyB,EAAE,CAAC;QACnD,MAAMtB,gBAAgB,CAACuB,wBAAwB,EAAE,CAAC;IACpD,CAAC;IAED,OAAOvB,gBAAgB,CAAC;AAC1B,CAAC"}