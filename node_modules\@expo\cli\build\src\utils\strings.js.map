{"version": 3, "sources": ["../../../src/utils/strings.ts"], "sourcesContent": ["/**\n * Joins strings with commas and 'and', based on English rules, limiting the number of items enumerated to keep from filling the console.\n * @param items strings to join\n * @param limit max number of strings to enumerate before using 'others'\n * @returns joined string\n */\nexport function joinWithCommasAnd(items: string[], limit: number | undefined = 10): string {\n  if (!items.length) {\n    return '';\n  }\n\n  const uniqueItems = items.filter((value, index, array) => array.indexOf(value) === index);\n\n  if (uniqueItems.length === 1) {\n    return uniqueItems[0];\n  }\n\n  if (limit && uniqueItems.length > limit) {\n    const first = uniqueItems.slice(0, limit);\n    const remaining = uniqueItems.length - limit;\n    return `${first.join(', ')}, and ${remaining} ${remaining > 1 ? 'others' : 'other'}`;\n  }\n\n  const last = uniqueItems.pop();\n  return `${uniqueItems.join(', ')}${uniqueItems.length >= 2 ? ',' : ''} and ${last}`;\n}\n"], "names": ["joinWithCommasAnd", "items", "limit", "length", "uniqueItems", "filter", "value", "index", "array", "indexOf", "first", "slice", "remaining", "join", "last", "pop"], "mappings": "AAAA;;;;;CAK<PERSON>,GACD;;;;+BAAg<PERSON>,mBAAiB;;aAAjBA,iBAAiB;;AAA1B,SAASA,iBAAiB,CAACC,KAAe,EAAEC,KAAyB,GAAG,EAAE,EAAU;IACzF,IAAI,CAACD,KAAK,CAACE,MAAM,EAAE;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAMC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,GAAKA,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,KAAKC,KAAK,CAAC,AAAC;IAE1F,IAAIH,WAAW,CAACD,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAOC,WAAW,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAED,IAAIF,KAAK,IAAIE,WAAW,CAACD,MAAM,GAAGD,KAAK,EAAE;QACvC,MAAMQ,KAAK,GAAGN,WAAW,CAACO,KAAK,CAAC,CAAC,EAAET,KAAK,CAAC,AAAC;QAC1C,MAAMU,SAAS,GAAGR,WAAW,CAACD,MAAM,GAAGD,KAAK,AAAC;QAC7C,OAAO,CAAC,EAAEQ,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAED,SAAS,CAAC,CAAC,EAAEA,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,MAAME,IAAI,GAAGV,WAAW,CAACW,GAAG,EAAE,AAAC;IAC/B,OAAO,CAAC,EAAEX,WAAW,CAACS,IAAI,CAAC,IAAI,CAAC,CAAC,EAAET,WAAW,CAACD,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,KAAK,EAAEW,IAAI,CAAC,CAAC,CAAC;AACtF,CAAC"}