{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/pageIsSupported.ts"], "sourcesContent": ["import { Page } from './types';\n\ntype DevicePageInternal = Pick<Page, 'title' | 'capabilities'>;\ntype DevicePageResponse = {\n  title: string;\n  reactNative?: {\n    logicalDeviceId: string;\n    capabilities: Page['capabilities'];\n  };\n};\n\n/**\n * Determine if a page debug target is supported by our debugging extensions.\n * If it's not, the extended CDP handlers will not be enabled.\n */\nexport function pageIsSupported(page: DevicePageInternal | DevicePageResponse): boolean {\n  // @ts-expect-error No good way to filter this properly in TypeScript\n  const capabilities = page.capabilities ?? page.reactNative?.capabilities ?? {};\n\n  return (\n    page.title === 'React Native Experimental (Improved Chrome Reloads)' ||\n    capabilities.nativePageReloads === true\n  );\n}\n"], "names": ["pageIsSupported", "page", "capabilities", "reactNative", "title", "nativePageReloads"], "mappings": "AAAA;;;;+<PERSON>eg<PERSON>,iBAAe;;aAAfA,eAAe;;AAAxB,SAASA,eAAe,CAACC,IAA6C,EAAW;QAE5CA,GAAgB;IAD1D,qEAAqE;IACrE,MAAMC,YAAY,GAAGD,IAAI,CAACC,YAAY,IAAID,CAAAA,CAAAA,GAAgB,GAAhBA,IAAI,CAACE,WAAW,SAAc,GAA9BF,KAAAA,CAA8B,GAA9BA,GAAgB,CAAEC,YAAY,CAAA,IAAI,EAAE,AAAC;IAE/E,OACED,IAAI,CAACG,KAAK,KAAK,qDAAqD,IACpEF,YAAY,CAACG,iBAAiB,KAAK,IAAI,CACvC;AACJ,CAAC"}