{"version": 3, "sources": ["../../../src/utils/jsonSchemaDeref.ts"], "sourcesContent": ["type RefPath = readonly (string | number)[];\n\ninterface NodeRef {\n  $ref: string;\n}\n\n/** Return JSON schema ref if input is of `NodeRef` type */\nconst getRef = (node: NodeRef | unknown): string | undefined =>\n  node != null && typeof node === 'object' && '$ref' in node && typeof node.$ref === 'string'\n    ? node.$ref\n    : undefined;\n\n/** Parse a JSON schema ref into a path array, or return undefined */\nconst parseRefMaybe = (ref: string): RefPath | undefined => {\n  if (ref[0] !== '#') {\n    return undefined;\n  }\n  const props = [];\n  let startIndex = 1;\n  let index = 1;\n  let char: number;\n  while (index < ref.length) {\n    while ((char = ref.charCodeAt(index++)) && char !== 47 /*'/'*/);\n    const prop = ref.slice(startIndex, index - 1);\n    startIndex = index;\n    if (prop) props.push(prop);\n  }\n  return props.length ? props : undefined;\n};\n\nconst NOT_FOUND_SYMBOL = Symbol();\n\n/** Get value at given JSON schema path or return `NOT_FOUND_SYMBOL` */\nconst getValueAtPath = (input: unknown, ref: RefPath): unknown | typeof NOT_FOUND_SYMBOL => {\n  let node = input;\n  for (let index = 0; index < ref.length; index++) {\n    const part = ref[index];\n    if (node != null && typeof node === 'object' && part in node) {\n      node = (node as Record<string, unknown>)[part];\n    } else {\n      node = NOT_FOUND_SYMBOL;\n      break;\n    }\n  }\n  return node;\n};\n\n/** Find all JSON schema refs recursively and add them to `refs` Map */\nconst findRefsRec = (\n  node: unknown,\n  refs: Map<RefPath, RefPath>,\n  path: (string | number)[]\n): void => {\n  if (node == null || typeof node !== 'object') {\n  } else if (Array.isArray(node)) {\n    for (let index = 0, l = node.length; index < l; index++) {\n      const value = node[index];\n      const ref = getRef(value);\n      if (ref) {\n        const targetRef = parseRefMaybe(ref);\n        if (targetRef) refs.set([...path, index], targetRef);\n      } else if (value != null && typeof value === 'object') {\n        path.push(index);\n        findRefsRec(value, refs, path);\n        path.pop();\n      }\n    }\n  } else {\n    const record = node as Record<string, unknown>;\n    for (const key in record) {\n      const value = record[key];\n      const ref = getRef(value);\n      if (ref) {\n        const targetRef = parseRefMaybe(ref);\n        if (targetRef) refs.set([...path, key], targetRef);\n      } else if (value != null && typeof value === 'object') {\n        path.push(key);\n        findRefsRec(value, refs, path);\n        path.pop();\n      }\n    }\n  }\n};\n\n/** Detect whether target (where we set the source value) is a nested path inside the source path */\nconst isSelfReferencingRefEntry = (target: RefPath, source: RefPath) => {\n  for (let index = 0; index < source.length; index++) {\n    if (source[index] !== target[index]) return false;\n  }\n  return true;\n};\n\n/** Return sorted refs entries. Longest target paths will be returned first */\nconst getSortedRefEntries = (refs: Map<RefPath, RefPath>): readonly [RefPath, RefPath][] => {\n  const entries = [...refs.entries()].sort((a, b) => b[1].length - a[1].length);\n  // Filter out self-referenceing paths. If we set nested targets to source values, we'd\n  // create unserializable circular references\n  return entries.filter((entry) => !isSelfReferencingRefEntry(entry[0], entry[1]));\n};\n\n/** Dereference JSON schema pointers.\n *\n * @remarks\n * This is a minimal reimplementation of `json-schema-deref-sync` without\n * file reference, URL/web reference, and loader support.\n *\n * @see https://github.com/cvent/json-schema-deref-sync\n */\nexport function jsonSchemaDeref(input: any): any {\n  // Find all JSON schema refs paths\n  const refs = new Map<RefPath, RefPath>();\n  findRefsRec(input, refs, []);\n  // Shallow copy output\n  const output = { ...input };\n  // Process all ref entries with deepest targets first\n  nextRef: for (const [target, source] of getSortedRefEntries(refs)) {\n    let inputNode = input;\n    let outputNode = output;\n    let targetIndex = 0;\n    // For each path part on the target, traverse the output and clone the input\n    // to not pollute it\n    for (; targetIndex < target.length - 1; targetIndex++) {\n      const part = target[targetIndex];\n      if (inputNode == null || typeof inputNode !== 'object' || !(part in inputNode)) {\n        // If the part doesn't exist, we abort\n        break;\n      } else if (outputNode[part] === inputNode[part]) {\n        // Copy the input on the output if references are equal\n        outputNode[part] = Array.isArray(inputNode[part])\n          ? [...inputNode[part]]\n          : { ...inputNode[part] };\n        inputNode = inputNode[part];\n        outputNode = outputNode[part];\n      } else {\n        // If this part has already been copied, abort\n        break;\n      }\n    }\n    // For each remaining part on the target, continue traversing the output\n    for (; targetIndex < target.length - 1; targetIndex++) {\n      const part = target[targetIndex];\n      if (outputNode == null || typeof outputNode !== 'object' || !(part in outputNode)) {\n        // If the part doesn't exist, skip the entire ref\n        continue nextRef;\n      } else {\n        outputNode = outputNode[part];\n      }\n    }\n    // Get value from output\n    let sourceValue = getValueAtPath(output, source);\n    if (sourceValue === NOT_FOUND_SYMBOL) {\n      // If no value was found, try to get a value from the input instead\n      sourceValue = getValueAtPath(input, source);\n      // Otherwise, skip this ref\n      if (sourceValue === NOT_FOUND_SYMBOL) continue;\n    }\n    // Set the source value on the target path\n    // The for-loops prior have made sure that the output has already been deeply\n    // cloned and traversed for the entire path\n    outputNode[target[target.length - 1]] = sourceValue;\n  }\n  // Return the output with resolved refs\n  return output;\n}\n"], "names": ["jsonSchemaDeref", "getRef", "node", "$ref", "undefined", "parseRefMaybe", "ref", "props", "startIndex", "index", "char", "length", "charCodeAt", "prop", "slice", "push", "NOT_FOUND_SYMBOL", "Symbol", "getValueAtPath", "input", "part", "findRefsRec", "refs", "path", "Array", "isArray", "l", "value", "targetRef", "set", "pop", "record", "key", "isSelfReferencingRefEntry", "target", "source", "getSortedRefEntries", "entries", "sort", "a", "b", "filter", "entry", "Map", "output", "nextRef", "inputNode", "outputNode", "targetIndex", "sourceValue"], "mappings": "AAAA;;;;+BA4GgBA,iBAAe;;aAAfA,eAAe;;AAtG/B,yDAAyD,GACzD,MAAMC,MAAM,GAAG,CAACC,IAAuB,GACrCA,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAIA,IAAI,IAAI,OAAOA,IAAI,CAACC,IAAI,KAAK,QAAQ,GACvFD,IAAI,CAACC,IAAI,GACTC,SAAS,AAAC;AAEhB,mEAAmE,GACnE,MAAMC,aAAa,GAAG,CAACC,GAAW,GAA0B;IAC1D,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAClB,OAAOF,SAAS,CAAC;IACnB,CAAC;IACD,MAAMG,KAAK,GAAG,EAAE,AAAC;IACjB,IAAIC,UAAU,GAAG,CAAC,AAAC;IACnB,IAAIC,KAAK,GAAG,CAAC,AAAC;IACd,IAAIC,IAAI,AAAQ,AAAC;IACjB,MAAOD,KAAK,GAAGH,GAAG,CAACK,MAAM,CAAE;QACzB,MAAO,CAACD,IAAI,GAAGJ,GAAG,CAACM,UAAU,CAACH,KAAK,EAAE,CAAC,CAAC,IAAIC,IAAI,KAAK,EAAE,CAAC,KAAK,GAAN;QACtD,MAAMG,IAAI,GAAGP,GAAG,CAACQ,KAAK,CAACN,UAAU,EAAEC,KAAK,GAAG,CAAC,CAAC,AAAC;QAC9CD,UAAU,GAAGC,KAAK,CAAC;QACnB,IAAII,IAAI,EAAEN,KAAK,CAACQ,IAAI,CAACF,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,OAAON,KAAK,CAACI,MAAM,GAAGJ,KAAK,GAAGH,SAAS,CAAC;AAC1C,CAAC,AAAC;AAEF,MAAMY,gBAAgB,GAAGC,MAAM,EAAE,AAAC;AAElC,qEAAqE,GACrE,MAAMC,cAAc,GAAG,CAACC,KAAc,EAAEb,GAAY,GAAwC;IAC1F,IAAIJ,IAAI,GAAGiB,KAAK,AAAC;IACjB,IAAK,IAAIV,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,GAAG,CAACK,MAAM,EAAEF,KAAK,EAAE,CAAE;QAC/C,MAAMW,IAAI,GAAGd,GAAG,CAACG,KAAK,CAAC,AAAC;QACxB,IAAIP,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIkB,IAAI,IAAIlB,IAAI,EAAE;YAC5DA,IAAI,GAAG,AAACA,IAAI,AAA4B,CAACkB,IAAI,CAAC,CAAC;QACjD,OAAO;YACLlB,IAAI,GAAGc,gBAAgB,CAAC;YACxB,MAAM;QACR,CAAC;IACH,CAAC;IACD,OAAOd,IAAI,CAAC;AACd,CAAC,AAAC;AAEF,qEAAqE,GACrE,MAAMmB,WAAW,GAAG,CAClBnB,IAAa,EACboB,IAA2B,EAC3BC,IAAyB,GAChB;IACT,IAAIrB,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE,CAC9C,OAAO,IAAIsB,KAAK,CAACC,OAAO,CAACvB,IAAI,CAAC,EAAE;QAC9B,IAAK,IAAIO,KAAK,GAAG,CAAC,EAAEiB,CAAC,GAAGxB,IAAI,CAACS,MAAM,EAAEF,KAAK,GAAGiB,CAAC,EAAEjB,KAAK,EAAE,CAAE;YACvD,MAAMkB,KAAK,GAAGzB,IAAI,CAACO,KAAK,CAAC,AAAC;YAC1B,MAAMH,GAAG,GAAGL,MAAM,CAAC0B,KAAK,CAAC,AAAC;YAC1B,IAAIrB,GAAG,EAAE;gBACP,MAAMsB,SAAS,GAAGvB,aAAa,CAACC,GAAG,CAAC,AAAC;gBACrC,IAAIsB,SAAS,EAAEN,IAAI,CAACO,GAAG,CAAC;uBAAIN,IAAI;oBAAEd,KAAK;iBAAC,EAAEmB,SAAS,CAAC,CAAC;YACvD,OAAO,IAAID,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;gBACrDJ,IAAI,CAACR,IAAI,CAACN,KAAK,CAAC,CAAC;gBACjBY,WAAW,CAACM,KAAK,EAAEL,IAAI,EAAEC,IAAI,CAAC,CAAC;gBAC/BA,IAAI,CAACO,GAAG,EAAE,CAAC;YACb,CAAC;QACH,CAAC;IACH,OAAO;QACL,MAAMC,MAAM,GAAG7B,IAAI,AAA2B,AAAC;QAC/C,IAAK,MAAM8B,GAAG,IAAID,MAAM,CAAE;YACxB,MAAMJ,MAAK,GAAGI,MAAM,CAACC,GAAG,CAAC,AAAC;YAC1B,MAAM1B,IAAG,GAAGL,MAAM,CAAC0B,MAAK,CAAC,AAAC;YAC1B,IAAIrB,IAAG,EAAE;gBACP,MAAMsB,UAAS,GAAGvB,aAAa,CAACC,IAAG,CAAC,AAAC;gBACrC,IAAIsB,UAAS,EAAEN,IAAI,CAACO,GAAG,CAAC;uBAAIN,IAAI;oBAAES,GAAG;iBAAC,EAAEJ,UAAS,CAAC,CAAC;YACrD,OAAO,IAAID,MAAK,IAAI,IAAI,IAAI,OAAOA,MAAK,KAAK,QAAQ,EAAE;gBACrDJ,IAAI,CAACR,IAAI,CAACiB,GAAG,CAAC,CAAC;gBACfX,WAAW,CAACM,MAAK,EAAEL,IAAI,EAAEC,IAAI,CAAC,CAAC;gBAC/BA,IAAI,CAACO,GAAG,EAAE,CAAC;YACb,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC,AAAC;AAEF,kGAAkG,GAClG,MAAMG,yBAAyB,GAAG,CAACC,MAAe,EAAEC,MAAe,GAAK;IACtE,IAAK,IAAI1B,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG0B,MAAM,CAACxB,MAAM,EAAEF,KAAK,EAAE,CAAE;QAClD,IAAI0B,MAAM,CAAC1B,KAAK,CAAC,KAAKyB,MAAM,CAACzB,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;IACpD,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,AAAC;AAEF,4EAA4E,GAC5E,MAAM2B,mBAAmB,GAAG,CAACd,IAA2B,GAAoC;IAC1F,MAAMe,OAAO,GAAG;WAAIf,IAAI,CAACe,OAAO,EAAE;KAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKA,CAAC,CAAC,CAAC,CAAC,CAAC7B,MAAM,GAAG4B,CAAC,CAAC,CAAC,CAAC,CAAC5B,MAAM,CAAC,AAAC;IAC9E,sFAAsF;IACtF,4CAA4C;IAC5C,OAAO0B,OAAO,CAACI,MAAM,CAAC,CAACC,KAAK,GAAK,CAACT,yBAAyB,CAACS,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC,AAAC;AAUK,SAAS1C,eAAe,CAACmB,KAAU,EAAO;IAC/C,kCAAkC;IAClC,MAAMG,IAAI,GAAG,IAAIqB,GAAG,EAAoB,AAAC;IACzCtB,WAAW,CAACF,KAAK,EAAEG,IAAI,EAAE,EAAE,CAAC,CAAC;IAC7B,sBAAsB;IACtB,MAAMsB,MAAM,GAAG;QAAE,GAAGzB,KAAK;KAAE,AAAC;IAC5B,qDAAqD;IACrD0B,OAAO,EAAE,KAAK,MAAM,CAACX,MAAM,EAAEC,MAAM,CAAC,IAAIC,mBAAmB,CAACd,IAAI,CAAC,CAAE;QACjE,IAAIwB,SAAS,GAAG3B,KAAK,AAAC;QACtB,IAAI4B,UAAU,GAAGH,MAAM,AAAC;QACxB,IAAII,WAAW,GAAG,CAAC,AAAC;QACpB,4EAA4E;QAC5E,oBAAoB;QACpB,MAAOA,WAAW,GAAGd,MAAM,CAACvB,MAAM,GAAG,CAAC,EAAEqC,WAAW,EAAE,CAAE;YACrD,MAAM5B,IAAI,GAAGc,MAAM,CAACc,WAAW,CAAC,AAAC;YACjC,IAAIF,SAAS,IAAI,IAAI,IAAI,OAAOA,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC1B,IAAI,IAAI0B,SAAS,CAAC,EAAE;gBAE9E,MAAM;YACR,OAAO,IAAIC,UAAU,CAAC3B,IAAI,CAAC,KAAK0B,SAAS,CAAC1B,IAAI,CAAC,EAAE;gBAC/C,uDAAuD;gBACvD2B,UAAU,CAAC3B,IAAI,CAAC,GAAGI,KAAK,CAACC,OAAO,CAACqB,SAAS,CAAC1B,IAAI,CAAC,CAAC,GAC7C;uBAAI0B,SAAS,CAAC1B,IAAI,CAAC;iBAAC,GACpB;oBAAE,GAAG0B,SAAS,CAAC1B,IAAI,CAAC;iBAAE,CAAC;gBAC3B0B,SAAS,GAAGA,SAAS,CAAC1B,IAAI,CAAC,CAAC;gBAC5B2B,UAAU,GAAGA,UAAU,CAAC3B,IAAI,CAAC,CAAC;YAChC,OAAO;gBAEL,MAAM;YACR,CAAC;QACH,CAAC;QACD,wEAAwE;QACxE,MAAO4B,WAAW,GAAGd,MAAM,CAACvB,MAAM,GAAG,CAAC,EAAEqC,WAAW,EAAE,CAAE;YACrD,MAAM5B,KAAI,GAAGc,MAAM,CAACc,WAAW,CAAC,AAAC;YACjC,IAAID,UAAU,IAAI,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAI,CAAC,CAAC3B,KAAI,IAAI2B,UAAU,CAAC,EAAE;gBAEjF,SAASF,OAAO,CAAC;YACnB,OAAO;gBACLE,UAAU,GAAGA,UAAU,CAAC3B,KAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,wBAAwB;QACxB,IAAI6B,WAAW,GAAG/B,cAAc,CAAC0B,MAAM,EAAET,MAAM,CAAC,AAAC;QACjD,IAAIc,WAAW,KAAKjC,gBAAgB,EAAE;YACpC,mEAAmE;YACnEiC,WAAW,GAAG/B,cAAc,CAACC,KAAK,EAAEgB,MAAM,CAAC,CAAC;YAC5C,2BAA2B;YAC3B,IAAIc,WAAW,KAAKjC,gBAAgB,EAAE,SAAS;QACjD,CAAC;QACD,0CAA0C;QAC1C,6EAA6E;QAC7E,2CAA2C;QAC3C+B,UAAU,CAACb,MAAM,CAACA,MAAM,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGsC,WAAW,CAAC;IACtD,CAAC;IACD,uCAAuC;IACvC,OAAOL,MAAM,CAAC;AAChB,CAAC"}