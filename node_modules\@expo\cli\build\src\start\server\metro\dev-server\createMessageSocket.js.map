{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createMessageSocket.ts"], "sourcesContent": ["import { parse } from 'node:url';\nimport { type WebSocket, WebSocketServer, type RawData as WebSocketRawData } from 'ws';\n\nimport { createBroadcaster } from './utils/createSocketBroadcaster';\nimport { createSocketMap, type SocketId } from './utils/createSocketMap';\nimport { parseRawMessage, serializeMessage } from './utils/socketMessages';\n\ntype MessageSocketOptions = {\n  logger: {\n    warn: (message: string) => any;\n  };\n};\n\n/**\n * Client \"command\" server that dispatches basic commands to connected clients.\n * This basic client to client communication, reload, or open dev menu cli commands.\n */\nexport function createMessagesSocket(options: MessageSocketOptions) {\n  const clients = createSocketMap();\n  const broadcast = createBroadcaster(clients.map);\n\n  const server = new WebSocketServer({ noServer: true });\n\n  server.on('connection', (socket, req) => {\n    const client = clients.registerSocket(socket);\n\n    // Assign the query parameters to the socket, used for `getpeers` requests\n    // NOTE(cedric): this looks like a legacy feature, might be able to drop it\n    if (req.url) {\n      Object.defineProperty(socket, '_upgradeQuery', {\n        value: parse(req.url).query,\n      });\n    }\n\n    // Register disconnect handlers\n    socket.on('close', client.terminate);\n    socket.on('error', client.terminate);\n    // Register message handler\n    socket.on('message', createClientMessageHandler(socket, client.id, clients, broadcast));\n  });\n\n  return {\n    endpoint: '/message' as const,\n    server,\n    broadcast: (method: BroadcastMessage['method'], params?: BroadcastMessage['params']) => {\n      if (clients.map.size === 0) {\n        return options.logger.warn(\n          `No apps connected. Sending \"${method}\" to all React Native apps failed. Make sure your app is running in the simulator or on a phone connected via USB.`\n        );\n      }\n\n      broadcast(null, serializeMessage({ method, params }));\n    },\n  };\n}\n\nfunction createClientMessageHandler(\n  socket: WebSocket,\n  clientId: SocketId,\n  clients: ReturnType<typeof createSocketMap>,\n  broadcast: ReturnType<typeof createBroadcaster>\n) {\n  function handleServerRequest(message: RequestMessage) {\n    // Ignore messages without identifiers, unable to link responses\n    if (!message.id) return;\n\n    if (message.method === 'getid') {\n      return socket.send(serializeMessage({ id: message.id, result: clientId }));\n    }\n\n    if (message.method === 'getpeers') {\n      const peers: Record<string, any> = {};\n      clients.map.forEach((peerSocket, peerSocketId) => {\n        if (peerSocketId !== clientId) {\n          peers[peerSocketId] = '_upgradeQuery' in peerSocket ? peerSocket._upgradeQuery : {};\n        }\n      });\n      return socket.send(serializeMessage({ id: message.id, result: peers }));\n    }\n  }\n\n  return (data: WebSocketRawData, isBinary: boolean) => {\n    const message = parseRawMessage<IncomingMessage>(data, isBinary);\n    if (!message) return;\n\n    // Handle broadcast messages\n    if (messageIsBroadcast(message)) {\n      return broadcast(null, data.toString());\n    }\n\n    // Handle incoming requests from clients\n    if (messageIsRequest(message)) {\n      if (message.target === 'server') {\n        return handleServerRequest(message);\n      }\n\n      return clients.findSocket(message.target)?.send(\n        serializeMessage({\n          method: message.method,\n          params: message.params,\n          id: !message.id\n            ? undefined\n            : {\n                requestId: message.id,\n                clientId,\n              },\n        })\n      );\n    }\n\n    // Handle incoming responses\n    if (messageIsResponse(message)) {\n      return clients.findSocket(message.id.clientId)?.send(\n        serializeMessage({\n          id: message.id.requestId,\n          result: message.result,\n          error: message.error,\n        })\n      );\n    }\n  };\n}\n\ntype MessageId = {\n  requestId: string;\n  clientId: SocketId;\n};\n\ntype IncomingMessage = BroadcastMessage | RequestMessage | ResponseMessage;\n\ntype BroadcastMessage = {\n  method: string;\n  params?: Record<string, any>;\n};\n\ntype RequestMessage = {\n  method: string;\n  params?: Record<string, any>;\n  target: string;\n  id?: string;\n};\n\ntype ResponseMessage = {\n  result?: any;\n  error?: Error;\n  id: MessageId;\n};\n\nfunction messageIsBroadcast(message: IncomingMessage): message is BroadcastMessage {\n  return (\n    'method' in message &&\n    typeof message.method === 'string' &&\n    (!('id' in message) || message.id === undefined) &&\n    (!('target' in message) || message.target === undefined)\n  );\n}\n\nfunction messageIsRequest(message: IncomingMessage): message is RequestMessage {\n  return (\n    'method' in message &&\n    typeof message.method === 'string' &&\n    'target' in message &&\n    typeof message.target === 'string'\n  );\n}\n\nfunction messageIsResponse(message: IncomingMessage): message is ResponseMessage {\n  return (\n    'id' in message &&\n    typeof message.id === 'object' &&\n    typeof message.id.requestId !== 'undefined' &&\n    typeof message.id.clientId === 'string' &&\n    (('result' in message && !!message.result) || ('error' in message && !!message.error))\n  );\n}\n"], "names": ["createMessagesSocket", "options", "clients", "createSocketMap", "broadcast", "createBroadcaster", "map", "server", "WebSocketServer", "noServer", "on", "socket", "req", "client", "registerSocket", "url", "Object", "defineProperty", "value", "parse", "query", "terminate", "createClientMessageHandler", "id", "endpoint", "method", "params", "size", "logger", "warn", "serializeMessage", "clientId", "handleServerRequest", "message", "send", "result", "peers", "for<PERSON>ach", "peerSocket", "peerSocketId", "_upgradeQuery", "data", "isBinary", "parseRawMessage", "messageIsBroadcast", "toString", "messageIsRequest", "target", "findSocket", "undefined", "requestId", "messageIsResponse", "error"], "mappings": "AAAA;;;;+BAiBgBA,sBAAoB;;aAApBA,oBAAoB;;;yBAjBd,UAAU;;;;;;;yBACkD,IAAI;;;;;;yCAEpD,iCAAiC;iCACpB,yBAAyB;gCACtB,wBAAwB;AAYnE,SAASA,oBAAoB,CAACC,OAA6B,EAAE;IAClE,MAAMC,OAAO,GAAGC,IAAAA,gBAAe,gBAAA,GAAE,AAAC;IAClC,MAAMC,SAAS,GAAGC,IAAAA,wBAAiB,kBAAA,EAACH,OAAO,CAACI,GAAG,CAAC,AAAC;IAEjD,MAAMC,MAAM,GAAG,IAAIC,CAAAA,GAAe,EAAA,CAAA,gBAAA,CAAC;QAAEC,QAAQ,EAAE,IAAI;KAAE,CAAC,AAAC;IAEvDF,MAAM,CAACG,EAAE,CAAC,YAAY,EAAE,CAACC,MAAM,EAAEC,GAAG,GAAK;QACvC,MAAMC,MAAM,GAAGX,OAAO,CAACY,cAAc,CAACH,MAAM,CAAC,AAAC;QAE9C,0EAA0E;QAC1E,2EAA2E;QAC3E,IAAIC,GAAG,CAACG,GAAG,EAAE;YACXC,MAAM,CAACC,cAAc,CAACN,MAAM,EAAE,eAAe,EAAE;gBAC7CO,KAAK,EAAEC,IAAAA,QAAK,EAAA,MAAA,EAACP,GAAG,CAACG,GAAG,CAAC,CAACK,KAAK;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/BT,MAAM,CAACD,EAAE,CAAC,OAAO,EAAEG,MAAM,CAACQ,SAAS,CAAC,CAAC;QACrCV,MAAM,CAACD,EAAE,CAAC,OAAO,EAAEG,MAAM,CAACQ,SAAS,CAAC,CAAC;QACrC,2BAA2B;QAC3BV,MAAM,CAACD,EAAE,CAAC,SAAS,EAAEY,0BAA0B,CAACX,MAAM,EAAEE,MAAM,CAACU,EAAE,EAAErB,OAAO,EAAEE,SAAS,CAAC,CAAC,CAAC;IAC1F,CAAC,CAAC,CAAC;IAEH,OAAO;QACLoB,QAAQ,EAAE,UAAU;QACpBjB,MAAM;QACNH,SAAS,EAAE,CAACqB,MAAkC,EAAEC,MAAmC,GAAK;YACtF,IAAIxB,OAAO,CAACI,GAAG,CAACqB,IAAI,KAAK,CAAC,EAAE;gBAC1B,OAAO1B,OAAO,CAAC2B,MAAM,CAACC,IAAI,CACxB,CAAC,4BAA4B,EAAEJ,MAAM,CAAC,kHAAkH,CAAC,CAC1J,CAAC;YACJ,CAAC;YAEDrB,SAAS,CAAC,IAAI,EAAE0B,IAAAA,eAAgB,iBAAA,EAAC;gBAAEL,MAAM;gBAAEC,MAAM;aAAE,CAAC,CAAC,CAAC;QACxD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAASJ,0BAA0B,CACjCX,MAAiB,EACjBoB,QAAkB,EAClB7B,OAA2C,EAC3CE,SAA+C,EAC/C;IACA,SAAS4B,mBAAmB,CAACC,OAAuB,EAAE;QACpD,gEAAgE;QAChE,IAAI,CAACA,OAAO,CAACV,EAAE,EAAE,OAAO;QAExB,IAAIU,OAAO,CAACR,MAAM,KAAK,OAAO,EAAE;YAC9B,OAAOd,MAAM,CAACuB,IAAI,CAACJ,IAAAA,eAAgB,iBAAA,EAAC;gBAAEP,EAAE,EAAEU,OAAO,CAACV,EAAE;gBAAEY,MAAM,EAAEJ,QAAQ;aAAE,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,IAAIE,OAAO,CAACR,MAAM,KAAK,UAAU,EAAE;YACjC,MAAMW,KAAK,GAAwB,EAAE,AAAC;YACtClC,OAAO,CAACI,GAAG,CAAC+B,OAAO,CAAC,CAACC,UAAU,EAAEC,YAAY,GAAK;gBAChD,IAAIA,YAAY,KAAKR,QAAQ,EAAE;oBAC7BK,KAAK,CAACG,YAAY,CAAC,GAAG,eAAe,IAAID,UAAU,GAAGA,UAAU,CAACE,aAAa,GAAG,EAAE,CAAC;gBACtF,CAAC;YACH,CAAC,CAAC,CAAC;YACH,OAAO7B,MAAM,CAACuB,IAAI,CAACJ,IAAAA,eAAgB,iBAAA,EAAC;gBAAEP,EAAE,EAAEU,OAAO,CAACV,EAAE;gBAAEY,MAAM,EAAEC,KAAK;aAAE,CAAC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,OAAO,CAACK,IAAsB,EAAEC,QAAiB,GAAK;QACpD,MAAMT,OAAO,GAAGU,IAAAA,eAAe,gBAAA,EAAkBF,IAAI,EAAEC,QAAQ,CAAC,AAAC;QACjE,IAAI,CAACT,OAAO,EAAE,OAAO;QAErB,4BAA4B;QAC5B,IAAIW,kBAAkB,CAACX,OAAO,CAAC,EAAE;YAC/B,OAAO7B,SAAS,CAAC,IAAI,EAAEqC,IAAI,CAACI,QAAQ,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,wCAAwC;QACxC,IAAIC,gBAAgB,CAACb,OAAO,CAAC,EAAE;gBAKtB/B,GAAkC;YAJzC,IAAI+B,OAAO,CAACc,MAAM,KAAK,QAAQ,EAAE;gBAC/B,OAAOf,mBAAmB,CAACC,OAAO,CAAC,CAAC;YACtC,CAAC;YAED,OAAO/B,CAAAA,GAAkC,GAAlCA,OAAO,CAAC8C,UAAU,CAACf,OAAO,CAACc,MAAM,CAAC,SAAM,GAAxC7C,KAAAA,CAAwC,GAAxCA,GAAkC,CAAEgC,IAAI,CAC7CJ,IAAAA,eAAgB,iBAAA,EAAC;gBACfL,MAAM,EAAEQ,OAAO,CAACR,MAAM;gBACtBC,MAAM,EAAEO,OAAO,CAACP,MAAM;gBACtBH,EAAE,EAAE,CAACU,OAAO,CAACV,EAAE,GACX0B,SAAS,GACT;oBACEC,SAAS,EAAEjB,OAAO,CAACV,EAAE;oBACrBQ,QAAQ;iBACT;aACN,CAAC,CACH,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,IAAIoB,iBAAiB,CAAClB,OAAO,CAAC,EAAE;gBACvB/B,IAAuC;YAA9C,OAAOA,CAAAA,IAAuC,GAAvCA,OAAO,CAAC8C,UAAU,CAACf,OAAO,CAACV,EAAE,CAACQ,QAAQ,CAAC,SAAM,GAA7C7B,KAAAA,CAA6C,GAA7CA,IAAuC,CAAEgC,IAAI,CAClDJ,IAAAA,eAAgB,iBAAA,EAAC;gBACfP,EAAE,EAAEU,OAAO,CAACV,EAAE,CAAC2B,SAAS;gBACxBf,MAAM,EAAEF,OAAO,CAACE,MAAM;gBACtBiB,KAAK,EAAEnB,OAAO,CAACmB,KAAK;aACrB,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AA2BD,SAASR,kBAAkB,CAACX,OAAwB,EAA+B;IACjF,OACE,QAAQ,IAAIA,OAAO,IACnB,OAAOA,OAAO,CAACR,MAAM,KAAK,QAAQ,IAClC,CAAC,CAAC,CAAC,IAAI,IAAIQ,OAAO,CAAC,IAAIA,OAAO,CAACV,EAAE,KAAK0B,SAAS,CAAC,IAChD,CAAC,CAAC,CAAC,QAAQ,IAAIhB,OAAO,CAAC,IAAIA,OAAO,CAACc,MAAM,KAAKE,SAAS,CAAC,CACxD;AACJ,CAAC;AAED,SAASH,gBAAgB,CAACb,OAAwB,EAA6B;IAC7E,OACE,QAAQ,IAAIA,OAAO,IACnB,OAAOA,OAAO,CAACR,MAAM,KAAK,QAAQ,IAClC,QAAQ,IAAIQ,OAAO,IACnB,OAAOA,OAAO,CAACc,MAAM,KAAK,QAAQ,CAClC;AACJ,CAAC;AAED,SAASI,iBAAiB,CAAClB,OAAwB,EAA8B;IAC/E,OACE,IAAI,IAAIA,OAAO,IACf,OAAOA,OAAO,CAACV,EAAE,KAAK,QAAQ,IAC9B,OAAOU,OAAO,CAACV,EAAE,CAAC2B,SAAS,KAAK,WAAW,IAC3C,OAAOjB,OAAO,CAACV,EAAE,CAACQ,QAAQ,KAAK,QAAQ,IACvC,CAAC,AAAC,QAAQ,IAAIE,OAAO,IAAI,CAAC,CAACA,OAAO,CAACE,MAAM,IAAM,OAAO,IAAIF,OAAO,IAAI,CAAC,CAACA,OAAO,CAACmB,KAAK,AAAC,CAAC,CACtF;AACJ,CAAC"}