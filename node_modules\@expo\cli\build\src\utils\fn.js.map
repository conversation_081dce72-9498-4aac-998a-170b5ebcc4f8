{"version": 3, "sources": ["../../../src/utils/fn.ts"], "sourcesContent": ["/** `lodash.memoize` */\nexport function memoize<T extends (...args: any[]) => any>(fn: T): T {\n  const cache = new Map<string, any>();\n  return ((...args: any[]) => {\n    const key = JSON.stringify(args);\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    const result = fn(...args);\n    cache.set(key, result);\n    return result;\n  }) as T;\n}\n\n/** memoizes an async function to prevent subsequent calls that might be invoked before the function has finished resolving. */\nexport function guardAsync<V, T extends (...args: any[]) => Promise<V>>(fn: T): T {\n  let invoked = false;\n  let returnValue: V;\n\n  const guard: any = async (...args: any[]): Promise<V> => {\n    if (!invoked) {\n      invoked = true;\n      returnValue = await fn(...args);\n    }\n\n    return returnValue;\n  };\n\n  return guard;\n}\n"], "names": ["memoize", "<PERSON><PERSON><PERSON>", "fn", "cache", "Map", "args", "key", "JSON", "stringify", "has", "get", "result", "set", "invoked", "returnValue", "guard"], "mappings": "AAAA,qBAAqB,GACrB;;;;;;;;;;;IAAgBA,OAAO,MAAPA,OAAO;IAcPC,UAAU,MAAVA,UAAU;;AAdnB,SAASD,OAAO,CAAoCE,EAAK,EAAK;IACnE,MAAMC,KAAK,GAAG,IAAIC,GAAG,EAAe,AAAC;IACrC,OAAQ,CAAC,GAAGC,IAAI,AAAO,GAAK;QAC1B,MAAMC,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC,AAAC;QACjC,IAAIF,KAAK,CAACM,GAAG,CAACH,GAAG,CAAC,EAAE;YAClB,OAAOH,KAAK,CAACO,GAAG,CAACJ,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,MAAMK,MAAM,GAAGT,EAAE,IAAIG,IAAI,CAAC,AAAC;QAC3BF,KAAK,CAACS,GAAG,CAACN,GAAG,EAAEK,MAAM,CAAC,CAAC;QACvB,OAAOA,MAAM,CAAC;IAChB,CAAC,CAAO;AACV,CAAC;AAGM,SAASV,UAAU,CAA8CC,EAAK,EAAK;IAChF,IAAIW,OAAO,GAAG,KAAK,AAAC;IACpB,IAAIC,WAAW,AAAG,AAAC;IAEnB,MAAMC,KAAK,GAAQ,OAAO,GAAGV,IAAI,AAAO,GAAiB;QACvD,IAAI,CAACQ,OAAO,EAAE;YACZA,OAAO,GAAG,IAAI,CAAC;YACfC,WAAW,GAAG,MAAMZ,EAAE,IAAIG,IAAI,CAAC,CAAC;QAClC,CAAC;QAED,OAAOS,WAAW,CAAC;IACrB,CAAC,AAAC;IAEF,OAAOC,KAAK,CAAC;AACf,CAAC"}