{"version": 3, "sources": ["../../../../../src/start/server/middleware/ServeStaticMiddleware.ts"], "sourcesContent": ["import path from 'path';\nimport send from 'send';\nimport { parse } from 'url';\n\nimport { parsePlatformHeader } from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:start:server:middleware:serveStatic') as typeof console.log;\n\n/**\n * Adds support for serving the files in the static `public/` folder to web apps.\n */\nexport class ServeStaticMiddleware {\n  constructor(private projectRoot: string) {}\n  getHandler() {\n    const publicPath = path.join(this.projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n    debug(`Serving static files from:`, publicPath);\n    const opts = {\n      root: publicPath,\n    };\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      if (!req?.url || (req.method !== 'GET' && req.method !== 'HEAD')) {\n        return next();\n      }\n\n      const platform = parsePlatformHeader(req);\n      // Currently this is web-only\n      if (platform && platform !== 'web') {\n        return next();\n      }\n\n      const pathname = parse(req.url).pathname;\n      if (!pathname) {\n        return next();\n      }\n\n      debug(`Maybe serve static:`, pathname);\n      const stream = send(req, pathname, opts);\n\n      // add file listener for fallthrough\n      let forwardError = false;\n      stream.on('file', function onFile() {\n        // once file is determined, always forward error\n        forwardError = true;\n      });\n\n      // forward errors\n      stream.on('error', function error(err: any) {\n        if (forwardError || !(err.statusCode < 500)) {\n          next(err);\n          return;\n        }\n\n        next();\n      });\n\n      // pipe\n      stream.pipe(res);\n    };\n  }\n}\n"], "names": ["ServeStaticMiddleware", "debug", "require", "constructor", "projectRoot", "<PERSON><PERSON><PERSON><PERSON>", "publicPath", "path", "join", "env", "EXPO_PUBLIC_FOLDER", "opts", "root", "req", "res", "next", "url", "method", "platform", "parsePlatformHeader", "pathname", "parse", "stream", "send", "forward<PERSON><PERSON>r", "on", "onFile", "error", "err", "statusCode", "pipe"], "mappings": "AAAA;;;;+<PERSON>aa<PERSON>,uBAAqB;;aAArBA,qBAAqB;;;8DAbjB,MAAM;;;;;;;8DACN,MAAM;;;;;;;yBACD,KAAK;;;;;;iCAES,mBAAmB;qBAEnC,oBAAoB;;;;;;AAExC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0CAA0C,CAAC,AAAsB,AAAC;AAK1F,MAAMF,qBAAqB;IAChCG,YAAoBC,WAAmB,CAAE;QAArBA,mBAAAA,WAAmB,CAAA;IAAG;IAC1CC,UAAU,GAAG;QACX,MAAMC,UAAU,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAEK,IAAG,IAAA,CAACC,kBAAkB,CAAC,AAAC;QAEvET,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAEK,UAAU,CAAC,CAAC;QAChD,MAAMK,IAAI,GAAG;YACXC,IAAI,EAAEN,UAAU;SACjB,AAAC;QACF,OAAO,CAACO,GAAkB,EAAEC,GAAmB,EAAEC,IAAS,GAAK;YAC7D,IAAI,CAACF,CAAAA,GAAG,QAAK,GAARA,KAAAA,CAAQ,GAARA,GAAG,CAAEG,GAAG,CAAA,IAAKH,GAAG,CAACI,MAAM,KAAK,KAAK,IAAIJ,GAAG,CAACI,MAAM,KAAK,MAAM,AAAC,EAAE;gBAChE,OAAOF,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAMG,QAAQ,GAAGC,IAAAA,gBAAmB,oBAAA,EAACN,GAAG,CAAC,AAAC;YAC1C,6BAA6B;YAC7B,IAAIK,QAAQ,IAAIA,QAAQ,KAAK,KAAK,EAAE;gBAClC,OAAOH,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,MAAMK,QAAQ,GAAGC,IAAAA,IAAK,EAAA,MAAA,EAACR,GAAG,CAACG,GAAG,CAAC,CAACI,QAAQ,AAAC;YACzC,IAAI,CAACA,QAAQ,EAAE;gBACb,OAAOL,IAAI,EAAE,CAAC;YAChB,CAAC;YAEDd,KAAK,CAAC,CAAC,mBAAmB,CAAC,EAAEmB,QAAQ,CAAC,CAAC;YACvC,MAAME,MAAM,GAAGC,IAAAA,KAAI,EAAA,QAAA,EAACV,GAAG,EAAEO,QAAQ,EAAET,IAAI,CAAC,AAAC;YAEzC,oCAAoC;YACpC,IAAIa,YAAY,GAAG,KAAK,AAAC;YACzBF,MAAM,CAACG,EAAE,CAAC,MAAM,EAAE,SAASC,MAAM,GAAG;gBAClC,gDAAgD;gBAChDF,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,iBAAiB;YACjBF,MAAM,CAACG,EAAE,CAAC,OAAO,EAAE,SAASE,KAAK,CAACC,GAAQ,EAAE;gBAC1C,IAAIJ,YAAY,IAAI,CAAC,CAACI,GAAG,CAACC,UAAU,GAAG,GAAG,CAAC,EAAE;oBAC3Cd,IAAI,CAACa,GAAG,CAAC,CAAC;oBACV,OAAO;gBACT,CAAC;gBAEDb,IAAI,EAAE,CAAC;YACT,CAAC,CAAC,CAAC;YAEH,OAAO;YACPO,MAAM,CAACQ,IAAI,CAAChB,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC;IACJ;CACD"}