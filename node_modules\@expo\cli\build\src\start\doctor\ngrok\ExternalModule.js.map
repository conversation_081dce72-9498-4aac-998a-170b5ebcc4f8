{"version": 3, "sources": ["../../../../../src/start/doctor/ngrok/ExternalModule.ts"], "sourcesContent": ["import * as PackageManager from '@expo/package-manager';\nimport requireGlobal from 'requireg';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport * as Log from '../../../log';\nimport { delayAsync } from '../../../utils/delay';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { confirmAsync } from '../../../utils/prompts';\n\nconst debug = require('debug')('expo:doctor:externalModule') as typeof console.log;\n\n/** An error that is thrown when a package is installed but doesn't meet the version criteria. */\nexport class ExternalModuleVersionError extends CommandError {\n  constructor(\n    message: string,\n    public readonly shouldGloballyInstall: boolean\n  ) {\n    super('EXTERNAL_MODULE_VERSION', message);\n  }\n}\n\ninterface PromptOptions {\n  /** Should prompt the user to install, when false the module will just assert on missing packages, default `true`. Ignored when `autoInstall` is true. */\n  shouldPrompt?: boolean;\n  /** Should automatically install the package without prompting, default `false` */\n  autoInstall?: boolean;\n}\n\nexport interface InstallPromptOptions extends PromptOptions {\n  /** Should install the package globally, default `false` */\n  shouldGloballyInstall?: boolean;\n}\n\nexport interface ResolvePromptOptions extends PromptOptions {\n  /**\n   * Prefer to install the package globally, this can be overridden if the function\n   * detects that a locally installed package simply needs an upgrade, default `false`\n   */\n  prefersGlobalInstall?: boolean;\n}\n\n/** Resolves a local or globally installed package, prompts to install if missing. */\nexport class ExternalModule<TModule> {\n  private instance: TModule | null = null;\n\n  constructor(\n    /** Project root for checking if the package is installed locally. */\n    private projectRoot: string,\n    /** Info on the external package. */\n    private pkg: {\n      /** NPM package name. */\n      name: string;\n      /** Required semver range, ex: `^1.0.0`. */\n      versionRange: string;\n    },\n    /** A function used to create the installation prompt message. */\n    private promptMessage: (pkgName: string) => string\n  ) {}\n\n  /** Resolve the globally or locally installed instance, or prompt to install. */\n  async resolveAsync({\n    prefersGlobalInstall,\n    ...options\n  }: ResolvePromptOptions = {}): Promise<TModule> {\n    try {\n      return (\n        this.getVersioned() ??\n        this.installAsync({\n          ...options,\n          shouldGloballyInstall: prefersGlobalInstall,\n        })\n      );\n    } catch (error: any) {\n      if (error instanceof ExternalModuleVersionError) {\n        // If the module version in not compliant with the version range,\n        // we should prompt the user to install the package where it already exists.\n        return this.installAsync({\n          ...options,\n          shouldGloballyInstall: error.shouldGloballyInstall ?? prefersGlobalInstall,\n        });\n      }\n      throw error;\n    }\n  }\n\n  /** Prompt the user to install the package and try again. */\n  async installAsync({\n    shouldPrompt = true,\n    autoInstall,\n    shouldGloballyInstall,\n  }: InstallPromptOptions = {}): Promise<TModule> {\n    const packageName = [this.pkg.name, this.pkg.versionRange].join('@');\n    if (!autoInstall) {\n      // Delay the prompt so it doesn't conflict with other dev tool logs\n      await delayAsync(100);\n    }\n    const answer =\n      autoInstall ||\n      (shouldPrompt &&\n        (await confirmAsync({\n          message: this.promptMessage(packageName),\n          initial: true,\n        })));\n    if (answer) {\n      Log.log(`Installing ${packageName}...`);\n\n      // Always use npm for global installs\n      const packageManager = shouldGloballyInstall\n        ? new PackageManager.NpmPackageManager({\n            cwd: this.projectRoot,\n            log: Log.log,\n            silent: !(env.EXPO_DEBUG || env.CI),\n          })\n        : PackageManager.createForProject(this.projectRoot, {\n            silent: !(env.EXPO_DEBUG || env.CI),\n          });\n\n      try {\n        if (shouldGloballyInstall) {\n          await packageManager.addGlobalAsync([packageName]);\n        } else {\n          await packageManager.addDevAsync([packageName]);\n        }\n        Log.log(`Installed ${packageName}`);\n      } catch (error: any) {\n        error.message = `Failed to install ${packageName} ${\n          shouldGloballyInstall ? 'globally' : 'locally'\n        }: ${error.message}`;\n        throw error;\n      }\n      return await this.resolveAsync({ shouldPrompt: false });\n    }\n\n    throw new CommandError(\n      'EXTERNAL_MODULE_AVAILABILITY',\n      `Please install ${packageName} and try again`\n    );\n  }\n\n  /** Get the module. */\n  get(): TModule | null {\n    try {\n      return this.getVersioned();\n    } catch {\n      return null;\n    }\n  }\n\n  /** Get the module, throws if the module is not versioned correctly. */\n  getVersioned(): TModule | null {\n    this.instance ??= this._resolveModule(true) ?? this._resolveModule(false);\n    return this.instance;\n  }\n\n  /** Exposed for testing. */\n  _require(moduleId: string): any {\n    return require(moduleId);\n  }\n\n  /** Resolve a copy that's installed in the project. Exposed for testing. */\n  _resolveLocal(moduleId: string): string {\n    return resolveFrom(this.projectRoot, moduleId);\n  }\n\n  /** Resolve a copy that's installed globally. Exposed for testing. */\n  _resolveGlobal(moduleId: string): string {\n    return requireGlobal.resolve(moduleId);\n  }\n\n  /** Resolve the module and verify the version. Exposed for testing. */\n  _resolveModule(isLocal: boolean): TModule | null {\n    const resolver = isLocal ? this._resolveLocal.bind(this) : this._resolveGlobal.bind(this);\n    try {\n      const packageJsonPath = resolver(`${this.pkg.name}/package.json`);\n      const packageJson = this._require(packageJsonPath);\n      if (packageJson) {\n        if (semver.satisfies(packageJson.version, this.pkg.versionRange)) {\n          const modulePath = resolver(this.pkg.name);\n          const requiredModule = this._require(modulePath);\n          if (requiredModule == null) {\n            throw new CommandError(\n              'EXTERNAL_MODULE_EXPORT',\n              `${this.pkg.name} exports a nullish value, which is not allowed.`\n            );\n          }\n          return requiredModule;\n        }\n        throw new ExternalModuleVersionError(\n          `Required module '${this.pkg.name}@${packageJson.version}' does not satisfy ${this.pkg.versionRange}. Installed at: ${packageJsonPath}`,\n          !isLocal\n        );\n      }\n    } catch (error: any) {\n      if (error instanceof CommandError) {\n        throw error;\n      } else if (error.code !== 'MODULE_NOT_FOUND') {\n        debug('Failed to resolve module', error.message);\n      }\n    }\n    return null;\n  }\n}\n"], "names": ["ExternalModuleVersionError", "ExternalModule", "debug", "require", "CommandError", "constructor", "message", "shouldGloballyInstall", "projectRoot", "pkg", "promptMessage", "instance", "resolveAsync", "prefersGlobalInstall", "options", "getVersioned", "installAsync", "error", "should<PERSON>rompt", "autoInstall", "packageName", "name", "versionRange", "join", "delayAsync", "answer", "<PERSON><PERSON><PERSON>", "initial", "Log", "log", "packageManager", "PackageManager", "NpmPackageManager", "cwd", "silent", "env", "EXPO_DEBUG", "CI", "createForProject", "addGlobalAsync", "addDevAsync", "get", "_resolveModule", "_require", "moduleId", "_resolveLocal", "resolveFrom", "_resolveGlobal", "requireGlobal", "resolve", "isLocal", "resolver", "bind", "packageJsonPath", "packageJson", "semver", "satisfies", "version", "modulePath", "requiredModule", "code"], "mappings": "AAAA;;;;;;;;;;;IAcaA,0BAA0B,MAA1BA,0BAA0B;IA8B1BC,cAAc,MAAdA,cAAc;;;+DA5CK,uBAAuB;;;;;;;8DAC7B,UAAU;;;;;;;8DACZ,cAAc;;;;;;;8DACnB,QAAQ;;;;;;2DAEN,cAAc;uBACR,sBAAsB;qBAC7B,oBAAoB;wBACX,uBAAuB;yBACvB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAsB,AAAC;AAG5E,MAAMH,0BAA0B,SAASI,OAAY,aAAA;IAC1DC,YACEC,OAAe,EACCC,qBAA8B,CAC9C;QACA,KAAK,CAAC,yBAAyB,EAAED,OAAO,CAAC,CAAC;QAF1BC,6BAAAA,qBAA8B,CAAA;IAGhD;CACD;AAuBM,MAAMN,cAAc;IAGzBI,YAEUG,WAAmB,EAEnBC,GAKP,EAEOC,aAA0C,CAClD;QAVQF,mBAAAA,WAAmB,CAAA;QAEnBC,WAAAA,GAKP,CAAA;QAEOC,qBAAAA,aAA0C,CAAA;aAb5CC,QAAQ,GAAmB,IAAI;IAcpC;IAEH,8EAA8E,SACxEC,YAAY,CAAC,EACjBC,oBAAoB,CAAA,EACpB,GAAGC,OAAO,EACW,GAAG,EAAE,EAAoB;QAC9C,IAAI;YACF,OACE,IAAI,CAACC,YAAY,EAAE,IACnB,IAAI,CAACC,YAAY,CAAC;gBAChB,GAAGF,OAAO;gBACVP,qBAAqB,EAAEM,oBAAoB;aAC5C,CAAC,CACF;QACJ,EAAE,OAAOI,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAYjB,0BAA0B,EAAE;gBAC/C,iEAAiE;gBACjE,4EAA4E;gBAC5E,OAAO,IAAI,CAACgB,YAAY,CAAC;oBACvB,GAAGF,OAAO;oBACVP,qBAAqB,EAAEU,KAAK,CAACV,qBAAqB,IAAIM,oBAAoB;iBAC3E,CAAC,CAAC;YACL,CAAC;YACD,MAAMI,KAAK,CAAC;QACd,CAAC;IACH;IAEA,0DAA0D,SACpDD,YAAY,CAAC,EACjBE,YAAY,EAAG,IAAI,CAAA,EACnBC,WAAW,CAAA,EACXZ,qBAAqB,CAAA,EACA,GAAG,EAAE,EAAoB;QAC9C,MAAMa,WAAW,GAAG;YAAC,IAAI,CAACX,GAAG,CAACY,IAAI;YAAE,IAAI,CAACZ,GAAG,CAACa,YAAY;SAAC,CAACC,IAAI,CAAC,GAAG,CAAC,AAAC;QACrE,IAAI,CAACJ,WAAW,EAAE;YAChB,mEAAmE;YACnE,MAAMK,IAAAA,MAAU,WAAA,EAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,MAAMC,MAAM,GACVN,WAAW,IACVD,YAAY,IACV,MAAMQ,IAAAA,QAAY,aAAA,EAAC;YAClBpB,OAAO,EAAE,IAAI,CAACI,aAAa,CAACU,WAAW,CAAC;YACxCO,OAAO,EAAE,IAAI;SACd,CAAC,AAAC,AAAC,AAAC;QACT,IAAIF,MAAM,EAAE;YACVG,IAAG,CAACC,GAAG,CAAC,CAAC,WAAW,EAAET,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;YAExC,qCAAqC;YACrC,MAAMU,cAAc,GAAGvB,qBAAqB,GACxC,IAAIwB,CAAAA,eAAc,EAAA,CAAA,CAACC,iBAAiB,CAAC;gBACnCC,GAAG,EAAE,IAAI,CAACzB,WAAW;gBACrBqB,GAAG,EAAED,IAAG,CAACC,GAAG;gBACZK,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;aACpC,CAAC,GACFN,eAAc,EAAA,CAACO,gBAAgB,CAAC,IAAI,CAAC9B,WAAW,EAAE;gBAChD0B,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;aACpC,CAAC,AAAC;YAEP,IAAI;gBACF,IAAI9B,qBAAqB,EAAE;oBACzB,MAAMuB,cAAc,CAACS,cAAc,CAAC;wBAACnB,WAAW;qBAAC,CAAC,CAAC;gBACrD,OAAO;oBACL,MAAMU,cAAc,CAACU,WAAW,CAAC;wBAACpB,WAAW;qBAAC,CAAC,CAAC;gBAClD,CAAC;gBACDQ,IAAG,CAACC,GAAG,CAAC,CAAC,UAAU,EAAET,WAAW,CAAC,CAAC,CAAC,CAAC;YACtC,EAAE,OAAOH,KAAK,EAAO;gBACnBA,KAAK,CAACX,OAAO,GAAG,CAAC,kBAAkB,EAAEc,WAAW,CAAC,CAAC,EAChDb,qBAAqB,GAAG,UAAU,GAAG,SAAS,CAC/C,EAAE,EAAEU,KAAK,CAACX,OAAO,CAAC,CAAC,CAAC;gBACrB,MAAMW,KAAK,CAAC;YACd,CAAC;YACD,OAAO,MAAM,IAAI,CAACL,YAAY,CAAC;gBAAEM,YAAY,EAAE,KAAK;aAAE,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,IAAId,OAAY,aAAA,CACpB,8BAA8B,EAC9B,CAAC,eAAe,EAAEgB,WAAW,CAAC,cAAc,CAAC,CAC9C,CAAC;IACJ;IAEA,oBAAoB,GACpBqB,GAAG,GAAmB;QACpB,IAAI;YACF,OAAO,IAAI,CAAC1B,YAAY,EAAE,CAAC;QAC7B,EAAE,OAAM;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH;IAEA,qEAAqE,GACrEA,YAAY,GAAmB;QAC7B,IAAI,CAACJ,QAAQ,KAAK,IAAI,CAAC+B,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAACA,cAAc,CAAC,KAAK,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC/B,QAAQ,CAAC;IACvB;IAEA,yBAAyB,GACzBgC,QAAQ,CAACC,QAAgB,EAAO;QAC9B,OAAOzC,OAAO,CAACyC,QAAQ,CAAC,CAAC;IAC3B;IAEA,yEAAyE,GACzEC,aAAa,CAACD,QAAgB,EAAU;QACtC,OAAOE,IAAAA,YAAW,EAAA,QAAA,EAAC,IAAI,CAACtC,WAAW,EAAEoC,QAAQ,CAAC,CAAC;IACjD;IAEA,mEAAmE,GACnEG,cAAc,CAACH,QAAgB,EAAU;QACvC,OAAOI,SAAa,EAAA,QAAA,CAACC,OAAO,CAACL,QAAQ,CAAC,CAAC;IACzC;IAEA,oEAAoE,GACpEF,cAAc,CAACQ,OAAgB,EAAkB;QAC/C,MAAMC,QAAQ,GAAGD,OAAO,GAAG,IAAI,CAACL,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAACL,cAAc,CAACK,IAAI,CAAC,IAAI,CAAC,AAAC;QAC1F,IAAI;YACF,MAAMC,eAAe,GAAGF,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC1C,GAAG,CAACY,IAAI,CAAC,aAAa,CAAC,CAAC,AAAC;YAClE,MAAMiC,WAAW,GAAG,IAAI,CAACX,QAAQ,CAACU,eAAe,CAAC,AAAC;YACnD,IAAIC,WAAW,EAAE;gBACf,IAAIC,OAAM,EAAA,QAAA,CAACC,SAAS,CAACF,WAAW,CAACG,OAAO,EAAE,IAAI,CAAChD,GAAG,CAACa,YAAY,CAAC,EAAE;oBAChE,MAAMoC,UAAU,GAAGP,QAAQ,CAAC,IAAI,CAAC1C,GAAG,CAACY,IAAI,CAAC,AAAC;oBAC3C,MAAMsC,cAAc,GAAG,IAAI,CAAChB,QAAQ,CAACe,UAAU,CAAC,AAAC;oBACjD,IAAIC,cAAc,IAAI,IAAI,EAAE;wBAC1B,MAAM,IAAIvD,OAAY,aAAA,CACpB,wBAAwB,EACxB,CAAC,EAAE,IAAI,CAACK,GAAG,CAACY,IAAI,CAAC,+CAA+C,CAAC,CAClE,CAAC;oBACJ,CAAC;oBACD,OAAOsC,cAAc,CAAC;gBACxB,CAAC;gBACD,MAAM,IAAI3D,0BAA0B,CAClC,CAAC,iBAAiB,EAAE,IAAI,CAACS,GAAG,CAACY,IAAI,CAAC,CAAC,EAAEiC,WAAW,CAACG,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAChD,GAAG,CAACa,YAAY,CAAC,gBAAgB,EAAE+B,eAAe,CAAC,CAAC,EACvI,CAACH,OAAO,CACT,CAAC;YACJ,CAAC;QACH,EAAE,OAAOjC,KAAK,EAAO;YACnB,IAAIA,KAAK,YAAYb,OAAY,aAAA,EAAE;gBACjC,MAAMa,KAAK,CAAC;YACd,OAAO,IAAIA,KAAK,CAAC2C,IAAI,KAAK,kBAAkB,EAAE;gBAC5C1D,KAAK,CAAC,0BAA0B,EAAEe,KAAK,CAACX,OAAO,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd;CACD"}