{"version": 3, "file": "expo-updates.js", "names": ["_configPlugins", "data", "require", "packageName", "withExpoUpdates", "config", "withStaticPlugin", "_isLegacyPlugin", "plugin", "fallback", "createRunOncePlugin", "withUnversionedUpdates", "exports", "AndroidConfig", "Updates", "withUpdates", "IOSConfig", "_default", "default"], "sources": ["../../../src/plugins/unversioned/expo-updates.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  createRunOncePlugin,\n  IOSConfig,\n  withStaticPlugin,\n} from '@expo/config-plugins';\n\n// Local unversioned updates plugin\n\nconst packageName = 'expo-updates';\n\nexport const withExpoUpdates: ConfigPlugin = (config) => {\n  return withStaticPlugin(config, {\n    _isLegacyPlugin: true,\n    // Pass props to the static plugin if it exists.\n    plugin: packageName,\n    // If the static plugin isn't found, use the unversioned one.\n    fallback: createRunOncePlugin((config) => withUnversionedUpdates(config), packageName),\n  });\n};\n\nconst withUnversionedUpdates: ConfigPlugin = (config) => {\n  config = AndroidConfig.Updates.withUpdates(config);\n  config = IOSConfig.Updates.withUpdates(config);\n  return config;\n};\n\nexport default withExpoUpdates;\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA;;AAEA,MAAME,WAAW,GAAG,cAAc;AAE3B,MAAMC,eAA6B,GAAIC,MAAM,IAAK;EACvD,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE;IAC9BE,eAAe,EAAE,IAAI;IACrB;IACAC,MAAM,EAAEL,WAAW;IACnB;IACAM,QAAQ,EAAE,IAAAC,oCAAmB,EAAEL,MAAM,IAAKM,sBAAsB,CAACN,MAAM,CAAC,EAAEF,WAAW;EACvF,CAAC,CAAC;AACJ,CAAC;AAACS,OAAA,CAAAR,eAAA,GAAAA,eAAA;AAEF,MAAMO,sBAAoC,GAAIN,MAAM,IAAK;EACvDA,MAAM,GAAGQ,8BAAa,CAACC,OAAO,CAACC,WAAW,CAACV,MAAM,CAAC;EAClDA,MAAM,GAAGW,0BAAS,CAACF,OAAO,CAACC,WAAW,CAACV,MAAM,CAAC;EAC9C,OAAOA,MAAM;AACf,CAAC;AAAC,IAAAY,QAAA,GAAAL,OAAA,CAAAM,OAAA,GAEad,eAAe", "ignoreList": []}