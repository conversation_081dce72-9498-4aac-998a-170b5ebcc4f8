{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroTerminalReporter.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { Terminal } from 'metro-core';\nimport path from 'path';\n\nimport { logWarning, TerminalReporter } from './TerminalReporter';\nimport {\n  BuildPhase,\n  BundleDetails,\n  BundleProgress,\n  SnippetError,\n  TerminalReportableEvent,\n} from './TerminalReporter.types';\nimport { NODE_STDLIB_MODULES } from './externals';\nimport { learnMore } from '../../../utils/link';\n\nconst MAX_PROGRESS_BAR_CHAR_WIDTH = 16;\nconst DARK_BLOCK_CHAR = '\\u2593';\nconst LIGHT_BLOCK_CHAR = '\\u2591';\n/**\n * Extends the default Metro logger and adds some additional features.\n * Also removes the giant Metro logo from the output.\n */\nexport class MetroTerminalReporter extends TerminalReporter {\n  constructor(\n    public projectRoot: string,\n    terminal: Terminal\n  ) {\n    super(terminal);\n  }\n\n  // Used for testing\n  _getElapsedTime(startTime: bigint): bigint {\n    return process.hrtime.bigint() - startTime;\n  }\n  /**\n   * Extends the bundle progress to include the current platform that we're bundling.\n   *\n   * @returns `iOS path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)`\n   */\n  _getBundleStatusMessage(progress: BundleProgress, phase: BuildPhase): string {\n    const env = getEnvironmentForBuildDetails(progress.bundleDetails);\n    const platform = env || getPlatformTagForBuildDetails(progress.bundleDetails);\n    const inProgress = phase === 'in_progress';\n\n    let localPath: string;\n\n    if (\n      typeof progress.bundleDetails?.customTransformOptions?.dom === 'string' &&\n      progress.bundleDetails.customTransformOptions.dom.includes(path.sep)\n    ) {\n      // Because we use a generated entry file for DOM components, we need to adjust the logging path so it\n      // shows a unique path for each component.\n      // Here, we take the relative import path and remove all the starting slashes.\n      localPath = progress.bundleDetails.customTransformOptions.dom.replace(/^(\\.?\\.[\\\\/])+/, '');\n    } else {\n      const inputFile = progress.bundleDetails.entryFile;\n\n      localPath = path.isAbsolute(inputFile)\n        ? path.relative(this.projectRoot, inputFile)\n        : inputFile;\n    }\n\n    if (!inProgress) {\n      const status = phase === 'done' ? `Bundled ` : `Bundling failed `;\n      const color = phase === 'done' ? chalk.green : chalk.red;\n\n      const startTime = this._bundleTimers.get(progress.bundleDetails.buildID!);\n\n      let time: string = '';\n\n      if (startTime != null) {\n        const elapsed: bigint = this._getElapsedTime(startTime);\n        const micro = Number(elapsed) / 1000;\n        const converted = Number(elapsed) / 1e6;\n        // If the milliseconds are < 0.5 then it will display as 0, so we display in microseconds.\n        if (converted <= 0.5) {\n          const tenthFractionOfMicro = ((micro * 10) / 1000).toFixed(0);\n          // Format as microseconds to nearest tenth\n          time = chalk.cyan.bold(`0.${tenthFractionOfMicro}ms`);\n        } else {\n          time = chalk.dim(converted.toFixed(0) + 'ms');\n        }\n      }\n\n      // iOS Bundled 150ms\n      const plural = progress.totalFileCount === 1 ? '' : 's';\n      return (\n        color(platform + status) +\n        time +\n        chalk.reset.dim(` ${localPath} (${progress.totalFileCount} module${plural})`)\n      );\n    }\n\n    const filledBar = Math.floor(progress.ratio * MAX_PROGRESS_BAR_CHAR_WIDTH);\n\n    const _progress = inProgress\n      ? chalk.green.bgGreen(DARK_BLOCK_CHAR.repeat(filledBar)) +\n        chalk.bgWhite.white(LIGHT_BLOCK_CHAR.repeat(MAX_PROGRESS_BAR_CHAR_WIDTH - filledBar)) +\n        chalk.bold(` ${(100 * progress.ratio).toFixed(1).padStart(4)}% `) +\n        chalk.dim(\n          `(${progress.transformedFileCount\n            .toString()\n            .padStart(progress.totalFileCount.toString().length)}/${progress.totalFileCount})`\n        )\n      : '';\n\n    return (\n      platform +\n      chalk.reset.dim(`${path.dirname(localPath)}${path.sep}`) +\n      chalk.bold(path.basename(localPath)) +\n      ' ' +\n      _progress\n    );\n  }\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void {\n    // Don't print a giant logo...\n    this.terminal.log(chalk.dim('Starting Metro Bundler'));\n  }\n\n  shouldFilterClientLog(event: {\n    type: 'client_log';\n    level: 'trace' | 'info' | 'warn' | 'log' | 'group' | 'groupCollapsed' | 'groupEnd' | 'debug';\n    data: unknown[];\n  }): boolean {\n    return isAppRegistryStartupMessage(event.data);\n  }\n\n  shouldFilterBundleEvent(event: TerminalReportableEvent): boolean {\n    return 'bundleDetails' in event && event.bundleDetails?.bundleType === 'map';\n  }\n\n  /** Print the cache clear message. */\n  transformCacheReset(): void {\n    logWarning(\n      this.terminal,\n      chalk`Bundler cache is empty, rebuilding {dim (this may take a minute)}`\n    );\n  }\n\n  /** One of the first logs that will be printed */\n  dependencyGraphLoading(hasReducedPerformance: boolean): void {\n    // this.terminal.log('Dependency graph is loading...');\n    if (hasReducedPerformance) {\n      // Extends https://github.com/facebook/metro/blob/347b1d7ed87995d7951aaa9fd597c04b06013dac/packages/metro/src/lib/TerminalReporter.js#L283-L290\n      this.terminal.log(\n        chalk.red(\n          [\n            'Metro is operating with reduced performance.',\n            'Please fix the problem above and restart Metro.',\n          ].join('\\n')\n        )\n      );\n    }\n  }\n\n  _logBundlingError(error: SnippetError): void {\n    const moduleResolutionError = formatUsingNodeStandardLibraryError(this.projectRoot, error);\n    const cause = error.cause as undefined | { _expoImportStack?: string };\n    if (moduleResolutionError) {\n      let message = maybeAppendCodeFrame(moduleResolutionError, error.message);\n      if (cause?._expoImportStack) {\n        message += `\\n\\n${cause?._expoImportStack}`;\n      }\n      return this.terminal.log(message);\n    }\n    if (cause?._expoImportStack) {\n      error.message += `\\n\\n${cause._expoImportStack}`;\n    }\n    return super._logBundlingError(error);\n  }\n}\n\n/**\n * Formats an error where the user is attempting to import a module from the Node.js standard library.\n * Exposed for testing.\n *\n * @param error\n * @returns error message or null if not a module resolution error\n */\nexport function formatUsingNodeStandardLibraryError(\n  projectRoot: string,\n  error: SnippetError\n): string | null {\n  if (!error.message) {\n    return null;\n  }\n  const { targetModuleName, originModulePath } = error;\n  if (!targetModuleName || !originModulePath) {\n    return null;\n  }\n  const relativePath = path.relative(projectRoot, originModulePath);\n\n  const DOCS_PAGE_URL =\n    'https://docs.expo.dev/workflow/using-libraries/#using-third-party-libraries';\n\n  if (isNodeStdLibraryModule(targetModuleName)) {\n    if (originModulePath.includes('node_modules')) {\n      return [\n        `The package at \"${chalk.bold(\n          relativePath\n        )}\" attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    } else {\n      return [\n        `You attempted to import the Node standard library module \"${chalk.bold(\n          targetModuleName\n        )}\" from \"${chalk.bold(relativePath)}\".`,\n        `It failed because the native React runtime does not include the Node standard library.`,\n        learnMore(DOCS_PAGE_URL),\n      ].join('\\n');\n    }\n  }\n  return `Unable to resolve \"${targetModuleName}\" from \"${relativePath}\"`;\n}\n\nexport function isNodeStdLibraryModule(moduleName: string): boolean {\n  return /^node:/.test(moduleName) || NODE_STDLIB_MODULES.includes(moduleName);\n}\n\n/** If the code frame can be found then append it to the existing message.  */\nfunction maybeAppendCodeFrame(message: string, rawMessage: string): string {\n  const codeFrame = stripMetroInfo(rawMessage);\n  if (codeFrame) {\n    message += '\\n' + codeFrame;\n  }\n  return message;\n}\n\n/**\n * Remove the Metro cache clearing steps if they exist.\n * In future versions we won't need this.\n * Returns the remaining code frame logs.\n */\nexport function stripMetroInfo(errorMessage: string): string | null {\n  // Newer versions of Metro don't include the list.\n  if (!errorMessage.includes('4. Remove the cache')) {\n    return null;\n  }\n  const lines = errorMessage.split('\\n');\n  const index = lines.findIndex((line) => line.includes('4. Remove the cache'));\n  if (index === -1) {\n    return null;\n  }\n  return lines.slice(index + 1).join('\\n');\n}\n\n/** @returns if the message matches the initial startup log */\nfunction isAppRegistryStartupMessage(body: any[]): boolean {\n  return (\n    body.length === 1 &&\n    (/^Running application \"main\" with appParams:/.test(body[0]) ||\n      /^Running \"main\" with \\{/.test(body[0]))\n  );\n}\n\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getPlatformTagForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  const platform = bundleDetails?.platform ?? null;\n  if (platform) {\n    const formatted = { ios: 'iOS', android: 'Android', web: 'Web' }[platform] || platform;\n    return `${chalk.bold(formatted)} `;\n  }\n\n  return '';\n}\n/** @returns platform specific tag for a `BundleDetails` object */\nfunction getEnvironmentForBuildDetails(bundleDetails?: BundleDetails | null): string {\n  // Expo CLI will pass `customTransformOptions.environment = 'node'` when bundling for the server.\n  const env = bundleDetails?.customTransformOptions?.environment ?? null;\n  if (env === 'node') {\n    return chalk.bold('λ') + ' ';\n  } else if (env === 'react-server') {\n    return chalk.bold(`RSC(${getPlatformTagForBuildDetails(bundleDetails).trim()})`) + ' ';\n  }\n\n  if (\n    bundleDetails?.customTransformOptions?.dom &&\n    typeof bundleDetails?.customTransformOptions?.dom === 'string'\n  ) {\n    return chalk.bold(`DOM`) + ' ';\n  }\n\n  return '';\n}\n"], "names": ["MetroTerminalReporter", "formatUsingNodeStandardLibraryError", "isNodeStdLibraryModule", "stripMetroInfo", "MAX_PROGRESS_BAR_CHAR_WIDTH", "DARK_BLOCK_CHAR", "LIGHT_BLOCK_CHAR", "TerminalReporter", "constructor", "projectRoot", "terminal", "_getElapsedTime", "startTime", "process", "hrtime", "bigint", "_getBundleStatusMessage", "progress", "phase", "env", "getEnvironmentForBuildDetails", "bundleDetails", "platform", "getPlatformTagForBuildDetails", "inProgress", "localPath", "customTransformOptions", "dom", "includes", "path", "sep", "replace", "inputFile", "entryFile", "isAbsolute", "relative", "status", "color", "chalk", "green", "red", "_bundleTimers", "get", "buildID", "time", "elapsed", "micro", "Number", "converted", "tenthFractionOfMicro", "toFixed", "cyan", "bold", "dim", "plural", "totalFileCount", "reset", "<PERSON><PERSON><PERSON>", "Math", "floor", "ratio", "_progress", "bgGreen", "repeat", "bgWhite", "white", "padStart", "transformedFileCount", "toString", "length", "dirname", "basename", "_logInitializing", "port", "hasReducedPerformance", "log", "shouldFilterClientLog", "event", "isAppRegistryStartupMessage", "data", "shouldFilterBundleEvent", "bundleType", "transformCacheReset", "logWarning", "dependencyGraphLoading", "join", "_logBundlingError", "error", "moduleResolutionError", "cause", "message", "maybeAppendCodeFrame", "_expoImportStack", "targetModuleName", "originModulePath", "relativePath", "DOCS_PAGE_URL", "learnMore", "moduleName", "test", "NODE_STDLIB_MODULES", "rawMessage", "codeFrame", "errorMessage", "lines", "split", "index", "findIndex", "line", "slice", "body", "formatted", "ios", "android", "web", "environment", "trim"], "mappings": "AAAA;;;;;;;;;;;IAsBaA,qBAAqB,MAArBA,qBAAqB;IA8JlBC,mCAAmC,MAAnCA,mCAAmC;IAwCnCC,sBAAsB,MAAtBA,sBAAsB;IAkBtBC,cAAc,MAAdA,cAAc;;;8DA9OZ,OAAO;;;;;;;8DAER,MAAM;;;;;;kCAEsB,oBAAoB;2BAQ7B,aAAa;sBACvB,qBAAqB;;;;;;AAE/C,MAAMC,2BAA2B,GAAG,EAAE,AAAC;AACvC,MAAMC,eAAe,GAAG,GAAQ,AAAC;AACjC,MAAMC,gBAAgB,GAAG,GAAQ,AAAC;AAK3B,MAAMN,qBAAqB,SAASO,iBAAgB,iBAAA;IACzDC,YACSC,WAAmB,EAC1BC,QAAkB,CAClB;QACA,KAAK,CAACA,QAAQ,CAAC,CAAC;QAHTD,mBAAAA,WAAmB,CAAA;IAI5B;IAEA,mBAAmB;IACnBE,eAAe,CAACC,SAAiB,EAAU;QACzC,OAAOC,OAAO,CAACC,MAAM,CAACC,MAAM,EAAE,GAAGH,SAAS,CAAC;IAC7C;IACA;;;;GAIC,GACDI,uBAAuB,CAACC,QAAwB,EAAEC,KAAiB,EAAU;YAQlED,GAAsB;QAP/B,MAAME,GAAG,GAAGC,6BAA6B,CAACH,QAAQ,CAACI,aAAa,CAAC,AAAC;QAClE,MAAMC,QAAQ,GAAGH,GAAG,IAAII,6BAA6B,CAACN,QAAQ,CAACI,aAAa,CAAC,AAAC;QAC9E,MAAMG,UAAU,GAAGN,KAAK,KAAK,aAAa,AAAC;QAE3C,IAAIO,SAAS,AAAQ,AAAC;QAEtB,IACE,OAAOR,CAAAA,CAAAA,GAAsB,GAAtBA,QAAQ,CAACI,aAAa,SAAwB,GAA9CJ,KAAAA,CAA8C,GAA9CA,QAAAA,GAAsB,CAAES,sBAAsB,SAAA,GAA9CT,KAAAA,CAA8C,QAAEU,GAAG,AAAL,CAAA,AAAK,KAAK,QAAQ,IACvEV,QAAQ,CAACI,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAACC,QAAQ,CAACC,KAAI,EAAA,QAAA,CAACC,GAAG,CAAC,EACpE;YACA,qGAAqG;YACrG,0CAA0C;YAC1C,8EAA8E;YAC9EL,SAAS,GAAGR,QAAQ,CAACI,aAAa,CAACK,sBAAsB,CAACC,GAAG,CAACI,OAAO,mBAAmB,EAAE,CAAC,CAAC;QAC9F,OAAO;YACL,MAAMC,SAAS,GAAGf,QAAQ,CAACI,aAAa,CAACY,SAAS,AAAC;YAEnDR,SAAS,GAAGI,KAAI,EAAA,QAAA,CAACK,UAAU,CAACF,SAAS,CAAC,GAClCH,KAAI,EAAA,QAAA,CAACM,QAAQ,CAAC,IAAI,CAAC1B,WAAW,EAAEuB,SAAS,CAAC,GAC1CA,SAAS,CAAC;QAChB,CAAC;QAED,IAAI,CAACR,UAAU,EAAE;YACf,MAAMY,MAAM,GAAGlB,KAAK,KAAK,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,AAAC;YAClE,MAAMmB,KAAK,GAAGnB,KAAK,KAAK,MAAM,GAAGoB,MAAK,EAAA,QAAA,CAACC,KAAK,GAAGD,MAAK,EAAA,QAAA,CAACE,GAAG,AAAC;YAEzD,MAAM5B,SAAS,GAAG,IAAI,CAAC6B,aAAa,CAACC,GAAG,CAACzB,QAAQ,CAACI,aAAa,CAACsB,OAAO,CAAE,AAAC;YAE1E,IAAIC,IAAI,GAAW,EAAE,AAAC;YAEtB,IAAIhC,SAAS,IAAI,IAAI,EAAE;gBACrB,MAAMiC,OAAO,GAAW,IAAI,CAAClC,eAAe,CAACC,SAAS,CAAC,AAAC;gBACxD,MAAMkC,KAAK,GAAGC,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI,AAAC;gBACrC,MAAMG,SAAS,GAAGD,MAAM,CAACF,OAAO,CAAC,GAAG,GAAG,AAAC;gBACxC,0FAA0F;gBAC1F,IAAIG,SAAS,IAAI,GAAG,EAAE;oBACpB,MAAMC,oBAAoB,GAAG,CAAC,AAACH,KAAK,GAAG,EAAE,GAAI,IAAI,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,AAAC;oBAC9D,0CAA0C;oBAC1CN,IAAI,GAAGN,MAAK,EAAA,QAAA,CAACa,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,EAAEH,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxD,OAAO;oBACLL,IAAI,GAAGN,MAAK,EAAA,QAAA,CAACe,GAAG,CAACL,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAED,oBAAoB;YACpB,MAAMI,MAAM,GAAGrC,QAAQ,CAACsC,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,AAAC;YACxD,OACElB,KAAK,CAACf,QAAQ,GAAGc,MAAM,CAAC,GACxBQ,IAAI,GACJN,MAAK,EAAA,QAAA,CAACkB,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC,EAAE5B,SAAS,CAAC,EAAE,EAAER,QAAQ,CAACsC,cAAc,CAAC,OAAO,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC,CAC7E;QACJ,CAAC;QAED,MAAMG,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC1C,QAAQ,CAAC2C,KAAK,GAAGxD,2BAA2B,CAAC,AAAC;QAE3E,MAAMyD,SAAS,GAAGrC,UAAU,GACxBc,MAAK,EAAA,QAAA,CAACC,KAAK,CAACuB,OAAO,CAACzD,eAAe,CAAC0D,MAAM,CAACN,SAAS,CAAC,CAAC,GACtDnB,MAAK,EAAA,QAAA,CAAC0B,OAAO,CAACC,KAAK,CAAC3D,gBAAgB,CAACyD,MAAM,CAAC3D,2BAA2B,GAAGqD,SAAS,CAAC,CAAC,GACrFnB,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAGnC,QAAQ,CAAC2C,KAAK,CAAC,CAACV,OAAO,CAAC,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GACjE5B,MAAK,EAAA,QAAA,CAACe,GAAG,CACP,CAAC,CAAC,EAAEpC,QAAQ,CAACkD,oBAAoB,CAC9BC,QAAQ,EAAE,CACVF,QAAQ,CAACjD,QAAQ,CAACsC,cAAc,CAACa,QAAQ,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpD,QAAQ,CAACsC,cAAc,CAAC,CAAC,CAAC,CACrF,GACD,EAAE,AAAC;QAEP,OACEjC,QAAQ,GACRgB,MAAK,EAAA,QAAA,CAACkB,KAAK,CAACH,GAAG,CAAC,CAAC,EAAExB,KAAI,EAAA,QAAA,CAACyC,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAEI,KAAI,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,CAAC,GACxDQ,MAAK,EAAA,QAAA,CAACc,IAAI,CAACvB,KAAI,EAAA,QAAA,CAAC0C,QAAQ,CAAC9C,SAAS,CAAC,CAAC,GACpC,GAAG,GACHoC,SAAS,CACT;IACJ;IAEAW,gBAAgB,CAACC,IAAY,EAAEC,qBAA8B,EAAQ;QACnE,8BAA8B;QAC9B,IAAI,CAAChE,QAAQ,CAACiE,GAAG,CAACrC,MAAK,EAAA,QAAA,CAACe,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC;IACzD;IAEAuB,qBAAqB,CAACC,KAIrB,EAAW;QACV,OAAOC,2BAA2B,CAACD,KAAK,CAACE,IAAI,CAAC,CAAC;IACjD;IAEAC,uBAAuB,CAACH,KAA8B,EAAW;YAC5BA,GAAmB;QAAtD,OAAO,eAAe,IAAIA,KAAK,IAAIA,CAAAA,CAAAA,GAAmB,GAAnBA,KAAK,CAACxD,aAAa,SAAY,GAA/BwD,KAAAA,CAA+B,GAA/BA,GAAmB,CAAEI,UAAU,CAAA,KAAK,KAAK,CAAC;IAC/E;IAEA,mCAAmC,GACnCC,mBAAmB,GAAS;QAC1BC,IAAAA,iBAAU,WAAA,EACR,IAAI,CAACzE,QAAQ,EACb4B,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,iEAAiE,CAAC,CACzE,CAAC;IACJ;IAEA,+CAA+C,GAC/C8C,sBAAsB,CAACV,qBAA8B,EAAQ;QAC3D,uDAAuD;QACvD,IAAIA,qBAAqB,EAAE;YACzB,+IAA+I;YAC/I,IAAI,CAAChE,QAAQ,CAACiE,GAAG,CACfrC,MAAK,EAAA,QAAA,CAACE,GAAG,CACP;gBACE,8CAA8C;gBAC9C,iDAAiD;aAClD,CAAC6C,IAAI,CAAC,IAAI,CAAC,CACb,CACF,CAAC;QACJ,CAAC;IACH;IAEAC,iBAAiB,CAACC,KAAmB,EAAQ;QAC3C,MAAMC,qBAAqB,GAAGvF,mCAAmC,CAAC,IAAI,CAACQ,WAAW,EAAE8E,KAAK,CAAC,AAAC;QAC3F,MAAME,KAAK,GAAGF,KAAK,CAACE,KAAK,AAA6C,AAAC;QACvE,IAAID,qBAAqB,EAAE;YACzB,IAAIE,OAAO,GAAGC,oBAAoB,CAACH,qBAAqB,EAAED,KAAK,CAACG,OAAO,CAAC,AAAC;YACzE,IAAID,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,EAAE;gBAC3BF,OAAO,IAAI,CAAC,IAAI,EAAED,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,CAAC,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO,IAAI,CAAClF,QAAQ,CAACiE,GAAG,CAACe,OAAO,CAAC,CAAC;QACpC,CAAC;QACD,IAAID,KAAK,QAAkB,GAAvBA,KAAAA,CAAuB,GAAvBA,KAAK,CAAEG,gBAAgB,EAAE;YAC3BL,KAAK,CAACG,OAAO,IAAI,CAAC,IAAI,EAAED,KAAK,CAACG,gBAAgB,CAAC,CAAC,CAAC;QACnD,CAAC;QACD,OAAO,KAAK,CAACN,iBAAiB,CAACC,KAAK,CAAC,CAAC;IACxC;CACD;AASM,SAAStF,mCAAmC,CACjDQ,WAAmB,EACnB8E,KAAmB,EACJ;IACf,IAAI,CAACA,KAAK,CAACG,OAAO,EAAE;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,EAAEG,gBAAgB,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAGP,KAAK,AAAC;IACrD,IAAI,CAACM,gBAAgB,IAAI,CAACC,gBAAgB,EAAE;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAMC,YAAY,GAAGlE,KAAI,EAAA,QAAA,CAACM,QAAQ,CAAC1B,WAAW,EAAEqF,gBAAgB,CAAC,AAAC;IAElE,MAAME,aAAa,GACjB,6EAA6E,AAAC;IAEhF,IAAI9F,sBAAsB,CAAC2F,gBAAgB,CAAC,EAAE;QAC5C,IAAIC,gBAAgB,CAAClE,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC7C,OAAO;gBACL,CAAC,gBAAgB,EAAEU,MAAK,EAAA,QAAA,CAACc,IAAI,CAC3B2C,YAAY,CACb,CAAC,wDAAwD,EAAEzD,MAAK,EAAA,QAAA,CAACc,IAAI,CACpEyC,gBAAgB,CACjB,CAAC,EAAE,CAAC;gBACL,CAAC,sFAAsF,CAAC;gBACxFI,IAAAA,KAAS,UAAA,EAACD,aAAa,CAAC;aACzB,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC;QACf,OAAO;YACL,OAAO;gBACL,CAAC,0DAA0D,EAAE/C,MAAK,EAAA,QAAA,CAACc,IAAI,CACrEyC,gBAAgB,CACjB,CAAC,QAAQ,EAAEvD,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC2C,YAAY,CAAC,CAAC,EAAE,CAAC;gBACxC,CAAC,sFAAsF,CAAC;gBACxFE,IAAAA,KAAS,UAAA,EAACD,aAAa,CAAC;aACzB,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO,CAAC,mBAAmB,EAAEQ,gBAAgB,CAAC,QAAQ,EAAEE,YAAY,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAEM,SAAS7F,sBAAsB,CAACgG,UAAkB,EAAW;IAClE,OAAO,SAASC,IAAI,CAACD,UAAU,CAAC,IAAIE,UAAmB,oBAAA,CAACxE,QAAQ,CAACsE,UAAU,CAAC,CAAC;AAC/E,CAAC;AAED,4EAA4E,GAC5E,SAASP,oBAAoB,CAACD,OAAe,EAAEW,UAAkB,EAAU;IACzE,MAAMC,SAAS,GAAGnG,cAAc,CAACkG,UAAU,CAAC,AAAC;IAC7C,IAAIC,SAAS,EAAE;QACbZ,OAAO,IAAI,IAAI,GAAGY,SAAS,CAAC;IAC9B,CAAC;IACD,OAAOZ,OAAO,CAAC;AACjB,CAAC;AAOM,SAASvF,cAAc,CAACoG,YAAoB,EAAiB;IAClE,kDAAkD;IAClD,IAAI,CAACA,YAAY,CAAC3E,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM4E,KAAK,GAAGD,YAAY,CAACE,KAAK,CAAC,IAAI,CAAC,AAAC;IACvC,MAAMC,KAAK,GAAGF,KAAK,CAACG,SAAS,CAAC,CAACC,IAAI,GAAKA,IAAI,CAAChF,QAAQ,CAAC,qBAAqB,CAAC,CAAC,AAAC;IAC9E,IAAI8E,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAOF,KAAK,CAACK,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACrB,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAED,4DAA4D,GAC5D,SAASP,2BAA2B,CAACgC,IAAW,EAAW;IACzD,OACEA,IAAI,CAACzC,MAAM,KAAK,CAAC,IACjB,CAAC,8CAA8C8B,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,IAC1D,0BAA0BX,IAAI,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1C;AACJ,CAAC;AAED,gEAAgE,GAChE,SAASvF,6BAA6B,CAACF,aAAoC,EAAU;IACnF,MAAMC,QAAQ,GAAGD,CAAAA,aAAa,QAAU,GAAvBA,KAAAA,CAAuB,GAAvBA,aAAa,CAAEC,QAAQ,CAAA,IAAI,IAAI,AAAC;IACjD,IAAIA,QAAQ,EAAE;QACZ,MAAMyF,SAAS,GAAG;YAAEC,GAAG,EAAE,KAAK;YAAEC,OAAO,EAAE,SAAS;YAAEC,GAAG,EAAE,KAAK;SAAE,CAAC5F,QAAQ,CAAC,IAAIA,QAAQ,AAAC;QACvF,OAAO,CAAC,EAAEgB,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC2D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AACD,gEAAgE,GAChE,SAAS3F,6BAA6B,CAACC,aAAoC,EAAU;QAEvEA,GAAqC,EAQ/CA,IAAqC,EAC9BA,IAAqC;IAV9C,iGAAiG;IACjG,MAAMF,GAAG,GAAGE,CAAAA,aAAa,QAAwB,GAArCA,KAAAA,CAAqC,GAArCA,CAAAA,GAAqC,GAArCA,aAAa,CAAEK,sBAAsB,SAAA,GAArCL,KAAAA,CAAqC,GAArCA,GAAqC,CAAE8F,WAAW,AAAb,CAAA,IAAiB,IAAI,AAAC;IACvE,IAAIhG,GAAG,KAAK,MAAM,EAAE;QAClB,OAAOmB,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IAC/B,OAAO,IAAIjC,GAAG,KAAK,cAAc,EAAE;QACjC,OAAOmB,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,CAAC,IAAI,EAAE7B,6BAA6B,CAACF,aAAa,CAAC,CAAC+F,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IACzF,CAAC;IAED,IACE/F,CAAAA,aAAa,QAAwB,GAArCA,KAAAA,CAAqC,GAArCA,CAAAA,IAAqC,GAArCA,aAAa,CAAEK,sBAAsB,SAAA,GAArCL,KAAAA,CAAqC,GAArCA,IAAqC,CAAEM,GAAG,AAAL,CAAA,IACrC,OAAON,CAAAA,aAAa,QAAwB,GAArCA,KAAAA,CAAqC,GAArCA,CAAAA,IAAqC,GAArCA,aAAa,CAAEK,sBAAsB,SAAA,GAArCL,KAAAA,CAAqC,GAArCA,IAAqC,CAAEM,GAAG,AAAL,CAAA,AAAK,KAAK,QAAQ,EAC9D;QACA,OAAOW,MAAK,EAAA,QAAA,CAACc,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IACjC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC"}