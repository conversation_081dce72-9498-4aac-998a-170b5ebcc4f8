{"version": 3, "sources": ["../../../../src/run/ios/runIosAsync.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as Log from '../../log';\nimport { AppleAppIdResolver } from '../../start/platforms/ios/AppleAppIdResolver';\nimport { maybePromptToSyncPodsAsync } from '../../utils/cocoapods';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { ensurePortAvailabilityAsync } from '../../utils/port';\nimport { profile } from '../../utils/profile';\nimport { getSchemesForIosAsync } from '../../utils/scheme';\nimport { ensureNativeProjectAsync } from '../ensureNativeProject';\nimport { logProjectLogsLocation } from '../hints';\nimport { startBundlerAsync } from '../startBundler';\nimport * as XcodeBuild from './XcodeBuild';\nimport { Options } from './XcodeBuild.types';\nimport { getLaunchInfoForBinaryAsync, launchAppAsync } from './launchApp';\nimport { resolveOptionsAsync } from './options/resolveOptions';\nimport { getValidBinaryPathAsync } from './validateExternalBinary';\nimport { exportEagerAsync } from '../../export/embed/exportEager';\nimport { getContainerPathAsync, simctlAsync } from '../../start/platforms/ios/simctl';\nimport { CommandError } from '../../utils/errors';\n\nconst debug = require('debug')('expo:run:ios');\n\nexport async function runIosAsync(projectRoot: string, options: Options) {\n  setNodeEnv(options.configuration === 'Release' ? 'production' : 'development');\n  require('@expo/env').load(projectRoot);\n\n  assertPlatform();\n\n  const install = !!options.install;\n\n  if ((await ensureNativeProjectAsync(projectRoot, { platform: 'ios', install })) && install) {\n    await maybePromptToSyncPodsAsync(projectRoot);\n  }\n\n  // Resolve the CLI arguments into useable options.\n  const props = await profile(resolveOptionsAsync)(projectRoot, options);\n\n  if (options.rebundle) {\n    Log.warn(`The --unstable-rebundle flag is experimental and may not work as expected.`);\n    // Get the existing binary path to re-bundle the app.\n\n    let binaryPath: string;\n    if (!options.binary) {\n      if (!props.isSimulator) {\n        throw new Error('Re-bundling on physical devices requires the --binary flag.');\n      }\n      const appId = await new AppleAppIdResolver(projectRoot).getAppIdAsync();\n      const possibleBinaryPath = await getContainerPathAsync(props.device, {\n        appId,\n      });\n      if (!possibleBinaryPath) {\n        throw new CommandError(\n          `Cannot rebundle because no --binary was provided and no existing binary was found on the device for ID: ${appId}.`\n        );\n      }\n      binaryPath = possibleBinaryPath;\n      Log.log('Re-using existing binary path:', binaryPath);\n      // Set the binary path to the existing binary path.\n      options.binary = binaryPath;\n    }\n\n    Log.log('Rebundling the Expo config file');\n    // Re-bundle the config file the same way the app was originally bundled.\n    await spawnAsync('node', [\n      path.join(require.resolve('expo-constants/package.json'), '../scripts/getAppConfig.js'),\n      projectRoot,\n      path.join(options.binary, 'EXConstants.bundle'),\n    ]);\n    // Re-bundle the app.\n\n    const possibleBundleOutput = path.join(options.binary, 'main.jsbundle');\n\n    if (fs.existsSync(possibleBundleOutput)) {\n      Log.log('Rebundling the app...');\n      await exportEagerAsync(projectRoot, {\n        resetCache: false,\n        dev: false,\n        platform: 'ios',\n        assetsDest: path.join(options.binary, 'assets'),\n        bundleOutput: possibleBundleOutput,\n      });\n    } else {\n      Log.warn('Bundle output not found at expected location:', possibleBundleOutput);\n    }\n  }\n\n  let binaryPath: string;\n  if (options.binary) {\n    binaryPath = await getValidBinaryPathAsync(options.binary, props);\n    Log.log('Using custom binary path:', binaryPath);\n  } else {\n    let eagerBundleOptions: string | undefined;\n\n    if (options.configuration === 'Release') {\n      eagerBundleOptions = JSON.stringify(\n        await exportEagerAsync(projectRoot, {\n          dev: false,\n          platform: 'ios',\n        })\n      );\n    }\n\n    // Spawn the `xcodebuild` process to create the app binary.\n    const buildOutput = await XcodeBuild.buildAsync({\n      ...props,\n      eagerBundleOptions,\n    });\n\n    // Find the path to the built app binary, this will be used to install the binary\n    // on a device.\n    binaryPath = await profile(XcodeBuild.getAppBinaryPath)(buildOutput);\n  }\n  debug('Binary path:', binaryPath);\n\n  // Ensure the port hasn't become busy during the build.\n  if (props.shouldStartBundler && !(await ensurePortAvailabilityAsync(projectRoot, props))) {\n    props.shouldStartBundler = false;\n  }\n\n  const launchInfo = await getLaunchInfoForBinaryAsync(binaryPath);\n  const isCustomBinary = !!options.binary;\n\n  // Always close the app before launching on a simulator. Otherwise certain cached resources like the splashscreen will not be available.\n  if (props.isSimulator) {\n    try {\n      await simctlAsync(['terminate', props.device.udid, launchInfo.bundleId]);\n    } catch (error) {\n      // If we failed it's likely that the app was not running to begin with and we will get an `invalid device` error\n      debug('Failed to terminate app (possibly because it was not running):', error);\n    }\n  }\n\n  // Start the dev server which creates all of the required info for\n  // launching the app on a simulator.\n  const manager = await startBundlerAsync(projectRoot, {\n    port: props.port,\n    headless: !props.shouldStartBundler,\n    // If a scheme is specified then use that instead of the package name.\n\n    scheme: isCustomBinary\n      ? // If launching a custom binary, use the schemes in the Info.plist.\n        launchInfo.schemes[0]\n      : // If a scheme is specified then use that instead of the package name.\n        (await getSchemesForIosAsync(projectRoot))?.[0],\n  });\n\n  // Install and launch the app binary on a device.\n  await launchAppAsync(\n    binaryPath,\n    manager,\n    {\n      isSimulator: props.isSimulator,\n      device: props.device,\n      shouldStartBundler: props.shouldStartBundler,\n    },\n    launchInfo.bundleId\n  );\n\n  // Log the location of the JS logs for the device.\n  if (props.shouldStartBundler) {\n    logProjectLogsLocation();\n  } else {\n    await manager.stopAsync();\n  }\n}\n\nfunction assertPlatform() {\n  if (process.platform !== 'darwin') {\n    Log.exit(\n      chalk`iOS apps can only be built on macOS devices. Use {cyan eas build -p ios} to build in the cloud.`\n    );\n  }\n}\n"], "names": ["runIosAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "configuration", "load", "assertPlatform", "install", "ensureNativeProjectAsync", "platform", "maybePromptToSyncPodsAsync", "props", "profile", "resolveOptionsAsync", "rebundle", "Log", "warn", "binaryPath", "binary", "isSimulator", "Error", "appId", "AppleAppIdResolver", "getAppIdAsync", "possibleBinary<PERSON>ath", "getContainerPathAsync", "device", "CommandError", "log", "spawnAsync", "path", "join", "resolve", "possibleBundleOutput", "fs", "existsSync", "exportEagerAsync", "resetCache", "dev", "assetsDest", "bundleOutput", "getValidBinaryPathAsync", "eagerBundleOptions", "JSON", "stringify", "buildOutput", "XcodeBuild", "buildAsync", "getAppBinaryPath", "shouldStartBundler", "ensurePortAvailabilityAsync", "launchInfo", "getLaunchInfoForBinaryAsync", "isCustomBinary", "simctlAsync", "udid", "bundleId", "error", "manager", "startBundlerAsync", "port", "headless", "scheme", "schemes", "getSchemesForIosAsync", "launchAppAsync", "logProjectLogsLocation", "stopAsync", "process", "exit", "chalk"], "mappings": "AAAA;;;;+BA0BsBA,aAAW;;aAAXA,WAAW;;;8DA1BV,mBAAmB;;;;;;;8DACxB,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DACF,MAAM;;;;;;2DAEF,WAAW;oCACG,8CAA8C;2BACtC,uBAAuB;yBACvC,qBAAqB;sBACJ,kBAAkB;yBACtC,qBAAqB;wBACP,oBAAoB;qCACjB,wBAAwB;uBAC1B,UAAU;8BACf,iBAAiB;kEACvB,cAAc;2BAEkB,aAAa;gCACrC,0BAA0B;wCACtB,0BAA0B;6BACjC,gCAAgC;wBACd,kCAAkC;wBACxD,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,AAAC;AAExC,eAAeF,WAAW,CAACG,WAAmB,EAAEC,OAAgB,EAAE;QAyHjE,GAA0C;IAxHhDC,IAAAA,QAAU,WAAA,EAACD,OAAO,CAACE,aAAa,KAAK,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC,CAAC;IAC/EJ,OAAO,CAAC,WAAW,CAAC,CAACK,IAAI,CAACJ,WAAW,CAAC,CAAC;IAEvCK,cAAc,EAAE,CAAC;IAEjB,MAAMC,OAAO,GAAG,CAAC,CAACL,OAAO,CAACK,OAAO,AAAC;IAElC,IAAI,AAAC,MAAMC,IAAAA,oBAAwB,yBAAA,EAACP,WAAW,EAAE;QAAEQ,QAAQ,EAAE,KAAK;QAAEF,OAAO;KAAE,CAAC,IAAKA,OAAO,EAAE;QAC1F,MAAMG,IAAAA,UAA0B,2BAAA,EAACT,WAAW,CAAC,CAAC;IAChD,CAAC;IAED,kDAAkD;IAClD,MAAMU,KAAK,GAAG,MAAMC,IAAAA,QAAO,QAAA,EAACC,eAAmB,oBAAA,CAAC,CAACZ,WAAW,EAAEC,OAAO,CAAC,AAAC;IAEvE,IAAIA,OAAO,CAACY,QAAQ,EAAE;QACpBC,IAAG,CAACC,IAAI,CAAC,CAAC,0EAA0E,CAAC,CAAC,CAAC;QACvF,qDAAqD;QAErD,IAAIC,UAAU,AAAQ,AAAC;QACvB,IAAI,CAACf,OAAO,CAACgB,MAAM,EAAE;YACnB,IAAI,CAACP,KAAK,CAACQ,WAAW,EAAE;gBACtB,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;YACD,MAAMC,KAAK,GAAG,MAAM,IAAIC,mBAAkB,mBAAA,CAACrB,WAAW,CAAC,CAACsB,aAAa,EAAE,AAAC;YACxE,MAAMC,kBAAkB,GAAG,MAAMC,IAAAA,OAAqB,sBAAA,EAACd,KAAK,CAACe,MAAM,EAAE;gBACnEL,KAAK;aACN,CAAC,AAAC;YACH,IAAI,CAACG,kBAAkB,EAAE;gBACvB,MAAM,IAAIG,OAAY,aAAA,CACpB,CAAC,wGAAwG,EAAEN,KAAK,CAAC,CAAC,CAAC,CACpH,CAAC;YACJ,CAAC;YACDJ,UAAU,GAAGO,kBAAkB,CAAC;YAChCT,IAAG,CAACa,GAAG,CAAC,gCAAgC,EAAEX,UAAU,CAAC,CAAC;YACtD,mDAAmD;YACnDf,OAAO,CAACgB,MAAM,GAAGD,UAAU,CAAC;QAC9B,CAAC;QAEDF,IAAG,CAACa,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC3C,yEAAyE;QACzE,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,MAAM,EAAE;YACvBC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC/B,OAAO,CAACgC,OAAO,CAAC,6BAA6B,CAAC,EAAE,4BAA4B,CAAC;YACvF/B,WAAW;YACX6B,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC7B,OAAO,CAACgB,MAAM,EAAE,oBAAoB,CAAC;SAChD,CAAC,CAAC;QACH,qBAAqB;QAErB,MAAMe,oBAAoB,GAAGH,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC7B,OAAO,CAACgB,MAAM,EAAE,eAAe,CAAC,AAAC;QAExE,IAAIgB,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,oBAAoB,CAAC,EAAE;YACvClB,IAAG,CAACa,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACjC,MAAMQ,IAAAA,YAAgB,iBAAA,EAACnC,WAAW,EAAE;gBAClCoC,UAAU,EAAE,KAAK;gBACjBC,GAAG,EAAE,KAAK;gBACV7B,QAAQ,EAAE,KAAK;gBACf8B,UAAU,EAAET,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC7B,OAAO,CAACgB,MAAM,EAAE,QAAQ,CAAC;gBAC/CsB,YAAY,EAAEP,oBAAoB;aACnC,CAAC,CAAC;QACL,OAAO;YACLlB,IAAG,CAACC,IAAI,CAAC,+CAA+C,EAAEiB,oBAAoB,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED,IAAIhB,WAAU,AAAQ,AAAC;IACvB,IAAIf,OAAO,CAACgB,MAAM,EAAE;QAClBD,WAAU,GAAG,MAAMwB,IAAAA,uBAAuB,wBAAA,EAACvC,OAAO,CAACgB,MAAM,EAAEP,KAAK,CAAC,CAAC;QAClEI,IAAG,CAACa,GAAG,CAAC,2BAA2B,EAAEX,WAAU,CAAC,CAAC;IACnD,OAAO;QACL,IAAIyB,kBAAkB,AAAoB,AAAC;QAE3C,IAAIxC,OAAO,CAACE,aAAa,KAAK,SAAS,EAAE;YACvCsC,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CACjC,MAAMR,IAAAA,YAAgB,iBAAA,EAACnC,WAAW,EAAE;gBAClCqC,GAAG,EAAE,KAAK;gBACV7B,QAAQ,EAAE,KAAK;aAChB,CAAC,CACH,CAAC;QACJ,CAAC;QAED,2DAA2D;QAC3D,MAAMoC,WAAW,GAAG,MAAMC,WAAU,CAACC,UAAU,CAAC;YAC9C,GAAGpC,KAAK;YACR+B,kBAAkB;SACnB,CAAC,AAAC;QAEH,iFAAiF;QACjF,eAAe;QACfzB,WAAU,GAAG,MAAML,IAAAA,QAAO,QAAA,EAACkC,WAAU,CAACE,gBAAgB,CAAC,CAACH,WAAW,CAAC,CAAC;IACvE,CAAC;IACD9C,KAAK,CAAC,cAAc,EAAEkB,WAAU,CAAC,CAAC;IAElC,uDAAuD;IACvD,IAAIN,KAAK,CAACsC,kBAAkB,IAAI,CAAE,MAAMC,IAAAA,KAA2B,4BAAA,EAACjD,WAAW,EAAEU,KAAK,CAAC,AAAC,EAAE;QACxFA,KAAK,CAACsC,kBAAkB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,MAAME,UAAU,GAAG,MAAMC,IAAAA,UAA2B,4BAAA,EAACnC,WAAU,CAAC,AAAC;IACjE,MAAMoC,cAAc,GAAG,CAAC,CAACnD,OAAO,CAACgB,MAAM,AAAC;IAExC,wIAAwI;IACxI,IAAIP,KAAK,CAACQ,WAAW,EAAE;QACrB,IAAI;YACF,MAAMmC,IAAAA,OAAW,YAAA,EAAC;gBAAC,WAAW;gBAAE3C,KAAK,CAACe,MAAM,CAAC6B,IAAI;gBAAEJ,UAAU,CAACK,QAAQ;aAAC,CAAC,CAAC;QAC3E,EAAE,OAAOC,KAAK,EAAE;YACd,gHAAgH;YAChH1D,KAAK,CAAC,gEAAgE,EAAE0D,KAAK,CAAC,CAAC;QACjF,CAAC;IACH,CAAC;IAED,kEAAkE;IAClE,oCAAoC;IACpC,MAAMC,OAAO,GAAG,MAAMC,IAAAA,aAAiB,kBAAA,EAAC1D,WAAW,EAAE;QACnD2D,IAAI,EAAEjD,KAAK,CAACiD,IAAI;QAChBC,QAAQ,EAAE,CAAClD,KAAK,CAACsC,kBAAkB;QACnC,sEAAsE;QAEtEa,MAAM,EAAET,cAAc,GAElBF,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,GAErB,CAAA,GAA0C,GAAzC,MAAMC,IAAAA,OAAqB,sBAAA,EAAC/D,WAAW,CAAC,SAAM,GAA/C,KAAA,CAA+C,GAA/C,GAA0C,AAAE,CAAC,CAAC,CAAC;KACpD,CAAC,AAAC;IAEH,iDAAiD;IACjD,MAAMgE,IAAAA,UAAc,eAAA,EAClBhD,WAAU,EACVyC,OAAO,EACP;QACEvC,WAAW,EAAER,KAAK,CAACQ,WAAW;QAC9BO,MAAM,EAAEf,KAAK,CAACe,MAAM;QACpBuB,kBAAkB,EAAEtC,KAAK,CAACsC,kBAAkB;KAC7C,EACDE,UAAU,CAACK,QAAQ,CACpB,CAAC;IAEF,kDAAkD;IAClD,IAAI7C,KAAK,CAACsC,kBAAkB,EAAE;QAC5BiB,IAAAA,MAAsB,uBAAA,GAAE,CAAC;IAC3B,OAAO;QACL,MAAMR,OAAO,CAACS,SAAS,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED,SAAS7D,cAAc,GAAG;IACxB,IAAI8D,OAAO,CAAC3D,QAAQ,KAAK,QAAQ,EAAE;QACjCM,IAAG,CAACsD,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,+FAA+F,CAAC,CACvG,CAAC;IACJ,CAAC;AACH,CAAC"}