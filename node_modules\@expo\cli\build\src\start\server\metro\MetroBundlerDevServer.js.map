{"version": 3, "sources": ["../../../../../src/start/server/metro/MetroBundlerDevServer.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getConfig } from '@expo/config';\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport * as runtimeEnv from '@expo/env';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport { DeltaResult, TransformInputOptions } from 'metro';\nimport baseJSBundle from 'metro/src/DeltaBundler/Serializers/baseJSBundle';\nimport {\n  sourceMapGeneratorNonBlocking,\n  type SourceMapGeneratorOptions,\n} from 'metro/src/DeltaBundler/Serializers/sourceMapGenerator';\nimport type MetroHmrServer from 'metro/src/HmrServer';\nimport type { Client as MetroHmrClient } from 'metro/src/HmrServer';\nimport { GraphRevision } from 'metro/src/IncrementalBundler';\nimport bundleToString from 'metro/src/lib/bundleToString';\nimport getGraphId from 'metro/src/lib/getGraphId';\nimport { TransformProfile } from 'metro-babel-transformer';\nimport type { CustomResolverOptions } from 'metro-resolver/src/types';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport {\n  createServerComponentsMiddleware,\n  fileURLToFilePath,\n} from './createServerComponentsMiddleware';\nimport { createRouteHandlerMiddleware } from './createServerRouteMiddleware';\nimport { ExpoRouterServerManifestV1, fetchManifest } from './fetchRouterManifest';\nimport { instantiateMetroAsync } from './instantiateMetro';\nimport { getErrorOverlayHtmlAsync, IS_METRO_BUNDLE_ERROR_SYMBOL } from './metroErrorInterface';\nimport { assertMetroPrivateServer, MetroPrivateServer } from './metroPrivateServer';\nimport { metroWatchTypeScriptFiles } from './metroWatchTypeScriptFiles';\nimport {\n  getRouterDirectoryModuleIdWithManifest,\n  hasWarnedAboutApiRoutes,\n  isApiRouteConvention,\n  warnInvalidWebOutput,\n} from './router';\nimport { serializeHtmlWithAssets } from './serializeHtml';\nimport { observeAnyFileChanges, observeFileChanges } from './waitForMetroToObserveTypeScriptFile';\nimport { BundleAssetWithFileHashes, ExportAssetMap } from '../../../export/saveAssets';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getFreePortAsync } from '../../../utils/port';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\nimport {\n  cachedSourceMaps,\n  evalMetroAndWrapFunctions,\n  evalMetroNoHandling,\n} from '../getStaticRenderFunctions';\nimport { ContextModuleSourceMapsMiddleware } from '../middleware/ContextModuleSourceMapsMiddleware';\nimport { CreateFileMiddleware } from '../middleware/CreateFileMiddleware';\nimport { DevToolsPluginMiddleware } from '../middleware/DevToolsPluginMiddleware';\nimport { createDomComponentsMiddleware } from '../middleware/DomComponentsMiddleware';\nimport { FaviconMiddleware } from '../middleware/FaviconMiddleware';\nimport { HistoryFallbackMiddleware } from '../middleware/HistoryFallbackMiddleware';\nimport { InterstitialPageMiddleware } from '../middleware/InterstitialPageMiddleware';\nimport { resolveMainModuleName } from '../middleware/ManifestMiddleware';\nimport { ReactDevToolsPageMiddleware } from '../middleware/ReactDevToolsPageMiddleware';\nimport { RuntimeRedirectMiddleware } from '../middleware/RuntimeRedirectMiddleware';\nimport { ServeStaticMiddleware } from '../middleware/ServeStaticMiddleware';\nimport {\n  convertPathToModuleSpecifier,\n  createBundleUrlPath,\n  createBundleUrlOsPath,\n  ExpoMetroOptions,\n  getAsyncRoutesFromExpoConfig,\n  getBaseUrlFromExpoConfig,\n  getMetroDirectBundleOptions,\n  shouldEnableAsyncImports,\n} from '../middleware/metroOptions';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { startTypescriptTypeGenerationAsync } from '../type-generation/startTypescriptTypeGeneration';\n\nexport type ExpoRouterRuntimeManifest = Awaited<\n  ReturnType<typeof import('expo-router/build/static/renderStaticContent').getManifest>\n>;\ntype MetroOnProgress = NonNullable<\n  import('metro/src/DeltaBundler/types').Options<void>['onProgress']\n>;\ntype SSRLoadModuleFunc = <T extends Record<string, any>>(\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>,\n  extras?: { hot?: boolean }\n) => Promise<T>;\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\n/** Default port to use for apps running in Expo Go. */\nconst EXPO_GO_METRO_PORT = 8081;\n\n/** Default port to use for apps that run in standard React Native projects or Expo Dev Clients. */\nconst DEV_CLIENT_METRO_PORT = 8081;\n\nexport class MetroBundlerDevServer extends BundlerDevServer {\n  private metro: MetroPrivateServer | null = null;\n  private hmrServer: MetroHmrServer | null = null;\n  private ssrHmrClients: Map<string, MetroHmrClient> = new Map();\n  isReactServerComponentsEnabled?: boolean;\n  isReactServerRoutesEnabled?: boolean;\n\n  get name(): string {\n    return 'metro';\n  }\n\n  async resolvePortAsync(options: Partial<BundlerStartOptions> = {}): Promise<number> {\n    const port =\n      // If the manually defined port is busy then an error should be thrown...\n      options.port ??\n      // Otherwise use the default port based on the runtime target.\n      (options.devClient\n        ? // Don't check if the port is busy if we're using the dev client since most clients are hardcoded to 8081.\n          Number(process.env.RCT_METRO_PORT) || DEV_CLIENT_METRO_PORT\n        : // Otherwise (running in Expo Go) use a free port that falls back on the classic 8081 port.\n          await getFreePortAsync(EXPO_GO_METRO_PORT));\n\n    return port;\n  }\n\n  async exportExpoRouterApiRoutesAsync({\n    includeSourceMaps,\n    outputDir,\n    prerenderManifest,\n    platform,\n  }: {\n    includeSourceMaps?: boolean;\n    outputDir: string;\n    // This does not contain the API routes info.\n    prerenderManifest: ExpoRouterServerManifestV1;\n    platform: string;\n  }): Promise<{ files: ExportAssetMap; manifest: ExpoRouterServerManifestV1<string> }> {\n    const { routerRoot } = this.instanceMetroOptions;\n    assert(\n      routerRoot != null,\n      'The server must be started before calling exportExpoRouterApiRoutesAsync.'\n    );\n\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const manifest = await this.getExpoRouterRoutesManifestAsync({ appDir });\n\n    const files: ExportAssetMap = new Map();\n\n    // Inject RSC middleware.\n    const rscPath = '/_flight/[...rsc]';\n\n    if (\n      this.isReactServerComponentsEnabled &&\n      // If the RSC route is not already in the manifest, add it.\n      !manifest.apiRoutes.find((route) => route.page.startsWith('/_flight/'))\n    ) {\n      debug('Adding RSC route to the manifest:', rscPath);\n      // NOTE: This might need to be sorted to the correct spot in the future.\n      manifest.apiRoutes.push({\n        file: resolveFrom(this.projectRoot, '@expo/cli/static/template/[...rsc]+api.ts'),\n        page: rscPath,\n        namedRegex: '^/_flight(?:/(?<rsc>.+?))?(?:/)?$',\n        routeKeys: { rsc: 'rsc' },\n      });\n    }\n\n    for (const route of manifest.apiRoutes) {\n      const filepath = path.isAbsolute(route.file) ? route.file : path.join(appDir, route.file);\n      const contents = await this.bundleApiRoute(filepath, { platform });\n\n      const artifactFilename =\n        route.page === rscPath\n          ? // HACK: Add RSC renderer to the output...\n            convertPathToModuleSpecifier(path.join(outputDir, '.' + rscPath + '.js'))\n          : convertPathToModuleSpecifier(\n              path.join(outputDir, path.relative(appDir, filepath.replace(/\\.[tj]sx?$/, '.js')))\n            );\n\n      if (contents) {\n        let src = contents.src;\n\n        if (includeSourceMaps && contents.map) {\n          // TODO(kitten): Merge the source map transformer in the future\n          // https://github.com/expo/expo/blob/0dffdb15/packages/%40expo/metro-config/src/serializer/serializeChunks.ts#L422-L439\n          // Alternatively, check whether `sourcesRoot` helps here\n          const artifactBasename = encodeURIComponent(path.basename(artifactFilename) + '.map');\n          src = src.replace(\n            /\\/\\/# sourceMappingURL=.*/g,\n            `//# sourceMappingURL=${artifactBasename}`\n          );\n\n          const parsedMap =\n            typeof contents.map === 'string' ? JSON.parse(contents.map) : contents.map;\n          files.set(artifactFilename + '.map', {\n            contents: JSON.stringify({\n              version: parsedMap.version,\n              sources: parsedMap.sources.map((source: string) => {\n                source =\n                  typeof source === 'string' && source.startsWith(this.projectRoot)\n                    ? path.relative(this.projectRoot, source)\n                    : source;\n                return convertPathToModuleSpecifier(source);\n              }),\n              sourcesContent: new Array(parsedMap.sources.length).fill(null),\n              names: parsedMap.names,\n              mappings: parsedMap.mappings,\n            }),\n            apiRouteId: route.page,\n            targetDomain: 'server',\n          });\n        }\n        files.set(artifactFilename, {\n          contents: src,\n          apiRouteId: route.page,\n          targetDomain: 'server',\n        });\n      }\n      // Remap the manifest files to represent the output files.\n      route.file = artifactFilename;\n    }\n\n    return {\n      manifest: {\n        ...manifest,\n        htmlRoutes: prerenderManifest.htmlRoutes,\n      },\n      files,\n    };\n  }\n\n  async getExpoRouterRoutesManifestAsync({ appDir }: { appDir: string }) {\n    // getBuiltTimeServerManifest\n    const { exp } = getConfig(this.projectRoot);\n    const manifest = await fetchManifest(this.projectRoot, {\n      ...exp.extra?.router?.platformRoutes,\n      asJson: true,\n      appDir,\n    });\n\n    if (!manifest) {\n      throw new CommandError(\n        'EXPO_ROUTER_SERVER_MANIFEST',\n        'Unexpected error: server manifest could not be fetched.'\n      );\n    }\n\n    return manifest;\n  }\n\n  async getServerManifestAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    htmlManifest: ExpoRouterRuntimeManifest;\n  }> {\n    // NOTE: This could probably be folded back into `renderStaticContent` when expo-asset and font support RSC.\n    const { getBuildTimeServerManifestAsync, getManifest } = await this.ssrLoadModule<\n      typeof import('expo-router/build/static/getServerManifest')\n    >('expo-router/build/static/getServerManifest.js', {\n      // Only use react-server environment when the routes are using react-server rendering by default.\n      environment: this.isReactServerRoutesEnabled ? 'react-server' : 'node',\n    });\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync(),\n      htmlManifest: await getManifest(),\n    };\n  }\n\n  async getStaticRenderFunctionAsync(): Promise<{\n    serverManifest: ExpoRouterServerManifestV1;\n    manifest: ExpoRouterRuntimeManifest;\n    renderAsync: (path: string) => Promise<string>;\n  }> {\n    const url = this.getDevServerUrlOrAssert();\n\n    const { getStaticContent, getManifest, getBuildTimeServerManifestAsync } =\n      await this.ssrLoadModule<typeof import('expo-router/build/static/renderStaticContent')>(\n        'expo-router/node/render.js',\n        {\n          // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n          // the previous React SSG utilities which aren't available in React 19.\n          environment: 'node',\n        }\n      );\n\n    const { exp } = getConfig(this.projectRoot);\n\n    return {\n      serverManifest: await getBuildTimeServerManifestAsync(),\n      // Get routes from Expo Router.\n      manifest: await getManifest({ preserveApiRoutes: false, ...exp.extra?.router }),\n      // Get route generating function\n      async renderAsync(path: string) {\n        return await getStaticContent(new URL(path, url));\n      },\n    };\n  }\n\n  async getStaticResourcesAsync({\n    includeSourceMaps,\n    mainModuleName,\n    clientBoundaries = this.instanceMetroOptions.clientBoundaries ?? [],\n    platform = 'web',\n  }: {\n    includeSourceMaps?: boolean;\n    mainModuleName?: string;\n    clientBoundaries?: string[];\n    platform?: string;\n  } = {}) {\n    const { mode, minify, isExporting, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        routerRoot != null &&\n        reactCompiler != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticResourcesAsync.'\n    );\n\n    const resolvedMainModuleName =\n      mainModuleName ?? './' + resolveMainModuleName(this.projectRoot, { platform });\n    return await this.metroImportAsArtifactsAsync(resolvedMainModuleName, {\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      minify,\n      environment: 'client',\n      serializerIncludeMaps: includeSourceMaps,\n      mainModuleName: resolvedMainModuleName,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      asyncRoutes,\n      baseUrl,\n      isExporting,\n      routerRoot,\n      clientBoundaries,\n      reactCompiler,\n      bytecode: false,\n    });\n  }\n\n  private async getStaticPageAsync(pathname: string) {\n    const { mode, isExporting, clientBoundaries, baseUrl, reactCompiler, routerRoot, asyncRoutes } =\n      this.instanceMetroOptions;\n    assert(\n      mode != null &&\n        isExporting != null &&\n        baseUrl != null &&\n        reactCompiler != null &&\n        routerRoot != null &&\n        asyncRoutes != null,\n      'The server must be started before calling getStaticPageAsync.'\n    );\n    const platform = 'web';\n\n    const devBundleUrlPathname = createBundleUrlPath({\n      splitChunks: isExporting && !env.EXPO_NO_BUNDLE_SPLITTING,\n      platform,\n      mode,\n      environment: 'client',\n      reactCompiler,\n      mainModuleName: resolveMainModuleName(this.projectRoot, { platform }),\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      baseUrl,\n      isExporting,\n      asyncRoutes,\n      routerRoot,\n      clientBoundaries,\n      bytecode: false,\n    });\n\n    const bundleStaticHtml = async (): Promise<string> => {\n      const { getStaticContent } = await this.ssrLoadModule<\n        typeof import('expo-router/build/static/renderStaticContent')\n      >('expo-router/node/render.js', {\n        // This must always use the legacy rendering resolution (no `react-server`) because it leverages\n        // the previous React SSG utilities which aren't available in React 19.\n        environment: 'node',\n        minify: false,\n        isExporting,\n        platform,\n      });\n\n      const location = new URL(pathname, this.getDevServerUrlOrAssert());\n      return await getStaticContent(location);\n    };\n\n    const [{ artifacts: resources }, staticHtml] = await Promise.all([\n      this.getStaticResourcesAsync({\n        clientBoundaries: [],\n      }),\n      bundleStaticHtml(),\n    ]);\n    const content = serializeHtmlWithAssets({\n      isExporting,\n      resources,\n      template: staticHtml,\n      devBundleUrl: devBundleUrlPathname,\n      baseUrl,\n      hydrate: env.EXPO_WEB_DEV_HYDRATE,\n    });\n    return {\n      content,\n      resources,\n    };\n  }\n\n  // Set when the server is started.\n  private instanceMetroOptions: Partial<ExpoMetroOptions> = {};\n\n  private ssrLoadModule: SSRLoadModuleFunc = async (\n    filePath,\n    specificOptions = {},\n    extras = {}\n  ) => {\n    const res = await this.ssrLoadModuleContents(filePath, specificOptions);\n\n    if (\n      // TODO: hot should be a callback function for invalidating the related SSR module.\n      extras.hot &&\n      this.instanceMetroOptions.isExporting !== true\n    ) {\n      // Register SSR HMR\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n      const relativePath = path.relative(serverRoot, res.filename);\n      const url = new URL(relativePath, this.getDevServerUrlOrAssert());\n      this.setupHmr(url);\n    }\n\n    return evalMetroAndWrapFunctions(\n      this.projectRoot,\n      res.src,\n      res.filename,\n      specificOptions.isExporting ?? this.instanceMetroOptions.isExporting!\n    );\n  };\n\n  private async metroImportAsArtifactsAsync(\n    filePath: string,\n    specificOptions: Partial<Omit<ExpoMetroOptions, 'serializerOutput'>> = {}\n  ) {\n    const results = await this.ssrLoadModuleContents(filePath, {\n      serializerOutput: 'static',\n      ...specificOptions,\n    });\n\n    // NOTE: This could potentially need more validation in the future.\n    if (results.artifacts && results.assets) {\n      return {\n        artifacts: results.artifacts,\n        assets: results.assets,\n        src: results.src,\n        filename: results.filename,\n        map: results.map,\n      };\n    }\n    throw new CommandError('Invalid bundler results: ' + results);\n  }\n\n  private async metroLoadModuleContents(\n    filePath: string,\n    specificOptions: ExpoMetroOptions,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ) {\n    const { baseUrl } = this.instanceMetroOptions;\n    assert(baseUrl != null, 'The server must be started before calling metroLoadModuleContents.');\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      // mainModuleName: filePath,\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      // bytecode: false,\n      // Bundle in Node.js mode for SSR.\n      environment: 'node',\n      // platform: 'web',\n      // mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n      baseUrl,\n      // routerRoot,\n      // isExporting,\n      ...specificOptions,\n    };\n\n    const expoBundleOptions = getMetroDirectBundleOptions(opts);\n\n    const resolverOptions = {\n      customResolverOptions: expoBundleOptions.customResolverOptions ?? {},\n      dev: expoBundleOptions.dev ?? true,\n    };\n\n    const transformOptions: TransformInputOptions = {\n      dev: expoBundleOptions.dev ?? true,\n      hot: true,\n      minify: expoBundleOptions.minify ?? false,\n      type: 'module',\n      unstable_transformProfile:\n        extraOptions.unstable_transformProfile ??\n        expoBundleOptions.unstable_transformProfile ??\n        'default',\n      customTransformOptions: expoBundleOptions.customTransformOptions ?? Object.create(null),\n      platform: expoBundleOptions.platform ?? 'web',\n      // @ts-expect-error: `runtimeBytecodeVersion` does not exist in `expoBundleOptions` or `TransformInputOptions`\n      runtimeBytecodeVersion: expoBundleOptions.runtimeBytecodeVersion,\n    };\n\n    const resolvedEntryFilePath = await this.resolveRelativePathAsync(filePath, {\n      resolverOptions,\n      transformOptions,\n    });\n\n    // Use fully qualified URL with all options to represent the file path that's used for source maps and HMR. This prevents collisions.\n    const filename = createBundleUrlOsPath({\n      ...opts,\n      mainModuleName: resolvedEntryFilePath,\n    });\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const results = await this._bundleDirectAsync(resolvedEntryFilePath, {\n      graphOptions: {\n        lazy: expoBundleOptions.lazy ?? false,\n        shallow: expoBundleOptions.shallow ?? false,\n      },\n      resolverOptions,\n      serializerOptions: {\n        ...expoBundleOptions.serializerOptions,\n\n        inlineSourceMap: expoBundleOptions.inlineSourceMap ?? false,\n        modulesOnly: expoBundleOptions.modulesOnly ?? false,\n        runModule: expoBundleOptions.runModule ?? true,\n        // @ts-expect-error\n        sourceUrl: expoBundleOptions.sourceUrl,\n        // @ts-expect-error\n        sourceMapUrl: extraOptions.sourceMapUrl ?? expoBundleOptions.sourceMapUrl,\n      },\n      transformOptions,\n    });\n\n    return {\n      ...results,\n      filename,\n    };\n  }\n\n  private async ssrLoadModuleContents(\n    filePath: string,\n    specificOptions: Partial<ExpoMetroOptions> = {}\n  ) {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling ssrLoadModuleContents.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      // TODO: Possibly issues with using an absolute path here...\n      mainModuleName: convertPathToModuleSpecifier(filePath),\n      lazy: false,\n      asyncRoutes: false,\n      inlineSourceMap: false,\n      engine: 'hermes',\n      minify: false,\n      bytecode: false,\n      // Bundle in Node.js mode for SSR unless RSC is enabled.\n      environment: this.isReactServerComponentsEnabled ? 'react-server' : 'node',\n      platform: 'web',\n      mode: 'development',\n      //\n      ...this.instanceMetroOptions,\n\n      // Mostly disable compiler in SSR bundles.\n      reactCompiler: false,\n      baseUrl,\n      routerRoot,\n      isExporting,\n\n      ...specificOptions,\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    const { filename, bundle, map, ...rest } = await this.metroLoadModuleContents(filePath, opts);\n    const scriptContents = wrapBundle(bundle);\n\n    if (map) {\n      debug('Registering SSR source map for:', filename);\n      cachedSourceMaps.set(filename, { url: this.projectRoot, map });\n    } else {\n      debug('No SSR source map found for:', filename);\n    }\n\n    return {\n      ...rest,\n      src: scriptContents,\n      filename,\n      map,\n    };\n  }\n\n  async nativeExportBundleAsync(\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files?: ExportAssetMap;\n  }> {\n    if (this.isReactServerComponentsEnabled) {\n      return this.singlePageReactServerComponentExportAsync(options, files, extraOptions);\n    }\n\n    return this.legacySinglePageExportBundleAsync(options, extraOptions);\n  }\n\n  private async singlePageReactServerComponentExportAsync(\n    options: Omit<\n      ExpoMetroOptions,\n      'baseUrl' | 'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    files: ExportAssetMap,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{\n    artifacts: SerialAsset[];\n    assets: readonly BundleAssetWithFileHashes[];\n    files: ExportAssetMap;\n  }> {\n    const getReactServerReferences = (artifacts: SerialAsset[]): string[] => {\n      // Get the React server action boundaries from the client bundle.\n      return unique(\n        artifacts\n          .filter((a) => a.type === 'js')\n          .map((artifact) =>\n            artifact.metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref))\n          )\n          // TODO: Segment by module for splitting.\n          .flat()\n          .filter(Boolean) as string[]\n      );\n    };\n\n    // NOTE(EvanBacon): This will not support any code elimination since it's a static pass.\n    let {\n      reactClientReferences: clientBoundaries,\n      reactServerReferences: serverActionReferencesInServer,\n      cssModules,\n    } = await this.rscRenderer!.getExpoRouterClientReferencesAsync(\n      {\n        platform: options.platform,\n        domRoot: options.domRoot,\n      },\n      files\n    );\n\n    // TODO: The output keys should be in production format or use a lookup manifest.\n\n    const processClientBoundaries = async (\n      reactServerReferences: string[]\n    ): Promise<{\n      artifacts: SerialAsset[];\n      assets: readonly BundleAssetWithFileHashes[];\n    }> => {\n      debug('Evaluated client boundaries:', clientBoundaries);\n\n      // Run metro bundler and create the JS bundles/source maps.\n      const bundle = await this.legacySinglePageExportBundleAsync(\n        {\n          ...options,\n          clientBoundaries,\n        },\n        extraOptions\n      );\n\n      // Get the React server action boundaries from the client bundle.\n      const newReactServerReferences = getReactServerReferences(bundle.artifacts);\n\n      if (!newReactServerReferences) {\n        // Possible issue with babel plugin / metro-config.\n        throw new Error(\n          'Static server action references were not returned from the Metro client bundle'\n        );\n      }\n      debug('React server action boundaries from client:', newReactServerReferences);\n\n      const allKnownReactServerReferences = unique([\n        ...reactServerReferences,\n        ...newReactServerReferences,\n      ]);\n\n      // When we export the server actions that were imported from the client, we may need to re-bundle the client with the new client boundaries.\n      const { clientBoundaries: nestedClientBoundaries } =\n        await this.rscRenderer!.exportServerActionsAsync(\n          {\n            platform: options.platform,\n            domRoot: options.domRoot,\n            entryPoints: allKnownReactServerReferences,\n          },\n          files\n        );\n\n      // TODO: Check against all modules in the initial client bundles.\n      const hasUniqueClientBoundaries = nestedClientBoundaries.some(\n        (boundary) => !clientBoundaries.includes(boundary)\n      );\n\n      if (!hasUniqueClientBoundaries) {\n        return bundle;\n      }\n\n      debug('Re-bundling client with nested client boundaries:', nestedClientBoundaries);\n\n      clientBoundaries = unique(clientBoundaries.concat(nestedClientBoundaries));\n\n      // Re-bundle the client with the new client boundaries that only exist in server actions that were imported from the client.\n      // Run metro bundler and create the JS bundles/source maps.\n      return processClientBoundaries(allKnownReactServerReferences);\n    };\n\n    const bundle = await processClientBoundaries(serverActionReferencesInServer);\n\n    // Inject the global CSS that was imported during the server render.\n    bundle.artifacts.push(...cssModules);\n\n    const serverRoot = getMetroServerRoot(this.projectRoot);\n\n    // HACK: Maybe this should be done in the serializer.\n    const clientBoundariesAsOpaqueIds = clientBoundaries.map((boundary) =>\n      // NOTE(cedric): relative module specifiers / IDs should always be POSIX formatted\n      toPosixPath(path.relative(serverRoot, boundary))\n    );\n    const moduleIdToSplitBundle = (\n      bundle.artifacts\n        .map((artifact) => artifact?.metadata?.paths && Object.values(artifact.metadata.paths))\n        .filter(Boolean)\n        .flat() as Record<string, string>[]\n    ).reduce((acc, paths) => ({ ...acc, ...paths }), {});\n\n    debug('SSR Manifest:', moduleIdToSplitBundle, clientBoundariesAsOpaqueIds);\n\n    const ssrManifest = new Map<string, string>();\n\n    if (Object.keys(moduleIdToSplitBundle).length) {\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        if (boundary in moduleIdToSplitBundle) {\n          ssrManifest.set(boundary, moduleIdToSplitBundle[boundary]);\n        } else {\n          throw new Error(\n            `Could not find boundary \"${boundary}\" in the SSR manifest. Available: ${Object.keys(moduleIdToSplitBundle).join(', ')}`\n          );\n        }\n      });\n    } else {\n      // Native apps with bundle splitting disabled.\n      debug('No split bundles');\n      clientBoundariesAsOpaqueIds.forEach((boundary) => {\n        // @ts-expect-error\n        ssrManifest.set(boundary, null);\n      });\n    }\n\n    // Export the static RSC files\n    await this.rscRenderer!.exportRoutesAsync(\n      {\n        platform: options.platform,\n        ssrManifest,\n      },\n      files\n    );\n\n    // Save the SSR manifest so we can perform more replacements in the server renderer and with server actions.\n    files.set(`_expo/rsc/${options.platform}/ssr-manifest.js`, {\n      targetDomain: 'server',\n      contents:\n        'module.exports = ' +\n        JSON.stringify(\n          // TODO: Add a less leaky version of this across the framework with just [key, value] (module ID, chunk).\n          Object.fromEntries(\n            Array.from(ssrManifest.entries()).map(([key, value]) => [\n              path.join(serverRoot, key),\n              [key, value],\n            ])\n          )\n        ),\n    });\n\n    return { ...bundle, files };\n  }\n\n  async legacySinglePageExportBundleAsync(\n    options: Omit<\n      ExpoMetroOptions,\n      'routerRoot' | 'asyncRoutes' | 'isExporting' | 'serializerOutput' | 'environment'\n    >,\n    extraOptions: {\n      sourceMapUrl?: string;\n      unstable_transformProfile?: TransformProfile;\n    } = {}\n  ): Promise<{ artifacts: SerialAsset[]; assets: readonly BundleAssetWithFileHashes[] }> {\n    const { baseUrl, routerRoot, isExporting } = this.instanceMetroOptions;\n    assert(options.mainModuleName != null, 'mainModuleName must be provided in options.');\n    assert(\n      baseUrl != null && routerRoot != null && isExporting != null,\n      'The server must be started before calling legacySinglePageExportBundleAsync.'\n    );\n\n    const opts: ExpoMetroOptions = {\n      ...this.instanceMetroOptions,\n      baseUrl,\n      routerRoot,\n      isExporting,\n      ...options,\n      environment: 'client',\n      serializerOutput: 'static',\n    };\n\n    // https://github.com/facebook/metro/blob/2405f2f6c37a1b641cc379b9c733b1eff0c1c2a1/packages/metro/src/lib/parseOptionsFromUrl.js#L55-L87\n    if (!opts.mainModuleName.startsWith('/') && !path.isAbsolute(opts.mainModuleName)) {\n      opts.mainModuleName = './' + opts.mainModuleName;\n    }\n\n    const output = await this.metroLoadModuleContents(opts.mainModuleName, opts, extraOptions);\n\n    return {\n      artifacts: output.artifacts!,\n      assets: output.assets!,\n    };\n  }\n\n  async watchEnvironmentVariables() {\n    if (!this.instance) {\n      throw new Error(\n        'Cannot observe environment variable changes without a running Metro instance.'\n      );\n    }\n    if (!this.metro) {\n      // This can happen when the run command is used and the server is already running in another\n      // process.\n      debug('Skipping Environment Variable observation because Metro is not running (headless).');\n      return;\n    }\n\n    const envFiles = runtimeEnv\n      .getFiles(process.env.NODE_ENV)\n      .map((fileName) => path.join(this.projectRoot, fileName));\n\n    observeFileChanges(\n      {\n        metro: this.metro,\n        server: this.instance.server,\n      },\n      envFiles,\n      () => {\n        debug('Reloading environment variables...');\n        // Force reload the environment variables.\n        runtimeEnv.load(this.projectRoot, { force: true });\n      }\n    );\n  }\n\n  rscRenderer: Awaited<ReturnType<typeof createServerComponentsMiddleware>> | null = null;\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    options.port = await this.resolvePortAsync(options);\n    this.urlCreator = this.getUrlCreator(options);\n\n    const config = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n    const { exp } = config;\n    // NOTE: This will change in the future when it's less experimental, we enable React 19, and turn on more RSC flags by default.\n    const isReactServerComponentsEnabled =\n      !!exp.experiments?.reactServerComponentRoutes || !!exp.experiments?.reactServerFunctions;\n    const isReactServerActionsOnlyEnabled =\n      !exp.experiments?.reactServerComponentRoutes && !!exp.experiments?.reactServerFunctions;\n    this.isReactServerComponentsEnabled = isReactServerComponentsEnabled;\n    this.isReactServerRoutesEnabled = !!exp.experiments?.reactServerComponentRoutes;\n\n    const useServerRendering = ['static', 'server'].includes(exp.web?.output ?? '');\n    const hasApiRoutes = isReactServerComponentsEnabled || exp.web?.output === 'server';\n    const baseUrl = getBaseUrlFromExpoConfig(exp);\n    const asyncRoutes = getAsyncRoutesFromExpoConfig(exp, options.mode ?? 'development', 'web');\n    const routerRoot = getRouterDirectoryModuleIdWithManifest(this.projectRoot, exp);\n    const reactCompiler = !!exp.experiments?.reactCompiler;\n    const appDir = path.join(this.projectRoot, routerRoot);\n    const mode = options.mode ?? 'development';\n\n    if (isReactServerComponentsEnabled && exp.web?.output === 'static') {\n      throw new CommandError(\n        `Experimental server component support does not support 'web.output: ${exp.web!.output}' yet. Use 'web.output: \"server\"' during the experimental phase.`\n      );\n    }\n\n    // Error early about the window.location polyfill when React Server Components are enabled.\n    if (isReactServerComponentsEnabled && exp?.extra?.router?.origin === false) {\n      const configPath = config.dynamicConfigPath ?? config.staticConfigPath ?? '/app.json';\n      const configFileName = path.basename(configPath);\n      throw new CommandError(\n        `The Expo Router \"origin\" property in the Expo config (${configFileName}) cannot be \"false\" when React Server Components is enabled. Remove it from the ${configFileName} file and try again.`\n      );\n    }\n\n    const instanceMetroOptions = {\n      isExporting: !!options.isExporting,\n      baseUrl,\n      mode,\n      routerRoot,\n      reactCompiler,\n      minify: options.minify,\n      asyncRoutes,\n      // Options that are changing between platforms like engine, platform, and environment aren't set here.\n    };\n    this.instanceMetroOptions = instanceMetroOptions;\n\n    const parsedOptions = {\n      port: options.port,\n      maxWorkers: options.maxWorkers,\n      resetCache: options.resetDevServer,\n    };\n\n    // Required for symbolication:\n    process.env.EXPO_DEV_SERVER_ORIGIN = `http://localhost:${options.port}`;\n\n    const { metro, hmrServer, server, middleware, messageSocket } = await instantiateMetroAsync(\n      this,\n      parsedOptions,\n      {\n        isExporting: !!options.isExporting,\n        exp,\n      }\n    );\n\n    if (!options.isExporting) {\n      const manifestMiddleware = await this.getManifestMiddlewareAsync(options);\n\n      // Important that we noop source maps for context modules as soon as possible.\n      prependMiddleware(middleware, new ContextModuleSourceMapsMiddleware().getHandler());\n\n      // We need the manifest handler to be the first middleware to run so our\n      // routes take precedence over static files. For example, the manifest is\n      // served from '/' and if the user has an index.html file in their project\n      // then the manifest handler will never run, the static middleware will run\n      // and serve index.html instead of the manifest.\n      // https://github.com/expo/expo/issues/13114\n      prependMiddleware(middleware, manifestMiddleware.getHandler());\n\n      middleware.use(\n        new InterstitialPageMiddleware(this.projectRoot, {\n          // TODO: Prevent this from becoming stale.\n          scheme: options.location.scheme ?? null,\n        }).getHandler()\n      );\n      middleware.use(new ReactDevToolsPageMiddleware(this.projectRoot).getHandler());\n      middleware.use(\n        new DevToolsPluginMiddleware(this.projectRoot, this.devToolsPluginManager).getHandler()\n      );\n\n      const deepLinkMiddleware = new RuntimeRedirectMiddleware(this.projectRoot, {\n        getLocation: ({ runtime }) => {\n          if (runtime === 'custom') {\n            return this.urlCreator?.constructDevClientUrl();\n          } else {\n            return this.urlCreator?.constructUrl({\n              scheme: 'exp',\n            });\n          }\n        },\n      });\n      middleware.use(deepLinkMiddleware.getHandler());\n\n      const serverRoot = getMetroServerRoot(this.projectRoot);\n\n      const domComponentRenderer = createDomComponentsMiddleware(\n        {\n          metroRoot: serverRoot,\n          projectRoot: this.projectRoot,\n        },\n        instanceMetroOptions\n      );\n      // Add support for DOM components.\n      // TODO: Maybe put behind a flag for now?\n      middleware.use(domComponentRenderer);\n\n      middleware.use(new CreateFileMiddleware(this.projectRoot).getHandler());\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        // This MUST be after the manifest middleware so it doesn't have a chance to serve the template `public/index.html`.\n        middleware.use(new ServeStaticMiddleware(this.projectRoot).getHandler());\n\n        // This should come after the static middleware so it doesn't serve the favicon from `public/favicon.ico`.\n        middleware.use(new FaviconMiddleware(this.projectRoot).getHandler());\n      }\n\n      if (useServerRendering || isReactServerComponentsEnabled) {\n        observeAnyFileChanges(\n          {\n            metro,\n            server,\n          },\n          (events) => {\n            if (hasApiRoutes) {\n              // NOTE(EvanBacon): We aren't sure what files the API routes are using so we'll just invalidate\n              // aggressively to ensure we always have the latest. The only caching we really get here is for\n              // cases where the user is making subsequent requests to the same API route without changing anything.\n              // This is useful for testing but pretty suboptimal. Luckily our caching is pretty aggressive so it makes\n              // up for a lot of the overhead.\n              this.invalidateApiRouteCache();\n            } else if (!hasWarnedAboutApiRoutes()) {\n              for (const event of events) {\n                if (\n                  // If the user did not delete a file that matches the Expo Router API Route convention, then we should warn that\n                  // API Routes are not enabled in the project.\n                  event.metadata?.type !== 'd' &&\n                  // Ensure the file is in the project's routes directory to prevent false positives in monorepos.\n                  event.filePath.startsWith(appDir) &&\n                  isApiRouteConvention(event.filePath)\n                ) {\n                  warnInvalidWebOutput();\n                }\n              }\n            }\n          }\n        );\n      }\n\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n        });\n        this.rscRenderer = rscMiddleware;\n        middleware.use(rscMiddleware.middleware);\n        this.onReloadRscEvent = rscMiddleware.onReloadRscEvent;\n      }\n\n      // Append support for redirecting unhandled requests to the index.html page on web.\n      if (this.isTargetingWeb()) {\n        if (!useServerRendering) {\n          // This MUST run last since it's the fallback.\n          middleware.use(\n            new HistoryFallbackMiddleware(manifestMiddleware.getHandler().internal).getHandler()\n          );\n        } else {\n          middleware.use(\n            createRouteHandlerMiddleware(this.projectRoot, {\n              appDir,\n              routerRoot,\n              config,\n              ...config.exp.extra?.router,\n              bundleApiRoute: (functionFilePath) =>\n                this.ssrImportApiRoute(functionFilePath, { platform: 'web' }),\n              getStaticPageAsync: async (pathname) => {\n                // TODO: Add server rendering when RSC is enabled.\n                if (isReactServerComponentsEnabled) {\n                  // NOTE: This is a temporary hack to return the SPA/template index.html in development when RSC is enabled.\n                  // While this technically works, it doesn't provide the correct experience of server rendering the React code to HTML first.\n                  const html = await manifestMiddleware.getSingleHtmlTemplateAsync();\n                  return { content: html };\n                }\n\n                // Non-RSC apps will bundle the static HTML for a given pathname and respond with it.\n                return this.getStaticPageAsync(pathname);\n              },\n            })\n          );\n        }\n      }\n    } else {\n      // If React 19 is enabled, then add RSC middleware to the dev server.\n      if (isReactServerComponentsEnabled) {\n        this.bindRSCDevModuleInjectionHandler();\n        const rscMiddleware = createServerComponentsMiddleware(this.projectRoot, {\n          instanceMetroOptions: this.instanceMetroOptions,\n          rscPath: '/_flight',\n          ssrLoadModule: this.ssrLoadModule.bind(this),\n          ssrLoadModuleArtifacts: this.metroImportAsArtifactsAsync.bind(this),\n          useClientRouter: isReactServerActionsOnlyEnabled,\n          createModuleId: metro._createModuleId.bind(metro),\n        });\n        this.rscRenderer = rscMiddleware;\n      }\n    }\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        this.metro = null;\n        this.hmrServer = null;\n        this.ssrHmrClients = new Map();\n        callback?.(err);\n      });\n    };\n\n    assertMetroPrivateServer(metro);\n    this.metro = metro;\n    this.hmrServer = hmrServer;\n    return {\n      server,\n      location: {\n        // The port is the main thing we want to send back.\n        port: options.port,\n        // localhost isn't always correct.\n        host: 'localhost',\n        // http is the only supported protocol on native.\n        url: `http://localhost:${options.port}`,\n        protocol: 'http',\n      },\n      middleware,\n      messageSocket,\n    };\n  }\n\n  private onReloadRscEvent: ((platform: string) => void) | null = null;\n\n  private async registerSsrHmrAsync(url: string, onReload: (platform: string[]) => void) {\n    if (!this.hmrServer || this.ssrHmrClients.has(url)) {\n      return;\n    }\n\n    debug('[SSR] Register HMR:', url);\n\n    const sendFn = (message: string) => {\n      const data = JSON.parse(String(message)) as { type: string; body: any };\n\n      switch (data.type) {\n        case 'bundle-registered':\n        case 'update-done':\n        case 'update-start':\n          break;\n        case 'update':\n          {\n            const update = data.body;\n            const {\n              isInitialUpdate,\n              added,\n              modified,\n              deleted,\n            }: {\n              isInitialUpdate?: boolean;\n              added: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              modified: {\n                module: [number | string, string];\n                sourceURL: string;\n                sourceMappingURL: string;\n              }[];\n              deleted: (number | string)[];\n            } = update;\n\n            const hasUpdate = added.length || modified.length || deleted.length;\n\n            // NOTE: We throw away the updates and instead simply send a trigger to the client to re-fetch the server route.\n            if (!isInitialUpdate && hasUpdate) {\n              // Clear all SSR modules before sending the reload event. This ensures that the next event will rebuild the in-memory state from scratch.\n              // @ts-expect-error\n              if (typeof globalThis.__c === 'function') globalThis.__c();\n\n              const allModuleIds = new Set(\n                [...added, ...modified].map((m) => m.module[0]).concat(deleted)\n              );\n\n              const platforms = unique(\n                Array.from(allModuleIds)\n                  .map((moduleId) => {\n                    if (typeof moduleId !== 'string') {\n                      return null;\n                    }\n                    // Extract platforms from the module IDs.\n                    return moduleId.match(/[?&]platform=([\\w]+)/)?.[1] ?? null;\n                  })\n                  .filter(Boolean)\n              ) as string[];\n\n              onReload(platforms);\n            }\n          }\n          break;\n        case 'error':\n          // GraphNotFound can mean that we have an issue in metroOptions where the URL doesn't match the object props.\n          Log.error('[SSR] HMR Error: ' + JSON.stringify(data, null, 2));\n\n          if (data.body?.type === 'GraphNotFoundError') {\n            Log.error(\n              'Available SSR HMR keys:',\n              // @ts-expect-error\n              (this.metro?._bundler._revisionsByGraphId as Map).keys()\n            );\n          }\n          break;\n        default:\n          debug('Unknown HMR message:', data);\n          break;\n      }\n    };\n\n    const client = await this.hmrServer!.onClientConnect(url, sendFn);\n    this.ssrHmrClients.set(url, client);\n    // Opt in...\n    client.optedIntoHMR = true;\n    await this.hmrServer!._registerEntryPoint(client, url, sendFn);\n  }\n\n  public async waitForTypeScriptAsync(): Promise<boolean> {\n    if (!this.instance) {\n      throw new Error('Cannot wait for TypeScript without a running server.');\n    }\n\n    return new Promise<boolean>((resolve) => {\n      if (!this.metro) {\n        // This can happen when the run command is used and the server is already running in another\n        // process. In this case we can't wait for the TypeScript check to complete because we don't\n        // have access to the Metro server.\n        debug('Skipping TypeScript check because Metro is not running (headless).');\n        return resolve(false);\n      }\n\n      const off = metroWatchTypeScriptFiles({\n        projectRoot: this.projectRoot,\n        server: this.instance!.server,\n        metro: this.metro,\n        tsconfig: true,\n        throttle: true,\n        eventTypes: ['change', 'add'],\n        callback: async () => {\n          // Run once, this prevents the TypeScript project prerequisite from running on every file change.\n          off();\n          const { TypeScriptProjectPrerequisite } = await import(\n            '../../doctor/typescript/TypeScriptProjectPrerequisite.js'\n          );\n\n          try {\n            const req = new TypeScriptProjectPrerequisite(this.projectRoot);\n            await req.bootstrapAsync();\n            resolve(true);\n          } catch (error: any) {\n            // Ensure the process doesn't fail if the TypeScript check fails.\n            // This could happen during the install.\n            Log.log();\n            Log.error(\n              chalk.red`Failed to automatically setup TypeScript for your project. Try restarting the dev server to fix.`\n            );\n            Log.exception(error);\n            resolve(false);\n          }\n        },\n      });\n    });\n  }\n\n  public async startTypeScriptServices() {\n    return startTypescriptTypeGenerationAsync({\n      server: this.instance?.server,\n      metro: this.metro,\n      projectRoot: this.projectRoot,\n    });\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./metro.config.js', './metro.config.json', './rn-cli.config.js'];\n  }\n\n  // API Routes\n\n  private pendingRouteOperations = new Map<\n    string,\n    Promise<{ src: string; filename: string; map: string } | null>\n  >();\n\n  // Bundle the API Route with Metro and return the string contents to be evaluated in the server.\n  private async bundleApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<{ src: string; filename: string; map?: any } | null | undefined> {\n    if (this.pendingRouteOperations.has(filePath)) {\n      return this.pendingRouteOperations.get(filePath);\n    }\n    const bundleAsync = async () => {\n      try {\n        debug('Bundle API route:', this.instanceMetroOptions.routerRoot, filePath);\n        return await this.ssrLoadModuleContents(filePath, {\n          isExporting: this.instanceMetroOptions.isExporting,\n          platform,\n        });\n      } catch (error: any) {\n        const appDir = this.instanceMetroOptions?.routerRoot\n          ? path.join(this.projectRoot, this.instanceMetroOptions!.routerRoot!)\n          : undefined;\n        const relativePath = appDir ? path.relative(appDir, filePath) : filePath;\n\n        // Expected errors: invalid syntax, missing resolutions.\n        // Wrap with command error for better error messages.\n        const err = new CommandError(\n          'API_ROUTE',\n          chalk`Failed to bundle API Route: {bold ${relativePath}}\\n\\n` + error.message\n        );\n\n        for (const key in error) {\n          // @ts-expect-error\n          err[key] = error[key];\n        }\n\n        throw err;\n      } finally {\n        // pendingRouteOperations.delete(filepath);\n      }\n    };\n    const route = bundleAsync();\n\n    this.pendingRouteOperations.set(filePath, route);\n    return route;\n  }\n\n  private async ssrImportApiRoute(\n    filePath: string,\n    { platform }: { platform: string }\n  ): Promise<null | Record<string, Function> | Response> {\n    // TODO: Cache the evaluated function.\n    try {\n      const apiRoute = await this.bundleApiRoute(filePath, { platform });\n\n      if (!apiRoute?.src) {\n        return null;\n      }\n      return evalMetroNoHandling(this.projectRoot, apiRoute.src, apiRoute.filename);\n    } catch (error) {\n      // Format any errors that were thrown in the global scope of the evaluation.\n      if (error instanceof Error) {\n        try {\n          const htmlServerError = await getErrorOverlayHtmlAsync({\n            error,\n            projectRoot: this.projectRoot,\n            routerRoot: this.instanceMetroOptions.routerRoot!,\n          });\n\n          return new Response(htmlServerError, {\n            status: 500,\n            headers: {\n              'Content-Type': 'text/html',\n            },\n          });\n        } catch (internalError) {\n          debug('Failed to generate Metro server error UI for API Route error:', internalError);\n          throw error;\n        }\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  private invalidateApiRouteCache() {\n    this.pendingRouteOperations.clear();\n  }\n\n  // Ensure the global is available for SSR CSS modules to inject client updates.\n  private bindRSCDevModuleInjectionHandler() {\n    // Used by SSR CSS modules to broadcast client updates.\n    // @ts-expect-error\n    globalThis.__expo_rsc_inject_module = this.sendClientModule.bind(this);\n  }\n\n  // NOTE: This can only target a single platform at a time (web).\n  // used for sending RSC CSS to the root client in development.\n  private sendClientModule({ code, id }: { code: string; id: string }) {\n    this.broadcastMessage('sendDevCommand', {\n      name: 'module-import',\n      data: {\n        code,\n        id,\n      },\n    });\n  }\n\n  // Metro HMR\n\n  private setupHmr(url: URL) {\n    const onReload = (platforms: string[] = []) => {\n      // Send reload command to client from Fast Refresh code.\n\n      if (!platforms.length) {\n        // TODO: When is this called?\n        this.broadcastMessage('sendDevCommand', {\n          name: 'rsc-reload',\n        });\n      } else {\n        for (const platform of platforms) {\n          this.onReloadRscEvent?.(platform);\n          this.broadcastMessage('sendDevCommand', {\n            name: 'rsc-reload',\n            platform,\n          });\n        }\n      }\n    };\n\n    this.registerSsrHmrAsync(url.toString(), onReload);\n  }\n\n  // Direct Metro access\n\n  // Emulates the Metro dev server .bundle endpoint without having to go through a server.\n  private async _bundleDirectAsync(\n    resolvedEntryFilePath: string,\n    {\n      transformOptions,\n      resolverOptions,\n      graphOptions,\n      serializerOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      serializerOptions: {\n        modulesOnly: boolean;\n        runModule: boolean;\n        sourceMapUrl: string;\n        sourceUrl: string;\n        inlineSourceMap: boolean;\n        excludeSource: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ): Promise<{\n    numModifiedFiles: number;\n    lastModifiedDate: Date;\n    nextRevId: string;\n    bundle: string;\n    map: string;\n\n    // Defined if the output is multi-bundle.\n    artifacts?: SerialAsset[];\n    assets?: readonly BundleAssetWithFileHashes[];\n  }> {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n    const buildNumber = this.metro.getNewBuildNumber();\n    const bundlePerfLogger = config.unstable_perfLoggerFactory?.('BUNDLING_REQUEST', {\n      key: buildNumber,\n    });\n\n    const onProgress: MetroOnProgress = (transformedFileCount: number, totalFileCount: number) => {\n      this.metro?._reporter?.update?.({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_transform_progressed',\n        transformedFileCount,\n        totalFileCount,\n      });\n    };\n\n    const revPromise = this.getMetroRevision(resolvedEntryFilePath, {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    });\n\n    bundlePerfLogger?.point('resolvingAndTransformingDependencies_start');\n    bundlePerfLogger?.annotate({\n      bool: {\n        initial_build: revPromise == null,\n      },\n    });\n    this.metro?._reporter.update({\n      buildID: getBuildID(buildNumber),\n      bundleDetails: {\n        bundleType: transformOptions.type,\n        dev: transformOptions.dev,\n        entryFile: resolvedEntryFilePath,\n        minify: transformOptions.minify,\n        platform: transformOptions.platform,\n        customResolverOptions: resolverOptions.customResolverOptions,\n        customTransformOptions: transformOptions.customTransformOptions ?? {},\n      },\n      isPrefetch: false,\n      type: 'bundle_build_started',\n    });\n\n    try {\n      let delta: DeltaResult;\n      let revision: GraphRevision;\n\n      // TODO: Some bug in Metro/RSC causes this to break when changing imports in server components.\n      // We should resolve the bug because it results in ~6x faster bundling to reuse the graph revision.\n      if (transformOptions.customTransformOptions?.environment === 'react-server') {\n        const props = await this.metro.getBundler().initializeGraph(\n          // NOTE: Using absolute path instead of relative input path is a breaking change.\n          // entryFile,\n          resolvedEntryFilePath,\n\n          transformOptions,\n          resolverOptions,\n          {\n            onProgress,\n            shallow: graphOptions.shallow,\n            lazy: graphOptions.lazy,\n          }\n        );\n        delta = props.delta;\n        revision = props.revision;\n      } else {\n        const props = await (revPromise != null\n          ? this.metro.getBundler().updateGraph(await revPromise, false)\n          : this.metro.getBundler().initializeGraph(\n              // NOTE: Using absolute path instead of relative input path is a breaking change.\n              // entryFile,\n              resolvedEntryFilePath,\n\n              transformOptions,\n              resolverOptions,\n              {\n                onProgress,\n                shallow: graphOptions.shallow,\n                lazy: graphOptions.lazy,\n              }\n            ));\n        delta = props.delta;\n        revision = props.revision;\n      }\n\n      bundlePerfLogger?.annotate({\n        int: {\n          graph_node_count: revision.graph.dependencies.size,\n        },\n      });\n      bundlePerfLogger?.point('resolvingAndTransformingDependencies_end');\n      bundlePerfLogger?.point('serializingBundle_start');\n\n      const shouldAddToIgnoreList = this.metro._shouldAddModuleToIgnoreList.bind(this.metro);\n\n      const serializer = this.getMetroSerializer();\n\n      const bundle = await serializer(\n        // NOTE: Using absolute path instead of relative input path is a breaking change.\n        // entryFile,\n        resolvedEntryFilePath,\n\n        revision.prepend as any,\n        revision.graph as any,\n        {\n          asyncRequireModulePath: await this.metro._resolveRelativePath(\n            config.transformer.asyncRequireModulePath,\n            {\n              relativeTo: 'project',\n              resolverOptions,\n              transformOptions,\n            }\n          ),\n          // ...serializerOptions,\n          processModuleFilter: config.serializer.processModuleFilter,\n          createModuleId: this.metro._createModuleId,\n          getRunModuleStatement: config.serializer.getRunModuleStatement,\n          includeAsyncPaths: graphOptions.lazy,\n          dev: transformOptions.dev,\n          projectRoot: config.projectRoot,\n          modulesOnly: serializerOptions.modulesOnly,\n          runBeforeMainModule: config.serializer.getModulesRunBeforeMainModule(\n            resolvedEntryFilePath\n            // path.relative(config.projectRoot, entryFile)\n          ),\n          runModule: serializerOptions.runModule,\n          sourceMapUrl: serializerOptions.sourceMapUrl,\n          sourceUrl: serializerOptions.sourceUrl,\n          inlineSourceMap: serializerOptions.inlineSourceMap,\n          serverRoot: config.server.unstable_serverRoot ?? config.projectRoot,\n          shouldAddToIgnoreList,\n\n          // @ts-expect-error: passed to our serializer to enable non-serial return values.\n          serializerOptions,\n        }\n      );\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_done',\n      });\n\n      bundlePerfLogger?.point('serializingBundle_end');\n\n      let bundleCode: string | null = null;\n      let bundleMap: string | null = null;\n\n      // @ts-expect-error: If the output is multi-bundle...\n      if (serializerOptions.output === 'static') {\n        try {\n          const parsed = typeof bundle === 'string' ? JSON.parse(bundle) : bundle;\n\n          assert(\n            'artifacts' in parsed && Array.isArray(parsed.artifacts),\n            'Expected serializer to return an object with key artifacts to contain an array of serial assets.'\n          );\n\n          const artifacts = parsed.artifacts as SerialAsset[];\n          const assets = parsed.assets;\n\n          const bundleCode = artifacts.filter((asset) => asset.type === 'js')[0];\n          const bundleMap = artifacts.filter((asset) => asset.type === 'map')?.[0]?.source ?? '';\n\n          return {\n            numModifiedFiles: delta.reset\n              ? delta.added.size + revision.prepend.length\n              : delta.added.size + delta.modified.size + delta.deleted.size,\n            lastModifiedDate: revision.date,\n            nextRevId: revision.id,\n            bundle: bundleCode.source,\n            map: bundleMap,\n            artifacts,\n            assets,\n          };\n        } catch (error: any) {\n          throw new Error(\n            'Serializer did not return expected format. The project copy of `expo/metro-config` may be out of date. Error: ' +\n              error.message\n          );\n        }\n      }\n\n      if (typeof bundle === 'string') {\n        bundleCode = bundle;\n\n        // Create the source map in a second pass...\n        let { prepend, graph } = revision;\n        if (serializerOptions.modulesOnly) {\n          prepend = [];\n        }\n\n        bundleMap = await sourceMapStringAsync(\n          [\n            //\n            ...prepend,\n            ...this.metro._getSortedModules(graph),\n          ],\n          {\n            excludeSource: serializerOptions.excludeSource,\n            processModuleFilter: config.serializer.processModuleFilter,\n            shouldAddToIgnoreList,\n          }\n        );\n      } else {\n        bundleCode = bundle.code;\n        bundleMap = bundle.map;\n      }\n\n      return {\n        numModifiedFiles: delta.reset\n          ? delta.added.size + revision.prepend.length\n          : delta.added.size + delta.modified.size + delta.deleted.size,\n        lastModifiedDate: revision.date,\n        nextRevId: revision.id,\n        bundle: bundleCode,\n        map: bundleMap,\n      };\n    } catch (error) {\n      // Mark the error so we know how to format and return it later.\n      // @ts-expect-error\n      error[IS_METRO_BUNDLE_ERROR_SYMBOL] = true;\n\n      this.metro._reporter.update({\n        buildID: getBuildID(buildNumber),\n        type: 'bundle_build_failed',\n      });\n\n      throw error;\n    }\n  }\n\n  private getMetroSerializer() {\n    return (\n      this.metro?._config?.serializer.customSerializer ||\n      ((entryPoint, preModules, graph, options) =>\n        bundleToString(baseJSBundle(entryPoint, preModules, graph, options)).code)\n    );\n  }\n\n  private getMetroRevision(\n    resolvedEntryFilePath: string,\n    {\n      graphOptions,\n      transformOptions,\n      resolverOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n      graphOptions: {\n        shallow: boolean;\n        lazy: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'Metro server must be running to bundle directly.');\n    const config = this.metro._config;\n\n    const graphId = getGraphId(resolvedEntryFilePath, transformOptions, {\n      unstable_allowRequireContext: config.transformer.unstable_allowRequireContext,\n      resolverOptions,\n      shallow: graphOptions.shallow,\n      lazy: graphOptions.lazy,\n    });\n    return this.metro.getBundler().getRevisionByGraphId(graphId);\n  }\n\n  private async resolveRelativePathAsync(\n    moduleId: string,\n    {\n      resolverOptions,\n      transformOptions,\n    }: {\n      transformOptions: TransformInputOptions;\n      resolverOptions: {\n        customResolverOptions: CustomResolverOptions;\n        dev: boolean;\n      };\n    }\n  ) {\n    assert(this.metro, 'cannot invoke resolveRelativePathAsync without metro instance');\n    return await this.metro._resolveRelativePath(convertPathToModuleSpecifier(moduleId), {\n      relativeTo: 'server',\n      resolverOptions,\n      transformOptions,\n    });\n  }\n}\n\nfunction getBuildID(buildNumber: number): string {\n  return buildNumber.toString(36);\n}\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n\nasync function sourceMapStringAsync(\n  modules: readonly import('metro/src/DeltaBundler/types').Module<any>[],\n  options: SourceMapGeneratorOptions\n): Promise<string> {\n  return (await sourceMapGeneratorNonBlocking(modules, options)).toString(undefined, {\n    excludeSource: options.excludeSource,\n  });\n}\n\nfunction unique<T>(array: T[]): T[] {\n  return Array.from(new Set(array));\n}\n"], "names": ["MetroBundlerDevServer", "debug", "require", "EXPO_GO_METRO_PORT", "DEV_CLIENT_METRO_PORT", "BundlerDevServer", "metro", "hmrServer", "ssrHmrClients", "Map", "name", "resolvePortAsync", "options", "port", "devClient", "Number", "process", "env", "RCT_METRO_PORT", "getFreePortAsync", "exportExpoRouterApiRoutesAsync", "includeSourceMaps", "outputDir", "prerenderManifest", "platform", "routerRoot", "instanceMetroOptions", "assert", "appDir", "path", "join", "projectRoot", "manifest", "getExpoRouterRoutesManifestAsync", "files", "rscPath", "isReactServerComponentsEnabled", "apiRoutes", "find", "route", "page", "startsWith", "push", "file", "resolveFrom", "namedRegex", "routeKeys", "rsc", "filepath", "isAbsolute", "contents", "bundleApiRoute", "artifactFilename", "convertPathToModuleSpecifier", "relative", "replace", "src", "map", "artifactBasename", "encodeURIComponent", "basename", "parsedMap", "JSON", "parse", "set", "stringify", "version", "sources", "source", "sourcesContent", "Array", "length", "fill", "names", "mappings", "apiRouteId", "targetDomain", "htmlRoutes", "exp", "getConfig", "fetchManifest", "extra", "router", "platformRoutes", "as<PERSON><PERSON>", "CommandError", "getServerManifestAsync", "getBuildTimeServerManifestAsync", "getManifest", "ssrLoadModule", "environment", "isReactServerRoutesEnabled", "serverManifest", "htmlManifest", "getStaticRenderFunctionAsync", "url", "getDevServerUrlOrAssert", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preserveApiRoutes", "renderAsync", "URL", "getStaticResourcesAsync", "mainModuleName", "clientBoundaries", "mode", "minify", "isExporting", "baseUrl", "reactCompiler", "asyncRoutes", "resolvedMainModuleName", "resolveMainModuleName", "metroImportAsArtifactsAsync", "splitChunks", "EXPO_NO_BUNDLE_SPLITTING", "serializerIncludeMaps", "lazy", "shouldEnableAsyncImports", "bytecode", "getStaticPageAsync", "pathname", "devBundleUrlPathname", "createBundleUrlPath", "bundleStaticHtml", "location", "artifacts", "resources", "staticHtml", "Promise", "all", "content", "serializeHtmlWithAssets", "template", "devBundleUrl", "hydrate", "EXPO_WEB_DEV_HYDRATE", "filePath", "specificOptions", "extras", "res", "ssrLoadModuleContents", "hot", "serverRoot", "getMetroServerRoot", "relativePath", "filename", "setupHmr", "evalMetroAndWrapFunctions", "results", "serializerOutput", "assets", "metroLoadModuleContents", "extraOptions", "opts", "inlineSourceMap", "engine", "expoBundleOptions", "getMetroDirectBundleOptions", "resolverOptions", "customResolverOptions", "dev", "transformOptions", "type", "unstable_transformProfile", "customTransformOptions", "Object", "create", "runtimeBytecodeVersion", "resolvedEntryFilePath", "resolveRelativePathAsync", "createBundleUrlOsPath", "_bundleDirectAsync", "graphOptions", "shallow", "serializerOptions", "modulesOnly", "runModule", "sourceUrl", "sourceMapUrl", "bundle", "rest", "scriptContents", "wrapBundle", "cachedSourceMaps", "nativeExportBundleAsync", "singlePageReactServerComponentExportAsync", "legacySinglePageExportBundleAsync", "getReactServerReferences", "unique", "filter", "a", "artifact", "metadata", "reactServerReferences", "ref", "fileURLToFilePath", "flat", "Boolean", "reactClientReferences", "serverActionReferencesInServer", "cssModules", "rsc<PERSON><PERSON><PERSON>", "getExpoRouterClientReferencesAsync", "domRoot", "processClientBoundaries", "newReactServerReferences", "Error", "allKnownReactServerReferences", "nestedClientBoundaries", "exportServerActionsAsync", "entryPoints", "hasUniqueClientBoundaries", "some", "boundary", "includes", "concat", "clientBoundariesAsOpaqueIds", "toPosixPath", "moduleIdToSplitBundle", "paths", "values", "reduce", "acc", "ssrManifest", "keys", "for<PERSON>ach", "exportRoutesAsync", "fromEntries", "from", "entries", "key", "value", "output", "watchEnvironmentVariables", "instance", "envFiles", "runtimeEnv", "getFiles", "NODE_ENV", "fileName", "observeFileChanges", "server", "load", "force", "startImplementationAsync", "urlCreator", "getUrlCreator", "config", "skipSDKVersionRequirement", "experiments", "reactServerComponentRoutes", "reactServerFunctions", "isReactServerActionsOnlyEnabled", "useServerRendering", "web", "hasApiRoutes", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getRouterDirectoryModuleIdWithManifest", "origin", "config<PERSON><PERSON>", "dynamicConfigPath", "staticConfigPath", "configFileName", "parsedOptions", "maxWorkers", "resetCache", "resetDevServer", "EXPO_DEV_SERVER_ORIGIN", "middleware", "messageSocket", "instantiateMetroAsync", "manifestMiddleware", "getManifestMiddlewareAsync", "prependMiddleware", "ContextModuleSourceMapsMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "use", "InterstitialPageMiddleware", "scheme", "ReactDevToolsPageMiddleware", "DevToolsPluginMiddleware", "devToolsPluginManager", "deepLinkMiddleware", "RuntimeRedirectMiddleware", "getLocation", "runtime", "constructDevClientUrl", "constructUrl", "domComponent<PERSON><PERSON><PERSON>", "createDomComponentsMiddleware", "metroRoot", "CreateFileMiddleware", "isTargetingWeb", "ServeStaticMiddleware", "FaviconMiddleware", "observeAnyFileChanges", "events", "invalidateApiRouteCache", "hasWarnedAboutApiRoutes", "event", "isApiRouteConvention", "warnInvalidWebOutput", "bindRSCDevModuleInjectionHandler", "rscMiddleware", "createServerComponentsMiddleware", "bind", "ssrLoadModuleArtifacts", "useClientRouter", "createModuleId", "_createModuleId", "onReloadRscEvent", "HistoryFallbackMiddleware", "internal", "createRouteHandlerMiddleware", "functionFilePath", "ssrImportApiRoute", "html", "getSingleHtmlTemplateAsync", "originalClose", "close", "callback", "err", "assertMetroPrivateServer", "host", "protocol", "registerSsrHmrAsync", "onReload", "has", "sendFn", "message", "data", "String", "update", "body", "isInitialUpdate", "added", "modified", "deleted", "hasUpdate", "globalThis", "__c", "allModuleIds", "Set", "m", "module", "platforms", "moduleId", "match", "Log", "error", "_bundler", "_revisionsByGraphId", "client", "onClientConnect", "optedIntoHMR", "_registerEntryPoint", "waitForTypeScriptAsync", "resolve", "off", "metroWatchTypeScriptFiles", "tsconfig", "throttle", "eventTypes", "TypeScriptProjectPrerequisite", "req", "bootstrapAsync", "log", "chalk", "red", "exception", "startTypeScriptServices", "startTypescriptTypeGenerationAsync", "getConfigModuleIds", "pendingRouteOperations", "get", "bundleAsync", "undefined", "apiRoute", "evalMetroNoHandling", "htmlServerError", "getErrorOverlayHtmlAsync", "Response", "status", "headers", "internalError", "clear", "__expo_rsc_inject_module", "sendClientModule", "code", "id", "broadcastMessage", "toString", "_config", "buildNumber", "getNewBuildNumber", "bundlePerfLogger", "unstable_perfLoggerFactory", "onProgress", "transformedFileCount", "totalFileCount", "_reporter", "buildID", "getBuildID", "revPromise", "getMetroRevision", "point", "annotate", "bool", "initial_build", "bundleDetails", "bundleType", "entryFile", "isPrefetch", "delta", "revision", "props", "getBundler", "initializeGraph", "updateGraph", "int", "graph_node_count", "graph", "dependencies", "size", "shouldAddToIgnoreList", "_shouldAddModuleToIgnoreList", "serializer", "getMetroSerializer", "prepend", "asyncRequireModulePath", "_resolveRelativePath", "transformer", "relativeTo", "processModuleFilter", "getRunModuleStatement", "includeAsyncPaths", "runBeforeMainModule", "getModulesRunBeforeMainModule", "unstable_serverRoot", "bundleCode", "bundleMap", "parsed", "isArray", "asset", "numModifiedFiles", "reset", "lastModifiedDate", "date", "nextRevId", "sourceMapStringAsync", "_getSortedModules", "excludeSource", "IS_METRO_BUNDLE_ERROR_SYMBOL", "customSerializer", "entryPoint", "preModules", "bundleToString", "baseJSBundle", "graphId", "getGraphId", "unstable_allowRequireContext", "getRevisionByGraphId", "str", "modules", "sourceMapGeneratorNonBlocking", "array"], "mappings": "AAAA;;;;;CAKC,GACD;;;;+BAgGa<PERSON>,uBAAqB;;aAArBA,qBAAqB;;;yBAhGR,cAAc;;;;;;;yBACL,oBAAoB;;;;;;;+DAC3B,WAAW;;;;;;;8DAEpB,QAAQ;;;;;;;8DACT,OAAO;;;;;;;8DAEA,iDAAiD;;;;;;;yBAInE,uDAAuD;;;;;;;8DAInC,8BAA8B;;;;;;;8DAClC,0BAA0B;;;;;;;8DAGhC,MAAM;;;;;;;8DACC,cAAc;;;;;;kDAK/B,oCAAoC;6CACE,+BAA+B;qCAClB,uBAAuB;kCAC3C,oBAAoB;qCACa,uBAAuB;oCACjC,sBAAsB;2CACzC,6BAA6B;wBAMhE,UAAU;+BACuB,iBAAiB;qDACC,uCAAuC;qBAE7E,cAAc;sBACd,oBAAoB;wBACX,uBAAuB;0BACxB,yBAAyB;sBACpB,qBAAqB;kCACmB,qBAAqB;0CAKvF,6BAA6B;mDACc,iDAAiD;sCAC9D,oCAAoC;0CAChC,wCAAwC;yCACnC,uCAAuC;mCACnD,iCAAiC;2CACzB,yCAAyC;4CACxC,0CAA0C;oCAC/C,kCAAkC;6CAC5B,2CAA2C;2CAC7C,yCAAyC;uCAC7C,qCAAqC;8BAUpE,4BAA4B;2BACD,yBAAyB;+CACR,kDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrG,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,qDAAqD,GACrD,MAAMC,kBAAkB,GAAG,IAAI,AAAC;AAEhC,iGAAiG,GACjG,MAAMC,qBAAqB,GAAG,IAAI,AAAC;AAE5B,MAAMJ,qBAAqB,SAASK,iBAAgB,iBAAA;IACzD,AAAQC,KAAK,GAA8B,IAAI,CAAC;IAChD,AAAQC,SAAS,GAA0B,IAAI,CAAC;IAChD,AAAQC,aAAa,GAAgC,IAAIC,GAAG,EAAE,CAAC;QAI3DC,IAAI,GAAW;QACjB,OAAO,OAAO,CAAC;IACjB;UAEMC,gBAAgB,CAACC,OAAqC,GAAG,EAAE,EAAmB;QAClF,MAAMC,IAAI,GACR,yEAAyE;QACzED,OAAO,CAACC,IAAI,IACZ,8DAA8D;QAC9D,CAACD,OAAO,CAACE,SAAS,GAEdC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,IAAId,qBAAqB,GAE3D,MAAMe,IAAAA,KAAgB,iBAAA,EAAChB,kBAAkB,CAAC,CAAC,AAAC;QAElD,OAAOU,IAAI,CAAC;IACd;UAEMO,8BAA8B,CAAC,EACnCC,iBAAiB,CAAA,EACjBC,SAAS,CAAA,EACTC,iBAAiB,CAAA,EACjBC,QAAQ,CAAA,EAOT,EAAoF;QACnF,MAAM,EAAEC,UAAU,CAAA,EAAE,GAAG,IAAI,CAACC,oBAAoB,AAAC;QACjDC,IAAAA,OAAM,EAAA,QAAA,EACJF,UAAU,IAAI,IAAI,EAClB,2EAA2E,CAC5E,CAAC;QAEF,MAAMG,MAAM,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEN,UAAU,CAAC,AAAC;QACvD,MAAMO,QAAQ,GAAG,MAAM,IAAI,CAACC,gCAAgC,CAAC;YAAEL,MAAM;SAAE,CAAC,AAAC;QAEzE,MAAMM,KAAK,GAAmB,IAAIzB,GAAG,EAAE,AAAC;QAExC,yBAAyB;QACzB,MAAM0B,OAAO,GAAG,mBAAmB,AAAC;QAEpC,IACE,IAAI,CAACC,8BAA8B,IACnC,2DAA2D;QAC3D,CAACJ,QAAQ,CAACK,SAAS,CAACC,IAAI,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,EACvE;YACAxC,KAAK,CAAC,mCAAmC,EAAEkC,OAAO,CAAC,CAAC;YACpD,wEAAwE;YACxEH,QAAQ,CAACK,SAAS,CAACK,IAAI,CAAC;gBACtBC,IAAI,EAAEC,IAAAA,YAAW,EAAA,QAAA,EAAC,IAAI,CAACb,WAAW,EAAE,2CAA2C,CAAC;gBAChFS,IAAI,EAAEL,OAAO;gBACbU,UAAU,EAAE,mCAAmC;gBAC/CC,SAAS,EAAE;oBAAEC,GAAG,EAAE,KAAK;iBAAE;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,KAAK,MAAMR,KAAK,IAAIP,QAAQ,CAACK,SAAS,CAAE;YACtC,MAAMW,QAAQ,GAAGnB,KAAI,EAAA,QAAA,CAACoB,UAAU,CAACV,KAAK,CAACI,IAAI,CAAC,GAAGJ,KAAK,CAACI,IAAI,GAAGd,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,MAAM,EAAEW,KAAK,CAACI,IAAI,CAAC,AAAC;YAC1F,MAAMO,QAAQ,GAAG,MAAM,IAAI,CAACC,cAAc,CAACH,QAAQ,EAAE;gBAAExB,QAAQ;aAAE,CAAC,AAAC;YAEnE,MAAM4B,gBAAgB,GACpBb,KAAK,CAACC,IAAI,KAAKL,OAAO,GAElBkB,IAAAA,aAA4B,6BAAA,EAACxB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACR,SAAS,EAAE,GAAG,GAAGa,OAAO,GAAG,KAAK,CAAC,CAAC,GACzEkB,IAAAA,aAA4B,6BAAA,EAC1BxB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACR,SAAS,EAAEO,KAAI,EAAA,QAAA,CAACyB,QAAQ,CAAC1B,MAAM,EAAEoB,QAAQ,CAACO,OAAO,eAAe,KAAK,CAAC,CAAC,CAAC,CACnF,AAAC;YAER,IAAIL,QAAQ,EAAE;gBACZ,IAAIM,GAAG,GAAGN,QAAQ,CAACM,GAAG,AAAC;gBAEvB,IAAInC,iBAAiB,IAAI6B,QAAQ,CAACO,GAAG,EAAE;oBACrC,+DAA+D;oBAC/D,uHAAuH;oBACvH,wDAAwD;oBACxD,MAAMC,gBAAgB,GAAGC,kBAAkB,CAAC9B,KAAI,EAAA,QAAA,CAAC+B,QAAQ,CAACR,gBAAgB,CAAC,GAAG,MAAM,CAAC,AAAC;oBACtFI,GAAG,GAAGA,GAAG,CAACD,OAAO,+BAEf,CAAC,qBAAqB,EAAEG,gBAAgB,CAAC,CAAC,CAC3C,CAAC;oBAEF,MAAMG,SAAS,GACb,OAAOX,QAAQ,CAACO,GAAG,KAAK,QAAQ,GAAGK,IAAI,CAACC,KAAK,CAACb,QAAQ,CAACO,GAAG,CAAC,GAAGP,QAAQ,CAACO,GAAG,AAAC;oBAC7EvB,KAAK,CAAC8B,GAAG,CAACZ,gBAAgB,GAAG,MAAM,EAAE;wBACnCF,QAAQ,EAAEY,IAAI,CAACG,SAAS,CAAC;4BACvBC,OAAO,EAAEL,SAAS,CAACK,OAAO;4BAC1BC,OAAO,EAAEN,SAAS,CAACM,OAAO,CAACV,GAAG,CAAC,CAACW,MAAc,GAAK;gCACjDA,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC3B,UAAU,CAAC,IAAI,CAACV,WAAW,CAAC,GAC7DF,KAAI,EAAA,QAAA,CAACyB,QAAQ,CAAC,IAAI,CAACvB,WAAW,EAAEqC,MAAM,CAAC,GACvCA,MAAM,CAAC;gCACb,OAAOf,IAAAA,aAA4B,6BAAA,EAACe,MAAM,CAAC,CAAC;4BAC9C,CAAC,CAAC;4BACFC,cAAc,EAAE,IAAIC,KAAK,CAACT,SAAS,CAACM,OAAO,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;4BAC9DC,KAAK,EAAEZ,SAAS,CAACY,KAAK;4BACtBC,QAAQ,EAAEb,SAAS,CAACa,QAAQ;yBAC7B,CAAC;wBACFC,UAAU,EAAEpC,KAAK,CAACC,IAAI;wBACtBoC,YAAY,EAAE,QAAQ;qBACvB,CAAC,CAAC;gBACL,CAAC;gBACD1C,KAAK,CAAC8B,GAAG,CAACZ,gBAAgB,EAAE;oBAC1BF,QAAQ,EAAEM,GAAG;oBACbmB,UAAU,EAAEpC,KAAK,CAACC,IAAI;oBACtBoC,YAAY,EAAE,QAAQ;iBACvB,CAAC,CAAC;YACL,CAAC;YACD,0DAA0D;YAC1DrC,KAAK,CAACI,IAAI,GAAGS,gBAAgB,CAAC;QAChC,CAAC;QAED,OAAO;YACLpB,QAAQ,EAAE;gBACR,GAAGA,QAAQ;gBACX6C,UAAU,EAAEtD,iBAAiB,CAACsD,UAAU;aACzC;YACD3C,KAAK;SACN,CAAC;IACJ;UAEMD,gCAAgC,CAAC,EAAEL,MAAM,CAAA,EAAsB,EAAE;YAIhEkD,GAAS;QAHd,6BAA6B;QAC7B,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAChD,WAAW,CAAC,AAAC;QAC5C,MAAMC,QAAQ,GAAG,MAAMgD,IAAAA,oBAAa,cAAA,EAAC,IAAI,CAACjD,WAAW,EAAE;YACrD,GAAG+C,CAAAA,GAAS,GAATA,GAAG,CAACG,KAAK,SAAQ,GAAjBH,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEI,MAAM,SAAA,GAAjBJ,KAAAA,CAAiB,QAAEK,cAAc,AAAhB;YACpBC,MAAM,EAAE,IAAI;YACZxD,MAAM;SACP,CAAC,AAAC;QAEH,IAAI,CAACI,QAAQ,EAAE;YACb,MAAM,IAAIqD,OAAY,aAAA,CACpB,6BAA6B,EAC7B,yDAAyD,CAC1D,CAAC;QACJ,CAAC;QAED,OAAOrD,QAAQ,CAAC;IAClB;UAEMsD,sBAAsB,GAGzB;QACD,4GAA4G;QAC5G,MAAM,EAAEC,+BAA+B,CAAA,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAM,IAAI,CAACC,aAAa,CAE/E,+CAA+C,EAAE;YACjD,iGAAiG;YACjGC,WAAW,EAAE,IAAI,CAACC,0BAA0B,GAAG,cAAc,GAAG,MAAM;SACvE,CAAC,AAAC;QAEH,OAAO;YACLC,cAAc,EAAE,MAAML,+BAA+B,EAAE;YACvDM,YAAY,EAAE,MAAML,WAAW,EAAE;SAClC,CAAC;IACJ;UAEMM,4BAA4B,GAI/B;YAkB4DhB,GAAS;QAjBtE,MAAMiB,GAAG,GAAG,IAAI,CAACC,uBAAuB,EAAE,AAAC;QAE3C,MAAM,EAAEC,gBAAgB,CAAA,EAAET,WAAW,CAAA,EAAED,+BAA+B,CAAA,EAAE,GACtE,MAAM,IAAI,CAACE,aAAa,CACtB,4BAA4B,EAC5B;YACE,gGAAgG;YAChG,uEAAuE;YACvEC,WAAW,EAAE,MAAM;SACpB,CACF,AAAC;QAEJ,MAAM,EAAEZ,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAChD,WAAW,CAAC,AAAC;QAE5C,OAAO;YACL6D,cAAc,EAAE,MAAML,+BAA+B,EAAE;YACvD,+BAA+B;YAC/BvD,QAAQ,EAAE,MAAMwD,WAAW,CAAC;gBAAEU,iBAAiB,EAAE,KAAK;gBAAE,GAAGpB,CAAAA,GAAS,GAATA,GAAG,CAACG,KAAK,SAAQ,GAAjBH,KAAAA,CAAiB,GAAjBA,GAAS,CAAEI,MAAM;aAAE,CAAC;YAC/E,gCAAgC;YAChC,MAAMiB,WAAW,EAACtE,IAAY,EAAE;gBAC9B,OAAO,MAAMoE,gBAAgB,CAAC,IAAIG,GAAG,CAACvE,IAAI,EAAEkE,GAAG,CAAC,CAAC,CAAC;YACpD,CAAC;SACF,CAAC;IACJ;UAEMM,uBAAuB,CAAC,EAC5BhF,iBAAiB,CAAA,EACjBiF,cAAc,CAAA,EACdC,gBAAgB,EAAG,IAAI,CAAC7E,oBAAoB,CAAC6E,gBAAgB,IAAI,EAAE,CAAA,EACnE/E,QAAQ,EAAG,KAAK,CAAA,EAMjB,GAAG,EAAE,EAAE;QACN,MAAM,EAAEgF,IAAI,CAAA,EAAEC,MAAM,CAAA,EAAEC,WAAW,CAAA,EAAEC,OAAO,CAAA,EAAEC,aAAa,CAAA,EAAEnF,UAAU,CAAA,EAAEoF,WAAW,CAAA,EAAE,GAClF,IAAI,CAACnF,oBAAoB,AAAC;QAC5BC,IAAAA,OAAM,EAAA,QAAA,EACJ6E,IAAI,IAAI,IAAI,IACVE,WAAW,IAAI,IAAI,IACnBC,OAAO,IAAI,IAAI,IACflF,UAAU,IAAI,IAAI,IAClBmF,aAAa,IAAI,IAAI,IACrBC,WAAW,IAAI,IAAI,EACrB,oEAAoE,CACrE,CAAC;QAEF,MAAMC,sBAAsB,GAC1BR,cAAc,IAAI,IAAI,GAAGS,IAAAA,mBAAqB,sBAAA,EAAC,IAAI,CAAChF,WAAW,EAAE;YAAEP,QAAQ;SAAE,CAAC,AAAC;QACjF,OAAO,MAAM,IAAI,CAACwF,2BAA2B,CAACF,sBAAsB,EAAE;YACpEG,WAAW,EAAEP,WAAW,IAAI,CAACzF,KAAG,IAAA,CAACiG,wBAAwB;YACzD1F,QAAQ;YACRgF,IAAI;YACJC,MAAM;YACNf,WAAW,EAAE,QAAQ;YACrByB,qBAAqB,EAAE9F,iBAAiB;YACxCiF,cAAc,EAAEQ,sBAAsB;YACtCM,IAAI,EAAEC,IAAAA,aAAwB,yBAAA,EAAC,IAAI,CAACtF,WAAW,CAAC;YAChD8E,WAAW;YACXF,OAAO;YACPD,WAAW;YACXjF,UAAU;YACV8E,gBAAgB;YAChBK,aAAa;YACbU,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL;UAEcC,kBAAkB,CAACC,QAAgB,EAAE;QACjD,MAAM,EAAEhB,IAAI,CAAA,EAAEE,WAAW,CAAA,EAAEH,gBAAgB,CAAA,EAAEI,OAAO,CAAA,EAAEC,aAAa,CAAA,EAAEnF,UAAU,CAAA,EAAEoF,WAAW,CAAA,EAAE,GAC5F,IAAI,CAACnF,oBAAoB,AAAC;QAC5BC,IAAAA,OAAM,EAAA,QAAA,EACJ6E,IAAI,IAAI,IAAI,IACVE,WAAW,IAAI,IAAI,IACnBC,OAAO,IAAI,IAAI,IACfC,aAAa,IAAI,IAAI,IACrBnF,UAAU,IAAI,IAAI,IAClBoF,WAAW,IAAI,IAAI,EACrB,+DAA+D,CAChE,CAAC;QACF,MAAMrF,QAAQ,GAAG,KAAK,AAAC;QAEvB,MAAMiG,oBAAoB,GAAGC,IAAAA,aAAmB,oBAAA,EAAC;YAC/CT,WAAW,EAAEP,WAAW,IAAI,CAACzF,KAAG,IAAA,CAACiG,wBAAwB;YACzD1F,QAAQ;YACRgF,IAAI;YACJd,WAAW,EAAE,QAAQ;YACrBkB,aAAa;YACbN,cAAc,EAAES,IAAAA,mBAAqB,sBAAA,EAAC,IAAI,CAAChF,WAAW,EAAE;gBAAEP,QAAQ;aAAE,CAAC;YACrE4F,IAAI,EAAEC,IAAAA,aAAwB,yBAAA,EAAC,IAAI,CAACtF,WAAW,CAAC;YAChD4E,OAAO;YACPD,WAAW;YACXG,WAAW;YACXpF,UAAU;YACV8E,gBAAgB;YAChBe,QAAQ,EAAE,KAAK;SAChB,CAAC,AAAC;QAEH,MAAMK,gBAAgB,GAAG,UAA6B;YACpD,MAAM,EAAE1B,gBAAgB,CAAA,EAAE,GAAG,MAAM,IAAI,CAACR,aAAa,CAEnD,4BAA4B,EAAE;gBAC9B,gGAAgG;gBAChG,uEAAuE;gBACvEC,WAAW,EAAE,MAAM;gBACnBe,MAAM,EAAE,KAAK;gBACbC,WAAW;gBACXlF,QAAQ;aACT,CAAC,AAAC;YAEH,MAAMoG,QAAQ,GAAG,IAAIxB,GAAG,CAACoB,QAAQ,EAAE,IAAI,CAACxB,uBAAuB,EAAE,CAAC,AAAC;YACnE,OAAO,MAAMC,gBAAgB,CAAC2B,QAAQ,CAAC,CAAC;QAC1C,CAAC,AAAC;QAEF,MAAM,CAAC,EAAEC,SAAS,EAAEC,SAAS,CAAA,EAAE,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;YAC/D,IAAI,CAAC5B,uBAAuB,CAAC;gBAC3BE,gBAAgB,EAAE,EAAE;aACrB,CAAC;YACFoB,gBAAgB,EAAE;SACnB,CAAC,AAAC;QACH,MAAMO,OAAO,GAAGC,IAAAA,cAAuB,wBAAA,EAAC;YACtCzB,WAAW;YACXoB,SAAS;YACTM,QAAQ,EAAEL,UAAU;YACpBM,YAAY,EAAEZ,oBAAoB;YAClCd,OAAO;YACP2B,OAAO,EAAErH,KAAG,IAAA,CAACsH,oBAAoB;SAClC,CAAC,AAAC;QACH,OAAO;YACLL,OAAO;YACPJ,SAAS;SACV,CAAC;IACJ;IAEA,kCAAkC;IAClC,AAAQpG,oBAAoB,GAA8B,EAAE,CAAC;IAE7D,AAAQ+D,aAAa,GAAsB,OACzC+C,QAAQ,EACRC,eAAe,GAAG,EAAE,EACpBC,MAAM,GAAG,EAAE,GACR;QACH,MAAMC,GAAG,GAAG,MAAM,IAAI,CAACC,qBAAqB,CAACJ,QAAQ,EAAEC,eAAe,CAAC,AAAC;QAExE,IACE,mFAAmF;QACnFC,MAAM,CAACG,GAAG,IACV,IAAI,CAACnH,oBAAoB,CAACgF,WAAW,KAAK,IAAI,EAC9C;YACA,mBAAmB;YACnB,MAAMoC,UAAU,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAAC,IAAI,CAAChH,WAAW,CAAC,AAAC;YACxD,MAAMiH,YAAY,GAAGnH,KAAI,EAAA,QAAA,CAACyB,QAAQ,CAACwF,UAAU,EAAEH,GAAG,CAACM,QAAQ,CAAC,AAAC;YAC7D,MAAMlD,GAAG,GAAG,IAAIK,GAAG,CAAC4C,YAAY,EAAE,IAAI,CAAChD,uBAAuB,EAAE,CAAC,AAAC;YAClE,IAAI,CAACkD,QAAQ,CAACnD,GAAG,CAAC,CAAC;QACrB,CAAC;QAED,OAAOoD,IAAAA,yBAAyB,0BAAA,EAC9B,IAAI,CAACpH,WAAW,EAChB4G,GAAG,CAACnF,GAAG,EACPmF,GAAG,CAACM,QAAQ,EACZR,eAAe,CAAC/B,WAAW,IAAI,IAAI,CAAChF,oBAAoB,CAACgF,WAAW,AAAC,CACtE,CAAC;IACJ,CAAC,CAAC;UAEYM,2BAA2B,CACvCwB,QAAgB,EAChBC,eAAoE,GAAG,EAAE,EACzE;QACA,MAAMW,OAAO,GAAG,MAAM,IAAI,CAACR,qBAAqB,CAACJ,QAAQ,EAAE;YACzDa,gBAAgB,EAAE,QAAQ;YAC1B,GAAGZ,eAAe;SACnB,CAAC,AAAC;QAEH,mEAAmE;QACnE,IAAIW,OAAO,CAACvB,SAAS,IAAIuB,OAAO,CAACE,MAAM,EAAE;YACvC,OAAO;gBACLzB,SAAS,EAAEuB,OAAO,CAACvB,SAAS;gBAC5ByB,MAAM,EAAEF,OAAO,CAACE,MAAM;gBACtB9F,GAAG,EAAE4F,OAAO,CAAC5F,GAAG;gBAChByF,QAAQ,EAAEG,OAAO,CAACH,QAAQ;gBAC1BxF,GAAG,EAAE2F,OAAO,CAAC3F,GAAG;aACjB,CAAC;QACJ,CAAC;QACD,MAAM,IAAI4B,OAAY,aAAA,CAAC,2BAA2B,GAAG+D,OAAO,CAAC,CAAC;IAChE;UAEcG,uBAAuB,CACnCf,QAAgB,EAChBC,eAAiC,EACjCe,YAGC,GAAG,EAAE,EACN;QACA,MAAM,EAAE7C,OAAO,CAAA,EAAE,GAAG,IAAI,CAACjF,oBAAoB,AAAC;QAC9CC,IAAAA,OAAM,EAAA,QAAA,EAACgF,OAAO,IAAI,IAAI,EAAE,oEAAoE,CAAC,CAAC;QAE9F,MAAM8C,IAAI,GAAqB;YAC7B,4DAA4D;YAC5D,4BAA4B;YAC5BrC,IAAI,EAAE,KAAK;YACXP,WAAW,EAAE,KAAK;YAClB6C,eAAe,EAAE,KAAK;YACtBC,MAAM,EAAE,QAAQ;YAChBlD,MAAM,EAAE,KAAK;YACb,mBAAmB;YACnB,kCAAkC;YAClCf,WAAW,EAAE,MAAM;YACnB,mBAAmB;YACnB,uBAAuB;YACvB,EAAE;YACF,GAAG,IAAI,CAAChE,oBAAoB;YAC5BiF,OAAO;YACP,cAAc;YACd,eAAe;YACf,GAAG8B,eAAe;SACnB,AAAC;QAEF,MAAMmB,iBAAiB,GAAGC,IAAAA,aAA2B,4BAAA,EAACJ,IAAI,CAAC,AAAC;QAE5D,MAAMK,eAAe,GAAG;YACtBC,qBAAqB,EAAEH,iBAAiB,CAACG,qBAAqB,IAAI,EAAE;YACpEC,GAAG,EAAEJ,iBAAiB,CAACI,GAAG,IAAI,IAAI;SACnC,AAAC;QAEF,MAAMC,gBAAgB,GAA0B;YAC9CD,GAAG,EAAEJ,iBAAiB,CAACI,GAAG,IAAI,IAAI;YAClCnB,GAAG,EAAE,IAAI;YACTpC,MAAM,EAAEmD,iBAAiB,CAACnD,MAAM,IAAI,KAAK;YACzCyD,IAAI,EAAE,QAAQ;YACdC,yBAAyB,EACvBX,YAAY,CAACW,yBAAyB,IACtCP,iBAAiB,CAACO,yBAAyB,IAC3C,SAAS;YACXC,sBAAsB,EAAER,iBAAiB,CAACQ,sBAAsB,IAAIC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;YACvF9I,QAAQ,EAAEoI,iBAAiB,CAACpI,QAAQ,IAAI,KAAK;YAC7C,8GAA8G;YAC9G+I,sBAAsB,EAAEX,iBAAiB,CAACW,sBAAsB;SACjE,AAAC;QAEF,MAAMC,qBAAqB,GAAG,MAAM,IAAI,CAACC,wBAAwB,CAACjC,QAAQ,EAAE;YAC1EsB,eAAe;YACfG,gBAAgB;SACjB,CAAC,AAAC;QAEH,qIAAqI;QACrI,MAAMhB,QAAQ,GAAGyB,IAAAA,aAAqB,sBAAA,EAAC;YACrC,GAAGjB,IAAI;YACPnD,cAAc,EAAEkE,qBAAqB;SACtC,CAAC,AAAC;QAEH,wIAAwI;QACxI,MAAMpB,OAAO,GAAG,MAAM,IAAI,CAACuB,kBAAkB,CAACH,qBAAqB,EAAE;YACnEI,YAAY,EAAE;gBACZxD,IAAI,EAAEwC,iBAAiB,CAACxC,IAAI,IAAI,KAAK;gBACrCyD,OAAO,EAAEjB,iBAAiB,CAACiB,OAAO,IAAI,KAAK;aAC5C;YACDf,eAAe;YACfgB,iBAAiB,EAAE;gBACjB,GAAGlB,iBAAiB,CAACkB,iBAAiB;gBAEtCpB,eAAe,EAAEE,iBAAiB,CAACF,eAAe,IAAI,KAAK;gBAC3DqB,WAAW,EAAEnB,iBAAiB,CAACmB,WAAW,IAAI,KAAK;gBACnDC,SAAS,EAAEpB,iBAAiB,CAACoB,SAAS,IAAI,IAAI;gBAC9C,mBAAmB;gBACnBC,SAAS,EAAErB,iBAAiB,CAACqB,SAAS;gBACtC,mBAAmB;gBACnBC,YAAY,EAAE1B,YAAY,CAAC0B,YAAY,IAAItB,iBAAiB,CAACsB,YAAY;aAC1E;YACDjB,gBAAgB;SACjB,CAAC,AAAC;QAEH,OAAO;YACL,GAAGb,OAAO;YACVH,QAAQ;SACT,CAAC;IACJ;UAEcL,qBAAqB,CACjCJ,QAAgB,EAChBC,eAA0C,GAAG,EAAE,EAC/C;QACA,MAAM,EAAE9B,OAAO,CAAA,EAAElF,UAAU,CAAA,EAAEiF,WAAW,CAAA,EAAE,GAAG,IAAI,CAAChF,oBAAoB,AAAC;QACvEC,IAAAA,OAAM,EAAA,QAAA,EACJgF,OAAO,IAAI,IAAI,IAAIlF,UAAU,IAAI,IAAI,IAAIiF,WAAW,IAAI,IAAI,EAC5D,kEAAkE,CACnE,CAAC;QAEF,MAAM+C,IAAI,GAAqB;YAC7B,4DAA4D;YAC5DnD,cAAc,EAAEjD,IAAAA,aAA4B,6BAAA,EAACmF,QAAQ,CAAC;YACtDpB,IAAI,EAAE,KAAK;YACXP,WAAW,EAAE,KAAK;YAClB6C,eAAe,EAAE,KAAK;YACtBC,MAAM,EAAE,QAAQ;YAChBlD,MAAM,EAAE,KAAK;YACba,QAAQ,EAAE,KAAK;YACf,wDAAwD;YACxD5B,WAAW,EAAE,IAAI,CAACtD,8BAA8B,GAAG,cAAc,GAAG,MAAM;YAC1EZ,QAAQ,EAAE,KAAK;YACfgF,IAAI,EAAE,aAAa;YACnB,EAAE;YACF,GAAG,IAAI,CAAC9E,oBAAoB;YAE5B,0CAA0C;YAC1CkF,aAAa,EAAE,KAAK;YACpBD,OAAO;YACPlF,UAAU;YACViF,WAAW;YAEX,GAAG+B,eAAe;SACnB,AAAC;QAEF,wIAAwI;QACxI,MAAM,EAAEQ,QAAQ,CAAA,EAAEkC,MAAM,CAAA,EAAE1H,GAAG,CAAA,EAAE,GAAG2H,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC7B,uBAAuB,CAACf,QAAQ,EAAEiB,IAAI,CAAC,AAAC;QAC9F,MAAM4B,cAAc,GAAGC,UAAU,CAACH,MAAM,CAAC,AAAC;QAE1C,IAAI1H,GAAG,EAAE;YACPxD,KAAK,CAAC,iCAAiC,EAAEgJ,QAAQ,CAAC,CAAC;YACnDsC,yBAAgB,iBAAA,CAACvH,GAAG,CAACiF,QAAQ,EAAE;gBAAElD,GAAG,EAAE,IAAI,CAAChE,WAAW;gBAAE0B,GAAG;aAAE,CAAC,CAAC;QACjE,OAAO;YACLxD,KAAK,CAAC,8BAA8B,EAAEgJ,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,OAAO;YACL,GAAGmC,IAAI;YACP5H,GAAG,EAAE6H,cAAc;YACnBpC,QAAQ;YACRxF,GAAG;SACJ,CAAC;IACJ;UAEM+H,uBAAuB,CAC3B5K,OAGC,EACDsB,KAAqB,EACrBsH,YAGC,GAAG,EAAE,EAKL;QACD,IAAI,IAAI,CAACpH,8BAA8B,EAAE;YACvC,OAAO,IAAI,CAACqJ,yCAAyC,CAAC7K,OAAO,EAAEsB,KAAK,EAAEsH,YAAY,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,IAAI,CAACkC,iCAAiC,CAAC9K,OAAO,EAAE4I,YAAY,CAAC,CAAC;IACvE;UAEciC,yCAAyC,CACrD7K,OAGC,EACDsB,KAAqB,EACrBsH,YAGC,GAAG,EAAE,EAKL;QACD,MAAMmC,wBAAwB,GAAG,CAAC9D,SAAwB,GAAe;YACvE,iEAAiE;YACjE,OAAO+D,MAAM,CACX/D,SAAS,CACNgE,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAAC5B,IAAI,KAAK,IAAI,CAAC,CAC9BzG,GAAG,CAAC,CAACsI,QAAQ;oBACZA,GAAuC;gBAAvCA,OAAAA,CAAAA,GAAuC,GAAvCA,QAAQ,CAACC,QAAQ,CAACC,qBAAqB,SAAK,GAA5CF,KAAAA,CAA4C,GAA5CA,GAAuC,CAAEtI,GAAG,CAAC,CAACyI,GAAG,GAAKC,IAAAA,iCAAiB,kBAAA,EAACD,GAAG,CAAC,CAAC,CAAA;aAAA,CAC9E,AACD,yCAAyC;aACxCE,IAAI,EAAE,CACNP,MAAM,CAACQ,OAAO,CAAC,CACnB,CAAC;QACJ,CAAC,AAAC;QAEF,wFAAwF;QACxF,IAAI,EACFC,qBAAqB,EAAE/F,gBAAgB,CAAA,EACvC0F,qBAAqB,EAAEM,8BAA8B,CAAA,EACrDC,UAAU,CAAA,IACX,GAAG,MAAM,IAAI,CAACC,WAAW,CAAEC,kCAAkC,CAC5D;YACElL,QAAQ,EAAEZ,OAAO,CAACY,QAAQ;YAC1BmL,OAAO,EAAE/L,OAAO,CAAC+L,OAAO;SACzB,EACDzK,KAAK,CACN,AAAC;QAEF,iFAAiF;QAEjF,MAAM0K,uBAAuB,GAAG,OAC9BX,qBAA+B,GAI3B;YACJhM,KAAK,CAAC,8BAA8B,EAAEsG,gBAAgB,CAAC,CAAC;YAExD,2DAA2D;YAC3D,MAAM4E,MAAM,GAAG,MAAM,IAAI,CAACO,iCAAiC,CACzD;gBACE,GAAG9K,OAAO;gBACV2F,gBAAgB;aACjB,EACDiD,YAAY,CACb,AAAC;YAEF,iEAAiE;YACjE,MAAMqD,wBAAwB,GAAGlB,wBAAwB,CAACR,MAAM,CAACtD,SAAS,CAAC,AAAC;YAE5E,IAAI,CAACgF,wBAAwB,EAAE;gBAC7B,mDAAmD;gBACnD,MAAM,IAAIC,KAAK,CACb,gFAAgF,CACjF,CAAC;YACJ,CAAC;YACD7M,KAAK,CAAC,6CAA6C,EAAE4M,wBAAwB,CAAC,CAAC;YAE/E,MAAME,6BAA6B,GAAGnB,MAAM,CAAC;mBACxCK,qBAAqB;mBACrBY,wBAAwB;aAC5B,CAAC,AAAC;YAEH,4IAA4I;YAC5I,MAAM,EAAEtG,gBAAgB,EAAEyG,sBAAsB,CAAA,EAAE,GAChD,MAAM,IAAI,CAACP,WAAW,CAAEQ,wBAAwB,CAC9C;gBACEzL,QAAQ,EAAEZ,OAAO,CAACY,QAAQ;gBAC1BmL,OAAO,EAAE/L,OAAO,CAAC+L,OAAO;gBACxBO,WAAW,EAAEH,6BAA6B;aAC3C,EACD7K,KAAK,CACN,AAAC;YAEJ,iEAAiE;YACjE,MAAMiL,yBAAyB,GAAGH,sBAAsB,CAACI,IAAI,CAC3D,CAACC,QAAQ,GAAK,CAAC9G,gBAAgB,CAAC+G,QAAQ,CAACD,QAAQ,CAAC,CACnD,AAAC;YAEF,IAAI,CAACF,yBAAyB,EAAE;gBAC9B,OAAOhC,MAAM,CAAC;YAChB,CAAC;YAEDlL,KAAK,CAAC,mDAAmD,EAAE+M,sBAAsB,CAAC,CAAC;YAEnFzG,gBAAgB,GAAGqF,MAAM,CAACrF,gBAAgB,CAACgH,MAAM,CAACP,sBAAsB,CAAC,CAAC,CAAC;YAE3E,4HAA4H;YAC5H,2DAA2D;YAC3D,OAAOJ,uBAAuB,CAACG,6BAA6B,CAAC,CAAC;QAChE,CAAC,AAAC;QAEF,MAAM5B,MAAM,GAAG,MAAMyB,uBAAuB,CAACL,8BAA8B,CAAC,AAAC;QAE7E,oEAAoE;QACpEpB,MAAM,CAACtD,SAAS,CAACnF,IAAI,IAAI8J,UAAU,CAAC,CAAC;QAErC,MAAM1D,UAAU,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAAC,IAAI,CAAChH,WAAW,CAAC,AAAC;QAExD,qDAAqD;QACrD,MAAMyL,2BAA2B,GAAGjH,gBAAgB,CAAC9C,GAAG,CAAC,CAAC4J,QAAQ,GAChE,kFAAkF;YAClFI,IAAAA,SAAW,YAAA,EAAC5L,KAAI,EAAA,QAAA,CAACyB,QAAQ,CAACwF,UAAU,EAAEuE,QAAQ,CAAC,CAAC,CACjD,AAAC;QACF,MAAMK,qBAAqB,GAAG,AAC5BvC,MAAM,CAACtD,SAAS,CACbpE,GAAG,CAAC,CAACsI,QAAQ;gBAAKA,GAAkB;YAAlBA,OAAAA,CAAAA,QAAQ,QAAU,GAAlBA,KAAAA,CAAkB,GAAlBA,CAAAA,GAAkB,GAAlBA,QAAQ,CAAEC,QAAQ,SAAA,GAAlBD,KAAAA,CAAkB,GAAlBA,GAAkB,CAAE4B,KAAK,AAAP,CAAA,IAAWtD,MAAM,CAACuD,MAAM,CAAC7B,QAAQ,CAACC,QAAQ,CAAC2B,KAAK,CAAC,CAAA;SAAA,CAAC,CACtF9B,MAAM,CAACQ,OAAO,CAAC,CACfD,IAAI,EAAE,CACTyB,MAAM,CAAC,CAACC,GAAG,EAAEH,KAAK,GAAK,CAAC;gBAAE,GAAGG,GAAG;gBAAE,GAAGH,KAAK;aAAE,CAAC,EAAE,EAAE,CAAC,AAAC;QAErD1N,KAAK,CAAC,eAAe,EAAEyN,qBAAqB,EAAEF,2BAA2B,CAAC,CAAC;QAE3E,MAAMO,WAAW,GAAG,IAAItN,GAAG,EAAkB,AAAC;QAE9C,IAAI4J,MAAM,CAAC2D,IAAI,CAACN,qBAAqB,CAAC,CAACnJ,MAAM,EAAE;YAC7CiJ,2BAA2B,CAACS,OAAO,CAAC,CAACZ,QAAQ,GAAK;gBAChD,IAAIA,QAAQ,IAAIK,qBAAqB,EAAE;oBACrCK,WAAW,CAAC/J,GAAG,CAACqJ,QAAQ,EAAEK,qBAAqB,CAACL,QAAQ,CAAC,CAAC,CAAC;gBAC7D,OAAO;oBACL,MAAM,IAAIP,KAAK,CACb,CAAC,yBAAyB,EAAEO,QAAQ,CAAC,kCAAkC,EAAEhD,MAAM,CAAC2D,IAAI,CAACN,qBAAqB,CAAC,CAAC5L,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CACzH,CAAC;gBACJ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,OAAO;YACL,8CAA8C;YAC9C7B,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAC1BuN,2BAA2B,CAACS,OAAO,CAAC,CAACZ,QAAQ,GAAK;gBAChD,mBAAmB;gBACnBU,WAAW,CAAC/J,GAAG,CAACqJ,QAAQ,EAAE,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAACZ,WAAW,CAAEyB,iBAAiB,CACvC;YACE1M,QAAQ,EAAEZ,OAAO,CAACY,QAAQ;YAC1BuM,WAAW;SACZ,EACD7L,KAAK,CACN,CAAC;QAEF,4GAA4G;QAC5GA,KAAK,CAAC8B,GAAG,CAAC,CAAC,UAAU,EAAEpD,OAAO,CAACY,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YACzDoD,YAAY,EAAE,QAAQ;YACtB1B,QAAQ,EACN,mBAAmB,GACnBY,IAAI,CAACG,SAAS,CACZ,yGAAyG;YACzGoG,MAAM,CAAC8D,WAAW,CAChB7J,KAAK,CAAC8J,IAAI,CAACL,WAAW,CAACM,OAAO,EAAE,CAAC,CAAC5K,GAAG,CAAC,CAAC,CAAC6K,GAAG,EAAEC,KAAK,CAAC,GAAK;oBACtD1M,KAAI,EAAA,QAAA,CAACC,IAAI,CAACgH,UAAU,EAAEwF,GAAG,CAAC;oBAC1B;wBAACA,GAAG;wBAAEC,KAAK;qBAAC;iBACb,CAAC,CACH,CACF;SACJ,CAAC,CAAC;QAEH,OAAO;YAAE,GAAGpD,MAAM;YAAEjJ,KAAK;SAAE,CAAC;IAC9B;UAEMwJ,iCAAiC,CACrC9K,OAGC,EACD4I,YAGC,GAAG,EAAE,EAC+E;QACrF,MAAM,EAAE7C,OAAO,CAAA,EAAElF,UAAU,CAAA,EAAEiF,WAAW,CAAA,EAAE,GAAG,IAAI,CAAChF,oBAAoB,AAAC;QACvEC,IAAAA,OAAM,EAAA,QAAA,EAACf,OAAO,CAAC0F,cAAc,IAAI,IAAI,EAAE,6CAA6C,CAAC,CAAC;QACtF3E,IAAAA,OAAM,EAAA,QAAA,EACJgF,OAAO,IAAI,IAAI,IAAIlF,UAAU,IAAI,IAAI,IAAIiF,WAAW,IAAI,IAAI,EAC5D,8EAA8E,CAC/E,CAAC;QAEF,MAAM+C,IAAI,GAAqB;YAC7B,GAAG,IAAI,CAAC/H,oBAAoB;YAC5BiF,OAAO;YACPlF,UAAU;YACViF,WAAW;YACX,GAAG9F,OAAO;YACV8E,WAAW,EAAE,QAAQ;YACrB2D,gBAAgB,EAAE,QAAQ;SAC3B,AAAC;QAEF,wIAAwI;QACxI,IAAI,CAACI,IAAI,CAACnD,cAAc,CAAC7D,UAAU,CAAC,GAAG,CAAC,IAAI,CAACZ,KAAI,EAAA,QAAA,CAACoB,UAAU,CAACwG,IAAI,CAACnD,cAAc,CAAC,EAAE;YACjFmD,IAAI,CAACnD,cAAc,GAAG,IAAI,GAAGmD,IAAI,CAACnD,cAAc,CAAC;QACnD,CAAC;QAED,MAAMkI,MAAM,GAAG,MAAM,IAAI,CAACjF,uBAAuB,CAACE,IAAI,CAACnD,cAAc,EAAEmD,IAAI,EAAED,YAAY,CAAC,AAAC;QAE3F,OAAO;YACL3B,SAAS,EAAE2G,MAAM,CAAC3G,SAAS;YAC3ByB,MAAM,EAAEkF,MAAM,CAAClF,MAAM;SACtB,CAAC;IACJ;UAEMmF,yBAAyB,GAAG;QAChC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,MAAM,IAAI5B,KAAK,CACb,+EAA+E,CAChF,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,CAACxM,KAAK,EAAE;YACf,4FAA4F;YAC5F,WAAW;YACXL,KAAK,CAAC,oFAAoF,CAAC,CAAC;YAC5F,OAAO;QACT,CAAC;QAED,MAAM0O,QAAQ,GAAGC,IAAU,EAAA,CACxBC,QAAQ,CAAC7N,OAAO,CAACC,GAAG,CAAC6N,QAAQ,CAAC,CAC9BrL,GAAG,CAAC,CAACsL,QAAQ,GAAKlN,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEgN,QAAQ,CAAC,CAAC,AAAC;QAE5DC,IAAAA,oCAAkB,mBAAA,EAChB;YACE1O,KAAK,EAAE,IAAI,CAACA,KAAK;YACjB2O,MAAM,EAAE,IAAI,CAACP,QAAQ,CAACO,MAAM;SAC7B,EACDN,QAAQ,EACR,IAAM;YACJ1O,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC5C,0CAA0C;YAC1C2O,IAAU,EAAA,CAACM,IAAI,CAAC,IAAI,CAACnN,WAAW,EAAE;gBAAEoN,KAAK,EAAE,IAAI;aAAE,CAAC,CAAC;QACrD,CAAC,CACF,CAAC;IACJ;IAEA1C,WAAW,GAAwE,IAAI,CAAC;UAExE2C,wBAAwB,CACtCxO,OAA4B,EACA;YAQxBkE,GAAe,EAAkCA,IAAe,EAEjEA,IAAe,EAAkCA,IAAe,EAE/BA,IAAe,EAEMA,IAAO,EACTA,IAAO,EAItCA,IAAe,EAIDA,IAAO,EAOPA,IAAU;QA7BhDlE,OAAO,CAACC,IAAI,GAAG,MAAM,IAAI,CAACF,gBAAgB,CAACC,OAAO,CAAC,CAAC;QACpD,IAAI,CAACyO,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC1O,OAAO,CAAC,CAAC;QAE9C,MAAM2O,MAAM,GAAGxK,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAChD,WAAW,EAAE;YAAEyN,yBAAyB,EAAE,IAAI;SAAE,CAAC,AAAC;QAChF,MAAM,EAAE1K,GAAG,CAAA,EAAE,GAAGyK,MAAM,AAAC;QACvB,+HAA+H;QAC/H,MAAMnN,8BAA8B,GAClC,CAAC,CAAC0C,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAAC2K,WAAW,SAA4B,GAA3C3K,KAAAA,CAA2C,GAA3CA,GAAe,CAAE4K,0BAA0B,CAAA,IAAI,CAAC,CAAC5K,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAAC2K,WAAW,SAAsB,GAArC3K,KAAAA,CAAqC,GAArCA,IAAe,CAAE6K,oBAAoB,CAAA,AAAC;QAC3F,MAAMC,+BAA+B,GACnC,CAAC9K,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAAC2K,WAAW,SAA4B,GAA3C3K,KAAAA,CAA2C,GAA3CA,IAAe,CAAE4K,0BAA0B,CAAA,IAAI,CAAC,CAAC5K,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAAC2K,WAAW,SAAsB,GAArC3K,KAAAA,CAAqC,GAArCA,IAAe,CAAE6K,oBAAoB,CAAA,AAAC;QAC1F,IAAI,CAACvN,8BAA8B,GAAGA,8BAA8B,CAAC;QACrE,IAAI,CAACuD,0BAA0B,GAAG,CAAC,CAACb,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAAC2K,WAAW,SAA4B,GAA3C3K,KAAAA,CAA2C,GAA3CA,IAAe,CAAE4K,0BAA0B,CAAA,CAAC;QAEhF,MAAMG,kBAAkB,GAAG;YAAC,QAAQ;YAAE,QAAQ;SAAC,CAACvC,QAAQ,CAACxI,CAAAA,CAAAA,IAAO,GAAPA,GAAG,CAACgL,GAAG,SAAQ,GAAfhL,KAAAA,CAAe,GAAfA,IAAO,CAAE0J,MAAM,CAAA,IAAI,EAAE,CAAC,AAAC;QAChF,MAAMuB,YAAY,GAAG3N,8BAA8B,IAAI0C,CAAAA,CAAAA,IAAO,GAAPA,GAAG,CAACgL,GAAG,SAAQ,GAAfhL,KAAAA,CAAe,GAAfA,IAAO,CAAE0J,MAAM,CAAA,KAAK,QAAQ,AAAC;QACpF,MAAM7H,OAAO,GAAGqJ,IAAAA,aAAwB,yBAAA,EAAClL,GAAG,CAAC,AAAC;QAC9C,MAAM+B,WAAW,GAAGoJ,IAAAA,aAA4B,6BAAA,EAACnL,GAAG,EAAElE,OAAO,CAAC4F,IAAI,IAAI,aAAa,EAAE,KAAK,CAAC,AAAC;QAC5F,MAAM/E,UAAU,GAAGyO,IAAAA,OAAsC,uCAAA,EAAC,IAAI,CAACnO,WAAW,EAAE+C,GAAG,CAAC,AAAC;QACjF,MAAM8B,aAAa,GAAG,CAAC,CAAC9B,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAAC2K,WAAW,SAAe,GAA9B3K,KAAAA,CAA8B,GAA9BA,IAAe,CAAE8B,aAAa,CAAA,AAAC;QACvD,MAAMhF,MAAM,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAEN,UAAU,CAAC,AAAC;QACvD,MAAM+E,IAAI,GAAG5F,OAAO,CAAC4F,IAAI,IAAI,aAAa,AAAC;QAE3C,IAAIpE,8BAA8B,IAAI0C,CAAAA,CAAAA,IAAO,GAAPA,GAAG,CAACgL,GAAG,SAAQ,GAAfhL,KAAAA,CAAe,GAAfA,IAAO,CAAE0J,MAAM,CAAA,KAAK,QAAQ,EAAE;YAClE,MAAM,IAAInJ,OAAY,aAAA,CACpB,CAAC,oEAAoE,EAAEP,GAAG,CAACgL,GAAG,CAAEtB,MAAM,CAAC,gEAAgE,CAAC,CACzJ,CAAC;QACJ,CAAC;QAED,2FAA2F;QAC3F,IAAIpM,8BAA8B,IAAI0C,CAAAA,GAAG,QAAO,GAAVA,KAAAA,CAAU,GAAVA,CAAAA,IAAU,GAAVA,GAAG,CAAEG,KAAK,SAAA,GAAVH,KAAAA,CAAU,GAAVA,SAAAA,IAAU,CAAEI,MAAM,SAAR,GAAVJ,KAAAA,CAAU,SAAUqL,MAAM,AAAhB,CAAA,KAAqB,KAAK,EAAE;YAC1E,MAAMC,UAAU,GAAGb,MAAM,CAACc,iBAAiB,IAAId,MAAM,CAACe,gBAAgB,IAAI,WAAW,AAAC;YACtF,MAAMC,cAAc,GAAG1O,KAAI,EAAA,QAAA,CAAC+B,QAAQ,CAACwM,UAAU,CAAC,AAAC;YACjD,MAAM,IAAI/K,OAAY,aAAA,CACpB,CAAC,sDAAsD,EAAEkL,cAAc,CAAC,gFAAgF,EAAEA,cAAc,CAAC,oBAAoB,CAAC,CAC/L,CAAC;QACJ,CAAC;QAED,MAAM7O,oBAAoB,GAAG;YAC3BgF,WAAW,EAAE,CAAC,CAAC9F,OAAO,CAAC8F,WAAW;YAClCC,OAAO;YACPH,IAAI;YACJ/E,UAAU;YACVmF,aAAa;YACbH,MAAM,EAAE7F,OAAO,CAAC6F,MAAM;YACtBI,WAAW;SAEZ,AAAC;QACF,IAAI,CAACnF,oBAAoB,GAAGA,oBAAoB,CAAC;QAEjD,MAAM8O,aAAa,GAAG;YACpB3P,IAAI,EAAED,OAAO,CAACC,IAAI;YAClB4P,UAAU,EAAE7P,OAAO,CAAC6P,UAAU;YAC9BC,UAAU,EAAE9P,OAAO,CAAC+P,cAAc;SACnC,AAAC;QAEF,8BAA8B;QAC9B3P,OAAO,CAACC,GAAG,CAAC2P,sBAAsB,GAAG,CAAC,iBAAiB,EAAEhQ,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;QAExE,MAAM,EAAEP,KAAK,CAAA,EAAEC,SAAS,CAAA,EAAE0O,MAAM,CAAA,EAAE4B,UAAU,CAAA,EAAEC,aAAa,CAAA,EAAE,GAAG,MAAMC,IAAAA,iBAAqB,sBAAA,EACzF,IAAI,EACJP,aAAa,EACb;YACE9J,WAAW,EAAE,CAAC,CAAC9F,OAAO,CAAC8F,WAAW;YAClC5B,GAAG;SACJ,CACF,AAAC;QAEF,IAAI,CAAClE,OAAO,CAAC8F,WAAW,EAAE;YACxB,MAAMsK,kBAAkB,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAACrQ,OAAO,CAAC,AAAC;YAE1E,8EAA8E;YAC9EsQ,IAAAA,UAAiB,kBAAA,EAACL,UAAU,EAAE,IAAIM,kCAAiC,kCAAA,EAAE,CAACC,UAAU,EAAE,CAAC,CAAC;YAEpF,wEAAwE;YACxE,yEAAyE;YACzE,0EAA0E;YAC1E,2EAA2E;YAC3E,gDAAgD;YAChD,4CAA4C;YAC5CF,IAAAA,UAAiB,kBAAA,EAACL,UAAU,EAAEG,kBAAkB,CAACI,UAAU,EAAE,CAAC,CAAC;YAE/DP,UAAU,CAACQ,GAAG,CACZ,IAAIC,2BAA0B,2BAAA,CAAC,IAAI,CAACvP,WAAW,EAAE;gBAC/C,0CAA0C;gBAC1CwP,MAAM,EAAE3Q,OAAO,CAACgH,QAAQ,CAAC2J,MAAM,IAAI,IAAI;aACxC,CAAC,CAACH,UAAU,EAAE,CAChB,CAAC;YACFP,UAAU,CAACQ,GAAG,CAAC,IAAIG,4BAA2B,4BAAA,CAAC,IAAI,CAACzP,WAAW,CAAC,CAACqP,UAAU,EAAE,CAAC,CAAC;YAC/EP,UAAU,CAACQ,GAAG,CACZ,IAAII,yBAAwB,yBAAA,CAAC,IAAI,CAAC1P,WAAW,EAAE,IAAI,CAAC2P,qBAAqB,CAAC,CAACN,UAAU,EAAE,CACxF,CAAC;YAEF,MAAMO,kBAAkB,GAAG,IAAIC,0BAAyB,0BAAA,CAAC,IAAI,CAAC7P,WAAW,EAAE;gBACzE8P,WAAW,EAAE,CAAC,EAAEC,OAAO,CAAA,EAAE,GAAK;oBAC5B,IAAIA,OAAO,KAAK,QAAQ,EAAE;4BACjB,GAAe;wBAAtB,OAAO,CAAA,GAAe,GAAf,IAAI,CAACzC,UAAU,SAAuB,GAAtC,KAAA,CAAsC,GAAtC,GAAe,CAAE0C,qBAAqB,EAAE,CAAC;oBAClD,OAAO;4BACE,IAAe;wBAAtB,OAAO,CAAA,IAAe,GAAf,IAAI,CAAC1C,UAAU,SAAc,GAA7B,KAAA,CAA6B,GAA7B,IAAe,CAAE2C,YAAY,CAAC;4BACnCT,MAAM,EAAE,KAAK;yBACd,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;aACF,CAAC,AAAC;YACHV,UAAU,CAACQ,GAAG,CAACM,kBAAkB,CAACP,UAAU,EAAE,CAAC,CAAC;YAEhD,MAAMtI,UAAU,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAAC,IAAI,CAAChH,WAAW,CAAC,AAAC;YAExD,MAAMkQ,oBAAoB,GAAGC,IAAAA,wBAA6B,8BAAA,EACxD;gBACEC,SAAS,EAAErJ,UAAU;gBACrB/G,WAAW,EAAE,IAAI,CAACA,WAAW;aAC9B,EACDL,oBAAoB,CACrB,AAAC;YACF,kCAAkC;YAClC,yCAAyC;YACzCmP,UAAU,CAACQ,GAAG,CAACY,oBAAoB,CAAC,CAAC;YAErCpB,UAAU,CAACQ,GAAG,CAAC,IAAIe,qBAAoB,qBAAA,CAAC,IAAI,CAACrQ,WAAW,CAAC,CAACqP,UAAU,EAAE,CAAC,CAAC;YAExE,mFAAmF;YACnF,IAAI,IAAI,CAACiB,cAAc,EAAE,EAAE;gBACzB,oHAAoH;gBACpHxB,UAAU,CAACQ,GAAG,CAAC,IAAIiB,sBAAqB,sBAAA,CAAC,IAAI,CAACvQ,WAAW,CAAC,CAACqP,UAAU,EAAE,CAAC,CAAC;gBAEzE,0GAA0G;gBAC1GP,UAAU,CAACQ,GAAG,CAAC,IAAIkB,kBAAiB,kBAAA,CAAC,IAAI,CAACxQ,WAAW,CAAC,CAACqP,UAAU,EAAE,CAAC,CAAC;YACvE,CAAC;YAED,IAAIvB,kBAAkB,IAAIzN,8BAA8B,EAAE;gBACxDoQ,IAAAA,oCAAqB,sBAAA,EACnB;oBACElS,KAAK;oBACL2O,MAAM;iBACP,EACD,CAACwD,MAAM,GAAK;oBACV,IAAI1C,YAAY,EAAE;wBAChB,+FAA+F;wBAC/F,+FAA+F;wBAC/F,sGAAsG;wBACtG,yGAAyG;wBACzG,gCAAgC;wBAChC,IAAI,CAAC2C,uBAAuB,EAAE,CAAC;oBACjC,OAAO,IAAI,CAACC,IAAAA,OAAuB,wBAAA,GAAE,EAAE;wBACrC,KAAK,MAAMC,KAAK,IAAIH,MAAM,CAAE;gCAExB,gHAAgH;4BAChH,6CAA6C;4BAC7CG,GAAc;4BAHhB,IAGEA,CAAAA,CAAAA,GAAc,GAAdA,KAAK,CAAC5G,QAAQ,SAAM,GAApB4G,KAAAA,CAAoB,GAApBA,GAAc,CAAE1I,IAAI,CAAA,KAAK,GAAG,IAC5B,gGAAgG;4BAChG0I,KAAK,CAACpK,QAAQ,CAAC/F,UAAU,CAACb,MAAM,CAAC,IACjCiR,IAAAA,OAAoB,qBAAA,EAACD,KAAK,CAACpK,QAAQ,CAAC,EACpC;gCACAsK,IAAAA,OAAoB,qBAAA,GAAE,CAAC;4BACzB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC;YAED,qEAAqE;YACrE,IAAI1Q,8BAA8B,EAAE;gBAClC,IAAI,CAAC2Q,gCAAgC,EAAE,CAAC;gBACxC,MAAMC,aAAa,GAAGC,IAAAA,iCAAgC,iCAAA,EAAC,IAAI,CAAClR,WAAW,EAAE;oBACvEL,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;oBAC/CS,OAAO,EAAE,UAAU;oBACnBsD,aAAa,EAAE,IAAI,CAACA,aAAa,CAACyN,IAAI,CAAC,IAAI,CAAC;oBAC5CC,sBAAsB,EAAE,IAAI,CAACnM,2BAA2B,CAACkM,IAAI,CAAC,IAAI,CAAC;oBACnEE,eAAe,EAAExD,+BAA+B;oBAChDyD,cAAc,EAAE/S,KAAK,CAACgT,eAAe,CAACJ,IAAI,CAAC5S,KAAK,CAAC;iBAClD,CAAC,AAAC;gBACH,IAAI,CAACmM,WAAW,GAAGuG,aAAa,CAAC;gBACjCnC,UAAU,CAACQ,GAAG,CAAC2B,aAAa,CAACnC,UAAU,CAAC,CAAC;gBACzC,IAAI,CAAC0C,gBAAgB,GAAGP,aAAa,CAACO,gBAAgB,CAAC;YACzD,CAAC;YAED,mFAAmF;YACnF,IAAI,IAAI,CAAClB,cAAc,EAAE,EAAE;gBACzB,IAAI,CAACxC,kBAAkB,EAAE;oBACvB,8CAA8C;oBAC9CgB,UAAU,CAACQ,GAAG,CACZ,IAAImC,0BAAyB,0BAAA,CAACxC,kBAAkB,CAACI,UAAU,EAAE,CAACqC,QAAQ,CAAC,CAACrC,UAAU,EAAE,CACrF,CAAC;gBACJ,OAAO;wBAME7B,KAAgB;oBALvBsB,UAAU,CAACQ,GAAG,CACZqC,IAAAA,4BAA4B,6BAAA,EAAC,IAAI,CAAC3R,WAAW,EAAE;wBAC7CH,MAAM;wBACNH,UAAU;wBACV8N,MAAM;wBACN,GAAGA,CAAAA,KAAgB,GAAhBA,MAAM,CAACzK,GAAG,CAACG,KAAK,SAAQ,GAAxBsK,KAAAA,CAAwB,GAAxBA,KAAgB,CAAErK,MAAM;wBAC3B/B,cAAc,EAAE,CAACwQ,gBAAgB,GAC/B,IAAI,CAACC,iBAAiB,CAACD,gBAAgB,EAAE;gCAAEnS,QAAQ,EAAE,KAAK;6BAAE,CAAC;wBAC/D+F,kBAAkB,EAAE,OAAOC,QAAQ,GAAK;4BACtC,kDAAkD;4BAClD,IAAIpF,8BAA8B,EAAE;gCAClC,2GAA2G;gCAC3G,4HAA4H;gCAC5H,MAAMyR,IAAI,GAAG,MAAM7C,kBAAkB,CAAC8C,0BAA0B,EAAE,AAAC;gCACnE,OAAO;oCAAE5L,OAAO,EAAE2L,IAAI;iCAAE,CAAC;4BAC3B,CAAC;4BAED,qFAAqF;4BACrF,OAAO,IAAI,CAACtM,kBAAkB,CAACC,QAAQ,CAAC,CAAC;wBAC3C,CAAC;qBACF,CAAC,CACH,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,OAAO;YACL,qEAAqE;YACrE,IAAIpF,8BAA8B,EAAE;gBAClC,IAAI,CAAC2Q,gCAAgC,EAAE,CAAC;gBACxC,MAAMC,cAAa,GAAGC,IAAAA,iCAAgC,iCAAA,EAAC,IAAI,CAAClR,WAAW,EAAE;oBACvEL,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;oBAC/CS,OAAO,EAAE,UAAU;oBACnBsD,aAAa,EAAE,IAAI,CAACA,aAAa,CAACyN,IAAI,CAAC,IAAI,CAAC;oBAC5CC,sBAAsB,EAAE,IAAI,CAACnM,2BAA2B,CAACkM,IAAI,CAAC,IAAI,CAAC;oBACnEE,eAAe,EAAExD,+BAA+B;oBAChDyD,cAAc,EAAE/S,KAAK,CAACgT,eAAe,CAACJ,IAAI,CAAC5S,KAAK,CAAC;iBAClD,CAAC,AAAC;gBACH,IAAI,CAACmM,WAAW,GAAGuG,cAAa,CAAC;YACnC,CAAC;QACH,CAAC;QACD,qEAAqE;QACrE,MAAMe,aAAa,GAAG9E,MAAM,CAAC+E,KAAK,CAACd,IAAI,CAACjE,MAAM,CAAC,AAAC;QAEhDA,MAAM,CAAC+E,KAAK,GAAG,CAACC,QAAgC,GAAK;YACnD,OAAOF,aAAa,CAAC,CAACG,GAAW,GAAK;gBACpC,IAAI,CAACxF,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAACpO,KAAK,GAAG,IAAI,CAAC;gBAClB,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,EAAE,CAAC;gBAC/BwT,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAGC,GAAG,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEFC,IAAAA,mBAAwB,yBAAA,EAAC7T,KAAK,CAAC,CAAC;QAChC,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC;QACnB,IAAI,CAACC,SAAS,GAAGA,SAAS,CAAC;QAC3B,OAAO;YACL0O,MAAM;YACNrH,QAAQ,EAAE;gBACR,mDAAmD;gBACnD/G,IAAI,EAAED,OAAO,CAACC,IAAI;gBAClB,kCAAkC;gBAClCuT,IAAI,EAAE,WAAW;gBACjB,iDAAiD;gBACjDrO,GAAG,EAAE,CAAC,iBAAiB,EAAEnF,OAAO,CAACC,IAAI,CAAC,CAAC;gBACvCwT,QAAQ,EAAE,MAAM;aACjB;YACDxD,UAAU;YACVC,aAAa;SACd,CAAC;IACJ;IAEA,AAAQyC,gBAAgB,GAAwC,IAAI,CAAC;UAEvDe,mBAAmB,CAACvO,GAAW,EAAEwO,QAAsC,EAAE;QACrF,IAAI,CAAC,IAAI,CAAChU,SAAS,IAAI,IAAI,CAACC,aAAa,CAACgU,GAAG,CAACzO,GAAG,CAAC,EAAE;YAClD,OAAO;QACT,CAAC;QAED9F,KAAK,CAAC,qBAAqB,EAAE8F,GAAG,CAAC,CAAC;QAElC,MAAM0O,MAAM,GAAG,CAACC,OAAe,GAAK;YAClC,MAAMC,IAAI,GAAG7Q,IAAI,CAACC,KAAK,CAAC6Q,MAAM,CAACF,OAAO,CAAC,CAAC,AAA+B,AAAC;YAExE,OAAQC,IAAI,CAACzK,IAAI;gBACf,KAAK,mBAAmB,CAAC;gBACzB,KAAK,aAAa,CAAC;gBACnB,KAAK,cAAc;oBACjB,MAAM;gBACR,KAAK,QAAQ;oBACX;wBACE,MAAM2K,MAAM,GAAGF,IAAI,CAACG,IAAI,AAAC;wBACzB,MAAM,EACJC,eAAe,CAAA,EACfC,KAAK,CAAA,EACLC,QAAQ,CAAA,EACRC,OAAO,CAAA,IACR,GAaGL,MAAM,AAAC;wBAEX,MAAMM,SAAS,GAAGH,KAAK,CAACzQ,MAAM,IAAI0Q,QAAQ,CAAC1Q,MAAM,IAAI2Q,OAAO,CAAC3Q,MAAM,AAAC;wBAEpE,gHAAgH;wBAChH,IAAI,CAACwQ,eAAe,IAAII,SAAS,EAAE;4BACjC,yIAAyI;4BACzI,mBAAmB;4BACnB,IAAI,OAAOC,UAAU,CAACC,GAAG,KAAK,UAAU,EAAED,UAAU,CAACC,GAAG,EAAE,CAAC;4BAE3D,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAC1B;mCAAIP,KAAK;mCAAKC,QAAQ;6BAAC,CAACxR,GAAG,CAAC,CAAC+R,CAAC,GAAKA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAClI,MAAM,CAAC2H,OAAO,CAAC,CAChE,AAAC;4BAEF,MAAMQ,SAAS,GAAG9J,MAAM,CACtBtH,KAAK,CAAC8J,IAAI,CAACkH,YAAY,CAAC,CACrB7R,GAAG,CAAC,CAACkS,QAAQ,GAAK;oCAKVA,GAAsC;gCAJ7C,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;oCAChC,OAAO,IAAI,CAAC;gCACd,CAAC;gCACD,yCAAyC;gCACzC,OAAOA,CAAAA,CAAAA,GAAsC,GAAtCA,QAAQ,CAACC,KAAK,wBAAwB,SAAK,GAA3CD,KAAAA,CAA2C,GAA3CA,GAAsC,AAAE,CAAC,CAAC,CAAC,CAAA,IAAI,IAAI,CAAC;4BAC7D,CAAC,CAAC,CACD9J,MAAM,CAACQ,OAAO,CAAC,CACnB,AAAY,AAAC;4BAEdkI,QAAQ,CAACmB,SAAS,CAAC,CAAC;wBACtB,CAAC;oBACH;oBACA,MAAM;gBACR,KAAK,OAAO;wBAINf,GAAS;oBAHb,6GAA6G;oBAC7GkB,IAAG,IAAA,CAACC,KAAK,CAAC,mBAAmB,GAAGhS,IAAI,CAACG,SAAS,CAAC0Q,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;oBAE/D,IAAIA,CAAAA,CAAAA,GAAS,GAATA,IAAI,CAACG,IAAI,SAAM,GAAfH,KAAAA,CAAe,GAAfA,GAAS,CAAEzK,IAAI,CAAA,KAAK,oBAAoB,EAAE;4BAIzC,IAAU;wBAHb2L,IAAG,IAAA,CAACC,KAAK,CACP,yBAAyB,EACzB,mBAAmB;wBACnB,CAAC,CAAA,IAAU,GAAV,IAAI,CAACxV,KAAK,SAAU,GAApB,KAAA,CAAoB,GAApB,IAAU,CAAEyV,QAAQ,CAACC,mBAAmB,CAAQ,CAAChI,IAAI,EAAE,CACzD,CAAC;oBACJ,CAAC;oBACD,MAAM;gBACR;oBACE/N,KAAK,CAAC,sBAAsB,EAAE0U,IAAI,CAAC,CAAC;oBACpC,MAAM;aACT;QACH,CAAC,AAAC;QAEF,MAAMsB,MAAM,GAAG,MAAM,IAAI,CAAC1V,SAAS,CAAE2V,eAAe,CAACnQ,GAAG,EAAE0O,MAAM,CAAC,AAAC;QAClE,IAAI,CAACjU,aAAa,CAACwD,GAAG,CAAC+B,GAAG,EAAEkQ,MAAM,CAAC,CAAC;QACpC,YAAY;QACZA,MAAM,CAACE,YAAY,GAAG,IAAI,CAAC;QAC3B,MAAM,IAAI,CAAC5V,SAAS,CAAE6V,mBAAmB,CAACH,MAAM,EAAElQ,GAAG,EAAE0O,MAAM,CAAC,CAAC;IACjE;UAEa4B,sBAAsB,GAAqB;QACtD,IAAI,CAAC,IAAI,CAAC3H,QAAQ,EAAE;YAClB,MAAM,IAAI5B,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,IAAI9E,OAAO,CAAU,CAACsO,OAAO,GAAK;YACvC,IAAI,CAAC,IAAI,CAAChW,KAAK,EAAE;gBACf,4FAA4F;gBAC5F,4FAA4F;gBAC5F,mCAAmC;gBACnCL,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBAC5E,OAAOqW,OAAO,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YAED,MAAMC,GAAG,GAAGC,IAAAA,0BAAyB,0BAAA,EAAC;gBACpCzU,WAAW,EAAE,IAAI,CAACA,WAAW;gBAC7BkN,MAAM,EAAE,IAAI,CAACP,QAAQ,CAAEO,MAAM;gBAC7B3O,KAAK,EAAE,IAAI,CAACA,KAAK;gBACjBmW,QAAQ,EAAE,IAAI;gBACdC,QAAQ,EAAE,IAAI;gBACdC,UAAU,EAAE;oBAAC,QAAQ;oBAAE,KAAK;iBAAC;gBAC7B1C,QAAQ,EAAE,UAAY;oBACpB,iGAAiG;oBACjGsC,GAAG,EAAE,CAAC;oBACN,MAAM,EAAEK,6BAA6B,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CACpD,0DAA0D,GAC3D,AAAC;oBAEF,IAAI;wBACF,MAAMC,GAAG,GAAG,IAAID,6BAA6B,CAAC,IAAI,CAAC7U,WAAW,CAAC,AAAC;wBAChE,MAAM8U,GAAG,CAACC,cAAc,EAAE,CAAC;wBAC3BR,OAAO,CAAC,IAAI,CAAC,CAAC;oBAChB,EAAE,OAAOR,KAAK,EAAO;wBACnB,iEAAiE;wBACjE,wCAAwC;wBACxCD,IAAG,IAAA,CAACkB,GAAG,EAAE,CAAC;wBACVlB,IAAG,IAAA,CAACC,KAAK,CACPkB,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,gGAAgG,CAAC,CAC5G,CAAC;wBACFpB,IAAG,IAAA,CAACqB,SAAS,CAACpB,KAAK,CAAC,CAAC;wBACrBQ,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC;gBACH,CAAC;aACF,CAAC,AAAC;QACL,CAAC,CAAC,CAAC;IACL;UAEaa,uBAAuB,GAAG;YAE3B,GAAa;QADvB,OAAOC,IAAAA,8BAAkC,mCAAA,EAAC;YACxCnI,MAAM,EAAE,CAAA,GAAa,GAAb,IAAI,CAACP,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEO,MAAM;YAC7B3O,KAAK,EAAE,IAAI,CAACA,KAAK;YACjByB,WAAW,EAAE,IAAI,CAACA,WAAW;SAC9B,CAAC,CAAC;IACL;IAEUsV,kBAAkB,GAAa;QACvC,OAAO;YAAC,mBAAmB;YAAE,qBAAqB;YAAE,oBAAoB;SAAC,CAAC;IAC5E;IAEA,aAAa;IAEb,AAAQC,sBAAsB,GAAG,IAAI7W,GAAG,EAGrC,CAAC;IAEJ,gGAAgG;UAClF0C,cAAc,CAC1BqF,QAAgB,EAChB,EAAEhH,QAAQ,CAAA,EAAwB,EACwC;QAC1E,IAAI,IAAI,CAAC8V,sBAAsB,CAAC9C,GAAG,CAAChM,QAAQ,CAAC,EAAE;YAC7C,OAAO,IAAI,CAAC8O,sBAAsB,CAACC,GAAG,CAAC/O,QAAQ,CAAC,CAAC;QACnD,CAAC;QACD,MAAMgP,WAAW,GAAG,UAAY;YAC9B,IAAI;gBACFvX,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAACyB,oBAAoB,CAACD,UAAU,EAAE+G,QAAQ,CAAC,CAAC;gBAC3E,OAAO,MAAM,IAAI,CAACI,qBAAqB,CAACJ,QAAQ,EAAE;oBAChD9B,WAAW,EAAE,IAAI,CAAChF,oBAAoB,CAACgF,WAAW;oBAClDlF,QAAQ;iBACT,CAAC,CAAC;YACL,EAAE,OAAOsU,KAAK,EAAO;oBACJ,GAAyB;gBAAxC,MAAMlU,MAAM,GAAG,CAAA,CAAA,GAAyB,GAAzB,IAAI,CAACF,oBAAoB,SAAY,GAArC,KAAA,CAAqC,GAArC,GAAyB,CAAED,UAAU,CAAA,GAChDI,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACL,oBAAoB,CAAED,UAAU,CAAE,GACnEgW,SAAS,AAAC;gBACd,MAAMzO,YAAY,GAAGpH,MAAM,GAAGC,KAAI,EAAA,QAAA,CAACyB,QAAQ,CAAC1B,MAAM,EAAE4G,QAAQ,CAAC,GAAGA,QAAQ,AAAC;gBAEzE,wDAAwD;gBACxD,qDAAqD;gBACrD,MAAM0L,GAAG,GAAG,IAAI7O,OAAY,aAAA,CAC1B,WAAW,EACX2R,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,kCAAkC,EAAEhO,YAAY,CAAC,KAAK,CAAC,GAAG8M,KAAK,CAACpB,OAAO,CAC9E,AAAC;gBAEF,IAAK,MAAMpG,GAAG,IAAIwH,KAAK,CAAE;oBACvB,mBAAmB;oBACnB5B,GAAG,CAAC5F,GAAG,CAAC,GAAGwH,KAAK,CAACxH,GAAG,CAAC,CAAC;gBACxB,CAAC;gBAED,MAAM4F,GAAG,CAAC;YACZ,CAAC,QAAS;YACR,2CAA2C;YAC7C,CAAC;QACH,CAAC,AAAC;QACF,MAAM3R,KAAK,GAAGiV,WAAW,EAAE,AAAC;QAE5B,IAAI,CAACF,sBAAsB,CAACtT,GAAG,CAACwE,QAAQ,EAAEjG,KAAK,CAAC,CAAC;QACjD,OAAOA,KAAK,CAAC;IACf;UAEcqR,iBAAiB,CAC7BpL,QAAgB,EAChB,EAAEhH,QAAQ,CAAA,EAAwB,EACmB;QACrD,sCAAsC;QACtC,IAAI;YACF,MAAMkW,QAAQ,GAAG,MAAM,IAAI,CAACvU,cAAc,CAACqF,QAAQ,EAAE;gBAAEhH,QAAQ;aAAE,CAAC,AAAC;YAEnE,IAAI,CAACkW,CAAAA,QAAQ,QAAK,GAAbA,KAAAA,CAAa,GAAbA,QAAQ,CAAElU,GAAG,CAAA,EAAE;gBAClB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAOmU,IAAAA,yBAAmB,oBAAA,EAAC,IAAI,CAAC5V,WAAW,EAAE2V,QAAQ,CAAClU,GAAG,EAAEkU,QAAQ,CAACzO,QAAQ,CAAC,CAAC;QAChF,EAAE,OAAO6M,KAAK,EAAE;YACd,4EAA4E;YAC5E,IAAIA,KAAK,YAAYhJ,KAAK,EAAE;gBAC1B,IAAI;oBACF,MAAM8K,eAAe,GAAG,MAAMC,IAAAA,oBAAwB,yBAAA,EAAC;wBACrD/B,KAAK;wBACL/T,WAAW,EAAE,IAAI,CAACA,WAAW;wBAC7BN,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACD,UAAU;qBACjD,CAAC,AAAC;oBAEH,OAAO,IAAIqW,QAAQ,CAACF,eAAe,EAAE;wBACnCG,MAAM,EAAE,GAAG;wBACXC,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CAAC,CAAC;gBACL,EAAE,OAAOC,aAAa,EAAE;oBACtBhY,KAAK,CAAC,+DAA+D,EAAEgY,aAAa,CAAC,CAAC;oBACtF,MAAMnC,KAAK,CAAC;gBACd,CAAC;YACH,OAAO;gBACL,MAAMA,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH;IAEQpD,uBAAuB,GAAG;QAChC,IAAI,CAAC4E,sBAAsB,CAACY,KAAK,EAAE,CAAC;IACtC;IAEA,+EAA+E;IACvEnF,gCAAgC,GAAG;QACzC,uDAAuD;QACvD,mBAAmB;QACnBqC,UAAU,CAAC+C,wBAAwB,GAAG,IAAI,CAACC,gBAAgB,CAAClF,IAAI,CAAC,IAAI,CAAC,CAAC;IACzE;IAEA,gEAAgE;IAChE,8DAA8D;IACtDkF,gBAAgB,CAAC,EAAEC,IAAI,CAAA,EAAEC,EAAE,CAAA,EAAgC,EAAE;QACnE,IAAI,CAACC,gBAAgB,CAAC,gBAAgB,EAAE;YACtC7X,IAAI,EAAE,eAAe;YACrBiU,IAAI,EAAE;gBACJ0D,IAAI;gBACJC,EAAE;aACH;SACF,CAAC,CAAC;IACL;IAEA,YAAY;IAEJpP,QAAQ,CAACnD,GAAQ,EAAE;QACzB,MAAMwO,QAAQ,GAAG,CAACmB,SAAmB,GAAG,EAAE,GAAK;YAC7C,wDAAwD;YAExD,IAAI,CAACA,SAAS,CAACnR,MAAM,EAAE;gBACrB,6BAA6B;gBAC7B,IAAI,CAACgU,gBAAgB,CAAC,gBAAgB,EAAE;oBACtC7X,IAAI,EAAE,YAAY;iBACnB,CAAC,CAAC;YACL,OAAO;gBACL,KAAK,MAAMc,QAAQ,IAAIkU,SAAS,CAAE;wBAChC,IAAI,AAAiB,EAArB,GAAqB;oBAArB,CAAA,GAAqB,GAArB,CAAA,IAAI,GAAJ,IAAI,EAACnC,gBAAgB,SAAY,GAAjC,KAAA,CAAiC,GAAjC,GAAqB,CAArB,IAAiC,CAAjC,IAAI,EAAoB/R,QAAQ,CAAC,CAAC;oBAClC,IAAI,CAAC+W,gBAAgB,CAAC,gBAAgB,EAAE;wBACtC7X,IAAI,EAAE,YAAY;wBAClBc,QAAQ;qBACT,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,AAAC;QAEF,IAAI,CAAC8S,mBAAmB,CAACvO,GAAG,CAACyS,QAAQ,EAAE,EAAEjE,QAAQ,CAAC,CAAC;IACrD;IAEA,sBAAsB;IAEtB,wFAAwF;UAC1E5J,kBAAkB,CAC9BH,qBAA6B,EAC7B,EACEP,gBAAgB,CAAA,EAChBH,eAAe,CAAA,EACfc,YAAY,CAAA,EACZE,iBAAiB,CAAA,EAmBlB,EAWA;YA6BD,GAAU;QA5BVnJ,IAAAA,OAAM,EAAA,QAAA,EAAC,IAAI,CAACrB,KAAK,EAAE,kDAAkD,CAAC,CAAC;QACvE,MAAMiP,MAAM,GAAG,IAAI,CAACjP,KAAK,CAACmY,OAAO,AAAC;QAClC,MAAMC,WAAW,GAAG,IAAI,CAACpY,KAAK,CAACqY,iBAAiB,EAAE,AAAC;QACnD,MAAMC,gBAAgB,GAAGrJ,MAAM,CAACsJ,0BAA0B,QAExD,GAFuBtJ,KAAAA,CAEvB,GAFuBA,MAAM,CAACsJ,0BAA0B,CAAG,kBAAkB,EAAE;YAC/EvK,GAAG,EAAEoK,WAAW;SACjB,CAAC,AAAC;QAEH,MAAMI,UAAU,GAAoB,CAACC,oBAA4B,EAAEC,cAAsB,GAAK;gBAC5F,GAAU;YAAV,CAAA,GAAU,GAAV,IAAI,CAAC1Y,KAAK,SAAW,GAArB,KAAA,CAAqB,GAArB,QAAA,GAAU,CAAE2Y,SAAS,SAAA,GAArB,KAAA,CAAqB,GAArB,KAAuBpE,MAAM,QAK3B,GALF,KAAA,CAKE,GALF,KAAuBA,MAAM,CAAG;gBAC9BqE,OAAO,EAAEC,UAAU,CAACT,WAAW,CAAC;gBAChCxO,IAAI,EAAE,6BAA6B;gBACnC6O,oBAAoB;gBACpBC,cAAc;aACf,CAAC,CAAC;QACL,CAAC,AAAC;QAEF,MAAMI,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC7O,qBAAqB,EAAE;YAC9DI,YAAY;YACZX,gBAAgB;YAChBH,eAAe;SAChB,CAAC,AAAC;QAEH8O,gBAAgB,QAAO,GAAvBA,KAAAA,CAAuB,GAAvBA,gBAAgB,CAAEU,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtEV,gBAAgB,QAAU,GAA1BA,KAAAA,CAA0B,GAA1BA,gBAAgB,CAAEW,QAAQ,CAAC;YACzBC,IAAI,EAAE;gBACJC,aAAa,EAAEL,UAAU,IAAI,IAAI;aAClC;SACF,CAAC,CAAC;QACH,CAAA,GAAU,GAAV,IAAI,CAAC9Y,KAAK,SAAW,GAArB,KAAA,CAAqB,GAArB,GAAU,CAAE2Y,SAAS,CAACpE,MAAM,CAAC;YAC3BqE,OAAO,EAAEC,UAAU,CAACT,WAAW,CAAC;YAChCgB,aAAa,EAAE;gBACbC,UAAU,EAAE1P,gBAAgB,CAACC,IAAI;gBACjCF,GAAG,EAAEC,gBAAgB,CAACD,GAAG;gBACzB4P,SAAS,EAAEpP,qBAAqB;gBAChC/D,MAAM,EAAEwD,gBAAgB,CAACxD,MAAM;gBAC/BjF,QAAQ,EAAEyI,gBAAgB,CAACzI,QAAQ;gBACnCuI,qBAAqB,EAAED,eAAe,CAACC,qBAAqB;gBAC5DK,sBAAsB,EAAEH,gBAAgB,CAACG,sBAAsB,IAAI,EAAE;aACtE;YACDyP,UAAU,EAAE,KAAK;YACjB3P,IAAI,EAAE,sBAAsB;SAC7B,CAAC,CAAC;QAEH,IAAI;gBAMED,IAAuC;YAL3C,IAAI6P,KAAK,AAAa,AAAC;YACvB,IAAIC,QAAQ,AAAe,AAAC;YAE5B,+FAA+F;YAC/F,mGAAmG;YACnG,IAAI9P,CAAAA,CAAAA,IAAuC,GAAvCA,gBAAgB,CAACG,sBAAsB,SAAa,GAApDH,KAAAA,CAAoD,GAApDA,IAAuC,CAAEvE,WAAW,CAAA,KAAK,cAAc,EAAE;gBAC3E,MAAMsU,KAAK,GAAG,MAAM,IAAI,CAAC1Z,KAAK,CAAC2Z,UAAU,EAAE,CAACC,eAAe,CACzD,iFAAiF;gBACjF,aAAa;gBACb1P,qBAAqB,EAErBP,gBAAgB,EAChBH,eAAe,EACf;oBACEgP,UAAU;oBACVjO,OAAO,EAAED,YAAY,CAACC,OAAO;oBAC7BzD,IAAI,EAAEwD,YAAY,CAACxD,IAAI;iBACxB,CACF,AAAC;gBACF0S,KAAK,GAAGE,KAAK,CAACF,KAAK,CAAC;gBACpBC,QAAQ,GAAGC,KAAK,CAACD,QAAQ,CAAC;YAC5B,OAAO;gBACL,MAAMC,MAAK,GAAG,MAAM,CAACZ,UAAU,IAAI,IAAI,GACnC,IAAI,CAAC9Y,KAAK,CAAC2Z,UAAU,EAAE,CAACE,WAAW,CAAC,MAAMf,UAAU,EAAE,KAAK,CAAC,GAC5D,IAAI,CAAC9Y,KAAK,CAAC2Z,UAAU,EAAE,CAACC,eAAe,CACrC,iFAAiF;gBACjF,aAAa;gBACb1P,qBAAqB,EAErBP,gBAAgB,EAChBH,eAAe,EACf;oBACEgP,UAAU;oBACVjO,OAAO,EAAED,YAAY,CAACC,OAAO;oBAC7BzD,IAAI,EAAEwD,YAAY,CAACxD,IAAI;iBACxB,CACF,CAAC,AAAC;gBACP0S,KAAK,GAAGE,MAAK,CAACF,KAAK,CAAC;gBACpBC,QAAQ,GAAGC,MAAK,CAACD,QAAQ,CAAC;YAC5B,CAAC;YAEDnB,gBAAgB,QAAU,GAA1BA,KAAAA,CAA0B,GAA1BA,gBAAgB,CAAEW,QAAQ,CAAC;gBACzBa,GAAG,EAAE;oBACHC,gBAAgB,EAAEN,QAAQ,CAACO,KAAK,CAACC,YAAY,CAACC,IAAI;iBACnD;aACF,CAAC,CAAC;YACH5B,gBAAgB,QAAO,GAAvBA,KAAAA,CAAuB,GAAvBA,gBAAgB,CAAEU,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACpEV,gBAAgB,QAAO,GAAvBA,KAAAA,CAAuB,GAAvBA,gBAAgB,CAAEU,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAEnD,MAAMmB,qBAAqB,GAAG,IAAI,CAACna,KAAK,CAACoa,4BAA4B,CAACxH,IAAI,CAAC,IAAI,CAAC5S,KAAK,CAAC,AAAC;YAEvF,MAAMqa,UAAU,GAAG,IAAI,CAACC,kBAAkB,EAAE,AAAC;YAE7C,MAAMzP,MAAM,GAAG,MAAMwP,UAAU,CAC7B,iFAAiF;YACjF,aAAa;YACbnQ,qBAAqB,EAErBuP,QAAQ,CAACc,OAAO,EAChBd,QAAQ,CAACO,KAAK,EACd;gBACEQ,sBAAsB,EAAE,MAAM,IAAI,CAACxa,KAAK,CAACya,oBAAoB,CAC3DxL,MAAM,CAACyL,WAAW,CAACF,sBAAsB,EACzC;oBACEG,UAAU,EAAE,SAAS;oBACrBnR,eAAe;oBACfG,gBAAgB;iBACjB,CACF;gBACD,wBAAwB;gBACxBiR,mBAAmB,EAAE3L,MAAM,CAACoL,UAAU,CAACO,mBAAmB;gBAC1D7H,cAAc,EAAE,IAAI,CAAC/S,KAAK,CAACgT,eAAe;gBAC1C6H,qBAAqB,EAAE5L,MAAM,CAACoL,UAAU,CAACQ,qBAAqB;gBAC9DC,iBAAiB,EAAExQ,YAAY,CAACxD,IAAI;gBACpC4C,GAAG,EAAEC,gBAAgB,CAACD,GAAG;gBACzBjI,WAAW,EAAEwN,MAAM,CAACxN,WAAW;gBAC/BgJ,WAAW,EAAED,iBAAiB,CAACC,WAAW;gBAC1CsQ,mBAAmB,EAAE9L,MAAM,CAACoL,UAAU,CAACW,6BAA6B,CAClE9Q,qBAAqB,CAEtB;gBACDQ,SAAS,EAAEF,iBAAiB,CAACE,SAAS;gBACtCE,YAAY,EAAEJ,iBAAiB,CAACI,YAAY;gBAC5CD,SAAS,EAAEH,iBAAiB,CAACG,SAAS;gBACtCvB,eAAe,EAAEoB,iBAAiB,CAACpB,eAAe;gBAClDZ,UAAU,EAAEyG,MAAM,CAACN,MAAM,CAACsM,mBAAmB,IAAIhM,MAAM,CAACxN,WAAW;gBACnE0Y,qBAAqB;gBAErB,iFAAiF;gBACjF3P,iBAAiB;aAClB,CACF,AAAC;YAEF,IAAI,CAACxK,KAAK,CAAC2Y,SAAS,CAACpE,MAAM,CAAC;gBAC1BqE,OAAO,EAAEC,UAAU,CAACT,WAAW,CAAC;gBAChCxO,IAAI,EAAE,mBAAmB;aAC1B,CAAC,CAAC;YAEH0O,gBAAgB,QAAO,GAAvBA,KAAAA,CAAuB,GAAvBA,gBAAgB,CAAEU,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAEjD,IAAIkC,UAAU,GAAkB,IAAI,AAAC;YACrC,IAAIC,SAAS,GAAkB,IAAI,AAAC;YAEpC,qDAAqD;YACrD,IAAI3Q,iBAAiB,CAAC0D,MAAM,KAAK,QAAQ,EAAE;gBACzC,IAAI;wBAYgB3G,IAAiD;oBAXnE,MAAM6T,MAAM,GAAG,OAAOvQ,MAAM,KAAK,QAAQ,GAAGrH,IAAI,CAACC,KAAK,CAACoH,MAAM,CAAC,GAAGA,MAAM,AAAC;oBAExExJ,IAAAA,OAAM,EAAA,QAAA,EACJ,WAAW,IAAI+Z,MAAM,IAAIpX,KAAK,CAACqX,OAAO,CAACD,MAAM,CAAC7T,SAAS,CAAC,EACxD,kGAAkG,CACnG,CAAC;oBAEF,MAAMA,SAAS,GAAG6T,MAAM,CAAC7T,SAAS,AAAiB,AAAC;oBACpD,MAAMyB,MAAM,GAAGoS,MAAM,CAACpS,MAAM,AAAC;oBAE7B,MAAMkS,WAAU,GAAG3T,SAAS,CAACgE,MAAM,CAAC,CAAC+P,KAAK,GAAKA,KAAK,CAAC1R,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,AAAC;oBACvE,MAAMuR,UAAS,GAAG5T,CAAAA,CAAAA,IAAiD,GAAjDA,SAAS,CAACgE,MAAM,CAAC,CAAC+P,KAAK,GAAKA,KAAK,CAAC1R,IAAI,KAAK,KAAK,CAAC,SAAK,GAAtDrC,KAAAA,CAAsD,GAAtDA,QAAAA,IAAiD,AAAE,CAAC,CAAC,CAAC,SAAA,GAAtDA,KAAAA,CAAsD,QAAEzD,MAAM,AAAR,CAAA,IAAY,EAAE,AAAC;oBAEvF,OAAO;wBACLyX,gBAAgB,EAAE/B,KAAK,CAACgC,KAAK,GACzBhC,KAAK,CAAC9E,KAAK,CAACwF,IAAI,GAAGT,QAAQ,CAACc,OAAO,CAACtW,MAAM,GAC1CuV,KAAK,CAAC9E,KAAK,CAACwF,IAAI,GAAGV,KAAK,CAAC7E,QAAQ,CAACuF,IAAI,GAAGV,KAAK,CAAC5E,OAAO,CAACsF,IAAI;wBAC/DuB,gBAAgB,EAAEhC,QAAQ,CAACiC,IAAI;wBAC/BC,SAAS,EAAElC,QAAQ,CAACzB,EAAE;wBACtBnN,MAAM,EAAEqQ,WAAU,CAACpX,MAAM;wBACzBX,GAAG,EAAEgY,UAAS;wBACd5T,SAAS;wBACTyB,MAAM;qBACP,CAAC;gBACJ,EAAE,OAAOwM,KAAK,EAAO;oBACnB,MAAM,IAAIhJ,KAAK,CACb,gHAAgH,GAC9GgJ,KAAK,CAACpB,OAAO,CAChB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,OAAOvJ,MAAM,KAAK,QAAQ,EAAE;gBAC9BqQ,UAAU,GAAGrQ,MAAM,CAAC;gBAEpB,4CAA4C;gBAC5C,IAAI,EAAE0P,OAAO,CAAA,EAAEP,KAAK,CAAA,EAAE,GAAGP,QAAQ,AAAC;gBAClC,IAAIjP,iBAAiB,CAACC,WAAW,EAAE;oBACjC8P,OAAO,GAAG,EAAE,CAAC;gBACf,CAAC;gBAEDY,SAAS,GAAG,MAAMS,oBAAoB,CACpC;oBACE,EAAE;uBACCrB,OAAO;uBACP,IAAI,CAACva,KAAK,CAAC6b,iBAAiB,CAAC7B,KAAK,CAAC;iBACvC,EACD;oBACE8B,aAAa,EAAEtR,iBAAiB,CAACsR,aAAa;oBAC9ClB,mBAAmB,EAAE3L,MAAM,CAACoL,UAAU,CAACO,mBAAmB;oBAC1DT,qBAAqB;iBACtB,CACF,CAAC;YACJ,OAAO;gBACLe,UAAU,GAAGrQ,MAAM,CAACkN,IAAI,CAAC;gBACzBoD,SAAS,GAAGtQ,MAAM,CAAC1H,GAAG,CAAC;YACzB,CAAC;YAED,OAAO;gBACLoY,gBAAgB,EAAE/B,KAAK,CAACgC,KAAK,GACzBhC,KAAK,CAAC9E,KAAK,CAACwF,IAAI,GAAGT,QAAQ,CAACc,OAAO,CAACtW,MAAM,GAC1CuV,KAAK,CAAC9E,KAAK,CAACwF,IAAI,GAAGV,KAAK,CAAC7E,QAAQ,CAACuF,IAAI,GAAGV,KAAK,CAAC5E,OAAO,CAACsF,IAAI;gBAC/DuB,gBAAgB,EAAEhC,QAAQ,CAACiC,IAAI;gBAC/BC,SAAS,EAAElC,QAAQ,CAACzB,EAAE;gBACtBnN,MAAM,EAAEqQ,UAAU;gBAClB/X,GAAG,EAAEgY,SAAS;aACf,CAAC;QACJ,EAAE,OAAO3F,MAAK,EAAE;YACd,+DAA+D;YAC/D,mBAAmB;YACnBA,MAAK,CAACuG,oBAA4B,6BAAA,CAAC,GAAG,IAAI,CAAC;YAE3C,IAAI,CAAC/b,KAAK,CAAC2Y,SAAS,CAACpE,MAAM,CAAC;gBAC1BqE,OAAO,EAAEC,UAAU,CAACT,WAAW,CAAC;gBAChCxO,IAAI,EAAE,qBAAqB;aAC5B,CAAC,CAAC;YAEH,MAAM4L,MAAK,CAAC;QACd,CAAC;IACH;IAEQ8E,kBAAkB,GAAG;YAEzB,GAAU;QADZ,OACE,CAAA,CAAA,GAAU,GAAV,IAAI,CAACta,KAAK,SAAS,GAAnB,KAAA,CAAmB,GAAnB,QAAA,GAAU,CAAEmY,OAAO,SAAA,GAAnB,KAAA,CAAmB,GAAnB,KAAqBkC,UAAU,CAAC2B,gBAAgB,KAChD,CAAC,CAACC,UAAU,EAAEC,UAAU,EAAElC,KAAK,EAAE1Z,OAAO,GACtC6b,IAAAA,eAAc,EAAA,QAAA,EAACC,IAAAA,aAAY,EAAA,QAAA,EAACH,UAAU,EAAEC,UAAU,EAAElC,KAAK,EAAE1Z,OAAO,CAAC,CAAC,CAACyX,IAAI,CAAC,CAC5E;IACJ;IAEQgB,gBAAgB,CACtB7O,qBAA6B,EAC7B,EACEI,YAAY,CAAA,EACZX,gBAAgB,CAAA,EAChBH,eAAe,CAAA,EAWhB,EACD;QACAnI,IAAAA,OAAM,EAAA,QAAA,EAAC,IAAI,CAACrB,KAAK,EAAE,kDAAkD,CAAC,CAAC;QACvE,MAAMiP,MAAM,GAAG,IAAI,CAACjP,KAAK,CAACmY,OAAO,AAAC;QAElC,MAAMkE,OAAO,GAAGC,IAAAA,WAAU,EAAA,QAAA,EAACpS,qBAAqB,EAAEP,gBAAgB,EAAE;YAClE4S,4BAA4B,EAAEtN,MAAM,CAACyL,WAAW,CAAC6B,4BAA4B;YAC7E/S,eAAe;YACfe,OAAO,EAAED,YAAY,CAACC,OAAO;YAC7BzD,IAAI,EAAEwD,YAAY,CAACxD,IAAI;SACxB,CAAC,AAAC;QACH,OAAO,IAAI,CAAC9G,KAAK,CAAC2Z,UAAU,EAAE,CAAC6C,oBAAoB,CAACH,OAAO,CAAC,CAAC;IAC/D;UAEclS,wBAAwB,CACpCkL,QAAgB,EAChB,EACE7L,eAAe,CAAA,EACfG,gBAAgB,CAAA,EAOjB,EACD;QACAtI,IAAAA,OAAM,EAAA,QAAA,EAAC,IAAI,CAACrB,KAAK,EAAE,+DAA+D,CAAC,CAAC;QACpF,OAAO,MAAM,IAAI,CAACA,KAAK,CAACya,oBAAoB,CAAC1X,IAAAA,aAA4B,6BAAA,EAACsS,QAAQ,CAAC,EAAE;YACnFsF,UAAU,EAAE,QAAQ;YACpBnR,eAAe;YACfG,gBAAgB;SACjB,CAAC,CAAC;IACL;CACD;AAED,SAASkP,UAAU,CAACT,WAAmB,EAAU;IAC/C,OAAOA,WAAW,CAACF,QAAQ,CAAC,EAAE,CAAC,CAAC;AAClC,CAAC;AAED,SAASlN,UAAU,CAACyR,GAAW,EAAE;IAC/B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,GAAG,CAACxZ,OAAO,qBAAqB,qBAAqB,CAAC,CAAC;AAChE,CAAC;AAED,eAAe2Y,oBAAoB,CACjCc,OAAsE,EACtEpc,OAAkC,EACjB;IACjB,OAAO,CAAC,MAAMqc,IAAAA,mBAA6B,EAAA,8BAAA,EAACD,OAAO,EAAEpc,OAAO,CAAC,CAAC,CAAC4X,QAAQ,CAACf,SAAS,EAAE;QACjF2E,aAAa,EAAExb,OAAO,CAACwb,aAAa;KACrC,CAAC,CAAC;AACL,CAAC;AAED,SAASxQ,MAAM,CAAIsR,KAAU,EAAO;IAClC,OAAO5Y,KAAK,CAAC8J,IAAI,CAAC,IAAImH,GAAG,CAAC2H,KAAK,CAAC,CAAC,CAAC;AACpC,CAAC"}