{"version": 3, "file": "withIosSplashColors.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "_fs", "_path", "_interopRequireWildcard", "_InterfaceBuilder", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "debug", "Debug", "SPLASHSCREEN_COLORSET_PATH", "exports", "withIosSplashColors", "config", "splash", "withDangerousMod", "iosNamedProjectRoot", "IOSConfig", "Paths", "getSourceRoot", "modRequest", "projectRoot", "configureColorAssets", "backgroundColor", "darkBackgroundColor", "dark", "colorsetPath", "path", "resolve", "fs", "promises", "rm", "force", "recursive", "writeColorsContentsJsonFileAsync", "assetPath", "color", "parseColor", "darkColor", "colors", "components", "alpha", "blue", "rgb", "green", "red", "idiom", "push", "appearances", "appearance", "value", "writeContentsJsonAsync", "directory", "mkdir", "writeFile", "join", "JSON", "stringify", "info", "version", "author"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashColors.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, withDangerousMod } from '@expo/config-plugins';\nimport Debug from 'debug';\nimport fs from 'fs';\nimport path, { join } from 'path';\n\nimport { parseColor } from './InterfaceBuilder';\nimport { IOSSplashConfig } from './getIosSplashConfig';\nimport { ContentsJsonColor } from '../../icons/AssetContents';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios:splash-colorset');\n\nexport const SPLASHSCREEN_COLORSET_PATH = 'Images.xcassets/SplashScreenBackground.colorset';\n\nexport const withIosSplashColors: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  if (!splash) {\n    return config;\n  }\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      const iosNamedProjectRoot = IOSConfig.Paths.getSourceRoot(config.modRequest.projectRoot);\n\n      await configureColorAssets({\n        iosNamedProjectRoot,\n        backgroundColor: splash.backgroundColor,\n        darkBackgroundColor: splash.dark?.backgroundColor,\n      });\n      return config;\n    },\n  ]);\n};\n\nasync function configureColorAssets({\n  iosNamedProjectRoot,\n  backgroundColor = '#ffffff',\n  darkBackgroundColor,\n}: {\n  iosNamedProjectRoot: string;\n  backgroundColor: string;\n  darkBackgroundColor?: string | null;\n}) {\n  const colorsetPath = path.resolve(iosNamedProjectRoot, SPLASHSCREEN_COLORSET_PATH);\n\n  // ensure old SplashScreen colorSet is removed\n  await fs.promises.rm(colorsetPath, { force: true, recursive: true });\n\n  await writeColorsContentsJsonFileAsync({\n    assetPath: colorsetPath,\n    backgroundColor,\n    darkBackgroundColor: darkBackgroundColor ?? null,\n  });\n}\n\nasync function writeColorsContentsJsonFileAsync({\n  assetPath,\n  backgroundColor,\n  darkBackgroundColor,\n}: {\n  assetPath: string;\n  backgroundColor: string;\n  darkBackgroundColor: string | null;\n}) {\n  const color = parseColor(backgroundColor);\n  const darkColor = darkBackgroundColor ? parseColor(darkBackgroundColor) : null;\n\n  const colors: ContentsJsonColor[] = [\n    {\n      color: {\n        components: {\n          alpha: '1.000',\n          blue: color.rgb.blue,\n          green: color.rgb.green,\n          red: color.rgb.red,\n        },\n        'color-space': 'srgb',\n      },\n      idiom: 'universal',\n    },\n  ];\n\n  if (darkColor) {\n    colors.push({\n      color: {\n        components: {\n          alpha: '1.000',\n          blue: darkColor.rgb.blue,\n          green: darkColor.rgb.green,\n          red: darkColor.rgb.red,\n        },\n        'color-space': 'srgb',\n      },\n      idiom: 'universal',\n      appearances: [\n        {\n          appearance: 'luminosity',\n          value: 'dark',\n        },\n      ],\n    });\n  }\n  debug(`create colors contents.json:`, assetPath);\n  debug(`use colors:`, colors);\n  await writeContentsJsonAsync(assetPath, { colors });\n}\n\nasync function writeContentsJsonAsync(\n  directory: string,\n  { colors }: { colors: ContentsJsonColor[] }\n): Promise<void> {\n  await fs.promises.mkdir(directory, { recursive: true });\n  await fs.promises.writeFile(\n    join(directory, 'Contents.json'),\n    JSON.stringify(\n      {\n        colors,\n        info: {\n          version: 1,\n          // common practice is for the tool that generated the icons to be the \"author\"\n          author: 'expo',\n        },\n      },\n      null,\n      2\n    ),\n    'utf8'\n  );\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAL,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAO,kBAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,iBAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAgD,SAAAQ,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAd,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAIhD,MAAMmB,KAAK,GAAG,IAAAC,gBAAK,EAAC,6DAA6D,CAAC;AAE3E,MAAMC,0BAA0B,GAAAC,OAAA,CAAAD,0BAAA,GAAG,iDAAiD;AAEpF,MAAME,mBAAkD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACpF,IAAI,CAACA,MAAM,EAAE;IACX,OAAOD,MAAM;EACf;EACA,OAAO,IAAAE,iCAAgB,EAACF,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAMG,mBAAmB,GAAGC,0BAAS,CAACC,KAAK,CAACC,aAAa,CAACN,MAAM,CAACO,UAAU,CAACC,WAAW,CAAC;IAExF,MAAMC,oBAAoB,CAAC;MACzBN,mBAAmB;MACnBO,eAAe,EAAET,MAAM,CAACS,eAAe;MACvCC,mBAAmB,EAAEV,MAAM,CAACW,IAAI,EAAEF;IACpC,CAAC,CAAC;IACF,OAAOV,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACF,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAEF,eAAeU,oBAAoBA,CAAC;EAClCN,mBAAmB;EACnBO,eAAe,GAAG,SAAS;EAC3BC;AAKF,CAAC,EAAE;EACD,MAAME,YAAY,GAAGC,eAAI,CAACC,OAAO,CAACZ,mBAAmB,EAAEN,0BAA0B,CAAC;;EAElF;EACA,MAAMmB,aAAE,CAACC,QAAQ,CAACC,EAAE,CAACL,YAAY,EAAE;IAAEM,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;EAEpE,MAAMC,gCAAgC,CAAC;IACrCC,SAAS,EAAET,YAAY;IACvBH,eAAe;IACfC,mBAAmB,EAAEA,mBAAmB,IAAI;EAC9C,CAAC,CAAC;AACJ;AAEA,eAAeU,gCAAgCA,CAAC;EAC9CC,SAAS;EACTZ,eAAe;EACfC;AAKF,CAAC,EAAE;EACD,MAAMY,KAAK,GAAG,IAAAC,8BAAU,EAACd,eAAe,CAAC;EACzC,MAAMe,SAAS,GAAGd,mBAAmB,GAAG,IAAAa,8BAAU,EAACb,mBAAmB,CAAC,GAAG,IAAI;EAE9E,MAAMe,MAA2B,GAAG,CAClC;IACEH,KAAK,EAAE;MACLI,UAAU,EAAE;QACVC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAEN,KAAK,CAACO,GAAG,CAACD,IAAI;QACpBE,KAAK,EAAER,KAAK,CAACO,GAAG,CAACC,KAAK;QACtBC,GAAG,EAAET,KAAK,CAACO,GAAG,CAACE;MACjB,CAAC;MACD,aAAa,EAAE;IACjB,CAAC;IACDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,IAAIR,SAAS,EAAE;IACbC,MAAM,CAACQ,IAAI,CAAC;MACVX,KAAK,EAAE;QACLI,UAAU,EAAE;UACVC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAEJ,SAAS,CAACK,GAAG,CAACD,IAAI;UACxBE,KAAK,EAAEN,SAAS,CAACK,GAAG,CAACC,KAAK;UAC1BC,GAAG,EAAEP,SAAS,CAACK,GAAG,CAACE;QACrB,CAAC;QACD,aAAa,EAAE;MACjB,CAAC;MACDC,KAAK,EAAE,WAAW;MAClBE,WAAW,EAAE,CACX;QACEC,UAAU,EAAE,YAAY;QACxBC,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,CAAC;EACJ;EACA1C,KAAK,CAAC,8BAA8B,EAAE2B,SAAS,CAAC;EAChD3B,KAAK,CAAC,aAAa,EAAE+B,MAAM,CAAC;EAC5B,MAAMY,sBAAsB,CAAChB,SAAS,EAAE;IAAEI;EAAO,CAAC,CAAC;AACrD;AAEA,eAAeY,sBAAsBA,CACnCC,SAAiB,EACjB;EAAEb;AAAwC,CAAC,EAC5B;EACf,MAAMV,aAAE,CAACC,QAAQ,CAACuB,KAAK,CAACD,SAAS,EAAE;IAAEnB,SAAS,EAAE;EAAK,CAAC,CAAC;EACvD,MAAMJ,aAAE,CAACC,QAAQ,CAACwB,SAAS,CACzB,IAAAC,YAAI,EAACH,SAAS,EAAE,eAAe,CAAC,EAChCI,IAAI,CAACC,SAAS,CACZ;IACElB,MAAM;IACNmB,IAAI,EAAE;MACJC,OAAO,EAAE,CAAC;MACV;MACAC,MAAM,EAAE;IACV;EACF,CAAC,EACD,IAAI,EACJ,CACF,CAAC,EACD,MACF,CAAC;AACH", "ignoreList": []}