{"version": 3, "sources": ["../../../../src/start/server/AsyncNgrok.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport crypto from 'crypto';\nimport * as path from 'path';\nimport slugify from 'slugify';\n\nimport { getSettingsDirectory } from '../../api/user/UserSettings';\nimport { getActorDisplayName, getUserAsync } from '../../api/user/user';\nimport * as Log from '../../log';\nimport { delayAsync, resolveWithTimeout } from '../../utils/delay';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { isNgrokClientError, NgrokInstance, NgrokResolver } from '../doctor/ngrok/NgrokResolver';\nimport { hasAdbReverseAsync, startAdbReverseAsync } from '../platforms/android/adbReverse';\nimport { ProjectSettings } from '../project/settings';\n\nconst debug = require('debug')('expo:start:server:ngrok') as typeof console.log;\n\nconst NGROK_CONFIG = {\n  authToken: '5W1bR67GNbWcXqmxZzBG1_56GezNeaX6sSRvn8npeQ8',\n  domain: 'exp.direct',\n};\n\nconst TUNNEL_TIMEOUT = 10 * 1000;\n\nexport class AsyncNgrok {\n  /** Resolves the best instance of ngrok, exposed for testing. */\n  resolver: NgrokResolver;\n\n  /** Info about the currently running instance of ngrok. */\n  private serverUrl: string | null = null;\n\n  constructor(\n    private projectRoot: string,\n    private port: number\n  ) {\n    this.resolver = new NgrokResolver(projectRoot);\n  }\n\n  public getActiveUrl(): string | null {\n    return this.serverUrl;\n  }\n\n  /** Exposed for testing. */\n  async _getIdentifyingUrlSegmentsAsync(): Promise<string[]> {\n    const user = await getUserAsync();\n    if (user?.__typename === 'Robot') {\n      throw new CommandError('NGROK_ROBOT', 'Cannot use ngrok with a robot user.');\n    }\n    const username = getActorDisplayName(user);\n\n    return [\n      // NOTE: https://github.com/expo/expo/pull/16556#discussion_r822944286\n      await this.getProjectRandomnessAsync(),\n      // Strip out periods from the username to avoid subdomain issues with SSL certificates.\n      slugify(username, { remove: /\\./ }),\n      // Use the port to distinguish between multiple tunnels (webpack, metro).\n      String(this.port),\n    ];\n  }\n\n  /** Exposed for testing. */\n  async _getProjectHostnameAsync(): Promise<string> {\n    return `${(await this._getIdentifyingUrlSegmentsAsync()).join('-')}.${NGROK_CONFIG.domain}`;\n  }\n\n  /** Exposed for testing. */\n  async _getProjectSubdomainAsync(): Promise<string> {\n    return (await this._getIdentifyingUrlSegmentsAsync()).join('-');\n  }\n\n  /** Start ngrok on the given port for the project. */\n  async startAsync({ timeout }: { timeout?: number } = {}): Promise<void> {\n    // Ensure the instance is loaded first, this can linger so we should run it before the timeout.\n    await this.resolver.resolveAsync({\n      // For now, prefer global install since the package has native code (harder to install) and doesn't change very often.\n      prefersGlobalInstall: true,\n    });\n\n    // NOTE(EvanBacon): If the user doesn't have ADB installed,\n    // then skip attempting to reverse the port.\n    if (hasAdbReverseAsync()) {\n      // Ensure ADB reverse is running.\n      if (!(await startAdbReverseAsync([this.port]))) {\n        // TODO: Better error message.\n        throw new CommandError(\n          'NGROK_ADB',\n          `Cannot start tunnel URL because \\`adb reverse\\` failed for the connected Android device(s).`\n        );\n      }\n    }\n\n    this.serverUrl = await this._connectToNgrokAsync({ timeout });\n\n    debug('Tunnel URL:', this.serverUrl);\n    Log.log('Tunnel ready.');\n  }\n\n  /** Stop the ngrok process if it's running. */\n  public async stopAsync(): Promise<void> {\n    debug('Stopping Tunnel');\n\n    await this.resolver.get()?.kill?.();\n    this.serverUrl = null;\n  }\n\n  /** Exposed for testing. */\n  async _connectToNgrokAsync(\n    options: { timeout?: number } = {},\n    attempts: number = 0\n  ): Promise<string> {\n    // Attempt to stop any hanging processes, this increases the chances of a successful connection.\n    await this.stopAsync();\n\n    // Get the instance quietly or assert otherwise.\n    const instance = await this.resolver.resolveAsync({\n      shouldPrompt: false,\n      autoInstall: false,\n    });\n\n    // TODO(Bacon): Consider dropping the timeout functionality:\n    // https://github.com/expo/expo/pull/16556#discussion_r822307373\n    const results = await resolveWithTimeout(\n      () => this.connectToNgrokInternalAsync(instance, attempts),\n      {\n        timeout: options.timeout ?? TUNNEL_TIMEOUT,\n        errorMessage: 'ngrok tunnel took too long to connect.',\n      }\n    );\n    if (typeof results === 'string') {\n      return results;\n    }\n\n    // Wait 100ms and then try again\n    await delayAsync(100);\n\n    return this._connectToNgrokAsync(options, attempts + 1);\n  }\n\n  private async _getConnectionPropsAsync(): Promise<{ hostname?: string; subdomain?: string }> {\n    const userDefinedSubdomain = env.EXPO_TUNNEL_SUBDOMAIN;\n    if (userDefinedSubdomain) {\n      const subdomain =\n        typeof userDefinedSubdomain === 'string'\n          ? userDefinedSubdomain\n          : await this._getProjectSubdomainAsync();\n      debug('Subdomain:', subdomain);\n      return { subdomain };\n    } else {\n      const hostname = await this._getProjectHostnameAsync();\n      debug('Hostname:', hostname);\n      return { hostname };\n    }\n  }\n\n  private async connectToNgrokInternalAsync(\n    instance: NgrokInstance,\n    attempts: number = 0\n  ): Promise<string | false> {\n    try {\n      // Global config path.\n      const configPath = path.join(getSettingsDirectory(), 'ngrok.yml');\n      debug('Global config path:', configPath);\n      const urlProps = await this._getConnectionPropsAsync();\n\n      const url = await instance.connect({\n        ...urlProps,\n        authtoken: NGROK_CONFIG.authToken,\n        configPath,\n        onStatusChange(status) {\n          if (status === 'closed') {\n            Log.error(\n              chalk.red(\n                'Tunnel connection has been closed. This is often related to intermittent connection problems with the Ngrok servers. Restart the dev server to try connecting to Ngrok again.'\n              ) + chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n            );\n          } else if (status === 'connected') {\n            Log.log('Tunnel connected.');\n          }\n        },\n        port: this.port,\n      });\n      return url;\n    } catch (error: any) {\n      const assertNgrok = () => {\n        if (isNgrokClientError(error)) {\n          throw new CommandError(\n            'NGROK_CONNECT',\n            [\n              error.body.msg,\n              error.body.details?.err,\n              chalk.gray('Check the Ngrok status page for outages: https://status.ngrok.com/'),\n            ]\n              .filter(Boolean)\n              .join('\\n\\n')\n          );\n        }\n        throw new CommandError(\n          'NGROK_CONNECT',\n          error.toString() +\n            chalk.gray('\\nCheck the Ngrok status page for outages: https://status.ngrok.com/')\n        );\n      };\n\n      // Attempt to connect 3 times\n      if (attempts >= 2) {\n        assertNgrok();\n      }\n\n      // Attempt to fix the issue\n      if (isNgrokClientError(error) && error.body.error_code === 103) {\n        // Assert early if a custom subdomain is used since it cannot\n        // be changed and retried. If the tunnel subdomain is a boolean\n        // then we can reset the randomness and try again.\n        if (typeof env.EXPO_TUNNEL_SUBDOMAIN === 'string') {\n          assertNgrok();\n        }\n        // Change randomness to avoid conflict if killing ngrok doesn't help\n        await this._resetProjectRandomnessAsync();\n      }\n\n      return false;\n    }\n  }\n\n  private async getProjectRandomnessAsync() {\n    const { urlRandomness: randomness } = await ProjectSettings.readAsync(this.projectRoot);\n    if (randomness && /^[A-Za-z0-9]/.test(randomness)) {\n      return randomness;\n    }\n    return await this._resetProjectRandomnessAsync();\n  }\n\n  async _resetProjectRandomnessAsync() {\n    let randomness: string;\n    do {\n      randomness = crypto.randomBytes(5).toString('base64url');\n    } while (randomness.startsWith('_')); // _ is an invalid character for a hostname\n\n    await ProjectSettings.setAsync(this.projectRoot, { urlRandomness: randomness });\n    debug('Resetting project randomness:', randomness);\n    return randomness;\n  }\n}\n"], "names": ["AsyncNgrok", "debug", "require", "NGROK_CONFIG", "authToken", "domain", "TUNNEL_TIMEOUT", "constructor", "projectRoot", "port", "serverUrl", "resolver", "NgrokResolver", "getActiveUrl", "_getIdentifyingUrlSegmentsAsync", "user", "getUserAsync", "__typename", "CommandError", "username", "getActorDisplayName", "getProjectRandomnessAsync", "slugify", "remove", "String", "_getProjectHostnameAsync", "join", "_getProjectSubdomainAsync", "startAsync", "timeout", "resolveAsync", "prefersGlobalInstall", "hasAdbReverseAsync", "startAdbReverseAsync", "_connectToNgrokAsync", "Log", "log", "stopAsync", "get", "kill", "options", "attempts", "instance", "should<PERSON>rompt", "autoInstall", "results", "resolveWithTimeout", "connectToNgrokInternalAsync", "errorMessage", "delayAsync", "_getConnectionPropsAsync", "userDefinedSubdomain", "env", "EXPO_TUNNEL_SUBDOMAIN", "subdomain", "hostname", "config<PERSON><PERSON>", "path", "getSettingsDirectory", "urlProps", "url", "connect", "authtoken", "onStatusChange", "status", "error", "chalk", "red", "gray", "assertNgrok", "isNgrokClientError", "body", "msg", "details", "err", "filter", "Boolean", "toString", "error_code", "_resetProjectRandomnessAsync", "url<PERSON><PERSON><PERSON><PERSON>", "randomness", "ProjectSettings", "readAsync", "test", "crypto", "randomBytes", "startsWith", "setAsync"], "mappings": "AAAA;;;;+BAwBaA,YAAU;;aAAVA,UAAU;;;8DAxBL,OAAO;;;;;;;8DACN,QAAQ;;;;;;;+DAC<PERSON>,MAAM;;;;;;;8DACR,SAAS;;;;;;8BAEQ,6BAA6B;sBAChB,qBAAqB;2DAClD,WAAW;uBACe,mBAAmB;qBAC9C,iBAAiB;wBACR,oBAAoB;+BACgB,+BAA+B;4BACvC,iCAAiC;0BAC1D,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,6CAA6C;IACxDC,MAAM,EAAE,YAAY;CACrB,AAAC;AAEF,MAAMC,cAAc,GAAG,EAAE,GAAG,IAAI,AAAC;AAE1B,MAAMN,UAAU;IAOrBO,YACUC,WAAmB,EACnBC,IAAY,CACpB;QAFQD,mBAAAA,WAAmB,CAAA;QACnBC,YAAAA,IAAY,CAAA;aAJdC,SAAS,GAAkB,IAAI;QAMrC,IAAI,CAACC,QAAQ,GAAG,IAAIC,cAAa,cAAA,CAACJ,WAAW,CAAC,CAAC;IACjD;IAEOK,YAAY,GAAkB;QACnC,OAAO,IAAI,CAACH,SAAS,CAAC;IACxB;IAEA,yBAAyB,SACnBI,+BAA+B,GAAsB;QACzD,MAAMC,IAAI,GAAG,MAAMC,IAAAA,KAAY,aAAA,GAAE,AAAC;QAClC,IAAID,CAAAA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEE,UAAU,CAAA,KAAK,OAAO,EAAE;YAChC,MAAM,IAAIC,OAAY,aAAA,CAAC,aAAa,EAAE,qCAAqC,CAAC,CAAC;QAC/E,CAAC;QACD,MAAMC,QAAQ,GAAGC,IAAAA,KAAmB,oBAAA,EAACL,IAAI,CAAC,AAAC;QAE3C,OAAO;YACL,sEAAsE;YACtE,MAAM,IAAI,CAACM,yBAAyB,EAAE;YACtC,uFAAuF;YACvFC,IAAAA,QAAO,EAAA,QAAA,EAACH,QAAQ,EAAE;gBAAEI,MAAM,MAAM;aAAE,CAAC;YACnC,yEAAyE;YACzEC,MAAM,CAAC,IAAI,CAACf,IAAI,CAAC;SAClB,CAAC;IACJ;IAEA,yBAAyB,SACnBgB,wBAAwB,GAAoB;QAChD,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,CAACX,+BAA+B,EAAE,CAAC,CAACY,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEvB,YAAY,CAACE,MAAM,CAAC,CAAC,CAAC;IAC9F;IAEA,yBAAyB,SACnBsB,yBAAyB,GAAoB;QACjD,OAAO,CAAC,MAAM,IAAI,CAACb,+BAA+B,EAAE,CAAC,CAACY,IAAI,CAAC,GAAG,CAAC,CAAC;IAClE;IAEA,mDAAmD,SAC7CE,UAAU,CAAC,EAAEC,OAAO,CAAA,EAAwB,GAAG,EAAE,EAAiB;QACtE,+FAA+F;QAC/F,MAAM,IAAI,CAAClB,QAAQ,CAACmB,YAAY,CAAC;YAC/B,sHAAsH;YACtHC,oBAAoB,EAAE,IAAI;SAC3B,CAAC,CAAC;QAEH,2DAA2D;QAC3D,4CAA4C;QAC5C,IAAIC,IAAAA,WAAkB,mBAAA,GAAE,EAAE;YACxB,iCAAiC;YACjC,IAAI,CAAE,MAAMC,IAAAA,WAAoB,qBAAA,EAAC;gBAAC,IAAI,CAACxB,IAAI;aAAC,CAAC,AAAC,EAAE;gBAC9C,8BAA8B;gBAC9B,MAAM,IAAIS,OAAY,aAAA,CACpB,WAAW,EACX,CAAC,2FAA2F,CAAC,CAC9F,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAACR,SAAS,GAAG,MAAM,IAAI,CAACwB,oBAAoB,CAAC;YAAEL,OAAO;SAAE,CAAC,CAAC;QAE9D5B,KAAK,CAAC,aAAa,EAAE,IAAI,CAACS,SAAS,CAAC,CAAC;QACrCyB,IAAG,CAACC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC3B;IAEA,4CAA4C,SAC/BC,SAAS,GAAkB;YAGhC,GAAmB;QAFzBpC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAEzB,OAAM,CAAA,GAAmB,GAAnB,IAAI,CAACU,QAAQ,CAAC2B,GAAG,EAAE,SAAM,GAAzB,KAAA,CAAyB,GAAzB,GAAmB,CAAEC,IAAI,QAAI,GAA7B,KAAA,CAA6B,GAA7B,GAAmB,CAAEA,IAAI,EAAI,CAAA,CAAC;QACpC,IAAI,CAAC7B,SAAS,GAAG,IAAI,CAAC;IACxB;IAEA,yBAAyB,SACnBwB,oBAAoB,CACxBM,OAA6B,GAAG,EAAE,EAClCC,QAAgB,GAAG,CAAC,EACH;QACjB,gGAAgG;QAChG,MAAM,IAAI,CAACJ,SAAS,EAAE,CAAC;QAEvB,gDAAgD;QAChD,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAAC/B,QAAQ,CAACmB,YAAY,CAAC;YAChDa,YAAY,EAAE,KAAK;YACnBC,WAAW,EAAE,KAAK;SACnB,CAAC,AAAC;QAEH,4DAA4D;QAC5D,gEAAgE;QAChE,MAAMC,OAAO,GAAG,MAAMC,IAAAA,MAAkB,mBAAA,EACtC,IAAM,IAAI,CAACC,2BAA2B,CAACL,QAAQ,EAAED,QAAQ,CAAC,EAC1D;YACEZ,OAAO,EAAEW,OAAO,CAACX,OAAO,IAAIvB,cAAc;YAC1C0C,YAAY,EAAE,wCAAwC;SACvD,CACF,AAAC;QACF,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;YAC/B,OAAOA,OAAO,CAAC;QACjB,CAAC;QAED,gCAAgC;QAChC,MAAMI,IAAAA,MAAU,WAAA,EAAC,GAAG,CAAC,CAAC;QAEtB,OAAO,IAAI,CAACf,oBAAoB,CAACM,OAAO,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAAC;IAC1D;UAEcS,wBAAwB,GAAuD;QAC3F,MAAMC,oBAAoB,GAAGC,IAAG,IAAA,CAACC,qBAAqB,AAAC;QACvD,IAAIF,oBAAoB,EAAE;YACxB,MAAMG,SAAS,GACb,OAAOH,oBAAoB,KAAK,QAAQ,GACpCA,oBAAoB,GACpB,MAAM,IAAI,CAACxB,yBAAyB,EAAE,AAAC;YAC7C1B,KAAK,CAAC,YAAY,EAAEqD,SAAS,CAAC,CAAC;YAC/B,OAAO;gBAAEA,SAAS;aAAE,CAAC;QACvB,OAAO;YACL,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAAC9B,wBAAwB,EAAE,AAAC;YACvDxB,KAAK,CAAC,WAAW,EAAEsD,QAAQ,CAAC,CAAC;YAC7B,OAAO;gBAAEA,QAAQ;aAAE,CAAC;QACtB,CAAC;IACH;UAEcR,2BAA2B,CACvCL,QAAuB,EACvBD,QAAgB,GAAG,CAAC,EACK;QACzB,IAAI;YACF,sBAAsB;YACtB,MAAMe,UAAU,GAAGC,KAAI,EAAA,CAAC/B,IAAI,CAACgC,IAAAA,aAAoB,qBAAA,GAAE,EAAE,WAAW,CAAC,AAAC;YAClEzD,KAAK,CAAC,qBAAqB,EAAEuD,UAAU,CAAC,CAAC;YACzC,MAAMG,QAAQ,GAAG,MAAM,IAAI,CAACT,wBAAwB,EAAE,AAAC;YAEvD,MAAMU,GAAG,GAAG,MAAMlB,QAAQ,CAACmB,OAAO,CAAC;gBACjC,GAAGF,QAAQ;gBACXG,SAAS,EAAE3D,YAAY,CAACC,SAAS;gBACjCoD,UAAU;gBACVO,cAAc,EAACC,MAAM,EAAE;oBACrB,IAAIA,MAAM,KAAK,QAAQ,EAAE;wBACvB7B,IAAG,CAAC8B,KAAK,CACPC,MAAK,EAAA,QAAA,CAACC,GAAG,CACP,+KAA+K,CAChL,GAAGD,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,sEAAsE,CAAC,CACvF,CAAC;oBACJ,OAAO,IAAIJ,MAAM,KAAK,WAAW,EAAE;wBACjC7B,IAAG,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBACD3B,IAAI,EAAE,IAAI,CAACA,IAAI;aAChB,CAAC,AAAC;YACH,OAAOmD,GAAG,CAAC;QACb,EAAE,OAAOK,KAAK,EAAO;YACnB,MAAMI,WAAW,GAAG,IAAM;gBACxB,IAAIC,IAAAA,cAAkB,mBAAA,EAACL,KAAK,CAAC,EAAE;wBAKzBA,GAAkB;oBAJtB,MAAM,IAAI/C,OAAY,aAAA,CACpB,eAAe,EACf;wBACE+C,KAAK,CAACM,IAAI,CAACC,GAAG;wBACdP,CAAAA,GAAkB,GAAlBA,KAAK,CAACM,IAAI,CAACE,OAAO,SAAK,GAAvBR,KAAAA,CAAuB,GAAvBA,GAAkB,CAAES,GAAG;wBACvBR,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,oEAAoE,CAAC;qBACjF,CACEO,MAAM,CAACC,OAAO,CAAC,CACflD,IAAI,CAAC,MAAM,CAAC,CAChB,CAAC;gBACJ,CAAC;gBACD,MAAM,IAAIR,OAAY,aAAA,CACpB,eAAe,EACf+C,KAAK,CAACY,QAAQ,EAAE,GACdX,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,sEAAsE,CAAC,CACrF,CAAC;YACJ,CAAC,AAAC;YAEF,6BAA6B;YAC7B,IAAI3B,QAAQ,IAAI,CAAC,EAAE;gBACjB4B,WAAW,EAAE,CAAC;YAChB,CAAC;YAED,2BAA2B;YAC3B,IAAIC,IAAAA,cAAkB,mBAAA,EAACL,KAAK,CAAC,IAAIA,KAAK,CAACM,IAAI,CAACO,UAAU,KAAK,GAAG,EAAE;gBAC9D,6DAA6D;gBAC7D,+DAA+D;gBAC/D,kDAAkD;gBAClD,IAAI,OAAO1B,IAAG,IAAA,CAACC,qBAAqB,KAAK,QAAQ,EAAE;oBACjDgB,WAAW,EAAE,CAAC;gBAChB,CAAC;gBACD,oEAAoE;gBACpE,MAAM,IAAI,CAACU,4BAA4B,EAAE,CAAC;YAC5C,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH;UAEc1D,yBAAyB,GAAG;QACxC,MAAM,EAAE2D,aAAa,EAAEC,UAAU,CAAA,EAAE,GAAG,MAAMC,SAAe,gBAAA,CAACC,SAAS,CAAC,IAAI,CAAC3E,WAAW,CAAC,AAAC;QACxF,IAAIyE,UAAU,IAAI,eAAeG,IAAI,CAACH,UAAU,CAAC,EAAE;YACjD,OAAOA,UAAU,CAAC;QACpB,CAAC;QACD,OAAO,MAAM,IAAI,CAACF,4BAA4B,EAAE,CAAC;IACnD;UAEMA,4BAA4B,GAAG;QACnC,IAAIE,UAAU,AAAQ,AAAC;QACvB,GAAG;YACDA,UAAU,GAAGI,OAAM,EAAA,QAAA,CAACC,WAAW,CAAC,CAAC,CAAC,CAACT,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC3D,QAASI,UAAU,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,2CAA2C;QAEjF,MAAML,SAAe,gBAAA,CAACM,QAAQ,CAAC,IAAI,CAAChF,WAAW,EAAE;YAAEwE,aAAa,EAAEC,UAAU;SAAE,CAAC,CAAC;QAChFhF,KAAK,CAAC,+BAA+B,EAAEgF,UAAU,CAAC,CAAC;QACnD,OAAOA,UAAU,CAAC;IACpB;CACD"}