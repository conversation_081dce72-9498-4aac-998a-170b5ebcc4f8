{"version": 3, "sources": ["../../../src/utils/cocoapods.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport JsonFile from '@expo/json-file';\nimport * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ensureDirectoryAsync } from './dir';\nimport { env } from './env';\nimport { AbortCommandError } from './errors';\nimport { logNewSection } from './ora';\nimport * as Log from '../log';\nimport { hashForDependencyMap } from '../prebuild/updatePackageJson';\n\ntype PackageChecksums = {\n  /** checksum for the `package.json` dependency object. */\n  dependencies: string;\n  /** checksum for the `package.json` devDependency object. */\n  devDependencies: string;\n};\n\nconst PROJECT_PREBUILD_SETTINGS = '.expo/prebuild';\nconst CACHED_PACKAGE_JSON = 'cached-packages.json';\n\nfunction getTempPrebuildFolder(projectRoot: string): string {\n  return path.join(projectRoot, PROJECT_PREBUILD_SETTINGS);\n}\n\nfunction hasNewDependenciesSinceLastBuild(\n  projectRoot: string,\n  packageChecksums: PackageChecksums\n): boolean {\n  // TODO: Maybe comparing lock files would be better...\n  const templateDirectory = getTempPrebuildFolder(projectRoot);\n  const tempPkgJsonPath = path.join(templateDirectory, CACHED_PACKAGE_JSON);\n  if (!fs.existsSync(tempPkgJsonPath)) {\n    return true;\n  }\n  const { dependencies, devDependencies } = JsonFile.read(tempPkgJsonPath);\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies = packageChecksums.dependencies !== dependencies;\n  const hasNewDevDependencies = packageChecksums.devDependencies !== devDependencies;\n\n  return hasNewDependencies || hasNewDevDependencies;\n}\n\nfunction createPackageChecksums(pkg: PackageJSONConfig): PackageChecksums {\n  return {\n    dependencies: hashForDependencyMap(pkg.dependencies || {}),\n    devDependencies: hashForDependencyMap(pkg.devDependencies || {}),\n  };\n}\n\n/** @returns `true` if the package.json dependency hash does not match the cached hash from the last run. */\nexport async function hasPackageJsonDependencyListChangedAsync(\n  projectRoot: string\n): Promise<boolean> {\n  const pkg = getPackageJson(projectRoot);\n\n  const packages = createPackageChecksums(pkg);\n  const hasNewDependencies = hasNewDependenciesSinceLastBuild(projectRoot, packages);\n\n  // Cache package.json\n  await ensureDirectoryAsync(getTempPrebuildFolder(projectRoot));\n  const templateDirectory = path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON);\n  await JsonFile.writeAsync(templateDirectory, packages);\n\n  return hasNewDependencies;\n}\n\nexport async function installCocoaPodsAsync(projectRoot: string): Promise<boolean> {\n  let step = logNewSection('Installing CocoaPods...');\n  if (process.platform !== 'darwin') {\n    step.succeed('Skipped installing CocoaPods because operating system is not on macOS.');\n    return false;\n  }\n\n  const packageManager = new PackageManager.CocoaPodsPackageManager({\n    cwd: path.join(projectRoot, 'ios'),\n    silent: !(env.EXPO_DEBUG || env.CI),\n  });\n\n  if (!(await packageManager.isCLIInstalledAsync())) {\n    try {\n      // prompt user -- do you want to install cocoapods right now?\n      step.text = 'CocoaPods CLI not found in your PATH, installing it now.';\n      step.stopAndPersist();\n      await PackageManager.CocoaPodsPackageManager.installCLIAsync({\n        nonInteractive: true,\n        spawnOptions: {\n          ...packageManager.options,\n          // Don't silence this part\n          stdio: ['inherit', 'inherit', 'pipe'],\n        },\n      });\n      step.succeed('Installed CocoaPods CLI.');\n      step = logNewSection('Running `pod install` in the `ios` directory.');\n    } catch (error: any) {\n      step.stopAndPersist({\n        symbol: '⚠️ ',\n        text: chalk.red('Unable to install the CocoaPods CLI.'),\n      });\n      if (error instanceof PackageManager.CocoaPodsError) {\n        Log.log(error.message);\n      } else {\n        Log.log(`Unknown error: ${error.message}`);\n      }\n      return false;\n    }\n  }\n\n  try {\n    await packageManager.installAsync({ spinner: step });\n    // Create cached list for later\n    await hasPackageJsonDependencyListChangedAsync(projectRoot).catch(() => null);\n    step.succeed('Installed CocoaPods');\n    return true;\n  } catch (error: any) {\n    step.stopAndPersist({\n      symbol: '⚠️ ',\n      text: chalk.red('Something went wrong running `pod install` in the `ios` directory.'),\n    });\n    if (error instanceof PackageManager.CocoaPodsError) {\n      Log.log(error.message);\n    } else {\n      Log.log(`Unknown error: ${error.message}`);\n    }\n    return false;\n  }\n}\n\nfunction doesProjectUseCocoaPods(projectRoot: string): boolean {\n  return fs.existsSync(path.join(projectRoot, 'ios', 'Podfile'));\n}\n\nfunction isLockfileCreated(projectRoot: string): boolean {\n  const podfileLockPath = path.join(projectRoot, 'ios', 'Podfile.lock');\n  return fs.existsSync(podfileLockPath);\n}\n\nfunction isPodFolderCreated(projectRoot: string): boolean {\n  const podFolderPath = path.join(projectRoot, 'ios', 'Pods');\n  return fs.existsSync(podFolderPath);\n}\n\n// TODO: Same process but with app.config changes + default plugins.\n// This will ensure the user is prompted for extra setup.\nexport async function maybePromptToSyncPodsAsync(projectRoot: string) {\n  if (!doesProjectUseCocoaPods(projectRoot)) {\n    // Project does not use CocoaPods\n    return;\n  }\n  if (!isLockfileCreated(projectRoot) || !isPodFolderCreated(projectRoot)) {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n    return;\n  }\n\n  // Getting autolinked packages can be heavy, optimize around checking every time.\n  if (!(await hasPackageJsonDependencyListChangedAsync(projectRoot))) {\n    return;\n  }\n\n  await promptToInstallPodsAsync(projectRoot, []);\n}\n\nasync function promptToInstallPodsAsync(projectRoot: string, missingPods?: string[]) {\n  if (missingPods?.length) {\n    Log.log(\n      `Could not find the following native modules: ${missingPods\n        .map((pod) => chalk.bold(pod))\n        .join(', ')}. Did you forget to run \"${chalk.bold('pod install')}\" ?`\n    );\n  }\n\n  try {\n    if (!(await installCocoaPodsAsync(projectRoot))) {\n      throw new AbortCommandError();\n    }\n  } catch (error) {\n    await fs.promises.rm(path.join(getTempPrebuildFolder(projectRoot), CACHED_PACKAGE_JSON), {\n      recursive: true,\n      force: true,\n    });\n    throw error;\n  }\n}\n"], "names": ["hasPackageJsonDependencyListChangedAsync", "installCocoaPodsAsync", "maybePromptToSyncPodsAsync", "PROJECT_PREBUILD_SETTINGS", "CACHED_PACKAGE_JSON", "getTempPrebuildFolder", "projectRoot", "path", "join", "hasNewDependenciesSinceLastBuild", "packageChecksums", "templateDirectory", "tempPkgJsonPath", "fs", "existsSync", "dependencies", "devDependencies", "JsonFile", "read", "hasNewDependencies", "hasNewDevDependencies", "createPackageChecksums", "pkg", "hashForDependencyMap", "getPackageJson", "packages", "ensureDirectoryAsync", "writeAsync", "step", "logNewSection", "process", "platform", "succeed", "packageManager", "PackageManager", "CocoaPodsPackageManager", "cwd", "silent", "env", "EXPO_DEBUG", "CI", "isCLIInstalledAsync", "text", "stopAndPersist", "installCLIAsync", "nonInteractive", "spawnOptions", "options", "stdio", "error", "symbol", "chalk", "red", "CocoaPodsError", "Log", "log", "message", "installAsync", "spinner", "catch", "doesProjectUseCocoaPods", "isLockfileCreated", "podfileLockPath", "isPodFolderCreated", "podFolderPath", "AbortCommandError", "promptToInstallPodsAsync", "missingPods", "length", "map", "pod", "bold", "promises", "rm", "recursive", "force"], "mappings": "AAAA;;;;;;;;;;;IAsDsBA,wCAAwC,MAAxCA,wCAAwC;IAgBxCC,qBAAqB,MAArBA,qBAAqB;IA6ErBC,0BAA0B,MAA1BA,0BAA0B;;;yBAnJE,cAAc;;;;;;;8DAC3C,iBAAiB;;;;;;;+DACN,uBAAuB;;;;;;;8DACrC,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DACF,MAAM;;;;;;qBAEc,OAAO;qBACxB,OAAO;wBACO,UAAU;qBACd,OAAO;2DAChB,QAAQ;mCACQ,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE,MAAMC,yBAAyB,GAAG,gBAAgB,AAAC;AACnD,MAAMC,mBAAmB,GAAG,sBAAsB,AAAC;AAEnD,SAASC,qBAAqB,CAACC,WAAmB,EAAU;IAC1D,OAAOC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAEH,yBAAyB,CAAC,CAAC;AAC3D,CAAC;AAED,SAASM,gCAAgC,CACvCH,WAAmB,EACnBI,gBAAkC,EACzB;IACT,sDAAsD;IACtD,MAAMC,iBAAiB,GAAGN,qBAAqB,CAACC,WAAW,CAAC,AAAC;IAC7D,MAAMM,eAAe,GAAGL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACG,iBAAiB,EAAEP,mBAAmB,CAAC,AAAC;IAC1E,IAAI,CAACS,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,eAAe,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,EAAEG,YAAY,CAAA,EAAEC,eAAe,CAAA,EAAE,GAAGC,SAAQ,EAAA,QAAA,CAACC,IAAI,CAACN,eAAe,CAAC,AAAC;IACzE,yGAAyG;IACzG,MAAMO,kBAAkB,GAAGT,gBAAgB,CAACK,YAAY,KAAKA,YAAY,AAAC;IAC1E,MAAMK,qBAAqB,GAAGV,gBAAgB,CAACM,eAAe,KAAKA,eAAe,AAAC;IAEnF,OAAOG,kBAAkB,IAAIC,qBAAqB,CAAC;AACrD,CAAC;AAED,SAASC,sBAAsB,CAACC,GAAsB,EAAoB;IACxE,OAAO;QACLP,YAAY,EAAEQ,IAAAA,kBAAoB,qBAAA,EAACD,GAAG,CAACP,YAAY,IAAI,EAAE,CAAC;QAC1DC,eAAe,EAAEO,IAAAA,kBAAoB,qBAAA,EAACD,GAAG,CAACN,eAAe,IAAI,EAAE,CAAC;KACjE,CAAC;AACJ,CAAC;AAGM,eAAehB,wCAAwC,CAC5DM,WAAmB,EACD;IAClB,MAAMgB,GAAG,GAAGE,IAAAA,OAAc,EAAA,eAAA,EAAClB,WAAW,CAAC,AAAC;IAExC,MAAMmB,QAAQ,GAAGJ,sBAAsB,CAACC,GAAG,CAAC,AAAC;IAC7C,MAAMH,kBAAkB,GAAGV,gCAAgC,CAACH,WAAW,EAAEmB,QAAQ,CAAC,AAAC;IAEnF,qBAAqB;IACrB,MAAMC,IAAAA,IAAoB,qBAAA,EAACrB,qBAAqB,CAACC,WAAW,CAAC,CAAC,CAAC;IAC/D,MAAMK,iBAAiB,GAAGJ,KAAI,EAAA,QAAA,CAACC,IAAI,CAACH,qBAAqB,CAACC,WAAW,CAAC,EAAEF,mBAAmB,CAAC,AAAC;IAC7F,MAAMa,SAAQ,EAAA,QAAA,CAACU,UAAU,CAAChB,iBAAiB,EAAEc,QAAQ,CAAC,CAAC;IAEvD,OAAON,kBAAkB,CAAC;AAC5B,CAAC;AAEM,eAAelB,qBAAqB,CAACK,WAAmB,EAAoB;IACjF,IAAIsB,IAAI,GAAGC,IAAAA,IAAa,cAAA,EAAC,yBAAyB,CAAC,AAAC;IACpD,IAAIC,OAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;QACjCH,IAAI,CAACI,OAAO,CAAC,wEAAwE,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAMC,cAAc,GAAG,IAAIC,CAAAA,eAAc,EAAA,CAAA,CAACC,uBAAuB,CAAC;QAChEC,GAAG,EAAE7B,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,CAAC;QAClC+B,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;KACpC,CAAC,AAAC;IAEH,IAAI,CAAE,MAAMP,cAAc,CAACQ,mBAAmB,EAAE,AAAC,EAAE;QACjD,IAAI;YACF,6DAA6D;YAC7Db,IAAI,CAACc,IAAI,GAAG,0DAA0D,CAAC;YACvEd,IAAI,CAACe,cAAc,EAAE,CAAC;YACtB,MAAMT,eAAc,EAAA,CAACC,uBAAuB,CAACS,eAAe,CAAC;gBAC3DC,cAAc,EAAE,IAAI;gBACpBC,YAAY,EAAE;oBACZ,GAAGb,cAAc,CAACc,OAAO;oBACzB,0BAA0B;oBAC1BC,KAAK,EAAE;wBAAC,SAAS;wBAAE,SAAS;wBAAE,MAAM;qBAAC;iBACtC;aACF,CAAC,CAAC;YACHpB,IAAI,CAACI,OAAO,CAAC,0BAA0B,CAAC,CAAC;YACzCJ,IAAI,GAAGC,IAAAA,IAAa,cAAA,EAAC,+CAA+C,CAAC,CAAC;QACxE,EAAE,OAAOoB,KAAK,EAAO;YACnBrB,IAAI,CAACe,cAAc,CAAC;gBAClBO,MAAM,EAAE,KAAK;gBACbR,IAAI,EAAES,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,sCAAsC,CAAC;aACxD,CAAC,CAAC;YACH,IAAIH,KAAK,YAAYf,eAAc,EAAA,CAACmB,cAAc,EAAE;gBAClDC,IAAG,CAACC,GAAG,CAACN,KAAK,CAACO,OAAO,CAAC,CAAC;YACzB,OAAO;gBACLF,IAAG,CAACC,GAAG,CAAC,CAAC,eAAe,EAAEN,KAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,IAAI;QACF,MAAMvB,cAAc,CAACwB,YAAY,CAAC;YAAEC,OAAO,EAAE9B,IAAI;SAAE,CAAC,CAAC;QACrD,+BAA+B;QAC/B,MAAM5B,wCAAwC,CAACM,WAAW,CAAC,CAACqD,KAAK,CAAC,IAAM,IAAI,CAAC,CAAC;QAC9E/B,IAAI,CAACI,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,EAAE,OAAOiB,MAAK,EAAO;QACnBrB,IAAI,CAACe,cAAc,CAAC;YAClBO,MAAM,EAAE,KAAK;YACbR,IAAI,EAAES,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,oEAAoE,CAAC;SACtF,CAAC,CAAC;QACH,IAAIH,MAAK,YAAYf,eAAc,EAAA,CAACmB,cAAc,EAAE;YAClDC,IAAG,CAACC,GAAG,CAACN,MAAK,CAACO,OAAO,CAAC,CAAC;QACzB,OAAO;YACLF,IAAG,CAACC,GAAG,CAAC,CAAC,eAAe,EAAEN,MAAK,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAASI,uBAAuB,CAACtD,WAAmB,EAAW;IAC7D,OAAOO,GAAE,EAAA,QAAA,CAACC,UAAU,CAACP,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAASuD,iBAAiB,CAACvD,WAAmB,EAAW;IACvD,MAAMwD,eAAe,GAAGvD,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,AAAC;IACtE,OAAOO,GAAE,EAAA,QAAA,CAACC,UAAU,CAACgD,eAAe,CAAC,CAAC;AACxC,CAAC;AAED,SAASC,kBAAkB,CAACzD,WAAmB,EAAW;IACxD,MAAM0D,aAAa,GAAGzD,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,AAAC;IAC5D,OAAOO,GAAE,EAAA,QAAA,CAACC,UAAU,CAACkD,aAAa,CAAC,CAAC;AACtC,CAAC;AAIM,eAAe9D,0BAA0B,CAACI,WAAmB,EAAE;IACpE,IAAI,CAACsD,uBAAuB,CAACtD,WAAW,CAAC,EAAE;QACzC,iCAAiC;QACjC,OAAO;IACT,CAAC;IACD,IAAI,CAACuD,iBAAiB,CAACvD,WAAW,CAAC,IAAI,CAACyD,kBAAkB,CAACzD,WAAW,CAAC,EAAE;QACvE,IAAI,CAAE,MAAML,qBAAqB,CAACK,WAAW,CAAC,AAAC,EAAE;YAC/C,MAAM,IAAI2D,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QACD,OAAO;IACT,CAAC;IAED,iFAAiF;IACjF,IAAI,CAAE,MAAMjE,wCAAwC,CAACM,WAAW,CAAC,AAAC,EAAE;QAClE,OAAO;IACT,CAAC;IAED,MAAM4D,wBAAwB,CAAC5D,WAAW,EAAE,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,eAAe4D,wBAAwB,CAAC5D,WAAmB,EAAE6D,WAAsB,EAAE;IACnF,IAAIA,WAAW,QAAQ,GAAnBA,KAAAA,CAAmB,GAAnBA,WAAW,CAAEC,MAAM,EAAE;QACvBd,IAAG,CAACC,GAAG,CACL,CAAC,6CAA6C,EAAEY,WAAW,CACxDE,GAAG,CAAC,CAACC,GAAG,GAAKnB,MAAK,EAAA,QAAA,CAACoB,IAAI,CAACD,GAAG,CAAC,CAAC,CAC7B9D,IAAI,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE2C,MAAK,EAAA,QAAA,CAACoB,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC;IACJ,CAAC;IAED,IAAI;QACF,IAAI,CAAE,MAAMtE,qBAAqB,CAACK,WAAW,CAAC,AAAC,EAAE;YAC/C,MAAM,IAAI2D,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;IACH,EAAE,OAAOhB,KAAK,EAAE;QACd,MAAMpC,GAAE,EAAA,QAAA,CAAC2D,QAAQ,CAACC,EAAE,CAAClE,KAAI,EAAA,QAAA,CAACC,IAAI,CAACH,qBAAqB,CAACC,WAAW,CAAC,EAAEF,mBAAmB,CAAC,EAAE;YACvFsE,SAAS,EAAE,IAAI;YACfC,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,MAAM1B,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}