{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/LogBoxLog.ts"], "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as LogBoxSymbolication from './LogBoxSymbolication';\nimport type { CodeFrame, StackFrame } from './LogBoxSymbolication';\n\ntype Category = string;\n\ntype Message = {\n  content: string;\n  substitutions: {\n    length: number;\n    offset: number;\n  }[];\n};\n\ntype ComponentStack = CodeFrame[];\n\ntype SymbolicationStatus = 'NONE' | 'PENDING' | 'COMPLETE' | 'FAILED';\n\nexport type LogLevel = 'warn' | 'error' | 'fatal' | 'syntax' | 'static';\n\nexport type LogBoxLogData = {\n  level: LogLevel;\n  type?: string;\n  message: Message;\n  stack: StackFrame[];\n  category: string;\n  componentStack: ComponentStack;\n  codeFrame?: CodeFrame;\n  isComponentError: boolean;\n};\n\nexport type StackType = 'stack' | 'component';\n\nfunction componentStackToStack(componentStack: ComponentStack): StackFrame[] {\n  return componentStack.map((stack) => ({\n    file: stack.fileName,\n    methodName: stack.content,\n    lineNumber: stack.location?.row ?? 0,\n    column: stack.location?.column ?? 0,\n    arguments: [],\n  }));\n}\n\ntype SymbolicationCallback = (status: SymbolicationStatus) => void;\n\ntype SymbolicationResult =\n  | { error: null; stack: null; status: 'NONE' }\n  | { error: null; stack: null; status: 'PENDING' }\n  | { error: null; stack: StackFrame[]; status: 'COMPLETE' }\n  | { error: Error; stack: null; status: 'FAILED' };\n\nexport class LogBoxLog {\n  message: Message;\n  type: string;\n  category: Category;\n  componentStack: ComponentStack;\n  stack: StackFrame[];\n  count: number;\n  level: LogLevel;\n  codeFrame?: CodeFrame;\n  isComponentError: boolean;\n  symbolicated: Record<StackType, SymbolicationResult> = {\n    stack: {\n      error: null,\n      stack: null,\n      status: 'NONE',\n    },\n    component: {\n      error: null,\n      stack: null,\n      status: 'NONE',\n    },\n  };\n\n  private callbacks: Map<StackType, Set<SymbolicationCallback>> = new Map();\n\n  constructor(\n    data: LogBoxLogData & {\n      symbolicated?: Record<StackType, SymbolicationResult>;\n    }\n  ) {\n    this.level = data.level;\n    this.type = data.type ?? 'error';\n    this.message = data.message;\n    this.stack = data.stack;\n    this.category = data.category;\n    this.componentStack = data.componentStack;\n    this.codeFrame = data.codeFrame;\n    this.isComponentError = data.isComponentError;\n    this.count = 1;\n    this.symbolicated = data.symbolicated ?? this.symbolicated;\n  }\n\n  incrementCount(): void {\n    this.count += 1;\n  }\n\n  getAvailableStack(type: StackType): StackFrame[] | null {\n    if (this.symbolicated[type].status === 'COMPLETE') {\n      return this.symbolicated[type].stack;\n    }\n    return this.getStack(type);\n  }\n\n  private flushCallbacks(type: StackType): void {\n    const callbacks = this.callbacks.get(type);\n    const status = this.symbolicated[type].status;\n    if (callbacks) {\n      for (const callback of callbacks) {\n        callback(status);\n      }\n      callbacks.clear();\n    }\n  }\n\n  private pushCallback(type: StackType, callback: SymbolicationCallback): void {\n    let callbacks = this.callbacks.get(type);\n    if (!callbacks) {\n      callbacks = new Set();\n      this.callbacks.set(type, callbacks);\n    }\n    callbacks.add(callback);\n  }\n\n  retrySymbolicate(type: StackType, callback?: (status: SymbolicationStatus) => void): void {\n    this._symbolicate(type, true, callback);\n  }\n\n  symbolicate(type: StackType, callback?: (status: SymbolicationStatus) => void): void {\n    this._symbolicate(type, false, callback);\n  }\n\n  private _symbolicate(\n    type: StackType,\n    retry: boolean,\n    callback?: (status: SymbolicationStatus) => void\n  ): void {\n    if (callback) {\n      this.pushCallback(type, callback);\n    }\n    const status = this.symbolicated[type].status;\n\n    if (status === 'COMPLETE') {\n      return this.flushCallbacks(type);\n    }\n\n    if (retry) {\n      LogBoxSymbolication.deleteStack(this.getStack(type));\n      this.handleSymbolicate(type);\n    } else {\n      if (status === 'NONE') {\n        this.handleSymbolicate(type);\n      }\n    }\n  }\n\n  private componentStackCache: StackFrame[] | null = null;\n\n  private getStack(type: StackType): StackFrame[] {\n    if (type === 'component') {\n      if (this.componentStackCache == null) {\n        this.componentStackCache = componentStackToStack(this.componentStack);\n      }\n      return this.componentStackCache;\n    }\n    return this.stack;\n  }\n\n  private handleSymbolicate(type: StackType): void {\n    if (type === 'component' && !this.componentStack?.length) {\n      return;\n    }\n\n    if (this.symbolicated[type].status !== 'PENDING') {\n      this.updateStatus(type, null, null, null);\n      LogBoxSymbolication.symbolicate(this.getStack(type)).then(\n        (data) => {\n          this.updateStatus(type, null, data?.stack, data?.codeFrame);\n        },\n        (error) => {\n          this.updateStatus(type, error, null, null);\n        }\n      );\n    }\n  }\n\n  private updateStatus(\n    type: StackType,\n    error?: Error | null,\n    stack?: StackFrame[] | null,\n    codeFrame?: CodeFrame | null\n  ): void {\n    const lastStatus = this.symbolicated[type].status;\n    if (error != null) {\n      this.symbolicated[type] = {\n        error,\n        stack: null,\n        status: 'FAILED',\n      };\n    } else if (stack != null) {\n      if (codeFrame) {\n        this.codeFrame = codeFrame;\n      }\n\n      this.symbolicated[type] = {\n        error: null,\n        stack,\n        status: 'COMPLETE',\n      };\n    } else {\n      this.symbolicated[type] = {\n        error: null,\n        stack: null,\n        status: 'PENDING',\n      };\n    }\n\n    const status = this.symbolicated[type].status;\n    if (lastStatus !== status) {\n      if (['COMPLETE', 'FAILED'].includes(status)) {\n        this.flushCallbacks(type);\n      }\n    }\n  }\n}\n"], "names": ["LogBoxLog", "componentStackToStack", "componentStack", "map", "stack", "file", "fileName", "methodName", "content", "lineNumber", "location", "row", "column", "arguments", "symbolicated", "error", "status", "component", "callbacks", "Map", "constructor", "data", "level", "type", "message", "category", "codeFrame", "isComponentError", "count", "incrementCount", "getAvailableStack", "getStack", "flushCallbacks", "get", "callback", "clear", "pushCallback", "Set", "set", "add", "retrySymbolicate", "_symbolicate", "symbolicate", "retry", "LogBoxSymbolication", "deleteStack", "handleSymbolicate", "componentStackCache", "length", "updateStatus", "then", "lastStatus", "includes"], "mappings": "AAAA;;;;;;CAMC,GAED;;;;+BAkDaA,WAAS;;aAATA,SAAS;;2EAlDe,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC5D,SAASC,qBAAqB,CAACC,cAA8B,EAAgB;IAC3E,OAAOA,cAAc,CAACC,GAAG,CAAC,CAACC,KAAK;YAGlBA,GAAc,EAClBA,IAAc;QAJa,OAAC;YACpCC,IAAI,EAAED,KAAK,CAACE,QAAQ;YACpBC,UAAU,EAAEH,KAAK,CAACI,OAAO;YACzBC,UAAU,EAAEL,CAAAA,CAAAA,GAAc,GAAdA,KAAK,CAACM,QAAQ,SAAK,GAAnBN,KAAAA,CAAmB,GAAnBA,GAAc,CAAEO,GAAG,CAAA,IAAI,CAAC;YACpCC,MAAM,EAAER,CAAAA,CAAAA,IAAc,GAAdA,KAAK,CAACM,QAAQ,SAAQ,GAAtBN,KAAAA,CAAsB,GAAtBA,IAAc,CAAEQ,MAAM,CAAA,IAAI,CAAC;YACnCC,SAAS,EAAE,EAAE;SACd,CAAC;KAAA,CAAC,CAAC;AACN,CAAC;AAUM,MAAMb,SAAS;IAUpBc,YAAY,GAA2C;QACrDV,KAAK,EAAE;YACLW,KAAK,EAAE,IAAI;YACXX,KAAK,EAAE,IAAI;YACXY,MAAM,EAAE,MAAM;SACf;QACDC,SAAS,EAAE;YACTF,KAAK,EAAE,IAAI;YACXX,KAAK,EAAE,IAAI;YACXY,MAAM,EAAE,MAAM;SACf;KACF,CAAC;IAEF,AAAQE,SAAS,GAA+C,IAAIC,GAAG,EAAE,CAAC;IAE1EC,YACEC,IAEC,CACD;QACA,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC;QACxB,IAAI,CAACC,IAAI,GAAGF,IAAI,CAACE,IAAI,IAAI,OAAO,CAAC;QACjC,IAAI,CAACC,OAAO,GAAGH,IAAI,CAACG,OAAO,CAAC;QAC5B,IAAI,CAACpB,KAAK,GAAGiB,IAAI,CAACjB,KAAK,CAAC;QACxB,IAAI,CAACqB,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,CAAC;QAC9B,IAAI,CAACvB,cAAc,GAAGmB,IAAI,CAACnB,cAAc,CAAC;QAC1C,IAAI,CAACwB,SAAS,GAAGL,IAAI,CAACK,SAAS,CAAC;QAChC,IAAI,CAACC,gBAAgB,GAAGN,IAAI,CAACM,gBAAgB,CAAC;QAC9C,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAACd,YAAY,GAAGO,IAAI,CAACP,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC;IAC7D;IAEAe,cAAc,GAAS;QACrB,IAAI,CAACD,KAAK,IAAI,CAAC,CAAC;IAClB;IAEAE,iBAAiB,CAACP,IAAe,EAAuB;QACtD,IAAI,IAAI,CAACT,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,KAAK,UAAU,EAAE;YACjD,OAAO,IAAI,CAACF,YAAY,CAACS,IAAI,CAAC,CAACnB,KAAK,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC2B,QAAQ,CAACR,IAAI,CAAC,CAAC;IAC7B;IAEQS,cAAc,CAACT,IAAe,EAAQ;QAC5C,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS,CAACe,GAAG,CAACV,IAAI,CAAC,AAAC;QAC3C,MAAMP,MAAM,GAAG,IAAI,CAACF,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,AAAC;QAC9C,IAAIE,SAAS,EAAE;YACb,KAAK,MAAMgB,QAAQ,IAAIhB,SAAS,CAAE;gBAChCgB,QAAQ,CAAClB,MAAM,CAAC,CAAC;YACnB,CAAC;YACDE,SAAS,CAACiB,KAAK,EAAE,CAAC;QACpB,CAAC;IACH;IAEQC,YAAY,CAACb,IAAe,EAAEW,QAA+B,EAAQ;QAC3E,IAAIhB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACe,GAAG,CAACV,IAAI,CAAC,AAAC;QACzC,IAAI,CAACL,SAAS,EAAE;YACdA,SAAS,GAAG,IAAImB,GAAG,EAAE,CAAC;YACtB,IAAI,CAACnB,SAAS,CAACoB,GAAG,CAACf,IAAI,EAAEL,SAAS,CAAC,CAAC;QACtC,CAAC;QACDA,SAAS,CAACqB,GAAG,CAACL,QAAQ,CAAC,CAAC;IAC1B;IAEAM,gBAAgB,CAACjB,IAAe,EAAEW,QAAgD,EAAQ;QACxF,IAAI,CAACO,YAAY,CAAClB,IAAI,EAAE,IAAI,EAAEW,QAAQ,CAAC,CAAC;IAC1C;IAEAQ,WAAW,CAACnB,IAAe,EAAEW,QAAgD,EAAQ;QACnF,IAAI,CAACO,YAAY,CAAClB,IAAI,EAAE,KAAK,EAAEW,QAAQ,CAAC,CAAC;IAC3C;IAEQO,YAAY,CAClBlB,IAAe,EACfoB,KAAc,EACdT,QAAgD,EAC1C;QACN,IAAIA,QAAQ,EAAE;YACZ,IAAI,CAACE,YAAY,CAACb,IAAI,EAAEW,QAAQ,CAAC,CAAC;QACpC,CAAC;QACD,MAAMlB,MAAM,GAAG,IAAI,CAACF,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,AAAC;QAE9C,IAAIA,MAAM,KAAK,UAAU,EAAE;YACzB,OAAO,IAAI,CAACgB,cAAc,CAACT,IAAI,CAAC,CAAC;QACnC,CAAC;QAED,IAAIoB,KAAK,EAAE;YACTC,oBAAmB,CAACC,WAAW,CAAC,IAAI,CAACd,QAAQ,CAACR,IAAI,CAAC,CAAC,CAAC;YACrD,IAAI,CAACuB,iBAAiB,CAACvB,IAAI,CAAC,CAAC;QAC/B,OAAO;YACL,IAAIP,MAAM,KAAK,MAAM,EAAE;gBACrB,IAAI,CAAC8B,iBAAiB,CAACvB,IAAI,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;IACH;IAEA,AAAQwB,mBAAmB,GAAwB,IAAI,CAAC;IAEhDhB,QAAQ,CAACR,IAAe,EAAgB;QAC9C,IAAIA,IAAI,KAAK,WAAW,EAAE;YACxB,IAAI,IAAI,CAACwB,mBAAmB,IAAI,IAAI,EAAE;gBACpC,IAAI,CAACA,mBAAmB,GAAG9C,qBAAqB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC;YACxE,CAAC;YACD,OAAO,IAAI,CAAC6C,mBAAmB,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC3C,KAAK,CAAC;IACpB;IAEQ0C,iBAAiB,CAACvB,IAAe,EAAQ;YAClB,GAAmB;QAAhD,IAAIA,IAAI,KAAK,WAAW,IAAI,CAAC,CAAA,CAAA,GAAmB,GAAnB,IAAI,CAACrB,cAAc,SAAQ,GAA3B,KAAA,CAA2B,GAA3B,GAAmB,CAAE8C,MAAM,CAAA,EAAE;YACxD,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAClC,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,KAAK,SAAS,EAAE;YAChD,IAAI,CAACiC,YAAY,CAAC1B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC1CqB,oBAAmB,CAACF,WAAW,CAAC,IAAI,CAACX,QAAQ,CAACR,IAAI,CAAC,CAAC,CAAC2B,IAAI,CACvD,CAAC7B,IAAI,GAAK;gBACR,IAAI,CAAC4B,YAAY,CAAC1B,IAAI,EAAE,IAAI,EAAEF,IAAI,QAAO,GAAXA,KAAAA,CAAW,GAAXA,IAAI,CAAEjB,KAAK,EAAEiB,IAAI,QAAW,GAAfA,KAAAA,CAAe,GAAfA,IAAI,CAAEK,SAAS,CAAC,CAAC;YAC9D,CAAC,EACD,CAACX,KAAK,GAAK;gBACT,IAAI,CAACkC,YAAY,CAAC1B,IAAI,EAAER,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7C,CAAC,CACF,CAAC;QACJ,CAAC;IACH;IAEQkC,YAAY,CAClB1B,IAAe,EACfR,KAAoB,EACpBX,KAA2B,EAC3BsB,SAA4B,EACtB;QACN,MAAMyB,UAAU,GAAG,IAAI,CAACrC,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,AAAC;QAClD,IAAID,KAAK,IAAI,IAAI,EAAE;YACjB,IAAI,CAACD,YAAY,CAACS,IAAI,CAAC,GAAG;gBACxBR,KAAK;gBACLX,KAAK,EAAE,IAAI;gBACXY,MAAM,EAAE,QAAQ;aACjB,CAAC;QACJ,OAAO,IAAIZ,KAAK,IAAI,IAAI,EAAE;YACxB,IAAIsB,SAAS,EAAE;gBACb,IAAI,CAACA,SAAS,GAAGA,SAAS,CAAC;YAC7B,CAAC;YAED,IAAI,CAACZ,YAAY,CAACS,IAAI,CAAC,GAAG;gBACxBR,KAAK,EAAE,IAAI;gBACXX,KAAK;gBACLY,MAAM,EAAE,UAAU;aACnB,CAAC;QACJ,OAAO;YACL,IAAI,CAACF,YAAY,CAACS,IAAI,CAAC,GAAG;gBACxBR,KAAK,EAAE,IAAI;gBACXX,KAAK,EAAE,IAAI;gBACXY,MAAM,EAAE,SAAS;aAClB,CAAC;QACJ,CAAC;QAED,MAAMA,MAAM,GAAG,IAAI,CAACF,YAAY,CAACS,IAAI,CAAC,CAACP,MAAM,AAAC;QAC9C,IAAImC,UAAU,KAAKnC,MAAM,EAAE;YACzB,IAAI;gBAAC,UAAU;gBAAE,QAAQ;aAAC,CAACoC,QAAQ,CAACpC,MAAM,CAAC,EAAE;gBAC3C,IAAI,CAACgB,cAAc,CAACT,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;IACH;CACD"}