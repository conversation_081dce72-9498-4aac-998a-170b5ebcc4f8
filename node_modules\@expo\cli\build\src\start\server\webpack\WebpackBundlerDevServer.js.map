{"version": 3, "sources": ["../../../../../src/start/server/webpack/WebpackBundlerDevServer.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport type { Application } from 'express';\nimport fs from 'node:fs';\nimport http from 'node:http';\nimport path from 'node:path';\nimport resolveFrom from 'resolve-from';\nimport type webpack from 'webpack';\nimport type WebpackDevServer from 'webpack-dev-server';\n\nimport { compileAsync } from './compile';\nimport {\n  importExpoWebpackConfigFromProject,\n  importWebpackDevServerFromProject,\n  importWebpackFromProject,\n} from './resolveFromProject';\nimport { ensureEnvironmentSupportsTLSAsync } from './tls';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { getIpAddress } from '../../../utils/ip';\nimport { setNodeEnv } from '../../../utils/nodeEnv';\nimport { choosePortAsync } from '../../../utils/port';\nimport { createProgressBar } from '../../../utils/progress';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\nimport { BundlerDevServer, BundlerStartOptions, DevServerInstance } from '../BundlerDevServer';\n\nconst debug = require('debug')('expo:start:server:webpack:devServer') as typeof console.log;\n\nexport type WebpackConfiguration = webpack.Configuration & {\n  devServer?: {\n    before?: (app: Application, server: WebpackDevServer, compiler: webpack.Compiler) => void;\n  };\n};\n\nfunction assertIsWebpackDevServer(value: any): asserts value is WebpackDevServer {\n  if (!value?.sockWrite && !value?.sendMessage) {\n    throw new CommandError(\n      'WEBPACK',\n      value\n        ? 'Expected Webpack dev server, found: ' + (value.constructor?.name ?? value)\n        : 'Webpack dev server not started yet.'\n    );\n  }\n}\n\nexport class WebpackBundlerDevServer extends BundlerDevServer {\n  get name(): string {\n    return 'webpack';\n  }\n\n  public async startTypeScriptServices(): Promise<void> {\n    //  noop -- this feature is Metro-only.\n  }\n\n  public broadcastMessage(\n    method: string | 'reload' | 'devMenu' | 'sendDevCommand',\n    params?: Record<string, any>\n  ): void {\n    if (!this.instance) {\n      return;\n    }\n\n    assertIsWebpackDevServer(this.instance?.server);\n\n    // TODO(EvanBacon): Custom Webpack overlay.\n    // Default webpack-dev-server sockets use \"content-changed\" instead of \"reload\" (what we use on native).\n    // For now, just manually convert the value so our CLI interface can be unified.\n    const hackyConvertedMessage = method === 'reload' ? 'content-changed' : method;\n\n    if ('sendMessage' in this.instance.server) {\n      // @ts-expect-error: https://github.com/expo/expo/issues/21994#issuecomment-1517122501\n      this.instance.server.sendMessage(this.instance.server.sockets, hackyConvertedMessage, params);\n    } else {\n      this.instance.server.sockWrite(this.instance.server.sockets, hackyConvertedMessage, params);\n    }\n  }\n\n  isTargetingNative(): boolean {\n    return false;\n  }\n\n  private async getAvailablePortAsync(options: { defaultPort?: number }): Promise<number> {\n    try {\n      const defaultPort = options?.defaultPort ?? 19006;\n      const port = await choosePortAsync(this.projectRoot, {\n        defaultPort,\n        host: env.WEB_HOST,\n      });\n      if (!port) {\n        throw new CommandError('NO_PORT_FOUND', `Port ${defaultPort} not available.`);\n      }\n      return port;\n    } catch (error: any) {\n      throw new CommandError('NO_PORT_FOUND', error.message);\n    }\n  }\n\n  async bundleAsync({ mode, clear }: { mode: 'development' | 'production'; clear: boolean }) {\n    // Do this first to fail faster.\n    const webpack = importWebpackFromProject(this.projectRoot);\n\n    if (clear) {\n      await this.clearWebProjectCacheAsync(this.projectRoot, mode);\n    }\n\n    const config = await this.loadConfigAsync({\n      isImageEditingEnabled: true,\n      mode,\n    });\n\n    if (!config.plugins) {\n      config.plugins = [];\n    }\n\n    const bar = createProgressBar(chalk`{bold Web} Bundling Javascript [:bar] :percent`, {\n      width: 64,\n      total: 100,\n      clear: true,\n      complete: '=',\n      incomplete: ' ',\n    });\n\n    // NOTE(EvanBacon): Add a progress bar to the webpack logger if defined (e.g. not in CI).\n    if (bar != null) {\n      config.plugins.push(\n        new webpack.ProgressPlugin((percent: number) => {\n          bar?.update(percent);\n          if (percent === 1) {\n            bar?.terminate();\n          }\n        })\n      );\n    }\n\n    // Create a webpack compiler that is configured with custom messages.\n    const compiler = webpack(config);\n\n    try {\n      await compileAsync(compiler);\n    } catch (error: any) {\n      Log.error(chalk.red('Failed to compile'));\n      throw error;\n    } finally {\n      bar?.terminate();\n    }\n  }\n\n  protected async startImplementationAsync(\n    options: BundlerStartOptions\n  ): Promise<DevServerInstance> {\n    // Do this first to fail faster.\n    const webpack = importWebpackFromProject(this.projectRoot);\n    const WebpackDevServer = importWebpackDevServerFromProject(this.projectRoot);\n\n    await this.stopAsync();\n\n    options.port = await this.getAvailablePortAsync({\n      defaultPort: options.port,\n    });\n    const { resetDevServer, https, port, mode } = options;\n\n    this.urlCreator = this.getUrlCreator({\n      port,\n      location: {\n        scheme: https ? 'https' : 'http',\n      },\n    });\n\n    debug('Starting webpack on port: ' + port);\n\n    if (resetDevServer) {\n      await this.clearWebProjectCacheAsync(this.projectRoot, mode);\n    }\n\n    if (https) {\n      debug('Configuring TLS to enable HTTPS support');\n      await ensureEnvironmentSupportsTLSAsync(this.projectRoot).catch((error) => {\n        Log.error(`Error creating TLS certificates: ${error}`);\n      });\n    }\n\n    const config = await this.loadConfigAsync(options);\n\n    Log.log(chalk`Starting Webpack on port ${port} in {underline ${mode}} mode.`);\n\n    // Create a webpack compiler that is configured with custom messages.\n    const compiler = webpack(config);\n\n    const server = new WebpackDevServer(compiler, config.devServer);\n    // Launch WebpackDevServer.\n    server.listen(port, env.WEB_HOST, function (this: http.Server, error) {\n      if (error) {\n        Log.error(error.message);\n      }\n    });\n\n    // Extend the close method to ensure that we clean up the local info.\n    const originalClose = server.close.bind(server);\n\n    server.close = (callback?: (err?: Error) => void) => {\n      return originalClose((err?: Error) => {\n        this.instance = null;\n        callback?.(err);\n      });\n    };\n\n    const _host = getIpAddress();\n    const protocol = https ? 'https' : 'http';\n\n    return {\n      // Server instance\n      server,\n      // URL Info\n      location: {\n        url: `${protocol}://${_host}:${port}`,\n        port,\n        protocol,\n        host: _host,\n      },\n      middleware: null,\n      // Match the native protocol.\n      messageSocket: {\n        broadcast: this.broadcastMessage,\n      },\n    };\n  }\n\n  /** Load the Webpack config. Exposed for testing. */\n  getProjectConfigFilePath(): string | null {\n    // Check if the project has a webpack.config.js in the root.\n    return (\n      this.getConfigModuleIds().reduce<string | null | undefined>(\n        (prev, moduleId) => prev || resolveFrom.silent(this.projectRoot, moduleId),\n        null\n      ) ?? null\n    );\n  }\n\n  async loadConfigAsync(\n    options: Pick<BundlerStartOptions, 'mode' | 'isImageEditingEnabled' | 'https'>,\n    argv?: string[]\n  ): Promise<WebpackConfiguration> {\n    // let bar: ProgressBar | null = null;\n\n    const env = {\n      projectRoot: this.projectRoot,\n      pwa: !!options.isImageEditingEnabled,\n      // TODO: Use a new loader in Webpack config...\n      logger: {\n        info() {},\n      },\n      mode: options.mode,\n      https: options.https,\n    };\n    setNodeEnv(env.mode ?? 'development');\n    require('@expo/env').load(env.projectRoot);\n    // Check if the project has a webpack.config.js in the root.\n    const projectWebpackConfig = this.getProjectConfigFilePath();\n    let config: WebpackConfiguration;\n    if (projectWebpackConfig) {\n      const webpackConfig = require(projectWebpackConfig);\n      if (typeof webpackConfig === 'function') {\n        config = await webpackConfig(env, argv);\n      } else {\n        config = webpackConfig;\n      }\n    } else {\n      // Fallback to the default expo webpack config.\n      const loadDefaultConfigAsync = importExpoWebpackConfigFromProject(this.projectRoot);\n      config = await loadDefaultConfigAsync(env, argv);\n    }\n    return config;\n  }\n\n  protected getConfigModuleIds(): string[] {\n    return ['./webpack.config.js'];\n  }\n\n  protected async clearWebProjectCacheAsync(\n    projectRoot: string,\n    mode: string = 'development'\n  ): Promise<void> {\n    Log.log(chalk.dim(`Clearing Webpack ${mode} cache directory...`));\n\n    const dir = await ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    const cacheFolder = path.join(dir, 'web/cache', mode);\n    try {\n      await fs.promises.rm(cacheFolder, { recursive: true, force: true });\n    } catch (error: any) {\n      Log.error(`Could not clear ${mode} web cache directory: ${error.message}`);\n    }\n  }\n}\n\nexport function getProjectWebpackConfigFilePath(projectRoot: string) {\n  return resolveFrom.silent(projectRoot, './webpack.config.js');\n}\n"], "names": ["WebpackBundlerDevServer", "getProjectWebpackConfigFilePath", "debug", "require", "assertIsWebpackDevServer", "value", "sockWrite", "sendMessage", "CommandError", "constructor", "name", "BundlerDevServer", "startTypeScriptServices", "broadcastMessage", "method", "params", "instance", "server", "hackyConvertedMessage", "sockets", "isTargetingNative", "getAvailablePortAsync", "options", "defaultPort", "port", "choosePortAsync", "projectRoot", "host", "env", "WEB_HOST", "error", "message", "bundleAsync", "mode", "clear", "webpack", "importWebpackFromProject", "clearWebProjectCacheAsync", "config", "loadConfigAsync", "isImageEditingEnabled", "plugins", "bar", "createProgressBar", "chalk", "width", "total", "complete", "incomplete", "push", "ProgressPlugin", "percent", "update", "terminate", "compiler", "compileAsync", "Log", "red", "startImplementationAsync", "WebpackDevServer", "importWebpackDevServerFromProject", "stopAsync", "resetDevServer", "https", "urlCreator", "getUrlCreator", "location", "scheme", "ensureEnvironmentSupportsTLSAsync", "catch", "log", "devServer", "listen", "originalClose", "close", "bind", "callback", "err", "_host", "getIpAddress", "protocol", "url", "middleware", "messageSocket", "broadcast", "getProjectConfigFilePath", "getConfigModuleIds", "reduce", "prev", "moduleId", "resolveFrom", "silent", "argv", "pwa", "logger", "info", "setNodeEnv", "load", "projectWebpackConfig", "webpackConfig", "loadDefaultConfigAsync", "importExpoWebpackConfigFromProject", "dim", "dir", "ensureDotExpoProjectDirectoryInitialized", "cacheFolder", "path", "join", "fs", "promises", "rm", "recursive", "force"], "mappings": "AAAA;;;;;;;;;;;IA6CaA,uBAAuB,MAAvBA,uBAAuB;IAyPpBC,+BAA+B,MAA/BA,+BAA+B;;;8DAtS7B,OAAO;;;;;;;8DAEV,SAAS;;;;;;;8DAEP,WAAW;;;;;;;8DACJ,cAAc;;;;;;yBAIT,WAAW;oCAKjC,sBAAsB;qBACqB,OAAO;2DACpC,cAAc;qBACf,oBAAoB;wBACX,uBAAuB;oBACvB,mBAAmB;yBACrB,wBAAwB;sBACnB,qBAAqB;0BACnB,yBAAyB;yBACF,uBAAuB;kCACP,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9F,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC,AAAsB,AAAC;AAQ5F,SAASC,wBAAwB,CAACC,KAAU,EAAqC;IAC/E,IAAI,CAACA,CAAAA,KAAK,QAAW,GAAhBA,KAAAA,CAAgB,GAAhBA,KAAK,CAAEC,SAAS,CAAA,IAAI,CAACD,CAAAA,KAAK,QAAa,GAAlBA,KAAAA,CAAkB,GAAlBA,KAAK,CAAEE,WAAW,CAAA,EAAE;YAIIF,GAAiB;QAHjE,MAAM,IAAIG,OAAY,aAAA,CACpB,SAAS,EACTH,KAAK,GACD,sCAAsC,GAAG,CAACA,CAAAA,CAAAA,GAAiB,GAAjBA,KAAK,CAACI,WAAW,SAAM,GAAvBJ,KAAAA,CAAuB,GAAvBA,GAAiB,CAAEK,IAAI,CAAA,IAAIL,KAAK,CAAC,GAC3E,qCAAqC,CAC1C,CAAC;IACJ,CAAC;AACH,CAAC;AAEM,MAAML,uBAAuB,SAASW,iBAAgB,iBAAA;QACvDD,IAAI,GAAW;QACjB,OAAO,SAAS,CAAC;IACnB;UAEaE,uBAAuB,GAAkB;IACpD,uCAAuC;IACzC;IAEOC,gBAAgB,CACrBC,MAAwD,EACxDC,MAA4B,EACtB;YAKmB,GAAa;QAJtC,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;YAClB,OAAO;QACT,CAAC;QAEDZ,wBAAwB,CAAC,CAAA,GAAa,GAAb,IAAI,CAACY,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEC,MAAM,CAAC,CAAC;QAEhD,2CAA2C;QAC3C,wGAAwG;QACxG,gFAAgF;QAChF,MAAMC,qBAAqB,GAAGJ,MAAM,KAAK,QAAQ,GAAG,iBAAiB,GAAGA,MAAM,AAAC;QAE/E,IAAI,aAAa,IAAI,IAAI,CAACE,QAAQ,CAACC,MAAM,EAAE;YACzC,sFAAsF;YACtF,IAAI,CAACD,QAAQ,CAACC,MAAM,CAACV,WAAW,CAAC,IAAI,CAACS,QAAQ,CAACC,MAAM,CAACE,OAAO,EAAED,qBAAqB,EAAEH,MAAM,CAAC,CAAC;QAChG,OAAO;YACL,IAAI,CAACC,QAAQ,CAACC,MAAM,CAACX,SAAS,CAAC,IAAI,CAACU,QAAQ,CAACC,MAAM,CAACE,OAAO,EAAED,qBAAqB,EAAEH,MAAM,CAAC,CAAC;QAC9F,CAAC;IACH;IAEAK,iBAAiB,GAAY;QAC3B,OAAO,KAAK,CAAC;IACf;UAEcC,qBAAqB,CAACC,OAAiC,EAAmB;QACtF,IAAI;YACF,MAAMC,WAAW,GAAGD,CAAAA,OAAO,QAAa,GAApBA,KAAAA,CAAoB,GAApBA,OAAO,CAAEC,WAAW,CAAA,IAAI,KAAK,AAAC;YAClD,MAAMC,IAAI,GAAG,MAAMC,IAAAA,KAAe,gBAAA,EAAC,IAAI,CAACC,WAAW,EAAE;gBACnDH,WAAW;gBACXI,IAAI,EAAEC,IAAG,IAAA,CAACC,QAAQ;aACnB,CAAC,AAAC;YACH,IAAI,CAACL,IAAI,EAAE;gBACT,MAAM,IAAIhB,OAAY,aAAA,CAAC,eAAe,EAAE,CAAC,KAAK,EAAEe,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC;YAChF,CAAC;YACD,OAAOC,IAAI,CAAC;QACd,EAAE,OAAOM,KAAK,EAAO;YACnB,MAAM,IAAItB,OAAY,aAAA,CAAC,eAAe,EAAEsB,KAAK,CAACC,OAAO,CAAC,CAAC;QACzD,CAAC;IACH;UAEMC,WAAW,CAAC,EAAEC,IAAI,CAAA,EAAEC,KAAK,CAAA,EAA0D,EAAE;QACzF,gCAAgC;QAChC,MAAMC,OAAO,GAAGC,IAAAA,mBAAwB,yBAAA,EAAC,IAAI,CAACV,WAAW,CAAC,AAAC;QAE3D,IAAIQ,KAAK,EAAE;YACT,MAAM,IAAI,CAACG,yBAAyB,CAAC,IAAI,CAACX,WAAW,EAAEO,IAAI,CAAC,CAAC;QAC/D,CAAC;QAED,MAAMK,MAAM,GAAG,MAAM,IAAI,CAACC,eAAe,CAAC;YACxCC,qBAAqB,EAAE,IAAI;YAC3BP,IAAI;SACL,CAAC,AAAC;QAEH,IAAI,CAACK,MAAM,CAACG,OAAO,EAAE;YACnBH,MAAM,CAACG,OAAO,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,MAAMC,GAAG,GAAGC,IAAAA,SAAiB,kBAAA,EAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,8CAA8C,CAAC,EAAE;YACnFC,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE,GAAG;YACVZ,KAAK,EAAE,IAAI;YACXa,QAAQ,EAAE,GAAG;YACbC,UAAU,EAAE,GAAG;SAChB,CAAC,AAAC;QAEH,yFAAyF;QACzF,IAAIN,GAAG,IAAI,IAAI,EAAE;YACfJ,MAAM,CAACG,OAAO,CAACQ,IAAI,CACjB,IAAId,OAAO,CAACe,cAAc,CAAC,CAACC,OAAe,GAAK;gBAC9CT,GAAG,QAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEU,MAAM,CAACD,OAAO,CAAC,CAAC;gBACrB,IAAIA,OAAO,KAAK,CAAC,EAAE;oBACjBT,GAAG,QAAW,GAAdA,KAAAA,CAAc,GAAdA,GAAG,CAAEW,SAAS,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;QAED,qEAAqE;QACrE,MAAMC,QAAQ,GAAGnB,OAAO,CAACG,MAAM,CAAC,AAAC;QAEjC,IAAI;YACF,MAAMiB,IAAAA,QAAY,aAAA,EAACD,QAAQ,CAAC,CAAC;QAC/B,EAAE,OAAOxB,KAAK,EAAO;YACnB0B,IAAG,CAAC1B,KAAK,CAACc,MAAK,EAAA,QAAA,CAACa,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC1C,MAAM3B,KAAK,CAAC;QACd,CAAC,QAAS;YACRY,GAAG,QAAW,GAAdA,KAAAA,CAAc,GAAdA,GAAG,CAAEW,SAAS,EAAE,CAAC;QACnB,CAAC;IACH;UAEgBK,wBAAwB,CACtCpC,OAA4B,EACA;QAC5B,gCAAgC;QAChC,MAAMa,OAAO,GAAGC,IAAAA,mBAAwB,yBAAA,EAAC,IAAI,CAACV,WAAW,CAAC,AAAC;QAC3D,MAAMiC,gBAAgB,GAAGC,IAAAA,mBAAiC,kCAAA,EAAC,IAAI,CAAClC,WAAW,CAAC,AAAC;QAE7E,MAAM,IAAI,CAACmC,SAAS,EAAE,CAAC;QAEvBvC,OAAO,CAACE,IAAI,GAAG,MAAM,IAAI,CAACH,qBAAqB,CAAC;YAC9CE,WAAW,EAAED,OAAO,CAACE,IAAI;SAC1B,CAAC,CAAC;QACH,MAAM,EAAEsC,cAAc,CAAA,EAAEC,KAAK,CAAA,EAAEvC,IAAI,CAAA,EAAES,IAAI,CAAA,EAAE,GAAGX,OAAO,AAAC;QAEtD,IAAI,CAAC0C,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC;YACnCzC,IAAI;YACJ0C,QAAQ,EAAE;gBACRC,MAAM,EAAEJ,KAAK,GAAG,OAAO,GAAG,MAAM;aACjC;SACF,CAAC,CAAC;QAEH7D,KAAK,CAAC,4BAA4B,GAAGsB,IAAI,CAAC,CAAC;QAE3C,IAAIsC,cAAc,EAAE;YAClB,MAAM,IAAI,CAACzB,yBAAyB,CAAC,IAAI,CAACX,WAAW,EAAEO,IAAI,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI8B,KAAK,EAAE;YACT7D,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACjD,MAAMkE,IAAAA,IAAiC,kCAAA,EAAC,IAAI,CAAC1C,WAAW,CAAC,CAAC2C,KAAK,CAAC,CAACvC,KAAK,GAAK;gBACzE0B,IAAG,CAAC1B,KAAK,CAAC,CAAC,iCAAiC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAMQ,MAAM,GAAG,MAAM,IAAI,CAACC,eAAe,CAACjB,OAAO,CAAC,AAAC;QAEnDkC,IAAG,CAACc,GAAG,CAAC1B,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,yBAAyB,EAAEpB,IAAI,CAAC,eAAe,EAAES,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9E,qEAAqE;QACrE,MAAMqB,QAAQ,GAAGnB,OAAO,CAACG,MAAM,CAAC,AAAC;QAEjC,MAAMrB,MAAM,GAAG,IAAI0C,gBAAgB,CAACL,QAAQ,EAAEhB,MAAM,CAACiC,SAAS,CAAC,AAAC;QAChE,2BAA2B;QAC3BtD,MAAM,CAACuD,MAAM,CAAChD,IAAI,EAAEI,IAAG,IAAA,CAACC,QAAQ,EAAE,SAA6BC,KAAK,EAAE;YACpE,IAAIA,KAAK,EAAE;gBACT0B,IAAG,CAAC1B,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,qEAAqE;QACrE,MAAM0C,aAAa,GAAGxD,MAAM,CAACyD,KAAK,CAACC,IAAI,CAAC1D,MAAM,CAAC,AAAC;QAEhDA,MAAM,CAACyD,KAAK,GAAG,CAACE,QAAgC,GAAK;YACnD,OAAOH,aAAa,CAAC,CAACI,GAAW,GAAK;gBACpC,IAAI,CAAC7D,QAAQ,GAAG,IAAI,CAAC;gBACrB4D,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAGC,GAAG,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAMC,KAAK,GAAGC,IAAAA,GAAY,aAAA,GAAE,AAAC;QAC7B,MAAMC,QAAQ,GAAGjB,KAAK,GAAG,OAAO,GAAG,MAAM,AAAC;QAE1C,OAAO;YACL,kBAAkB;YAClB9C,MAAM;YACN,WAAW;YACXiD,QAAQ,EAAE;gBACRe,GAAG,EAAE,CAAC,EAAED,QAAQ,CAAC,GAAG,EAAEF,KAAK,CAAC,CAAC,EAAEtD,IAAI,CAAC,CAAC;gBACrCA,IAAI;gBACJwD,QAAQ;gBACRrD,IAAI,EAAEmD,KAAK;aACZ;YACDI,UAAU,EAAE,IAAI;YAChB,6BAA6B;YAC7BC,aAAa,EAAE;gBACbC,SAAS,EAAE,IAAI,CAACvE,gBAAgB;aACjC;SACF,CAAC;IACJ;IAEA,kDAAkD,GAClDwE,wBAAwB,GAAkB;QACxC,4DAA4D;QAC5D,OACE,IAAI,CAACC,kBAAkB,EAAE,CAACC,MAAM,CAC9B,CAACC,IAAI,EAAEC,QAAQ,GAAKD,IAAI,IAAIE,YAAW,EAAA,QAAA,CAACC,MAAM,CAAC,IAAI,CAACjE,WAAW,EAAE+D,QAAQ,CAAC,EAC1E,IAAI,CACL,IAAI,IAAI,CACT;IACJ;UAEMlD,eAAe,CACnBjB,OAA8E,EAC9EsE,IAAe,EACgB;QAC/B,sCAAsC;QAEtC,MAAMhE,GAAG,GAAG;YACVF,WAAW,EAAE,IAAI,CAACA,WAAW;YAC7BmE,GAAG,EAAE,CAAC,CAACvE,OAAO,CAACkB,qBAAqB;YACpC,8CAA8C;YAC9CsD,MAAM,EAAE;gBACNC,IAAI,IAAG,CAAC,CAAC;aACV;YACD9D,IAAI,EAAEX,OAAO,CAACW,IAAI;YAClB8B,KAAK,EAAEzC,OAAO,CAACyC,KAAK;SACrB,AAAC;QACFiC,IAAAA,QAAU,WAAA,EAACpE,GAAG,CAACK,IAAI,IAAI,aAAa,CAAC,CAAC;QACtC9B,OAAO,CAAC,WAAW,CAAC,CAAC8F,IAAI,CAACrE,GAAG,CAACF,WAAW,CAAC,CAAC;QAC3C,4DAA4D;QAC5D,MAAMwE,oBAAoB,GAAG,IAAI,CAACb,wBAAwB,EAAE,AAAC;QAC7D,IAAI/C,MAAM,AAAsB,AAAC;QACjC,IAAI4D,oBAAoB,EAAE;YACxB,MAAMC,aAAa,GAAGhG,OAAO,CAAC+F,oBAAoB,CAAC,AAAC;YACpD,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;gBACvC7D,MAAM,GAAG,MAAM6D,aAAa,CAACvE,GAAG,EAAEgE,IAAI,CAAC,CAAC;YAC1C,OAAO;gBACLtD,MAAM,GAAG6D,aAAa,CAAC;YACzB,CAAC;QACH,OAAO;YACL,+CAA+C;YAC/C,MAAMC,sBAAsB,GAAGC,IAAAA,mBAAkC,mCAAA,EAAC,IAAI,CAAC3E,WAAW,CAAC,AAAC;YACpFY,MAAM,GAAG,MAAM8D,sBAAsB,CAACxE,GAAG,EAAEgE,IAAI,CAAC,CAAC;QACnD,CAAC;QACD,OAAOtD,MAAM,CAAC;IAChB;IAEUgD,kBAAkB,GAAa;QACvC,OAAO;YAAC,qBAAqB;SAAC,CAAC;IACjC;UAEgBjD,yBAAyB,CACvCX,WAAmB,EACnBO,IAAY,GAAG,aAAa,EACb;QACfuB,IAAG,CAACc,GAAG,CAAC1B,MAAK,EAAA,QAAA,CAAC0D,GAAG,CAAC,CAAC,iBAAiB,EAAErE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAElE,MAAMsE,GAAG,GAAG,MAAMC,IAAAA,QAAwC,yCAAA,EAAC9E,WAAW,CAAC,AAAC;QACxE,MAAM+E,WAAW,GAAGC,SAAI,EAAA,QAAA,CAACC,IAAI,CAACJ,GAAG,EAAE,WAAW,EAAEtE,IAAI,CAAC,AAAC;QACtD,IAAI;YACF,MAAM2E,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,EAAE,CAACL,WAAW,EAAE;gBAAEM,SAAS,EAAE,IAAI;gBAAEC,KAAK,EAAE,IAAI;aAAE,CAAC,CAAC;QACtE,EAAE,OAAOlF,KAAK,EAAO;YACnB0B,IAAG,CAAC1B,KAAK,CAAC,CAAC,gBAAgB,EAAEG,IAAI,CAAC,sBAAsB,EAAEH,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC;IACH;CACD;AAEM,SAAS9B,+BAA+B,CAACyB,WAAmB,EAAE;IACnE,OAAOgE,YAAW,EAAA,QAAA,CAACC,MAAM,CAACjE,WAAW,EAAE,qBAAqB,CAAC,CAAC;AAChE,CAAC"}