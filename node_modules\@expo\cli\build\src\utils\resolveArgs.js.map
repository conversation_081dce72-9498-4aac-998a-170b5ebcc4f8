{"version": 3, "sources": ["../../../src/utils/resolveArgs.ts"], "sourcesContent": ["import arg, { Spec } from 'arg';\n\nimport { replaceValue } from './array';\nimport { CommandError } from './errors';\n\n/** Split up arguments that are formatted like `--foo=bar` or `-f=\"bar\"` to `['--foo', 'bar']` */\nfunction splitArgs(args: string[]): string[] {\n  const result: string[] = [];\n\n  for (const arg of args) {\n    if (arg.startsWith('-')) {\n      const [key, ...props] = arg.split('=');\n      result.push(key);\n      if (props.length) {\n        result.push(props.join('='));\n      }\n    } else {\n      result.push(arg);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Enables the resolution of arguments that can either be a string or a boolean.\n *\n * @param args arguments that were passed to the command.\n * @param rawMap raw map of arguments that are passed to the command.\n * @param extraArgs extra arguments and aliases that should be resolved as string or boolean.\n * @returns parsed arguments and project root.\n */\nexport async function resolveStringOrBooleanArgsAsync(\n  args: string[],\n  rawMap: arg.Spec,\n  extraArgs: arg.Spec\n) {\n  args = splitArgs(args);\n\n  // Assert any missing arguments\n  assertUnknownArgs(\n    {\n      ...rawMap,\n      ...extraArgs,\n    },\n    args\n  );\n\n  // Collapse aliases into fully qualified arguments.\n  args = collapseAliases(extraArgs, args);\n\n  // Resolve all of the string or boolean arguments and the project root.\n  return _resolveStringOrBooleanArgs({ ...rawMap, ...extraArgs }, args);\n}\n\n/**\n * Enables the resolution of boolean arguments that can be formatted like `--foo=true` or `--foo false`\n *\n * @param args arguments that were passed to the command.\n * @param rawMap raw map of arguments that are passed to the command.\n * @param extraArgs extra arguments and aliases that should be resolved as string or boolean.\n * @returns parsed arguments and project root.\n */\nexport async function resolveCustomBooleanArgsAsync(\n  args: string[],\n  rawMap: arg.Spec,\n  extraArgs: arg.Spec\n) {\n  const results = await resolveStringOrBooleanArgsAsync(args, rawMap, extraArgs);\n\n  return {\n    ...results,\n    args: Object.fromEntries(\n      Object.entries(results.args).map(([key, value]) => {\n        if (extraArgs[key]) {\n          if (typeof value === 'string') {\n            if (!['true', 'false'].includes(value)) {\n              throw new CommandError(\n                'BAD_ARGS',\n                `Invalid boolean argument: ${key}=${value}. Expected one of: true, false`\n              );\n            }\n            return [key, value === 'true'];\n          }\n        }\n        return [key, value];\n      })\n    ),\n  };\n}\n\nexport function _resolveStringOrBooleanArgs(arg: Spec, args: string[]) {\n  // Default project root, if a custom one is defined then it will overwrite this.\n  let projectRoot: string = '.';\n  // The resolved arguments.\n  const settings: Record<string, string | boolean | undefined> = {};\n\n  // Create a list of possible arguments, this will filter out aliases.\n  const possibleArgs = Object.entries(arg)\n    .filter(([, value]) => typeof value !== 'string')\n    .map(([key]) => key);\n\n  // Loop over arguments in reverse order so we can resolve if a value belongs to a flag.\n  for (let i = args.length - 1; i > -1; i--) {\n    const value = args[i];\n    // At this point we should have converted all aliases to fully qualified arguments.\n    if (value.startsWith('--')) {\n      // If we ever find an argument then it must be a boolean because we are checking in reverse\n      // and removing arguments from the array if we find a string.\n      // We don't override arguments that are already set\n      if (!(value in settings)) {\n        settings[value] = true;\n      }\n    } else {\n      // Get the previous argument in the array.\n      const nextValue = i > 0 ? args[i - 1] : null;\n      if (nextValue && possibleArgs.includes(nextValue)) {\n        // We don't override arguments that are already set\n        if (!(nextValue in settings)) {\n          settings[nextValue] = value;\n        }\n        i--;\n      } else if (\n        // If the last value is not a flag and it doesn't have a recognized flag before it (instead having a string value or nothing)\n        // then it must be the project root.\n        i ===\n        args.length - 1\n      ) {\n        projectRoot = value;\n      } else {\n        // This will asserts if two strings are passed in a row and not at the end of the line.\n        throw new CommandError('BAD_ARGS', `Unknown argument: ${value}`);\n      }\n    }\n  }\n\n  return {\n    args: settings,\n    projectRoot,\n  };\n}\n\n/** Convert all aliases to fully qualified flag names. */\nexport function collapseAliases(arg: Spec, args: string[]): string[] {\n  const aliasMap = getAliasTuples(arg);\n\n  for (const [arg, alias] of aliasMap) {\n    args = replaceValue(args, arg, alias);\n  }\n\n  // Assert if there are duplicate flags after we collapse the aliases.\n  assertDuplicateArgs(args, aliasMap);\n  return args;\n}\n\n/** Assert that the spec has unknown arguments. */\nexport function assertUnknownArgs(arg: Spec, args: string[]) {\n  const allowedArgs = Object.keys(arg);\n  const unknownArgs = args.filter((arg) => !allowedArgs.includes(arg) && arg.startsWith('-'));\n  if (unknownArgs.length > 0) {\n    throw new CommandError(`Unknown arguments: ${unknownArgs.join(', ')}`);\n  }\n}\n\nfunction getAliasTuples(arg: Spec): [string, string][] {\n  return Object.entries(arg).filter(([, value]) => typeof value === 'string') as [string, string][];\n}\n\n/** Asserts that a duplicate flag has been used, this naively throws without knowing if an alias or flag were used as the duplicate. */\nexport function assertDuplicateArgs(args: string[], argNameAliasTuple: [string, string][]) {\n  for (const [argName, argNameAlias] of argNameAliasTuple) {\n    if (args.filter((a) => [argName, argNameAlias].includes(a)).length > 1) {\n      throw new CommandError(\n        'BAD_ARGS',\n        `Can only provide one instance of ${argName} or ${argNameAlias}`\n      );\n    }\n  }\n}\n"], "names": ["resolveStringOrBooleanArgsAsync", "resolveCustomBooleanArgsAsync", "_resolveStringOrBooleanArgs", "collapseAliases", "assertUnknownArgs", "assertDuplicateArgs", "splitArgs", "args", "result", "arg", "startsWith", "key", "props", "split", "push", "length", "join", "rawMap", "extraArgs", "results", "Object", "fromEntries", "entries", "map", "value", "includes", "CommandError", "projectRoot", "settings", "<PERSON><PERSON>rgs", "filter", "i", "nextValue", "aliasMap", "get<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "alias", "replaceValue", "<PERSON><PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON><PERSON>", "argNameAliasTuple", "argName", "argNameAlias", "a"], "mappings": "AAAA;;;;;;;;;;;IAgCsBA,+BAA+B,MAA/BA,+BAA+B;IA+B/BC,6BAA6B,MAA7BA,6BAA6B;IA4BnCC,6BAA2B,MAA3BA,2BAA2B;IAoD3BC,eAAe,MAAfA,eAAe;IAafC,iBAAiB,MAAjBA,iBAAiB;IAajBC,mBAAmB,MAAnBA,mBAAmB;;uBAvKN,SAAS;wBACT,UAAU;AAEvC,+FAA+F,GAC/F,SAASC,SAAS,CAACC,IAAc,EAAY;IAC3C,MAAMC,MAAM,GAAa,EAAE,AAAC;IAE5B,KAAK,MAAMC,GAAG,IAAIF,IAAI,CAAE;QACtB,IAAIE,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACvB,MAAM,CAACC,GAAG,EAAE,GAAGC,KAAK,CAAC,GAAGH,GAAG,CAACI,KAAK,CAAC,GAAG,CAAC,AAAC;YACvCL,MAAM,CAACM,IAAI,CAACH,GAAG,CAAC,CAAC;YACjB,IAAIC,KAAK,CAACG,MAAM,EAAE;gBAChBP,MAAM,CAACM,IAAI,CAACF,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,CAAC;QACH,OAAO;YACLR,MAAM,CAACM,IAAI,CAACL,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,OAAOD,MAAM,CAAC;AAChB,CAAC;AAUM,eAAeR,+BAA+B,CACnDO,IAAc,EACdU,MAAgB,EAChBC,SAAmB,EACnB;IACAX,IAAI,GAAGD,SAAS,CAACC,IAAI,CAAC,CAAC;IAEvB,+BAA+B;IAC/BH,iBAAiB,CACf;QACE,GAAGa,MAAM;QACT,GAAGC,SAAS;KACb,EACDX,IAAI,CACL,CAAC;IAEF,mDAAmD;IACnDA,IAAI,GAAGJ,eAAe,CAACe,SAAS,EAAEX,IAAI,CAAC,CAAC;IAExC,uEAAuE;IACvE,OAAOL,2BAA2B,CAAC;QAAE,GAAGe,MAAM;QAAE,GAAGC,SAAS;KAAE,EAAEX,IAAI,CAAC,CAAC;AACxE,CAAC;AAUM,eAAeN,6BAA6B,CACjDM,IAAc,EACdU,MAAgB,EAChBC,SAAmB,EACnB;IACA,MAAMC,OAAO,GAAG,MAAMnB,+BAA+B,CAACO,IAAI,EAAEU,MAAM,EAAEC,SAAS,CAAC,AAAC;IAE/E,OAAO;QACL,GAAGC,OAAO;QACVZ,IAAI,EAAEa,MAAM,CAACC,WAAW,CACtBD,MAAM,CAACE,OAAO,CAACH,OAAO,CAACZ,IAAI,CAAC,CAACgB,GAAG,CAAC,CAAC,CAACZ,GAAG,EAAEa,KAAK,CAAC,GAAK;YACjD,IAAIN,SAAS,CAACP,GAAG,CAAC,EAAE;gBAClB,IAAI,OAAOa,KAAK,KAAK,QAAQ,EAAE;oBAC7B,IAAI,CAAC;wBAAC,MAAM;wBAAE,OAAO;qBAAC,CAACC,QAAQ,CAACD,KAAK,CAAC,EAAE;wBACtC,MAAM,IAAIE,OAAY,aAAA,CACpB,UAAU,EACV,CAAC,0BAA0B,EAAEf,GAAG,CAAC,CAAC,EAAEa,KAAK,CAAC,8BAA8B,CAAC,CAC1E,CAAC;oBACJ,CAAC;oBACD,OAAO;wBAACb,GAAG;wBAAEa,KAAK,KAAK,MAAM;qBAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YACD,OAAO;gBAACb,GAAG;gBAAEa,KAAK;aAAC,CAAC;QACtB,CAAC,CAAC,CACH;KACF,CAAC;AACJ,CAAC;AAEM,SAAStB,2BAA2B,CAACO,GAAS,EAAEF,IAAc,EAAE;IACrE,gFAAgF;IAChF,IAAIoB,WAAW,GAAW,GAAG,AAAC;IAC9B,0BAA0B;IAC1B,MAAMC,QAAQ,GAAiD,EAAE,AAAC;IAElE,qEAAqE;IACrE,MAAMC,YAAY,GAAGT,MAAM,CAACE,OAAO,CAACb,GAAG,CAAC,CACrCqB,MAAM,CAAC,CAAC,GAAGN,KAAK,CAAC,GAAK,OAAOA,KAAK,KAAK,QAAQ,CAAC,CAChDD,GAAG,CAAC,CAAC,CAACZ,GAAG,CAAC,GAAKA,GAAG,CAAC,AAAC;IAEvB,uFAAuF;IACvF,IAAK,IAAIoB,CAAC,GAAGxB,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAEgB,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,CAAE;QACzC,MAAMP,KAAK,GAAGjB,IAAI,CAACwB,CAAC,CAAC,AAAC;QACtB,mFAAmF;QACnF,IAAIP,KAAK,CAACd,UAAU,CAAC,IAAI,CAAC,EAAE;YAC1B,2FAA2F;YAC3F,6DAA6D;YAC7D,mDAAmD;YACnD,IAAI,CAAC,CAACc,KAAK,IAAII,QAAQ,CAAC,EAAE;gBACxBA,QAAQ,CAACJ,KAAK,CAAC,GAAG,IAAI,CAAC;YACzB,CAAC;QACH,OAAO;YACL,0CAA0C;YAC1C,MAAMQ,SAAS,GAAGD,CAAC,GAAG,CAAC,GAAGxB,IAAI,CAACwB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,AAAC;YAC7C,IAAIC,SAAS,IAAIH,YAAY,CAACJ,QAAQ,CAACO,SAAS,CAAC,EAAE;gBACjD,mDAAmD;gBACnD,IAAI,CAAC,CAACA,SAAS,IAAIJ,QAAQ,CAAC,EAAE;oBAC5BA,QAAQ,CAACI,SAAS,CAAC,GAAGR,KAAK,CAAC;gBAC9B,CAAC;gBACDO,CAAC,EAAE,CAAC;YACN,OAAO,IACL,6HAA6H;YAC7H,oCAAoC;YACpCA,CAAC,KACDxB,IAAI,CAACQ,MAAM,GAAG,CAAC,EACf;gBACAY,WAAW,GAAGH,KAAK,CAAC;YACtB,OAAO;gBACL,uFAAuF;gBACvF,MAAM,IAAIE,OAAY,aAAA,CAAC,UAAU,EAAE,CAAC,kBAAkB,EAAEF,KAAK,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO;QACLjB,IAAI,EAAEqB,QAAQ;QACdD,WAAW;KACZ,CAAC;AACJ,CAAC;AAGM,SAASxB,eAAe,CAACM,GAAS,EAAEF,IAAc,EAAY;IACnE,MAAM0B,QAAQ,GAAGC,cAAc,CAACzB,GAAG,CAAC,AAAC;IAErC,KAAK,MAAM,CAACA,IAAG,EAAE0B,KAAK,CAAC,IAAIF,QAAQ,CAAE;QACnC1B,IAAI,GAAG6B,IAAAA,MAAY,aAAA,EAAC7B,IAAI,EAAEE,IAAG,EAAE0B,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,qEAAqE;IACrE9B,mBAAmB,CAACE,IAAI,EAAE0B,QAAQ,CAAC,CAAC;IACpC,OAAO1B,IAAI,CAAC;AACd,CAAC;AAGM,SAASH,iBAAiB,CAACK,GAAS,EAAEF,IAAc,EAAE;IAC3D,MAAM8B,WAAW,GAAGjB,MAAM,CAACkB,IAAI,CAAC7B,GAAG,CAAC,AAAC;IACrC,MAAM8B,WAAW,GAAGhC,IAAI,CAACuB,MAAM,CAAC,CAACrB,GAAG,GAAK,CAAC4B,WAAW,CAACZ,QAAQ,CAAChB,GAAG,CAAC,IAAIA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,CAAC,AAAC;IAC5F,IAAI6B,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;QAC1B,MAAM,IAAIW,OAAY,aAAA,CAAC,CAAC,mBAAmB,EAAEa,WAAW,CAACvB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;AACH,CAAC;AAED,SAASkB,cAAc,CAACzB,GAAS,EAAsB;IACrD,OAAOW,MAAM,CAACE,OAAO,CAACb,GAAG,CAAC,CAACqB,MAAM,CAAC,CAAC,GAAGN,KAAK,CAAC,GAAK,OAAOA,KAAK,KAAK,QAAQ,CAAC,CAAuB;AACpG,CAAC;AAGM,SAASnB,mBAAmB,CAACE,IAAc,EAAEiC,iBAAqC,EAAE;IACzF,KAAK,MAAM,CAACC,OAAO,EAAEC,YAAY,CAAC,IAAIF,iBAAiB,CAAE;QACvD,IAAIjC,IAAI,CAACuB,MAAM,CAAC,CAACa,CAAC,GAAK;gBAACF,OAAO;gBAAEC,YAAY;aAAC,CAACjB,QAAQ,CAACkB,CAAC,CAAC,CAAC,CAAC5B,MAAM,GAAG,CAAC,EAAE;YACtE,MAAM,IAAIW,OAAY,aAAA,CACpB,UAAU,EACV,CAAC,iCAAiC,EAAEe,OAAO,CAAC,IAAI,EAAEC,YAAY,CAAC,CAAC,CACjE,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC"}