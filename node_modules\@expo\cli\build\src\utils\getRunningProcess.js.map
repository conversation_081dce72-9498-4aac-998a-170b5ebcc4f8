{"version": 3, "sources": ["../../../src/utils/getRunningProcess.ts"], "sourcesContent": ["import { execFileSync, execSync, ExecSyncOptionsWithStringEncoding } from 'child_process';\nimport * as path from 'path';\n\nconst debug = require('debug')('expo:utils:getRunningProcess') as typeof console.log;\n\nconst defaultOptions: ExecSyncOptionsWithStringEncoding = {\n  encoding: 'utf8',\n  stdio: ['pipe', 'pipe', 'ignore'],\n};\n\n/** Returns a pid value for a running port like `63828` or null if nothing is running on the given port. */\nexport function getPID(port: number): number | null {\n  try {\n    const results = execFileSync('lsof', [`-i:${port}`, '-P', '-t', '-sTCP:LISTEN'], defaultOptions)\n      .split('\\n')[0]\n      .trim();\n    const pid = Number(results);\n    debug(`pid: ${pid} for port: ${port}`);\n    return pid;\n  } catch (error: any) {\n    debug(`No pid found for port: ${port}. Error: ${error}`);\n    return null;\n  }\n}\n\n/** Get `package.json` `name` field for a given directory. Returns `null` if none exist. */\nfunction getPackageName(packageRoot: string): string | null {\n  const packageJson = path.join(packageRoot, 'package.json');\n  try {\n    return require(packageJson).name || null;\n  } catch {\n    return null;\n  }\n}\n\n/** Returns a command like `node /Users/<USER>/.../bin/expo start` or the package.json name. */\nfunction getProcessCommand(pid: number, procDirectory: string): string {\n  const name = getPackageName(procDirectory);\n\n  if (name) {\n    return name;\n  }\n  return execSync(`ps -o command -p ${pid} | sed -n 2p`, defaultOptions).replace(/\\n$/, '').trim();\n}\n\n/** Get directory for a given process ID. */\nexport function getDirectoryOfProcessById(processId: number): string {\n  return execSync(\n    `lsof -p ${processId} | awk '$4==\"cwd\" {for (i=9; i<=NF; i++) printf \"%s \", $i}'`,\n    defaultOptions\n  ).trim();\n}\n\n/** Get information about a running process given a port. Returns null if no process is running on the given port. */\nexport function getRunningProcess(port: number): {\n  /** The PID value for the port. */\n  pid: number;\n  /** Get the directory for the running process. */\n  directory: string;\n  /** The command running the process like `node /Users/<USER>/.../bin/expo start` or the `package.json` name like `my-app`. */\n  command: string;\n} | null {\n  // 63828\n  const pid = getPID(port);\n  if (!pid) {\n    return null;\n  }\n\n  try {\n    // /Users/<USER>/Documents/GitHub/lab/myapp\n    const directory = getDirectoryOfProcessById(pid);\n    // /Users/<USER>/Documents/GitHub/lab/myapp/package.json\n    const command = getProcessCommand(pid, directory);\n    // TODO: Have a better message for reusing another process.\n    return { pid, directory, command };\n  } catch {\n    return null;\n  }\n}\n"], "names": ["getPID", "getDirectoryOfProcessById", "getRunningProcess", "debug", "require", "defaultOptions", "encoding", "stdio", "port", "results", "execFileSync", "split", "trim", "pid", "Number", "error", "getPackageName", "packageRoot", "packageJson", "path", "join", "name", "getProcessCommand", "procDirectory", "execSync", "replace", "processId", "directory", "command"], "mappings": "AAAA;;;;;;;;;;;IAWgBA,MAAM,MAANA,MAAM;IAmCNC,yBAAyB,MAAzBA,yBAAyB;IAQzBC,iBAAiB,MAAjBA,iBAAiB;;;yBAtDyC,eAAe;;;;;;;+DACnE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC,AAAsB,AAAC;AAErF,MAAMC,cAAc,GAAsC;IACxDC,QAAQ,EAAE,MAAM;IAChBC,KAAK,EAAE;QAAC,MAAM;QAAE,MAAM;QAAE,QAAQ;KAAC;CAClC,AAAC;AAGK,SAASP,MAAM,CAACQ,IAAY,EAAiB;IAClD,IAAI;QACF,MAAMC,OAAO,GAAGC,IAAAA,aAAY,EAAA,aAAA,EAAC,MAAM,EAAE;YAAC,CAAC,GAAG,EAAEF,IAAI,CAAC,CAAC;YAAE,IAAI;YAAE,IAAI;YAAE,cAAc;SAAC,EAAEH,cAAc,CAAC,CAC7FM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACdC,IAAI,EAAE,AAAC;QACV,MAAMC,GAAG,GAAGC,MAAM,CAACL,OAAO,CAAC,AAAC;QAC5BN,KAAK,CAAC,CAAC,KAAK,EAAEU,GAAG,CAAC,WAAW,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,OAAOK,GAAG,CAAC;IACb,EAAE,OAAOE,KAAK,EAAO;QACnBZ,KAAK,CAAC,CAAC,uBAAuB,EAAEK,IAAI,CAAC,SAAS,EAAEO,KAAK,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,yFAAyF,GACzF,SAASC,cAAc,CAACC,WAAmB,EAAiB;IAC1D,MAAMC,WAAW,GAAGC,KAAI,EAAA,CAACC,IAAI,CAACH,WAAW,EAAE,cAAc,CAAC,AAAC;IAC3D,IAAI;QACF,OAAOb,OAAO,CAACc,WAAW,CAAC,CAACG,IAAI,IAAI,IAAI,CAAC;IAC3C,EAAE,OAAM;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,gGAAgG,GAChG,SAASC,iBAAiB,CAACT,GAAW,EAAEU,aAAqB,EAAU;IACrE,MAAMF,IAAI,GAAGL,cAAc,CAACO,aAAa,CAAC,AAAC;IAE3C,IAAIF,IAAI,EAAE;QACR,OAAOA,IAAI,CAAC;IACd,CAAC;IACD,OAAOG,IAAAA,aAAQ,EAAA,SAAA,EAAC,CAAC,iBAAiB,EAAEX,GAAG,CAAC,YAAY,CAAC,EAAER,cAAc,CAAC,CAACoB,OAAO,QAAQ,EAAE,CAAC,CAACb,IAAI,EAAE,CAAC;AACnG,CAAC;AAGM,SAASX,yBAAyB,CAACyB,SAAiB,EAAU;IACnE,OAAOF,IAAAA,aAAQ,EAAA,SAAA,EACb,CAAC,QAAQ,EAAEE,SAAS,CAAC,2DAA2D,CAAC,EACjFrB,cAAc,CACf,CAACO,IAAI,EAAE,CAAC;AACX,CAAC;AAGM,SAASV,iBAAiB,CAACM,IAAY,EAOrC;IACP,QAAQ;IACR,MAAMK,GAAG,GAAGb,MAAM,CAACQ,IAAI,CAAC,AAAC;IACzB,IAAI,CAACK,GAAG,EAAE;QACR,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI;QACF,8CAA8C;QAC9C,MAAMc,SAAS,GAAG1B,yBAAyB,CAACY,GAAG,CAAC,AAAC;QACjD,2DAA2D;QAC3D,MAAMe,OAAO,GAAGN,iBAAiB,CAACT,GAAG,EAAEc,SAAS,CAAC,AAAC;QAClD,2DAA2D;QAC3D,OAAO;YAAEd,GAAG;YAAEc,SAAS;YAAEC,OAAO;SAAE,CAAC;IACrC,EAAE,OAAM;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}