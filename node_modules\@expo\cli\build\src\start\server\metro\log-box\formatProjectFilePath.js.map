{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/formatProjectFilePath.ts"], "sourcesContent": ["import type { StackFrame } from 'stacktrace-parser';\n\nexport type MetroStackFrame = StackFrame & { collapse?: boolean };\n\nexport function formatProjectFilePath(projectRoot: string, file?: string | null): string {\n  if (file == null) {\n    return '<unknown>';\n  }\n\n  return pathRelativeToPath(file.replace(/\\\\/g, '/'), projectRoot.replace(/\\\\/g, '/')).replace(\n    /\\?.*$/,\n    ''\n  );\n}\n\nfunction pathRelativeToPath(path: string, relativeTo: string, sep = '/') {\n  const relativeToParts = relativeTo.split(sep);\n  const pathParts = path.split(sep);\n  let i = 0;\n  while (i < relativeToParts.length && i < pathParts.length) {\n    if (relativeToParts[i] !== pathParts[i]) {\n      break;\n    }\n    i++;\n  }\n  return pathParts.slice(i).join(sep);\n}\n\nexport function getStackFormattedLocation(projectRoot: string, frame: MetroStackFrame) {\n  const column = frame.column != null && parseInt(String(frame.column), 10);\n  const location =\n    formatProjectFilePath(projectRoot, frame.file) +\n    (frame.lineNumber != null\n      ? ':' + frame.lineNumber + (column && !isNaN(column) ? ':' + (column + 1) : '')\n      : '');\n\n  return location;\n}\n"], "names": ["formatProjectFilePath", "getStackFormattedLocation", "projectRoot", "file", "pathRelativeToPath", "replace", "path", "relativeTo", "sep", "relativeToParts", "split", "pathParts", "i", "length", "slice", "join", "frame", "column", "parseInt", "String", "location", "lineNumber", "isNaN"], "mappings": "AAAA;;;;;;;;;;;IAIgBA,qBAAqB,MAArBA,qBAAqB;IAwBrBC,yBAAyB,MAAzBA,yBAAyB;;AAxBlC,SAASD,qBAAqB,CAACE,WAAmB,EAAEC,IAAoB,EAAU;IACvF,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,OAAOC,kBAAkB,CAACD,IAAI,CAACE,OAAO,QAAQ,GAAG,CAAC,EAAEH,WAAW,CAACG,OAAO,QAAQ,GAAG,CAAC,CAAC,CAACA,OAAO,UAE1F,EAAE,CACH,CAAC;AACJ,CAAC;AAED,SAASD,kBAAkB,CAACE,IAAY,EAAEC,UAAkB,EAAEC,GAAG,GAAG,GAAG,EAAE;IACvE,MAAMC,eAAe,GAAGF,UAAU,CAACG,KAAK,CAACF,GAAG,CAAC,AAAC;IAC9C,MAAMG,SAAS,GAAGL,IAAI,CAACI,KAAK,CAACF,GAAG,CAAC,AAAC;IAClC,IAAII,CAAC,GAAG,CAAC,AAAC;IACV,MAAOA,CAAC,GAAGH,eAAe,CAACI,MAAM,IAAID,CAAC,GAAGD,SAAS,CAACE,MAAM,CAAE;QACzD,IAAIJ,eAAe,CAACG,CAAC,CAAC,KAAKD,SAAS,CAACC,CAAC,CAAC,EAAE;YACvC,MAAM;QACR,CAAC;QACDA,CAAC,EAAE,CAAC;IACN,CAAC;IACD,OAAOD,SAAS,CAACG,KAAK,CAACF,CAAC,CAAC,CAACG,IAAI,CAACP,GAAG,CAAC,CAAC;AACtC,CAAC;AAEM,SAASP,yBAAyB,CAACC,WAAmB,EAAEc,KAAsB,EAAE;IACrF,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM,IAAI,IAAI,IAAIC,QAAQ,CAACC,MAAM,CAACH,KAAK,CAACC,MAAM,CAAC,EAAE,EAAE,CAAC,AAAC;IAC1E,MAAMG,QAAQ,GACZpB,qBAAqB,CAACE,WAAW,EAAEc,KAAK,CAACb,IAAI,CAAC,GAC9C,CAACa,KAAK,CAACK,UAAU,IAAI,IAAI,GACrB,GAAG,GAAGL,KAAK,CAACK,UAAU,GAAG,CAACJ,MAAM,IAAI,CAACK,KAAK,CAACL,MAAM,CAAC,GAAG,GAAG,GAAG,CAACA,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAC7E,EAAE,CAAC,AAAC;IAEV,OAAOG,QAAQ,CAAC;AAClB,CAAC"}