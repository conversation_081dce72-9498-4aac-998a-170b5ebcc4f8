{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/AtlasPrerequisite.ts"], "sourcesContent": ["import { ProjectPrerequisite } from '../../../doctor/Prerequisite';\nimport {\n  type EnsureDependenciesOptions,\n  ensureDependenciesAsync,\n} from '../../../doctor/dependencies/ensureDependenciesAsync';\n\nexport class AtlasPrerequisite extends ProjectPrerequisite<\n  boolean,\n  Pick<EnsureDependenciesOptions, 'exp'>\n> {\n  async assertImplementation({ exp }: Pick<EnsureDependenciesOptions, 'exp'> = {}) {\n    await this.ensureAtlasInstalled({ exp });\n    return true;\n  }\n\n  async bootstrapAsync({ exp }: Pick<EnsureDependenciesOptions, 'exp'> = {}) {\n    await this.ensureAtlasInstalled({ exp, skipPrompt: true, isProjectMutable: true });\n  }\n\n  private async ensureAtlasInstalled(options: Partial<EnsureDependenciesOptions> = {}) {\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        ...options,\n        installMessage:\n          'Expo Atlas is required to gather bundle information, but it is not installed in this project.',\n        warningMessage:\n          'Expo Atlas is not installed in this project, unable to gather bundle information.',\n        requiredPackages: [\n          { version: '^0.4.0', pkg: 'expo-atlas', file: 'expo-atlas/package.json', dev: true },\n        ],\n      });\n    } catch (error) {\n      this.resetAssertion({});\n      throw error;\n    }\n  }\n}\n"], "names": ["AtlasPrerequisite", "ProjectPrerequisite", "assertImplementation", "exp", "ensureAtlasInstalled", "bootstrapAsync", "skip<PERSON>rompt", "isProjectMutable", "options", "ensureDependenciesAsync", "projectRoot", "installMessage", "warningMessage", "requiredPackages", "version", "pkg", "file", "dev", "error", "resetAssertion"], "mappings": "AAAA;;;;+BAMaA,mBAAiB;;aAAjBA,iBAAiB;;8BANM,8BAA8B;yCAI3D,sDAAsD;AAEtD,MAAMA,iBAAiB,SAASC,aAAmB,oBAAA;UAIlDC,oBAAoB,CAAC,EAAEC,GAAG,CAAA,EAA0C,GAAG,EAAE,EAAE;QAC/E,MAAM,IAAI,CAACC,oBAAoB,CAAC;YAAED,GAAG;SAAE,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd;UAEME,cAAc,CAAC,EAAEF,GAAG,CAAA,EAA0C,GAAG,EAAE,EAAE;QACzE,MAAM,IAAI,CAACC,oBAAoB,CAAC;YAAED,GAAG;YAAEG,UAAU,EAAE,IAAI;YAAEC,gBAAgB,EAAE,IAAI;SAAE,CAAC,CAAC;IACrF;UAEcH,oBAAoB,CAACI,OAA2C,GAAG,EAAE,EAAE;QACnF,IAAI;YACF,OAAO,MAAMC,IAAAA,wBAAuB,wBAAA,EAAC,IAAI,CAACC,WAAW,EAAE;gBACrD,GAAGF,OAAO;gBACVG,cAAc,EACZ,+FAA+F;gBACjGC,cAAc,EACZ,mFAAmF;gBACrFC,gBAAgB,EAAE;oBAChB;wBAAEC,OAAO,EAAE,QAAQ;wBAAEC,GAAG,EAAE,YAAY;wBAAEC,IAAI,EAAE,yBAAyB;wBAAEC,GAAG,EAAE,IAAI;qBAAE;iBACrF;aACF,CAAC,CAAC;QACL,EAAE,OAAOC,KAAK,EAAE;YACd,IAAI,CAACC,cAAc,CAAC,EAAE,CAAC,CAAC;YACxB,MAAMD,KAAK,CAAC;QACd,CAAC;IACH;CACD"}