{"version": 3, "sources": ["../../../src/utils/createTempPath.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport tempDir from 'temp-dir';\nimport uniqueString from 'unique-string';\n\nconst uniqueTempPath = (): string => path.join(tempDir, uniqueString());\n\n// Functionally equivalent to: https://github.com/sindresorhus/tempy/blob/943ade0c935367117adbe2b690516ebc94139c6d/index.js#L43-L47\nexport function createTempDirectoryPath(): string {\n  const directory = uniqueTempPath();\n  fs.mkdirSync(directory);\n  return directory;\n}\n\n// Functionally equivalent to: https://github.com/sindresorhus/tempy/blob/943ade0c935367117adbe2b690516ebc94139c6d/index.js#L25-L39\nexport function createTempFilePath(name = ''): string {\n  if (name) {\n    return path.join(createTempDirectoryPath(), name);\n  } else {\n    return uniqueTempPath();\n  }\n}\n"], "names": ["createTempDirectoryPath", "createTempFilePath", "uniqueTempPath", "path", "join", "tempDir", "uniqueString", "directory", "fs", "mkdirSync", "name"], "mappings": "AAAA;;;;;;;;;;;IAQgBA,uBAAuB,MAAvBA,uBAAuB;IAOvBC,kBAAkB,MAAlBA,kBAAkB;;;8DAfnB,IAAI;;;;;;;8DACF,MAAM;;;;;;;8DACH,UAAU;;;;;;;8DACL,eAAe;;;;;;;;;;;AAExC,MAAMC,cAAc,GAAG,IAAcC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,QAAO,EAAA,QAAA,EAAEC,IAAAA,aAAY,EAAA,QAAA,GAAE,CAAC,AAAC;AAGjE,SAASN,uBAAuB,GAAW;IAChD,MAAMO,SAAS,GAAGL,cAAc,EAAE,AAAC;IACnCM,GAAE,EAAA,QAAA,CAACC,SAAS,CAACF,SAAS,CAAC,CAAC;IACxB,OAAOA,SAAS,CAAC;AACnB,CAAC;AAGM,SAASN,kBAAkB,CAACS,IAAI,GAAG,EAAE,EAAU;IACpD,IAAIA,IAAI,EAAE;QACR,OAAOP,KAAI,EAAA,QAAA,CAACC,IAAI,CAACJ,uBAAuB,EAAE,EAAEU,IAAI,CAAC,CAAC;IACpD,OAAO;QACL,OAAOR,cAAc,EAAE,CAAC;IAC1B,CAAC;AACH,CAAC"}