{"version": 3, "sources": ["../../../../../src/start/platforms/android/getDevices.ts"], "sourcesContent": ["import { Device, getAttachedDevicesAsync } from './adb';\nimport { listAvdsAsync } from './emulator';\nimport { CommandError } from '../../../utils/errors';\n\n/** Get a list of all devices including offline emulators. Asserts if no devices are available. */\nexport async function getDevicesAsync(): Promise<Device[]> {\n  const bootedDevices = await getAttachedDevicesAsync();\n\n  const data = await listAvdsAsync();\n  const connectedNames = bootedDevices.map(({ name }) => name);\n\n  const offlineEmulators = data\n    .filter(({ name }) => !connectedNames.includes(name))\n    .map(({ name, type }) => {\n      return {\n        name,\n        type,\n        isBooted: false,\n        // TODO: Are emulators always authorized?\n        isAuthorized: true,\n      };\n    });\n\n  const allDevices = bootedDevices.concat(offlineEmulators);\n\n  if (!allDevices.length) {\n    throw new CommandError(\n      [\n        `No Android connected device found, and no emulators could be started automatically.`,\n        `Please connect a device or create an emulator (https://docs.expo.dev/workflow/android-studio-emulator).`,\n        `Then follow the instructions here to enable USB debugging:`,\n        `https://developer.android.com/studio/run/device.html#developer-device-options. If you are using Genymotion go to Settings -> ADB, select \"Use custom Android SDK tools\", and point it at your Android SDK directory.`,\n      ].join('\\n')\n    );\n  }\n\n  return allDevices;\n}\n"], "names": ["getDevicesAsync", "bootedDevices", "getAttachedDevicesAsync", "data", "listAvdsAsync", "connectedNames", "map", "name", "offlineEmulators", "filter", "includes", "type", "isBooted", "isAuthorized", "allDevices", "concat", "length", "CommandError", "join"], "mappings": "AAAA;;;;+BAKs<PERSON>,iBAAe;;aAAfA,eAAe;;qBALW,OAAO;0BACzB,YAAY;wBACb,uBAAuB;AAG7C,eAAeA,eAAe,GAAsB;IACzD,MAAMC,aAAa,GAAG,MAAMC,IAAAA,IAAuB,wBAAA,GAAE,AAAC;IAEtD,MAAMC,IAAI,GAAG,MAAMC,IAAAA,SAAa,cAAA,GAAE,AAAC;IACnC,MAAMC,cAAc,GAAGJ,aAAa,CAACK,GAAG,CAAC,CAAC,EAAEC,IAAI,CAAA,EAAE,GAAKA,IAAI,CAAC,AAAC;IAE7D,MAAMC,gBAAgB,GAAGL,IAAI,CAC1BM,MAAM,CAAC,CAAC,EAAEF,IAAI,CAAA,EAAE,GAAK,CAACF,cAAc,CAACK,QAAQ,CAACH,IAAI,CAAC,CAAC,CACpDD,GAAG,CAAC,CAAC,EAAEC,IAAI,CAAA,EAAEI,IAAI,CAAA,EAAE,GAAK;QACvB,OAAO;YACLJ,IAAI;YACJI,IAAI;YACJC,QAAQ,EAAE,KAAK;YACf,yCAAyC;YACzCC,YAAY,EAAE,IAAI;SACnB,CAAC;IACJ,CAAC,CAAC,AAAC;IAEL,MAAMC,UAAU,GAAGb,aAAa,CAACc,MAAM,CAACP,gBAAgB,CAAC,AAAC;IAE1D,IAAI,CAACM,UAAU,CAACE,MAAM,EAAE;QACtB,MAAM,IAAIC,OAAY,aAAA,CACpB;YACE,CAAC,mFAAmF,CAAC;YACrF,CAAC,uGAAuG,CAAC;YACzG,CAAC,0DAA0D,CAAC;YAC5D,CAAC,oNAAoN,CAAC;SACvN,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACJ,CAAC;IAED,OAAOJ,UAAU,CAAC;AACpB,CAAC"}