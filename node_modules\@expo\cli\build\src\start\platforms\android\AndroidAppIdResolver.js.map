{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidAppIdResolver.ts"], "sourcesContent": ["import { AndroidConfig } from '@expo/config-plugins';\n\nimport { AppIdResolver } from '../AppIdResolver';\n\nconst debug = require('debug')(\n  'expo:start:platforms:android:AndroidAppIdResolver'\n) as typeof console.log;\n\n/** Resolves the Android package name from the Expo config or native files. */\nexport class AndroidAppIdResolver extends AppIdResolver {\n  constructor(projectRoot: string) {\n    super(projectRoot, 'android', 'android.package');\n  }\n\n  async hasNativeProjectAsync(): Promise<boolean> {\n    try {\n      await AndroidConfig.Paths.getProjectPathOrThrowAsync(this.projectRoot);\n      return true;\n    } catch (error: any) {\n      debug('Expected error checking for native project:', error.message);\n      return false;\n    }\n  }\n\n  async resolveAppIdFromNativeAsync(): Promise<string | null> {\n    const applicationIdFromGradle = await AndroidConfig.Package.getApplicationIdAsync(\n      this.projectRoot\n    ).catch(() => null);\n    if (applicationIdFromGradle) {\n      return applicationIdFromGradle;\n    }\n\n    try {\n      const filePath = await AndroidConfig.Paths.getAndroidManifestAsync(this.projectRoot);\n      const androidManifest = await AndroidConfig.Manifest.readAndroidManifestAsync(filePath);\n      // Assert MainActivity defined.\n      await AndroidConfig.Manifest.getMainActivityOrThrow(androidManifest);\n      if (androidManifest.manifest?.$?.package) {\n        return androidManifest.manifest.$.package;\n      }\n    } catch (error: any) {\n      debug('Expected error resolving the package name from the AndroidManifest.xml:', error);\n    }\n\n    return null;\n  }\n}\n"], "names": ["AndroidAppIdResolver", "debug", "require", "AppIdResolver", "constructor", "projectRoot", "hasNativeProjectAsync", "AndroidConfig", "Paths", "getProjectPathOrThrowAsync", "error", "message", "resolveAppIdFromNativeAsync", "applicationIdFromGradle", "Package", "getApplicationIdAsync", "catch", "androidManifest", "filePath", "getAndroidManifestAsync", "Manifest", "readAndroidManifestAsync", "getMainActivityOrThrow", "manifest", "$", "package"], "mappings": "AAAA;;;;+BA<PERSON>a<PERSON>,sBAAoB;;aAApBA,oBAAoB;;;yBATH,sBAAsB;;;;;;+BAEtB,kBAAkB;AAEhD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,mDAAmD,CACpD,AAAsB,AAAC;AAGjB,MAAMF,oBAAoB,SAASG,cAAa,cAAA;IACrDC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,WAAW,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;IACnD;UAEMC,qBAAqB,GAAqB;QAC9C,IAAI;YACF,MAAMC,cAAa,EAAA,cAAA,CAACC,KAAK,CAACC,0BAA0B,CAAC,IAAI,CAACJ,WAAW,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,EAAE,OAAOK,KAAK,EAAO;YACnBT,KAAK,CAAC,6CAA6C,EAAES,KAAK,CAACC,OAAO,CAAC,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;IACH;UAEMC,2BAA2B,GAA2B;QAC1D,MAAMC,uBAAuB,GAAG,MAAMN,cAAa,EAAA,cAAA,CAACO,OAAO,CAACC,qBAAqB,CAC/E,IAAI,CAACV,WAAW,CACjB,CAACW,KAAK,CAAC,IAAM,IAAI,CAAC,AAAC;QACpB,IAAIH,uBAAuB,EAAE;YAC3B,OAAOA,uBAAuB,CAAC;QACjC,CAAC;QAED,IAAI;gBAKEI,GAAwB;YAJ5B,MAAMC,QAAQ,GAAG,MAAMX,cAAa,EAAA,cAAA,CAACC,KAAK,CAACW,uBAAuB,CAAC,IAAI,CAACd,WAAW,CAAC,AAAC;YACrF,MAAMY,eAAe,GAAG,MAAMV,cAAa,EAAA,cAAA,CAACa,QAAQ,CAACC,wBAAwB,CAACH,QAAQ,CAAC,AAAC;YACxF,+BAA+B;YAC/B,MAAMX,cAAa,EAAA,cAAA,CAACa,QAAQ,CAACE,sBAAsB,CAACL,eAAe,CAAC,CAAC;YACrE,IAAIA,CAAAA,GAAwB,GAAxBA,eAAe,CAACM,QAAQ,SAAG,GAA3BN,KAAAA,CAA2B,GAA3BA,QAAAA,GAAwB,CAAEO,CAAC,SAAA,GAA3BP,KAAAA,CAA2B,QAAEQ,OAAO,AAAT,EAAW;gBACxC,OAAOR,eAAe,CAACM,QAAQ,CAACC,CAAC,CAACC,OAAO,CAAC;YAC5C,CAAC;QACH,EAAE,OAAOf,KAAK,EAAO;YACnBT,KAAK,CAAC,yEAAyE,EAAES,KAAK,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,CAAC;IACd;CACD"}