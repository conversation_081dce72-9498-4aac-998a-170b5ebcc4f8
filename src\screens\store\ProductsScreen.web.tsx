import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Image,
  SafeAreaView
} from 'react-native';
import { Product } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';

// Mock data for testing
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Pizza Margherita',
    description: 'Deliciosa pizza con tomate, mozzarella y albahaca',
    price: 12.99,
    stock: 25,
    category: 'Pizza',
    image: 'https://via.placeholder.com/100'
  },
  {
    id: '2',
    name: 'Hamburguesa Clásica',
    description: 'Hamburguesa con carne, lechuga, tomate y queso',
    price: 8.50,
    stock: 15,
    category: 'Hamburguesas',
    image: 'https://via.placeholder.com/100'
  },
  {
    id: '3',
    name: '<PERSON>sal<PERSON> César',
    description: 'Ensalada fresca con pollo, crutones y aderezo césar',
    price: 7.25,
    stock: 30,
    category: 'Ensaladas',
    image: 'https://via.placeholder.com/100'
  }
];

const ProductsScreenWeb = () => {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [isLoading, setIsLoading] = useState(false);

  const handleAddProduct = () => {
    alert('Función: Agregar nuevo producto');
  };

  const handleEditProduct = (product: Product) => {
    alert(`Función: Editar producto ${product.name}`);
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <Card style={styles.productCard}>
      <TouchableOpacity 
        style={styles.productContent}
        onPress={() => handleEditProduct(item)}
      >
        <View style={styles.productImage}>
          <Text style={styles.imageText}>📦</Text>
        </View>
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>${item.price.toFixed(2)}</Text>
          <Text style={styles.productStock}>Stock: {item.stock}</Text>
          <Text style={styles.productCategory}>{item.category}</Text>
        </View>
        <Text style={styles.chevron}>▶</Text>
      </TouchableOpacity>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Products</Text>
          <Button 
            title="Add Product" 
            onPress={handleAddProduct}
            variant="primary"
            size="small"
            style={styles.addButton}
          />
        </View>
        
        <FlatList
          data={products}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            !isLoading ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyIcon}>📦</Text>
                <Text style={styles.emptyText}>No products found</Text>
                <Button 
                  title="Add Your First Product" 
                  onPress={handleAddProduct}
                  variant="primary"
                  style={styles.emptyButton}
                />
              </View>
            ) : null
          }
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.secondary,
  },
  addButton: {
    minWidth: 120,
  },
  listContent: {
    paddingBottom: SPACING.lg,
  },
  productCard: {
    marginBottom: SPACING.sm,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: SPACING.md,
    backgroundColor: COLORS.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageText: {
    fontSize: 24,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  productPrice: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.accent,
    marginBottom: SPACING.xs,
  },
  productStock: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.text,
    opacity: 0.7,
    marginBottom: SPACING.xs,
  },
  productCategory: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    backgroundColor: COLORS.border,
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  chevron: {
    fontSize: 18,
    color: COLORS.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: SPACING.md,
  },
  emptyText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
    marginBottom: SPACING.lg,
    textAlign: 'center',
  },
  emptyButton: {
    minWidth: 200,
  },
});

export default ProductsScreenWeb;
