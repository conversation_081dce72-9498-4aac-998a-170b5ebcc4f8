{"version": 3, "sources": ["../../../../../src/start/platforms/android/emulator.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { spawn } from 'child_process';\nimport os from 'os';\n\nimport { Device, getAttachedDevicesAsync, isBootAnimationCompleteAsync } from './adb';\nimport * as Log from '../../../log';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nexport const EMULATOR_MAX_WAIT_TIMEOUT = 60 * 1000 * 3;\n\nexport function whichEmulator(): string {\n  // https://developer.android.com/studio/command-line/variables\n  // TODO: Add ANDROID_SDK_ROOT support as well https://github.com/expo/expo/pull/16516#discussion_r820037917\n  if (process.env.ANDROID_HOME) {\n    return `${process.env.ANDROID_HOME}/emulator/emulator`;\n  }\n  return 'emulator';\n}\n\n/** Returns a list of emulator names. */\nexport async function listAvdsAsync(): Promise<Device[]> {\n  try {\n    const { stdout } = await spawnAsync(whichEmulator(), ['-list-avds']);\n    return (\n      stdout\n        .split(os.EOL)\n        .filter(Boolean)\n        /**\n         * AVD IDs cannot contain spaces. This removes extra info lines from the output. e.g.\n         * \"INFO    | Storing crashdata in: /tmp/android-brent/emu-crash-34.1.18.db\n         */\n        .filter((name) => !name.trim().includes(' '))\n        .map((name) => ({\n          name,\n          type: 'emulator',\n          // unsure from this\n          isBooted: false,\n          isAuthorized: true,\n        }))\n    );\n  } catch {\n    return [];\n  }\n}\n\n/** Start an Android device and wait until it is booted. */\nexport async function startDeviceAsync(\n  device: Pick<Device, 'name'>,\n  {\n    timeout = EMULATOR_MAX_WAIT_TIMEOUT,\n    interval = 1000,\n  }: {\n    /** Time in milliseconds to wait before asserting a timeout error. */\n    timeout?: number;\n    interval?: number;\n  } = {}\n): Promise<Device> {\n  Log.log(`\\u203A Opening emulator ${chalk.bold(device.name)}`);\n\n  // Start a process to open an emulator\n  const emulatorProcess = spawn(\n    whichEmulator(),\n    [\n      `@${device.name}`,\n      // disable animation for faster boot -- this might make it harder to detect if it mounted properly tho\n      //'-no-boot-anim'\n    ],\n    {\n      stdio: 'ignore',\n      detached: true,\n    }\n  );\n\n  emulatorProcess.unref();\n\n  return new Promise<Device>((resolve, reject) => {\n    const waitTimer = setInterval(async () => {\n      try {\n        const bootedDevices = await getAttachedDevicesAsync();\n        const connected = bootedDevices.find(({ name }) => name === device.name);\n        if (connected) {\n          const isBooted = await isBootAnimationCompleteAsync(connected.pid);\n          if (isBooted) {\n            stopWaiting();\n            resolve(connected);\n          }\n        }\n      } catch (error) {\n        stopWaiting();\n        reject(error);\n      }\n    }, interval);\n\n    // Reject command after timeout\n    const maxTimer = setTimeout(() => {\n      const manualCommand = `${whichEmulator()} @${device.name}`;\n      stopWaitingAndReject(\n        `It took too long to start the Android emulator: ${device.name}. You can try starting the emulator manually from the terminal with: ${manualCommand}`\n      );\n    }, timeout);\n\n    const stopWaiting = () => {\n      clearTimeout(maxTimer);\n      clearInterval(waitTimer);\n      removeExitHook();\n    };\n\n    const stopWaitingAndReject = (message: string) => {\n      stopWaiting();\n      reject(new Error(message));\n    };\n\n    const removeExitHook = installExitHooks((signal) => {\n      stopWaiting();\n      emulatorProcess.kill(signal);\n      reject(new AbortCommandError());\n    });\n\n    emulatorProcess.on('error', ({ message }) => stopWaitingAndReject(message));\n\n    emulatorProcess.on('exit', () => {\n      const manualCommand = `${whichEmulator()} @${device.name}`;\n      stopWaitingAndReject(\n        `The emulator (${device.name}) quit before it finished opening. You can try starting the emulator manually from the terminal with: ${manualCommand}`\n      );\n    });\n  });\n}\n"], "names": ["EMULATOR_MAX_WAIT_TIMEOUT", "whichEmulator", "listAvdsAsync", "startDeviceAsync", "process", "env", "ANDROID_HOME", "stdout", "spawnAsync", "split", "os", "EOL", "filter", "Boolean", "name", "trim", "includes", "map", "type", "isBooted", "isAuthorized", "device", "timeout", "interval", "Log", "log", "chalk", "bold", "emulatorProcess", "spawn", "stdio", "detached", "unref", "Promise", "resolve", "reject", "waitTimer", "setInterval", "bootedDevices", "getAttachedDevicesAsync", "connected", "find", "isBootAnimationCompleteAsync", "pid", "stopWaiting", "error", "maxTimer", "setTimeout", "manualCommand", "stopWaitingAndReject", "clearTimeout", "clearInterval", "removeExitHook", "message", "Error", "installExitHooks", "signal", "kill", "AbortCommandError", "on"], "mappings": "AAAA;;;;;;;;;;;IAUaA,yBAAyB,MAAzBA,yBAAyB;IAEtBC,aAAa,MAAbA,aAAa;IAUPC,aAAa,MAAbA,aAAa;IA0BbC,gBAAgB,MAAhBA,gBAAgB;;;8DAhDf,mBAAmB;;;;;;;8DACxB,OAAO;;;;;;;yBACH,eAAe;;;;;;;8DACtB,IAAI;;;;;;qBAE2D,OAAO;2DAChE,cAAc;wBACD,uBAAuB;sBACxB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMH,yBAAyB,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,AAAC;AAEhD,SAASC,aAAa,GAAW;IACtC,8DAA8D;IAC9D,2GAA2G;IAC3G,IAAIG,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE;QAC5B,OAAO,CAAC,EAAEF,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC,kBAAkB,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAGM,eAAeJ,aAAa,GAAsB;IACvD,IAAI;QACF,MAAM,EAAEK,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAACP,aAAa,EAAE,EAAE;YAAC,YAAY;SAAC,CAAC,AAAC;QACrE,OACEM,MAAM,CACHE,KAAK,CAACC,GAAE,EAAA,QAAA,CAACC,GAAG,CAAC,CACbC,MAAM,CAACC,OAAO,CAAC,AAChB;;;SAGC,IACAD,MAAM,CAAC,CAACE,IAAI,GAAK,CAACA,IAAI,CAACC,IAAI,EAAE,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC5CC,GAAG,CAAC,CAACH,IAAI,GAAK,CAAC;gBACdA,IAAI;gBACJI,IAAI,EAAE,UAAU;gBAChB,mBAAmB;gBACnBC,QAAQ,EAAE,KAAK;gBACfC,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC,CACL;IACJ,EAAE,OAAM;QACN,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAGM,eAAejB,gBAAgB,CACpCkB,MAA4B,EAC5B,EACEC,OAAO,EAAGtB,yBAAyB,CAAA,EACnCuB,QAAQ,EAAG,IAAI,CAAA,EAKhB,GAAG,EAAE,EACW;IACjBC,IAAG,CAACC,GAAG,CAAC,CAAC,wBAAwB,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAACN,MAAM,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,sCAAsC;IACtC,MAAMc,eAAe,GAAGC,IAAAA,aAAK,EAAA,MAAA,EAC3B5B,aAAa,EAAE,EACf;QACE,CAAC,CAAC,EAAEoB,MAAM,CAACP,IAAI,CAAC,CAAC;KAGlB,EACD;QACEgB,KAAK,EAAE,QAAQ;QACfC,QAAQ,EAAE,IAAI;KACf,CACF,AAAC;IAEFH,eAAe,CAACI,KAAK,EAAE,CAAC;IAExB,OAAO,IAAIC,OAAO,CAAS,CAACC,OAAO,EAAEC,MAAM,GAAK;QAC9C,MAAMC,SAAS,GAAGC,WAAW,CAAC,UAAY;YACxC,IAAI;gBACF,MAAMC,aAAa,GAAG,MAAMC,IAAAA,IAAuB,wBAAA,GAAE,AAAC;gBACtD,MAAMC,SAAS,GAAGF,aAAa,CAACG,IAAI,CAAC,CAAC,EAAE3B,IAAI,CAAA,EAAE,GAAKA,IAAI,KAAKO,MAAM,CAACP,IAAI,CAAC,AAAC;gBACzE,IAAI0B,SAAS,EAAE;oBACb,MAAMrB,QAAQ,GAAG,MAAMuB,IAAAA,IAA4B,6BAAA,EAACF,SAAS,CAACG,GAAG,CAAC,AAAC;oBACnE,IAAIxB,QAAQ,EAAE;wBACZyB,WAAW,EAAE,CAAC;wBACdV,OAAO,CAACM,SAAS,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;YACH,EAAE,OAAOK,KAAK,EAAE;gBACdD,WAAW,EAAE,CAAC;gBACdT,MAAM,CAACU,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,EAAEtB,QAAQ,CAAC,AAAC;QAEb,+BAA+B;QAC/B,MAAMuB,QAAQ,GAAGC,UAAU,CAAC,IAAM;YAChC,MAAMC,aAAa,GAAG,CAAC,EAAE/C,aAAa,EAAE,CAAC,EAAE,EAAEoB,MAAM,CAACP,IAAI,CAAC,CAAC,AAAC;YAC3DmC,oBAAoB,CAClB,CAAC,gDAAgD,EAAE5B,MAAM,CAACP,IAAI,CAAC,qEAAqE,EAAEkC,aAAa,CAAC,CAAC,CACtJ,CAAC;QACJ,CAAC,EAAE1B,OAAO,CAAC,AAAC;QAEZ,MAAMsB,WAAW,GAAG,IAAM;YACxBM,YAAY,CAACJ,QAAQ,CAAC,CAAC;YACvBK,aAAa,CAACf,SAAS,CAAC,CAAC;YACzBgB,cAAc,EAAE,CAAC;QACnB,CAAC,AAAC;QAEF,MAAMH,oBAAoB,GAAG,CAACI,OAAe,GAAK;YAChDT,WAAW,EAAE,CAAC;YACdT,MAAM,CAAC,IAAImB,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC;QAC7B,CAAC,AAAC;QAEF,MAAMD,cAAc,GAAGG,IAAAA,KAAgB,iBAAA,EAAC,CAACC,MAAM,GAAK;YAClDZ,WAAW,EAAE,CAAC;YACdhB,eAAe,CAAC6B,IAAI,CAACD,MAAM,CAAC,CAAC;YAC7BrB,MAAM,CAAC,IAAIuB,OAAiB,kBAAA,EAAE,CAAC,CAAC;QAClC,CAAC,CAAC,AAAC;QAEH9B,eAAe,CAAC+B,EAAE,CAAC,OAAO,EAAE,CAAC,EAAEN,OAAO,CAAA,EAAE,GAAKJ,oBAAoB,CAACI,OAAO,CAAC,CAAC,CAAC;QAE5EzB,eAAe,CAAC+B,EAAE,CAAC,MAAM,EAAE,IAAM;YAC/B,MAAMX,aAAa,GAAG,CAAC,EAAE/C,aAAa,EAAE,CAAC,EAAE,EAAEoB,MAAM,CAACP,IAAI,CAAC,CAAC,AAAC;YAC3DmC,oBAAoB,CAClB,CAAC,cAAc,EAAE5B,MAAM,CAACP,IAAI,CAAC,sGAAsG,EAAEkC,aAAa,CAAC,CAAC,CACrJ,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC"}