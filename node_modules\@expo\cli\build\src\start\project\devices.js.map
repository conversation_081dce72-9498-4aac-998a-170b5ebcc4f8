{"version": 3, "sources": ["../../../../src/start/project/devices.ts"], "sourcesContent": ["import { createTemporaryProjectFile } from './dotExpo';\n\nconst debug = require('debug')('expo:start:project:devices') as typeof console.log;\n\nexport type DeviceInfo = {\n  installationId: string;\n  lastUsed: number;\n};\n\nexport type DevicesInfo = {\n  devices: DeviceInfo[];\n};\n\nconst DEVICES_FILE_NAME = 'devices.json';\n\nconst MILLISECONDS_IN_30_DAYS = 30 * 24 * 60 * 60 * 1000;\n\nexport const DevicesFile = createTemporaryProjectFile<DevicesInfo>(DEVICES_FILE_NAME, {\n  devices: [],\n});\n\nlet devicesInfo: DevicesInfo | null = null;\n\nexport async function getDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  if (devicesInfo) {\n    return devicesInfo;\n  }\n  return readDevicesInfoAsync(projectRoot);\n}\n\nexport async function readDevicesInfoAsync(projectRoot: string): Promise<DevicesInfo> {\n  try {\n    devicesInfo = await DevicesFile.readAsync(projectRoot);\n\n    // if the file on disk has old devices, filter them out here before we use them\n    const filteredDevices = filterOldDevices(devicesInfo.devices);\n    if (filteredDevices.length < devicesInfo.devices.length) {\n      devicesInfo = {\n        ...devicesInfo,\n        devices: filteredDevices,\n      };\n      // save the newly filtered list for consistency\n      try {\n        await setDevicesInfoAsync(projectRoot, devicesInfo);\n      } catch {\n        // do nothing here, we'll just keep using the filtered list in memory for now\n      }\n    }\n\n    return devicesInfo;\n  } catch {\n    return await DevicesFile.setAsync(projectRoot, { devices: [] });\n  }\n}\n\nexport async function setDevicesInfoAsync(\n  projectRoot: string,\n  json: DevicesInfo\n): Promise<DevicesInfo> {\n  devicesInfo = json;\n  return await DevicesFile.setAsync(projectRoot, json);\n}\n\nexport async function saveDevicesAsync(\n  projectRoot: string,\n  deviceIds: string | string[]\n): Promise<void> {\n  const currentTime = Date.now();\n  const newDeviceIds = typeof deviceIds === 'string' ? [deviceIds] : deviceIds;\n\n  debug(`Saving devices: ${newDeviceIds}`);\n  const { devices } = await getDevicesInfoAsync(projectRoot);\n  const newDevicesJson = devices\n    .filter((device) => !newDeviceIds.includes(device.installationId))\n    .concat(newDeviceIds.map((deviceId) => ({ installationId: deviceId, lastUsed: currentTime })));\n  await setDevicesInfoAsync(projectRoot, { devices: filterOldDevices(newDevicesJson) });\n}\n\nfunction filterOldDevices(devices: DeviceInfo[]) {\n  const currentTime = Date.now();\n  return (\n    devices\n      // filter out any devices that haven't been used to open this project in 30 days\n      .filter((device) => currentTime - device.lastUsed <= MILLISECONDS_IN_30_DAYS)\n      // keep only the 10 most recently used devices\n      .sort((a, b) => b.lastUsed - a.lastUsed)\n      .slice(0, 10)\n  );\n}\n"], "names": ["DevicesFile", "getDevicesInfoAsync", "readDevicesInfoAsync", "setDevicesInfoAsync", "saveDevicesAsync", "debug", "require", "DEVICES_FILE_NAME", "MILLISECONDS_IN_30_DAYS", "createTemporaryProjectFile", "devices", "devicesInfo", "projectRoot", "readAsync", "filteredDevices", "filterOldDevices", "length", "setAsync", "json", "deviceIds", "currentTime", "Date", "now", "newDeviceIds", "newDevicesJson", "filter", "device", "includes", "installationId", "concat", "map", "deviceId", "lastUsed", "sort", "a", "b", "slice"], "mappings": "AAAA;;;;;;;;;;;IAiBaA,WAAW,MAAXA,WAAW;IAMFC,mBAAmB,MAAnBA,mBAAmB;IAOnBC,oBAAoB,MAApBA,oBAAoB;IAyBpBC,mBAAmB,MAAnBA,mBAAmB;IAQnBC,gBAAgB,MAAhBA,gBAAgB;;yBA/DK,WAAW;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAsB,AAAC;AAWnF,MAAMC,iBAAiB,GAAG,cAAc,AAAC;AAEzC,MAAMC,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,AAAC;AAElD,MAAMR,WAAW,GAAGS,IAAAA,QAA0B,2BAAA,EAAcF,iBAAiB,EAAE;IACpFG,OAAO,EAAE,EAAE;CACZ,CAAC,AAAC;AAEH,IAAIC,WAAW,GAAuB,IAAI,AAAC;AAEpC,eAAeV,mBAAmB,CAACW,WAAmB,EAAwB;IACnF,IAAID,WAAW,EAAE;QACf,OAAOA,WAAW,CAAC;IACrB,CAAC;IACD,OAAOT,oBAAoB,CAACU,WAAW,CAAC,CAAC;AAC3C,CAAC;AAEM,eAAeV,oBAAoB,CAACU,WAAmB,EAAwB;IACpF,IAAI;QACFD,WAAW,GAAG,MAAMX,WAAW,CAACa,SAAS,CAACD,WAAW,CAAC,CAAC;QAEvD,+EAA+E;QAC/E,MAAME,eAAe,GAAGC,gBAAgB,CAACJ,WAAW,CAACD,OAAO,CAAC,AAAC;QAC9D,IAAII,eAAe,CAACE,MAAM,GAAGL,WAAW,CAACD,OAAO,CAACM,MAAM,EAAE;YACvDL,WAAW,GAAG;gBACZ,GAAGA,WAAW;gBACdD,OAAO,EAAEI,eAAe;aACzB,CAAC;YACF,+CAA+C;YAC/C,IAAI;gBACF,MAAMX,mBAAmB,CAACS,WAAW,EAAED,WAAW,CAAC,CAAC;YACtD,EAAE,OAAM;YACN,6EAA6E;YAC/E,CAAC;QACH,CAAC;QAED,OAAOA,WAAW,CAAC;IACrB,EAAE,OAAM;QACN,OAAO,MAAMX,WAAW,CAACiB,QAAQ,CAACL,WAAW,EAAE;YAAEF,OAAO,EAAE,EAAE;SAAE,CAAC,CAAC;IAClE,CAAC;AACH,CAAC;AAEM,eAAeP,mBAAmB,CACvCS,WAAmB,EACnBM,IAAiB,EACK;IACtBP,WAAW,GAAGO,IAAI,CAAC;IACnB,OAAO,MAAMlB,WAAW,CAACiB,QAAQ,CAACL,WAAW,EAAEM,IAAI,CAAC,CAAC;AACvD,CAAC;AAEM,eAAed,gBAAgB,CACpCQ,WAAmB,EACnBO,SAA4B,EACb;IACf,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IAC/B,MAAMC,YAAY,GAAG,OAAOJ,SAAS,KAAK,QAAQ,GAAG;QAACA,SAAS;KAAC,GAAGA,SAAS,AAAC;IAE7Ed,KAAK,CAAC,CAAC,gBAAgB,EAAEkB,YAAY,CAAC,CAAC,CAAC,CAAC;IACzC,MAAM,EAAEb,OAAO,CAAA,EAAE,GAAG,MAAMT,mBAAmB,CAACW,WAAW,CAAC,AAAC;IAC3D,MAAMY,cAAc,GAAGd,OAAO,CAC3Be,MAAM,CAAC,CAACC,MAAM,GAAK,CAACH,YAAY,CAACI,QAAQ,CAACD,MAAM,CAACE,cAAc,CAAC,CAAC,CACjEC,MAAM,CAACN,YAAY,CAACO,GAAG,CAAC,CAACC,QAAQ,GAAK,CAAC;YAAEH,cAAc,EAAEG,QAAQ;YAAEC,QAAQ,EAAEZ,WAAW;SAAE,CAAC,CAAC,CAAC,AAAC;IACjG,MAAMjB,mBAAmB,CAACS,WAAW,EAAE;QAAEF,OAAO,EAAEK,gBAAgB,CAACS,cAAc,CAAC;KAAE,CAAC,CAAC;AACxF,CAAC;AAED,SAAST,gBAAgB,CAACL,OAAqB,EAAE;IAC/C,MAAMU,WAAW,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IAC/B,OACEZ,OAAO,AACL,gFAAgF;KAC/Ee,MAAM,CAAC,CAACC,MAAM,GAAKN,WAAW,GAAGM,MAAM,CAACM,QAAQ,IAAIxB,uBAAuB,CAAC,AAC7E,8CAA8C;KAC7CyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKA,CAAC,CAACH,QAAQ,GAAGE,CAAC,CAACF,QAAQ,CAAC,CACvCI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACf;AACJ,CAAC"}