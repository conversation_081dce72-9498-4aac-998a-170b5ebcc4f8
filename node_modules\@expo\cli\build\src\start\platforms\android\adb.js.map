{"version": 3, "sources": ["../../../../../src/start/platforms/android/adb.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport os from 'os';\n\nimport { ADBServer } from './ADBServer';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:platforms:android:adb') as typeof console.log;\n\nexport enum DeviceABI {\n  // The arch specific android target platforms are soft-deprecated.\n  // Instead of using TargetPlatform as a combination arch + platform\n  // the code will be updated to carry arch information in [DarwinArch]\n  // and [AndroidArch].\n  arm = 'arm',\n  arm64 = 'arm64',\n  x64 = 'x64',\n  x86 = 'x86',\n  x8664 = 'x86_64',\n  arm64v8a = 'arm64-v8a',\n  armeabiV7a = 'armeabi-v7a',\n  armeabi = 'armeabi',\n  universal = 'universal',\n}\n\n/** Represents a connected Android device. */\nexport type Device = {\n  /** Process ID. */\n  pid?: string;\n  /** Name of the device, also used as the ID for opening devices. */\n  name: string;\n  /** Is emulator or connected device. */\n  type: 'emulator' | 'device';\n  /** Is the device booted (emulator). */\n  isBooted: boolean;\n  /** Is device authorized for developing. https://expo.fyi/authorize-android-device */\n  isAuthorized: boolean;\n  /** The connection type to ADB, only available when `type: device` */\n  connectionType?: 'USB' | 'Network';\n};\n\ntype DeviceContext = Pick<Device, 'pid'>;\n\ntype DeviceProperties = Record<string, string>;\n\nconst CANT_START_ACTIVITY_ERROR = 'Activity not started, unable to resolve Intent';\n// http://developer.android.com/ndk/guides/abis.html\nconst PROP_CPU_NAME = 'ro.product.cpu.abi';\n\nconst PROP_CPU_ABI_LIST_NAME = 'ro.product.cpu.abilist';\n\n// Can sometimes be null\n// http://developer.android.com/ndk/guides/abis.html\nconst PROP_BOOT_ANIMATION_STATE = 'init.svc.bootanim';\n\nlet _server: ADBServer | null;\n\n/** Return the lazily loaded ADB server instance. */\nexport function getServer() {\n  _server ??= new ADBServer();\n  return _server;\n}\n\n/** Logs an FYI message about authorizing your device. */\nexport function logUnauthorized(device: Device) {\n  Log.warn(\n    `\\nThis computer is not authorized for developing on ${chalk.bold(device.name)}. ${chalk.dim(\n      learnMore('https://expo.fyi/authorize-android-device')\n    )}`\n  );\n}\n\n/** Returns true if the provided package name is installed on the provided Android device. */\nexport async function isPackageInstalledAsync(\n  device: DeviceContext,\n  androidPackage: string\n): Promise<boolean> {\n  const packages = await getServer().runAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'pm',\n      'list',\n      'packages',\n      '--user',\n      env.EXPO_ADB_USER,\n      androidPackage\n    )\n  );\n\n  const lines = packages.split(/\\r?\\n/);\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    if (line === `package:${androidPackage}`) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.launchActivity Activity to launch `[application identifier]/.[main activity name]`, ex: `com.bacon.app/.MainActivity`\n * @param props.url Optional (dev client) URL to launch\n */\nexport async function launchActivityAsync(\n  device: DeviceContext,\n  {\n    launchActivity,\n    url,\n  }: {\n    launchActivity: string;\n    url?: string;\n  }\n) {\n  const args: string[] = [\n    'shell',\n    'am',\n    'start',\n    // FLAG_ACTIVITY_SINGLE_TOP -- If set, the activity will not be launched if it is already running at the top of the history stack.\n    '-f',\n    '0x20000000',\n    // Activity to open first: com.bacon.app/.MainActivity\n    '-n',\n    launchActivity,\n  ];\n\n  if (url) {\n    args.push('-d', url);\n  }\n\n  return openAsync(adbArgs(device.pid, ...args));\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.applicationId package name to launch.\n */\nexport async function openAppIdAsync(\n  device: DeviceContext,\n  {\n    applicationId,\n  }: {\n    applicationId: string;\n  }\n) {\n  return openAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'monkey',\n      '-p',\n      applicationId,\n      '-c',\n      'android.intent.category.LAUNCHER',\n      '1'\n    )\n  );\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.url URL to launch.\n */\nexport async function openUrlAsync(\n  device: DeviceContext,\n  {\n    url,\n  }: {\n    url: string;\n  }\n) {\n  return openAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'am',\n      'start',\n      '-a',\n      'android.intent.action.VIEW',\n      '-d',\n      // ADB requires ampersands to be escaped.\n      url.replace(/&/g, String.raw`\\&`)\n    )\n  );\n}\n\n/** Runs a generic command watches for common errors in order to throw with an expected code. */\nasync function openAsync(args: string[]): Promise<string> {\n  const results = await getServer().runAsync(args);\n  if (\n    results.includes(CANT_START_ACTIVITY_ERROR) ||\n    results.match(/Error: Activity class .* does not exist\\./g)\n  ) {\n    throw new CommandError('APP_NOT_INSTALLED', results.substring(results.indexOf('Error: ')));\n  }\n  return results;\n}\n\n/** Uninstall an app given its Android package name. */\nexport async function uninstallAsync(\n  device: DeviceContext,\n  { appId }: { appId: string }\n): Promise<string> {\n  return await getServer().runAsync(\n    adbArgs(device.pid, 'uninstall', '--user', env.EXPO_ADB_USER, appId)\n  );\n}\n\n/** Get package info from an app based on its Android package name. */\nexport async function getPackageInfoAsync(\n  device: DeviceContext,\n  { appId }: { appId: string }\n): Promise<string> {\n  return await getServer().runAsync(adbArgs(device.pid, 'shell', 'dumpsys', 'package', appId));\n}\n\n/** Install an app on a connected device. */\nexport async function installAsync(device: DeviceContext, { filePath }: { filePath: string }) {\n  // TODO: Handle the `INSTALL_FAILED_INSUFFICIENT_STORAGE` error.\n  return await getServer().runAsync(\n    adbArgs(device.pid, 'install', '-r', '-d', '--user', env.EXPO_ADB_USER, filePath)\n  );\n}\n\n/** Format ADB args with process ID. */\nexport function adbArgs(pid: Device['pid'], ...options: string[]): string[] {\n  const args = [];\n  if (pid) {\n    args.push('-s', pid);\n  }\n\n  return args.concat(options);\n}\n\n// TODO: This is very expensive for some operations.\nexport async function getAttachedDevicesAsync(): Promise<Device[]> {\n  const output = await getServer().runAsync(['devices', '-l']);\n\n  const splitItems = output\n    .trim()\n    .replace(/\\n$/, '')\n    .split(os.EOL)\n    // Filter ADB trace logs from the output, e.g.\n    // adb D 03-06 15:25:53 63677 4018815 adb_client.cpp:393] adb_query: host:devices-l\n    // 03-04 12:29:44.557 16415 16415 D adb     : commandline.cpp:1646 Using server socket: tcp:************:5037\n    // 03-04 12:29:44.557 16415 16415 D adb     : adb_client.cpp:160 _adb_connect: host:version\n    .filter((line) => !line.match(/\\.cpp:[0-9]+/));\n\n  // First line is `\"List of devices attached\"`, remove it\n  // @ts-ignore: todo\n  const attachedDevices: {\n    props: string[];\n    type: Device['type'];\n    isAuthorized: Device['isAuthorized'];\n    isBooted: Device['isBooted'];\n    connectionType?: Device['connectionType'];\n  }[] = splitItems\n    .slice(1, splitItems.length)\n    .map((line) => {\n      // unauthorized: ['FA8251A00719', 'unauthorized', 'usb:338690048X', 'transport_id:5']\n      // authorized: ['FA8251A00719', 'device', 'usb:336592896X', 'product:walleye', 'model:Pixel_2', 'device:walleye', 'transport_id:4']\n      // emulator: ['emulator-5554', 'offline', 'transport_id:1']\n      const props = line.split(' ').filter(Boolean);\n      const type = line.includes('emulator') ? 'emulator' : 'device';\n\n      let connectionType;\n      if (type === 'device' && line.includes('usb:')) {\n        connectionType = 'USB';\n      } else if (type === 'device' && line.includes('_adb-tls-connect.')) {\n        connectionType = 'Network';\n      }\n\n      const isBooted = type === 'emulator' || props[1] !== 'offline';\n      const isAuthorized =\n        connectionType === 'Network'\n          ? line.includes('model:') // Network connected devices show `model:<name>` when authorized\n          : props[1] !== 'unauthorized';\n\n      return { props, type, isAuthorized, isBooted, connectionType };\n    })\n    .filter(({ props: [pid] }) => !!pid);\n\n  const devicePromises = attachedDevices.map<Promise<Device>>(async (props) => {\n    const {\n      type,\n      props: [pid, ...deviceInfo],\n      isAuthorized,\n      isBooted,\n    } = props;\n\n    let name: string | null = null;\n\n    if (type === 'device') {\n      if (isAuthorized) {\n        // Possibly formatted like `model:Pixel_2`\n        // Transform to `Pixel_2`\n        const modelItem = deviceInfo.find((info) => info.includes('model:'));\n        if (modelItem) {\n          name = modelItem.replace('model:', '');\n        }\n      }\n      // unauthorized devices don't have a name available to read\n      if (!name) {\n        // Device FA8251A00719\n        name = `Device ${pid}`;\n      }\n    } else {\n      // Given an emulator pid, get the emulator name which can be used to start the emulator later.\n      name = (await getAdbNameForDeviceIdAsync({ pid })) ?? '';\n    }\n\n    return props.connectionType\n      ? { pid, name, type, isAuthorized, isBooted, connectionType: props.connectionType }\n      : { pid, name, type, isAuthorized, isBooted };\n  });\n\n  return Promise.all(devicePromises);\n}\n\n/**\n * Return the Emulator name for an emulator ID, this can be used to determine if an emulator is booted.\n *\n * @param device.pid a value like `emulator-5554` from `abd devices`\n */\nexport async function getAdbNameForDeviceIdAsync(device: DeviceContext): Promise<string | null> {\n  const results = await getServer().runAsync(adbArgs(device.pid, 'emu', 'avd', 'name'));\n\n  if (results.match(/could not connect to TCP port .*: Connection refused/)) {\n    // Can also occur when the emulator does not exist.\n    throw new CommandError('EMULATOR_NOT_FOUND', results);\n  }\n\n  return sanitizeAdbDeviceName(results) ?? null;\n}\n\nexport async function isDeviceBootedAsync({\n  name,\n}: { name?: string } = {}): Promise<Device | null> {\n  const devices = await getAttachedDevicesAsync();\n\n  if (!name) {\n    return devices[0] ?? null;\n  }\n\n  return devices.find((device) => device.name === name) ?? null;\n}\n\n/**\n * Returns true when a device's splash screen animation has stopped.\n * This can be used to detect when a device is fully booted and ready to use.\n *\n * @param pid\n */\nexport async function isBootAnimationCompleteAsync(pid?: string): Promise<boolean> {\n  try {\n    const props = await getPropertyDataForDeviceAsync({ pid }, PROP_BOOT_ANIMATION_STATE);\n    return !!props[PROP_BOOT_ANIMATION_STATE].match(/stopped/);\n  } catch {\n    return false;\n  }\n}\n\n/** Get a list of ABIs for the provided device. */\nexport async function getDeviceABIsAsync(\n  device: Pick<Device, 'name' | 'pid'>\n): Promise<DeviceABI[]> {\n  const cpuAbiList = (await getPropertyDataForDeviceAsync(device, PROP_CPU_ABI_LIST_NAME))[\n    PROP_CPU_ABI_LIST_NAME\n  ];\n\n  if (cpuAbiList) {\n    return cpuAbiList.trim().split(',') as DeviceABI[];\n  }\n\n  const abi = (await getPropertyDataForDeviceAsync(device, PROP_CPU_NAME))[\n    PROP_CPU_NAME\n  ] as DeviceABI;\n  return [abi];\n}\n\nexport async function getPropertyDataForDeviceAsync(\n  device: DeviceContext,\n  prop?: string\n): Promise<DeviceProperties> {\n  // @ts-ignore\n  const propCommand = adbArgs(...[device.pid, 'shell', 'getprop', prop].filter(Boolean));\n  try {\n    // Prevent reading as UTF8.\n    const results = await getServer().getFileOutputAsync(propCommand);\n    // Like:\n    // [wifi.direct.interface]: [p2p-dev-wlan0]\n    // [wifi.interface]: [wlan0]\n\n    if (prop) {\n      debug(`Property data: (device pid: ${device.pid}, prop: ${prop}, data: ${results})`);\n      return {\n        [prop]: results,\n      };\n    }\n    const props = parseAdbDeviceProperties(results);\n\n    debug(`Parsed data:`, props);\n\n    return props;\n  } catch (error: any) {\n    // TODO: Ensure error has message and not stderr\n    throw new CommandError(`Failed to get properties for device (${device.pid}): ${error.message}`);\n  }\n}\n\nfunction parseAdbDeviceProperties(devicePropertiesString: string) {\n  const properties: DeviceProperties = {};\n  const propertyExp = /\\[(.*?)\\]: \\[(.*?)\\]/gm;\n  for (const match of devicePropertiesString.matchAll(propertyExp)) {\n    properties[match[1]] = match[2];\n  }\n  return properties;\n}\n\n/**\n * Sanitize the ADB device name to only get the actual device name.\n * On Windows, we need to do \\r, \\n, and \\r\\n filtering to get the name.\n */\nexport function sanitizeAdbDeviceName(deviceName: string) {\n  return deviceName\n    .trim()\n    .split(/[\\r\\n]+/)\n    .shift();\n}\n"], "names": ["getServer", "logUnauthorized", "isPackageInstalledAsync", "launchActivityAsync", "openAppIdAsync", "openUrlAsync", "uninstallAsync", "getPackageInfoAsync", "installAsync", "adbArgs", "getAttachedDevicesAsync", "getAdbNameForDeviceIdAsync", "isDeviceBootedAsync", "isBootAnimationCompleteAsync", "getDeviceABIsAsync", "getPropertyDataForDeviceAsync", "sanitizeAdbDeviceName", "debug", "require", "DeviceABI", "arm", "arm64", "x64", "x86", "x8664", "arm64v8a", "armeabiV7a", "<PERSON><PERSON><PERSON>", "universal", "CANT_START_ACTIVITY_ERROR", "PROP_CPU_NAME", "PROP_CPU_ABI_LIST_NAME", "PROP_BOOT_ANIMATION_STATE", "_server", "ADBServer", "device", "Log", "warn", "chalk", "bold", "name", "dim", "learnMore", "androidPackage", "packages", "runAsync", "pid", "env", "EXPO_ADB_USER", "lines", "split", "i", "length", "line", "trim", "launchActivity", "url", "args", "push", "openAsync", "applicationId", "replace", "String", "raw", "results", "includes", "match", "CommandError", "substring", "indexOf", "appId", "filePath", "options", "concat", "output", "splitItems", "os", "EOL", "filter", "attachedDevices", "slice", "map", "props", "Boolean", "type", "connectionType", "isBooted", "isAuthorized", "devicePromises", "deviceInfo", "modelItem", "find", "info", "Promise", "all", "devices", "cpuAbiList", "abi", "prop", "propCommand", "getFileOutputAsync", "parseAdbDeviceProperties", "error", "message", "devicePropertiesString", "properties", "propertyExp", "matchAll", "deviceName", "shift"], "mappings": "AAAA;;;;;;;;;;;;IA4DgBA,SAAS,MAATA,SAAS;IAMTC,eAAe,MAAfA,eAAe;IASTC,uBAAuB,MAAvBA,uBAAuB;IAgCvBC,mBAAmB,MAAnBA,mBAAmB;IAiCnBC,cAAc,MAAdA,cAAc;IA0BdC,YAAY,MAAZA,YAAY;IAoCZC,cAAc,MAAdA,cAAc;IAUdC,mBAAmB,MAAnBA,mBAAmB;IAQnBC,YAAY,MAAZA,YAAY;IAQlBC,OAAO,MAAPA,OAAO;IAUDC,uBAAuB,MAAvBA,uBAAuB;IAyFvBC,0BAA0B,MAA1BA,0BAA0B;IAW1BC,mBAAmB,MAAnBA,mBAAmB;IAkBnBC,4BAA4B,MAA5BA,4BAA4B;IAU5BC,kBAAkB,MAAlBA,kBAAkB;IAiBlBC,6BAA6B,MAA7BA,6BAA6B;IA2CnCC,qBAAqB,MAArBA,qBAAqB;;;8DA1anB,OAAO;;;;;;;8DACV,IAAI;;;;;;2BAEO,aAAa;2DAClB,cAAc;qBACf,oBAAoB;wBACX,uBAAuB;sBAC1B,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,AAAsB,AAAC;IAElF,SAcN;UAdWC,SAAS;IAATA,SAAS,CACnB,kEAAkE;IAClE,mEAAmE;IACnE,qEAAqE;IACrE,qBAAqB;IACrBC,KAAG,IAAHA,KAAG;IALOD,SAAS,CAMnBE,OAAK,IAALA,OAAK;IANKF,SAAS,CAOnBG,KAAG,IAAHA,KAAG;IAPOH,SAAS,CAQnBI,KAAG,IAAHA,KAAG;IAROJ,SAAS,CASnBK,OAAK,IAAG,QAAQ;IATNL,SAAS,CAUnBM,UAAQ,IAAG,WAAW;IAVZN,SAAS,CAWnBO,YAAU,IAAG,aAAa;IAXhBP,SAAS,CAYnBQ,SAAO,IAAPA,SAAO;IAZGR,SAAS,CAanBS,WAAS,IAATA,WAAS;GAbCT,SAAS,KAATA,SAAS;AAoCrB,MAAMU,yBAAyB,GAAG,gDAAgD,AAAC;AACnF,oDAAoD;AACpD,MAAMC,aAAa,GAAG,oBAAoB,AAAC;AAE3C,MAAMC,sBAAsB,GAAG,wBAAwB,AAAC;AAExD,wBAAwB;AACxB,oDAAoD;AACpD,MAAMC,yBAAyB,GAAG,mBAAmB,AAAC;AAEtD,IAAIC,OAAO,AAAkB,AAAC;AAGvB,SAASjC,SAAS,GAAG;IAC1BiC,OAAO,KAAK,IAAIC,UAAS,UAAA,EAAE,CAAC;IAC5B,OAAOD,OAAO,CAAC;AACjB,CAAC;AAGM,SAAShC,eAAe,CAACkC,MAAc,EAAE;IAC9CC,IAAG,CAACC,IAAI,CACN,CAAC,oDAAoD,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAACJ,MAAM,CAACK,IAAI,CAAC,CAAC,EAAE,EAAEF,MAAK,EAAA,QAAA,CAACG,GAAG,CAC1FC,IAAAA,KAAS,UAAA,EAAC,2CAA2C,CAAC,CACvD,CAAC,CAAC,CACJ,CAAC;AACJ,CAAC;AAGM,eAAexC,uBAAuB,CAC3CiC,MAAqB,EACrBQ,cAAsB,EACJ;IAClB,MAAMC,QAAQ,GAAG,MAAM5C,SAAS,EAAE,CAAC6C,QAAQ,CACzCpC,OAAO,CACL0B,MAAM,CAACW,GAAG,EACV,OAAO,EACP,IAAI,EACJ,MAAM,EACN,UAAU,EACV,QAAQ,EACRC,IAAG,IAAA,CAACC,aAAa,EACjBL,cAAc,CACf,CACF,AAAC;IAEF,MAAMM,KAAK,GAAGL,QAAQ,CAACM,KAAK,SAAS,AAAC;IACtC,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,CAAE;QACrC,MAAME,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC,CAACG,IAAI,EAAE,AAAC;QAC7B,IAAID,IAAI,KAAK,CAAC,QAAQ,EAAEV,cAAc,CAAC,CAAC,EAAE;YACxC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOM,eAAexC,mBAAmB,CACvCgC,MAAqB,EACrB,EACEoB,cAAc,CAAA,EACdC,GAAG,CAAA,EAIJ,EACD;IACA,MAAMC,IAAI,GAAa;QACrB,OAAO;QACP,IAAI;QACJ,OAAO;QACP,kIAAkI;QAClI,IAAI;QACJ,YAAY;QACZ,sDAAsD;QACtD,IAAI;QACJF,cAAc;KACf,AAAC;IAEF,IAAIC,GAAG,EAAE;QACPC,IAAI,CAACC,IAAI,CAAC,IAAI,EAAEF,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,OAAOG,SAAS,CAAClD,OAAO,CAAC0B,MAAM,CAACW,GAAG,KAAKW,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;AAMM,eAAerD,cAAc,CAClC+B,MAAqB,EACrB,EACEyB,aAAa,CAAA,EAGd,EACD;IACA,OAAOD,SAAS,CACdlD,OAAO,CACL0B,MAAM,CAACW,GAAG,EACV,OAAO,EACP,QAAQ,EACR,IAAI,EACJc,aAAa,EACb,IAAI,EACJ,kCAAkC,EAClC,GAAG,CACJ,CACF,CAAC;AACJ,CAAC;AAMM,eAAevD,YAAY,CAChC8B,MAAqB,EACrB,EACEqB,GAAG,CAAA,EAGJ,EACD;IACA,OAAOG,SAAS,CACdlD,OAAO,CACL0B,MAAM,CAACW,GAAG,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,4BAA4B,EAC5B,IAAI,EACJ,yCAAyC;IACzCU,GAAG,CAACK,OAAO,OAAOC,MAAM,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC,CAClC,CACF,CAAC;AACJ,CAAC;AAED,8FAA8F,GAC9F,eAAeJ,SAAS,CAACF,IAAc,EAAmB;IACxD,MAAMO,OAAO,GAAG,MAAMhE,SAAS,EAAE,CAAC6C,QAAQ,CAACY,IAAI,CAAC,AAAC;IACjD,IACEO,OAAO,CAACC,QAAQ,CAACpC,yBAAyB,CAAC,IAC3CmC,OAAO,CAACE,KAAK,8CAA8C,EAC3D;QACA,MAAM,IAAIC,OAAY,aAAA,CAAC,mBAAmB,EAAEH,OAAO,CAACI,SAAS,CAACJ,OAAO,CAACK,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC7F,CAAC;IACD,OAAOL,OAAO,CAAC;AACjB,CAAC;AAGM,eAAe1D,cAAc,CAClC6B,MAAqB,EACrB,EAAEmC,KAAK,CAAA,EAAqB,EACX;IACjB,OAAO,MAAMtE,SAAS,EAAE,CAAC6C,QAAQ,CAC/BpC,OAAO,CAAC0B,MAAM,CAACW,GAAG,EAAE,WAAW,EAAE,QAAQ,EAAEC,IAAG,IAAA,CAACC,aAAa,EAAEsB,KAAK,CAAC,CACrE,CAAC;AACJ,CAAC;AAGM,eAAe/D,mBAAmB,CACvC4B,MAAqB,EACrB,EAAEmC,KAAK,CAAA,EAAqB,EACX;IACjB,OAAO,MAAMtE,SAAS,EAAE,CAAC6C,QAAQ,CAACpC,OAAO,CAAC0B,MAAM,CAACW,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAEwB,KAAK,CAAC,CAAC,CAAC;AAC/F,CAAC;AAGM,eAAe9D,YAAY,CAAC2B,MAAqB,EAAE,EAAEoC,QAAQ,CAAA,EAAwB,EAAE;IAC5F,gEAAgE;IAChE,OAAO,MAAMvE,SAAS,EAAE,CAAC6C,QAAQ,CAC/BpC,OAAO,CAAC0B,MAAM,CAACW,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAEC,IAAG,IAAA,CAACC,aAAa,EAAEuB,QAAQ,CAAC,CAClF,CAAC;AACJ,CAAC;AAGM,SAAS9D,OAAO,CAACqC,GAAkB,EAAE,GAAG0B,OAAO,AAAU,EAAY;IAC1E,MAAMf,IAAI,GAAG,EAAE,AAAC;IAChB,IAAIX,GAAG,EAAE;QACPW,IAAI,CAACC,IAAI,CAAC,IAAI,EAAEZ,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,OAAOW,IAAI,CAACgB,MAAM,CAACD,OAAO,CAAC,CAAC;AAC9B,CAAC;AAGM,eAAe9D,uBAAuB,GAAsB;IACjE,MAAMgE,MAAM,GAAG,MAAM1E,SAAS,EAAE,CAAC6C,QAAQ,CAAC;QAAC,SAAS;QAAE,IAAI;KAAC,CAAC,AAAC;IAE7D,MAAM8B,UAAU,GAAGD,MAAM,CACtBpB,IAAI,EAAE,CACNO,OAAO,QAAQ,EAAE,CAAC,CAClBX,KAAK,CAAC0B,GAAE,EAAA,QAAA,CAACC,GAAG,CAAC,AACd,8CAA8C;IAC9C,mFAAmF;IACnF,6GAA6G;IAC7G,2FAA2F;KAC1FC,MAAM,CAAC,CAACzB,IAAI,GAAK,CAACA,IAAI,CAACa,KAAK,gBAAgB,CAAC,AAAC;IAEjD,wDAAwD;IACxD,mBAAmB;IACnB,MAAMa,eAAe,GAMfJ,UAAU,CACbK,KAAK,CAAC,CAAC,EAAEL,UAAU,CAACvB,MAAM,CAAC,CAC3B6B,GAAG,CAAC,CAAC5B,IAAI,GAAK;QACb,qFAAqF;QACrF,mIAAmI;QACnI,2DAA2D;QAC3D,MAAM6B,KAAK,GAAG7B,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC4B,MAAM,CAACK,OAAO,CAAC,AAAC;QAC9C,MAAMC,IAAI,GAAG/B,IAAI,CAACY,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,QAAQ,AAAC;QAE/D,IAAIoB,cAAc,AAAC;QACnB,IAAID,IAAI,KAAK,QAAQ,IAAI/B,IAAI,CAACY,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC9CoB,cAAc,GAAG,KAAK,CAAC;QACzB,OAAO,IAAID,IAAI,KAAK,QAAQ,IAAI/B,IAAI,CAACY,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YAClEoB,cAAc,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAMC,QAAQ,GAAGF,IAAI,KAAK,UAAU,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,AAAC;QAC/D,MAAMK,YAAY,GAChBF,cAAc,KAAK,SAAS,GACxBhC,IAAI,CAACY,QAAQ,CAAC,QAAQ,CAAC,CAAC,gEAAgE;WACxFiB,KAAK,CAAC,CAAC,CAAC,KAAK,cAAc,AAAC;QAElC,OAAO;YAAEA,KAAK;YAAEE,IAAI;YAAEG,YAAY;YAAED,QAAQ;YAAED,cAAc;SAAE,CAAC;IACjE,CAAC,CAAC,CACDP,MAAM,CAAC,CAAC,EAAEI,KAAK,EAAE,CAACpC,GAAG,CAAC,CAAA,EAAE,GAAK,CAAC,CAACA,GAAG,CAAC,AAAC;IAEvC,MAAM0C,cAAc,GAAGT,eAAe,CAACE,GAAG,CAAkB,OAAOC,KAAK,GAAK;QAC3E,MAAM,EACJE,IAAI,CAAA,EACJF,KAAK,EAAE,CAACpC,GAAG,EAAE,GAAG2C,UAAU,CAAC,CAAA,EAC3BF,YAAY,CAAA,EACZD,QAAQ,CAAA,IACT,GAAGJ,KAAK,AAAC;QAEV,IAAI1C,IAAI,GAAkB,IAAI,AAAC;QAE/B,IAAI4C,IAAI,KAAK,QAAQ,EAAE;YACrB,IAAIG,YAAY,EAAE;gBAChB,0CAA0C;gBAC1C,yBAAyB;gBACzB,MAAMG,SAAS,GAAGD,UAAU,CAACE,IAAI,CAAC,CAACC,IAAI,GAAKA,IAAI,CAAC3B,QAAQ,CAAC,QAAQ,CAAC,CAAC,AAAC;gBACrE,IAAIyB,SAAS,EAAE;oBACblD,IAAI,GAAGkD,SAAS,CAAC7B,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;YACD,2DAA2D;YAC3D,IAAI,CAACrB,IAAI,EAAE;gBACT,sBAAsB;gBACtBA,IAAI,GAAG,CAAC,OAAO,EAAEM,GAAG,CAAC,CAAC,CAAC;YACzB,CAAC;QACH,OAAO;YACL,8FAA8F;YAC9FN,IAAI,GAAG,AAAC,MAAM7B,0BAA0B,CAAC;gBAAEmC,GAAG;aAAE,CAAC,IAAK,EAAE,CAAC;QAC3D,CAAC;QAED,OAAOoC,KAAK,CAACG,cAAc,GACvB;YAAEvC,GAAG;YAAEN,IAAI;YAAE4C,IAAI;YAAEG,YAAY;YAAED,QAAQ;YAAED,cAAc,EAAEH,KAAK,CAACG,cAAc;SAAE,GACjF;YAAEvC,GAAG;YAAEN,IAAI;YAAE4C,IAAI;YAAEG,YAAY;YAAED,QAAQ;SAAE,CAAC;IAClD,CAAC,CAAC,AAAC;IAEH,OAAOO,OAAO,CAACC,GAAG,CAACN,cAAc,CAAC,CAAC;AACrC,CAAC;AAOM,eAAe7E,0BAA0B,CAACwB,MAAqB,EAA0B;IAC9F,MAAM6B,OAAO,GAAG,MAAMhE,SAAS,EAAE,CAAC6C,QAAQ,CAACpC,OAAO,CAAC0B,MAAM,CAACW,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,AAAC;IAEtF,IAAIkB,OAAO,CAACE,KAAK,wDAAwD,EAAE;QACzE,mDAAmD;QACnD,MAAM,IAAIC,OAAY,aAAA,CAAC,oBAAoB,EAAEH,OAAO,CAAC,CAAC;IACxD,CAAC;IAED,OAAOhD,qBAAqB,CAACgD,OAAO,CAAC,IAAI,IAAI,CAAC;AAChD,CAAC;AAEM,eAAepD,mBAAmB,CAAC,EACxC4B,IAAI,CAAA,EACc,GAAG,EAAE,EAA0B;IACjD,MAAMuD,OAAO,GAAG,MAAMrF,uBAAuB,EAAE,AAAC;IAEhD,IAAI,CAAC8B,IAAI,EAAE;QACT,OAAOuD,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC5B,CAAC;IAED,OAAOA,OAAO,CAACJ,IAAI,CAAC,CAACxD,MAAM,GAAKA,MAAM,CAACK,IAAI,KAAKA,IAAI,CAAC,IAAI,IAAI,CAAC;AAChE,CAAC;AAQM,eAAe3B,4BAA4B,CAACiC,GAAY,EAAoB;IACjF,IAAI;QACF,MAAMoC,KAAK,GAAG,MAAMnE,6BAA6B,CAAC;YAAE+B,GAAG;SAAE,EAAEd,yBAAyB,CAAC,AAAC;QACtF,OAAO,CAAC,CAACkD,KAAK,CAAClD,yBAAyB,CAAC,CAACkC,KAAK,WAAW,CAAC;IAC7D,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAGM,eAAepD,kBAAkB,CACtCqB,MAAoC,EACd;IACtB,MAAM6D,UAAU,GAAG,CAAC,MAAMjF,6BAA6B,CAACoB,MAAM,EAAEJ,sBAAsB,CAAC,CAAC,CACtFA,sBAAsB,CACvB,AAAC;IAEF,IAAIiE,UAAU,EAAE;QACd,OAAOA,UAAU,CAAC1C,IAAI,EAAE,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAgB;IACrD,CAAC;IAED,MAAM+C,GAAG,GAAG,CAAC,MAAMlF,6BAA6B,CAACoB,MAAM,EAAEL,aAAa,CAAC,CAAC,CACtEA,aAAa,CACd,AAAa,AAAC;IACf,OAAO;QAACmE,GAAG;KAAC,CAAC;AACf,CAAC;AAEM,eAAelF,6BAA6B,CACjDoB,MAAqB,EACrB+D,IAAa,EACc;IAC3B,aAAa;IACb,MAAMC,WAAW,GAAG1F,OAAO,IAAI;QAAC0B,MAAM,CAACW,GAAG;QAAE,OAAO;QAAE,SAAS;QAAEoD,IAAI;KAAC,CAACpB,MAAM,CAACK,OAAO,CAAC,CAAC,AAAC;IACvF,IAAI;QACF,2BAA2B;QAC3B,MAAMnB,OAAO,GAAG,MAAMhE,SAAS,EAAE,CAACoG,kBAAkB,CAACD,WAAW,CAAC,AAAC;QAClE,QAAQ;QACR,2CAA2C;QAC3C,4BAA4B;QAE5B,IAAID,IAAI,EAAE;YACRjF,KAAK,CAAC,CAAC,4BAA4B,EAAEkB,MAAM,CAACW,GAAG,CAAC,QAAQ,EAAEoD,IAAI,CAAC,QAAQ,EAAElC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACrF,OAAO;gBACL,CAACkC,IAAI,CAAC,EAAElC,OAAO;aAChB,CAAC;QACJ,CAAC;QACD,MAAMkB,KAAK,GAAGmB,wBAAwB,CAACrC,OAAO,CAAC,AAAC;QAEhD/C,KAAK,CAAC,CAAC,YAAY,CAAC,EAAEiE,KAAK,CAAC,CAAC;QAE7B,OAAOA,KAAK,CAAC;IACf,EAAE,OAAOoB,KAAK,EAAO;QACnB,gDAAgD;QAChD,MAAM,IAAInC,OAAY,aAAA,CAAC,CAAC,qCAAqC,EAAEhC,MAAM,CAACW,GAAG,CAAC,GAAG,EAAEwD,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;AACH,CAAC;AAED,SAASF,wBAAwB,CAACG,sBAA8B,EAAE;IAChE,MAAMC,UAAU,GAAqB,EAAE,AAAC;IACxC,MAAMC,WAAW,2BAA2B,AAAC;IAC7C,KAAK,MAAMxC,KAAK,IAAIsC,sBAAsB,CAACG,QAAQ,CAACD,WAAW,CAAC,CAAE;QAChED,UAAU,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,OAAOuC,UAAU,CAAC;AACpB,CAAC;AAMM,SAASzF,qBAAqB,CAAC4F,UAAkB,EAAE;IACxD,OAAOA,UAAU,CACdtD,IAAI,EAAE,CACNJ,KAAK,WAAW,CAChB2D,KAAK,EAAE,CAAC;AACb,CAAC"}