{"version": 3, "sources": ["../../../../../src/start/server/metro/fetchRouterManifest.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport resolveFrom from 'resolve-from';\n\nimport { getRoutePaths } from './router';\n\nexport type ExpoRouterServerManifestV1Route<TRegex = string> = {\n  file: string;\n  page: string;\n  routeKeys: Record<string, string>;\n  namedRegex: TRegex;\n  generated?: boolean;\n};\n\nexport type ExpoRouterServerManifestV1<TRegex = string> = {\n  apiRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  htmlRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  notFoundRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n};\n\nfunction getExpoRouteManifestBuilderAsync(projectRoot: string) {\n  return require(resolveFrom(projectRoot, 'expo-router/build/routes-manifest'))\n    .createRoutesManifest as typeof import('expo-router/build/routes-manifest').createRoutesManifest;\n}\n\n// TODO: Simplify this now that we use Node.js directly, no need for the Metro bundler caching layer.\nexport async function fetchManifest<TRegex = string>(\n  projectRoot: string,\n  options: {\n    asJson?: boolean;\n    appDir: string;\n  } & import('expo-router/build/routes-manifest').Options\n): Promise<ExpoRouterServerManifestV1<TRegex> | null> {\n  const getManifest = getExpoRouteManifestBuilderAsync(projectRoot);\n  const paths = getRoutePaths(options.appDir);\n  // Get the serialized manifest\n  const jsonManifest = getManifest(paths, options);\n\n  if (!jsonManifest) {\n    return null;\n  }\n\n  if (!jsonManifest.htmlRoutes || !jsonManifest.apiRoutes) {\n    throw new Error('Routes manifest is malformed: ' + JSON.stringify(jsonManifest, null, 2));\n  }\n\n  if (!options.asJson) {\n    // @ts-expect-error\n    return inflateManifest(jsonManifest);\n  }\n  // @ts-expect-error\n  return jsonManifest;\n}\n\n// Convert the serialized manifest to a usable format\nexport function inflateManifest(\n  json: ExpoRouterServerManifestV1<string>\n): ExpoRouterServerManifestV1<RegExp> {\n  return {\n    ...json,\n    htmlRoutes: json.htmlRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    apiRoutes: json.apiRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n    notFoundRoutes: json.notFoundRoutes?.map((value) => {\n      return {\n        ...value,\n        namedRegex: new RegExp(value.namedRegex),\n      };\n    }),\n  };\n}\n"], "names": ["fetchManifest", "inflateManifest", "getExpoRouteManifestBuilderAsync", "projectRoot", "require", "resolveFrom", "createRoutesManifest", "options", "getManifest", "paths", "getRoutePaths", "appDir", "jsonManifest", "htmlRoutes", "apiRoutes", "Error", "JSON", "stringify", "as<PERSON><PERSON>", "json", "map", "value", "namedRegex", "RegExp", "notFoundRoutes"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAwBsBA,aAAa,MAAbA,aAAa;IA6BnBC,eAAe,MAAfA,eAAe;;;8DArDP,cAAc;;;;;;wBAER,UAAU;;;;;;AAgBxC,SAASC,gCAAgC,CAACC,WAAmB,EAAE;IAC7D,OAAOC,OAAO,CAACC,IAAAA,YAAW,EAAA,QAAA,EAACF,WAAW,EAAE,mCAAmC,CAAC,CAAC,CAC1EG,oBAAoB,CAA4E;AACrG,CAAC;AAGM,eAAeN,aAAa,CACjCG,WAAmB,EACnBI,OAGuD,EACH;IACpD,MAAMC,WAAW,GAAGN,gCAAgC,CAACC,WAAW,CAAC,AAAC;IAClE,MAAMM,KAAK,GAAGC,IAAAA,OAAa,cAAA,EAACH,OAAO,CAACI,MAAM,CAAC,AAAC;IAC5C,8BAA8B;IAC9B,MAAMC,YAAY,GAAGJ,WAAW,CAACC,KAAK,EAAEF,OAAO,CAAC,AAAC;IAEjD,IAAI,CAACK,YAAY,EAAE;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAACA,YAAY,CAACC,UAAU,IAAI,CAACD,YAAY,CAACE,SAAS,EAAE;QACvD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,GAAGC,IAAI,CAACC,SAAS,CAACL,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5F,CAAC;IAED,IAAI,CAACL,OAAO,CAACW,MAAM,EAAE;QACnB,mBAAmB;QACnB,OAAOjB,eAAe,CAACW,YAAY,CAAC,CAAC;IACvC,CAAC;IACD,mBAAmB;IACnB,OAAOA,YAAY,CAAC;AACtB,CAAC;AAGM,SAASX,eAAe,CAC7BkB,IAAwC,EACJ;QAGtBA,GAAe,EAMhBA,IAAc,EAMTA,IAAmB;IAdrC,OAAO;QACL,GAAGA,IAAI;QACPN,UAAU,EAAEM,CAAAA,GAAe,GAAfA,IAAI,CAACN,UAAU,SAAK,GAApBM,KAAAA,CAAoB,GAApBA,GAAe,CAAEC,GAAG,CAAC,CAACC,KAAK,GAAK;YAC1C,OAAO;gBACL,GAAGA,KAAK;gBACRC,UAAU,EAAE,IAAIC,MAAM,CAACF,KAAK,CAACC,UAAU,CAAC;aACzC,CAAC;QACJ,CAAC,CAAC;QACFR,SAAS,EAAEK,CAAAA,IAAc,GAAdA,IAAI,CAACL,SAAS,SAAK,GAAnBK,KAAAA,CAAmB,GAAnBA,IAAc,CAAEC,GAAG,CAAC,CAACC,KAAK,GAAK;YACxC,OAAO;gBACL,GAAGA,KAAK;gBACRC,UAAU,EAAE,IAAIC,MAAM,CAACF,KAAK,CAACC,UAAU,CAAC;aACzC,CAAC;QACJ,CAAC,CAAC;QACFE,cAAc,EAAEL,CAAAA,IAAmB,GAAnBA,IAAI,CAACK,cAAc,SAAK,GAAxBL,KAAAA,CAAwB,GAAxBA,IAAmB,CAAEC,GAAG,CAAC,CAACC,KAAK,GAAK;YAClD,OAAO;gBACL,GAAGA,KAAK;gBACRC,UAAU,EAAE,IAAIC,MAAM,CAACF,KAAK,CAACC,UAAU,CAAC;aACzC,CAAC;QACJ,CAAC,CAAC;KACH,CAAC;AACJ,CAAC"}