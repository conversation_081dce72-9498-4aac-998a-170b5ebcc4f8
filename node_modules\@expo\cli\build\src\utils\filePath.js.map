{"version": 3, "sources": ["../../../src/utils/filePath.ts"], "sourcesContent": ["import fs from 'fs';\n\nconst REGEXP_REPLACE_SLASHES = /\\\\/g;\n\n/**\n * This is a workaround for Metro not resolving entry file paths to their real location.\n * When running exports through `eas build --local` on macOS, the `/var/folders` path is used instead of `/private/var/folders`.\n *\n * See: https://github.com/expo/expo/issues/28890\n */\nexport function resolveRealEntryFilePath(projectRoot: string, entryFile: string): string {\n  if (projectRoot.startsWith('/private/var') && entryFile.startsWith('/var')) {\n    return fs.realpathSync(entryFile);\n  }\n\n  return entryFile;\n}\n\n/**\n * Convert any platform-specific path to a POSIX path.\n */\nexport function toPosixPath(filePath: string): string {\n  return filePath.replace(REGEXP_REPLACE_SLASHES, '/');\n}\n"], "names": ["resolveRealEntryFilePath", "toPosixPath", "REGEXP_REPLACE_SLASHES", "projectRoot", "entryFile", "startsWith", "fs", "realpathSync", "filePath", "replace"], "mappings": "AAAA;;;;;;;;;;;IAUgBA,wBAAwB,MAAxBA,wBAAwB;IAWxBC,WAAW,MAAXA,WAAW;;;8DArBZ,IAAI;;;;;;;;;;;AAEnB,MAAMC,sBAAsB,QAAQ,AAAC;AAQ9B,SAASF,wBAAwB,CAACG,WAAmB,EAAEC,SAAiB,EAAU;IACvF,IAAID,WAAW,CAACE,UAAU,CAAC,cAAc,CAAC,IAAID,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC1E,OAAOC,GAAE,EAAA,QAAA,CAACC,YAAY,CAACH,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,OAAOA,SAAS,CAAC;AACnB,CAAC;AAKM,SAASH,WAAW,CAACO,QAAgB,EAAU;IACpD,OAAOA,QAAQ,CAACC,OAAO,CAACP,sBAAsB,EAAE,GAAG,CAAC,CAAC;AACvD,CAAC"}