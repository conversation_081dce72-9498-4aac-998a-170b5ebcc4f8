{"version": 3, "sources": ["../../../src/utils/ip.ts"], "sourcesContent": ["import internalIp from 'internal-ip';\nimport { spawnSync } from 'node:child_process';\nimport { isIPv4 } from 'node:net';\nimport { networkInterfaces } from 'node:os';\n\n/** Gets a route address by opening a UDP socket to a publicly routed address.\n * @privateRemarks\n * This is wrapped in `spawnSync` since the original `getIpAddress` utility exported\n * in this module is used synchronosly. An appropriate timeout has been set and UDP\n * ports don't send a message when opened.\n * @throws if `spawnSync` fails\n */\nfunction getRouteAddress(): string | null {\n  const { error, status, stdout } = spawnSync(process.execPath, ['-'], {\n    // This should be the cheapest method to determine the default route\n    // By opening a socket to a publicly routed IP address, we let the default\n    // gateway handle this socket, which means the socket's address will be\n    // the prioritised route for public IP addresses.\n    // It might fall back to `\"0.0.0.0\"` when no network connection is established\n    input: `\n      var socket = require('dgram').createSocket({ type: 'udp4', reuseAddr: true });\n      socket.unref();\n      socket.connect(53, '*******', function() {\n        var address = socket.address();\n        socket.close();\n        if (address && 'address' in address) {\n          process.stdout.write(address.address);\n          process.exit(0);\n        } else {\n          process.exit(1);\n        }\n      });\n    `,\n    shell: false,\n    timeout: 500,\n    encoding: 'utf8',\n    windowsVerbatimArguments: false,\n    windowsHide: true,\n  });\n  // We only use the stdout as an IP, if it validates as an IP and we got a zero exit code\n  if (status || error) {\n    return null;\n  } else if (!status && typeof stdout === 'string' && isIPv4(stdout.trim())) {\n    return stdout.trim();\n  } else {\n    return null;\n  }\n}\n\n/** By convention, a zero mac address means we have a virtual device, which we'd like to exclude */\nconst VIRTUAL_MAC_ADDRESS = '00:00:00:00:00:00';\n\n/** Determines the internal IP address by opening a socket, then checking the socket address against non-internal network interface assignments\n * @throws If no address can be determined.\n */\nfunction getRouteIPAddress(): string | null {\n  // We check the IP address we get against the available network interfaces\n  // It's only an internal IP address if we have a matching address on an interface's IP assignment\n  let routeAddress: string | null = null;\n  try {\n    routeAddress = getRouteAddress();\n  } catch {}\n  if (!routeAddress) {\n    return null;\n  }\n  let ifaces: ReturnType<typeof networkInterfaces>;\n  try {\n    ifaces = networkInterfaces();\n  } catch {\n    // NOTE: This usually doesn't throw, but invalid builds or unknown targets in Node.js\n    // can cause this call to unexpectedly raise a system error\n    return null;\n  }\n  for (const iface in ifaces) {\n    const assignments = ifaces[iface];\n    for (let i = 0; assignments && i < assignments.length; i++) {\n      const assignment = assignments[i];\n      // Only use IPv4 assigments that aren't internal\n      // Only use IPv4 assignment if it's not a virtual device (e.g. a VPN network interface)\n      if (\n        assignment.family === 'IPv4' &&\n        !assignment.internal &&\n        assignment.address === routeAddress &&\n        assignment.mac !== VIRTUAL_MAC_ADDRESS\n      )\n        return routeAddress;\n    }\n  }\n  return null;\n}\n\nexport function getIpAddress(): string {\n  return internalIp.v4.sync() || getRouteIPAddress() || '127.0.0.1';\n}\n"], "names": ["getIpAddress", "getRoute<PERSON>ddress", "error", "status", "stdout", "spawnSync", "process", "execPath", "input", "shell", "timeout", "encoding", "windowsVerbatimArguments", "windowsHide", "isIPv4", "trim", "VIRTUAL_MAC_ADDRESS", "getRouteIPAddress", "routeAddress", "ifaces", "networkInterfaces", "iface", "assignments", "i", "length", "assignment", "family", "internal", "address", "mac", "internalIp", "v4", "sync"], "mappings": "AAAA;;;;+BA2FgBA,cAAY;;aAAZA,YAAY;;;8DA3FL,aAAa;;;;;;;yBACV,oBAAoB;;;;;;;yBACvB,UAAU;;;;;;;yBACC,SAAS;;;;;;;;;;;AAE3C;;;;;;CAMC,GACD,SAASC,eAAe,GAAkB;IACxC,MAAM,EAAEC,KAAK,CAAA,EAAEC,MAAM,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGC,IAAAA,iBAAS,EAAA,UAAA,EAACC,OAAO,CAACC,QAAQ,EAAE;QAAC,GAAG;KAAC,EAAE;QACnE,oEAAoE;QACpE,0EAA0E;QAC1E,uEAAuE;QACvE,iDAAiD;QACjD,8EAA8E;QAC9EC,KAAK,EAAE,CAAC;;;;;;;;;;;;;IAaR,CAAC;QACDC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE,MAAM;QAChBC,wBAAwB,EAAE,KAAK;QAC/BC,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IACH,wFAAwF;IACxF,IAAIV,MAAM,IAAID,KAAK,EAAE;QACnB,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,CAACC,MAAM,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAIU,IAAAA,QAAM,EAAA,OAAA,EAACV,MAAM,CAACW,IAAI,EAAE,CAAC,EAAE;QACzE,OAAOX,MAAM,CAACW,IAAI,EAAE,CAAC;IACvB,OAAO;QACL,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,iGAAiG,GACjG,MAAMC,mBAAmB,GAAG,mBAAmB,AAAC;AAEhD;;CAEC,GACD,SAASC,iBAAiB,GAAkB;IAC1C,0EAA0E;IAC1E,iGAAiG;IACjG,IAAIC,YAAY,GAAkB,IAAI,AAAC;IACvC,IAAI;QACFA,YAAY,GAAGjB,eAAe,EAAE,CAAC;IACnC,EAAE,OAAM,CAAC,CAAC;IACV,IAAI,CAACiB,YAAY,EAAE;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAIC,MAAM,AAAsC,AAAC;IACjD,IAAI;QACFA,MAAM,GAAGC,IAAAA,OAAiB,EAAA,kBAAA,GAAE,CAAC;IAC/B,EAAE,OAAM;QACN,qFAAqF;QACrF,2DAA2D;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAK,MAAMC,KAAK,IAAIF,MAAM,CAAE;QAC1B,MAAMG,WAAW,GAAGH,MAAM,CAACE,KAAK,CAAC,AAAC;QAClC,IAAK,IAAIE,CAAC,GAAG,CAAC,EAAED,WAAW,IAAIC,CAAC,GAAGD,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,CAAE;YAC1D,MAAME,UAAU,GAAGH,WAAW,CAACC,CAAC,CAAC,AAAC;YAClC,gDAAgD;YAChD,uFAAuF;YACvF,IACEE,UAAU,CAACC,MAAM,KAAK,MAAM,IAC5B,CAACD,UAAU,CAACE,QAAQ,IACpBF,UAAU,CAACG,OAAO,KAAKV,YAAY,IACnCO,UAAU,CAACI,GAAG,KAAKb,mBAAmB,EAEtC,OAAOE,YAAY,CAAC;QACxB,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAASlB,YAAY,GAAW;IACrC,OAAO8B,WAAU,EAAA,QAAA,CAACC,EAAE,CAACC,IAAI,EAAE,IAAIf,iBAAiB,EAAE,IAAI,WAAW,CAAC;AACpE,CAAC"}