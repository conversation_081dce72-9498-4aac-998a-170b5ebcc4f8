{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeEvaluate.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode is trying to inject a script to configure Node environment variables.\n * This won't work in <PERSON><PERSON>, but vscode will retry this 200x.\n * Avoid sending this \"spam\" to the device.\n *\n * @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeAttacherBase.ts#L22-L54\n */\nexport class VscodeRuntimeEvaluateHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeEvaluate>) {\n    if (message.method === 'Runtime.evaluate' && isVscodeNodeAttachEnvironmentInjection(message)) {\n      return this.sendToDebugger<DeviceResponse<RuntimeEvaluate>>({\n        id: message.id,\n        result: {\n          result: {\n            type: 'string',\n            value: `Hermes doesn't support environment variables through process.env`,\n          },\n        },\n      });\n    }\n\n    if (message.method === 'Runtime.evaluate' && isVscodeNodeTelemetry(message)) {\n      return this.sendToDebugger<DeviceResponse<RuntimeEvaluate>>({\n        id: message.id,\n        result: {\n          result: {\n            type: 'object',\n            value: {\n              processId: this.page.id,\n              nodeVersion: process.version,\n              architecture: process.arch,\n            },\n          },\n        },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-evaluate */\nexport type RuntimeEvaluate = CdpMessage<\n  'Runtime.evaluate',\n  Protocol.Runtime.EvaluateRequest,\n  Protocol.Runtime.EvaluateResponse\n>;\n\n/** @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeAttacherBase.ts#L22-L54 */\nfunction isVscodeNodeAttachEnvironmentInjection(message: DebuggerRequest<RuntimeEvaluate>) {\n  return (\n    message.params?.expression.includes(`typeof process==='undefined'`) &&\n    message.params?.expression.includes(`'process not defined'`) &&\n    message.params?.expression.includes(`process.env[\"NODE_OPTIONS\"]`)\n  );\n}\n\n/** @see https://github.com/microsoft/vscode-js-debug/blob/1d104b5184736677ab5cc280c70bbd227403850c/src/targets/node/nodeLauncherBase.ts#L523-L531 */\nfunction isVscodeNodeTelemetry(message: DebuggerRequest<RuntimeEvaluate>) {\n  return (\n    message.params?.expression.includes(`typeof process === 'undefined'`) &&\n    message.params?.expression.includes(`'process not defined'`) &&\n    message.params?.expression.includes(`process.pid`) &&\n    message.params?.expression.includes(`process.version`) &&\n    message.params?.expression.includes(`process.arch`)\n  );\n}\n"], "names": ["VscodeRuntimeEvaluateHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "isVscodeNodeAttachEnvironmentInjection", "sendToDebugger", "id", "result", "type", "value", "isVscodeNodeTelemetry", "processId", "page", "nodeVersion", "process", "version", "architecture", "arch", "params", "expression", "includes"], "mappings": "AAAA;;;;+BAaa<PERSON>,8BAA4B;;aAA5BA,4BAA4B;;gCAXV,mBAAmB;iCAClB,oBAAoB;AAU7C,MAAMA,4BAA4B,SAASC,eAAc,eAAA;IAC9DC,SAAS,GAAG;QACV,OAAOC,IAAAA,gBAAe,gBAAA,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,CAAC,KAAK,QAAQ,CAAC;IAC/D;IAEAC,qBAAqB,CAACC,OAAyC,EAAE;QAC/D,IAAIA,OAAO,CAACC,MAAM,KAAK,kBAAkB,IAAIC,sCAAsC,CAACF,OAAO,CAAC,EAAE;YAC5F,OAAO,IAAI,CAACG,cAAc,CAAkC;gBAC1DC,EAAE,EAAEJ,OAAO,CAACI,EAAE;gBACdC,MAAM,EAAE;oBACNA,MAAM,EAAE;wBACNC,IAAI,EAAE,QAAQ;wBACdC,KAAK,EAAE,CAAC,gEAAgE,CAAC;qBAC1E;iBACF;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAIP,OAAO,CAACC,MAAM,KAAK,kBAAkB,IAAIO,qBAAqB,CAACR,OAAO,CAAC,EAAE;YAC3E,OAAO,IAAI,CAACG,cAAc,CAAkC;gBAC1DC,EAAE,EAAEJ,OAAO,CAACI,EAAE;gBACdC,MAAM,EAAE;oBACNA,MAAM,EAAE;wBACNC,IAAI,EAAE,QAAQ;wBACdC,KAAK,EAAE;4BACLE,SAAS,EAAE,IAAI,CAACC,IAAI,CAACN,EAAE;4BACvBO,WAAW,EAAEC,OAAO,CAACC,OAAO;4BAC5BC,YAAY,EAAEF,OAAO,CAACG,IAAI;yBAC3B;qBACF;iBACF;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf;CACD;AASD,iJAAiJ,GACjJ,SAASb,sCAAsC,CAACF,OAAyC,EAAE;QAEvFA,GAAc,EACdA,IAAc,EACdA,IAAc;IAHhB,OACEA,CAAAA,CAAAA,GAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,GAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAA,IACnElB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAA,IAC5DlB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAA,CAClE;AACJ,CAAC;AAED,mJAAmJ,GACnJ,SAASV,qBAAqB,CAACR,OAAyC,EAAE;QAEtEA,GAAc,EACdA,IAAc,EACdA,IAAc,EACdA,IAAc,EACdA,IAAc;IALhB,OACEA,CAAAA,CAAAA,GAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,GAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAA,IACrElB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAA,IAC5DlB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAA,IAClDlB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAA,IACtDlB,CAAAA,CAAAA,IAAc,GAAdA,OAAO,CAACgB,MAAM,SAAY,GAA1BhB,KAAAA,CAA0B,GAA1BA,IAAc,CAAEiB,UAAU,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAA,CACnD;AACJ,CAAC"}