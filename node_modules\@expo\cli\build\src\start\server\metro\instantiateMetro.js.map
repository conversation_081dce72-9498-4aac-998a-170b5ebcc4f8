{"version": 3, "sources": ["../../../../../src/start/server/metro/instantiateMetro.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport { getDefaultConfig, LoadOptions } from '@expo/metro-config';\nimport chalk from 'chalk';\nimport http from 'http';\nimport type Metro from 'metro';\nimport { ReadOnlyGraph } from 'metro';\nimport Bundler from 'metro/src/Bundler';\nimport hmrJSBundle from 'metro/src/DeltaBundler/Serializers/hmrJSBundle';\nimport type { TransformOptions } from 'metro/src/DeltaBundler/Worker';\nimport MetroHmrServer from 'metro/src/HmrServer';\nimport RevisionNotFoundError from 'metro/src/IncrementalBundler/RevisionNotFoundError';\nimport formatBundlingError from 'metro/src/lib/formatBundlingError';\nimport { loadConfig, resolveConfig, ConfigT } from 'metro-config';\nimport { Terminal } from 'metro-core';\nimport util from 'node:util';\nimport path from 'path';\n\nimport { createDevToolsPluginWebsocketEndpoint } from './DevToolsPluginWebsocketEndpoint';\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { MetroTerminalReporter } from './MetroTerminalReporter';\nimport { attachAtlasAsync } from './debugging/attachAtlas';\nimport { createDebugMiddleware } from './debugging/createDebugMiddleware';\nimport { createMetroMiddleware } from './dev-server/createMetroMiddleware';\nimport { runServer } from './runServer-fork';\nimport { withMetroMultiPlatformAsync } from './withMetroMultiPlatform';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { createCorsMiddleware } from '../middleware/CorsMiddleware';\nimport { createJsInspectorMiddleware } from '../middleware/inspector/createJsInspectorMiddleware';\nimport { prependMiddleware } from '../middleware/mutations';\nimport { getPlatformBundlers } from '../platformBundlers';\n\n// From expo/dev-server but with ability to use custom logger.\ntype MessageSocket = {\n  broadcast: (method: string, params?: Record<string, any> | undefined) => void;\n};\n\n// Wrap terminal and polyfill console.log so we can log during bundling without breaking the indicator.\nclass LogRespectingTerminal extends Terminal {\n  constructor(stream: import('node:net').Socket | import('node:stream').Writable) {\n    super(stream);\n\n    const sendLog = (...args: any[]) => {\n      this._logLines.push(\n        // format args like console.log\n        util.format(...args)\n      );\n      this._scheduleUpdate();\n\n      // Flush the logs to the terminal immediately so logs at the end of the process are not lost.\n      this.flush();\n    };\n\n    console.log = sendLog;\n    console.info = sendLog;\n  }\n}\n\n// Share one instance of Terminal for all instances of Metro.\nconst terminal = new LogRespectingTerminal(process.stdout);\n\nexport async function loadMetroConfigAsync(\n  projectRoot: string,\n  options: LoadOptions,\n  {\n    exp,\n    isExporting,\n    getMetroBundler,\n  }: { exp: ExpoConfig; isExporting: boolean; getMetroBundler: () => Bundler }\n) {\n  let reportEvent: ((event: any) => void) | undefined;\n\n  const serverActionsEnabled =\n    exp.experiments?.reactServerFunctions ?? env.EXPO_UNSTABLE_SERVER_FUNCTIONS;\n\n  if (serverActionsEnabled) {\n    process.env.EXPO_UNSTABLE_SERVER_FUNCTIONS = '1';\n  }\n\n  // NOTE: Enable all the experimental Metro flags when RSC is enabled.\n  if (exp.experiments?.reactServerComponentRoutes || serverActionsEnabled) {\n    process.env.EXPO_USE_METRO_REQUIRE = '1';\n    process.env.EXPO_USE_FAST_RESOLVER = '1';\n  }\n\n  const serverRoot = getMetroServerRoot(projectRoot);\n  const terminalReporter = new MetroTerminalReporter(serverRoot, terminal);\n\n  const hasConfig = await resolveConfig(options.config, projectRoot);\n  let config: ConfigT = {\n    ...(await loadConfig(\n      { cwd: projectRoot, projectRoot, ...options },\n      // If the project does not have a metro.config.js, then we use the default config.\n      hasConfig.isEmpty ? getDefaultConfig(projectRoot) : undefined\n    )),\n    reporter: {\n      update(event: any) {\n        terminalReporter.update(event);\n        if (reportEvent) {\n          reportEvent(event);\n        }\n      },\n    },\n  };\n\n  // @ts-expect-error: Set the global require cycle ignore patterns for SSR bundles. This won't work with custom global prefixes, but we don't use those.\n  globalThis.__requireCycleIgnorePatterns = config.resolver?.requireCycleIgnorePatterns;\n\n  if (isExporting) {\n    // This token will be used in the asset plugin to ensure the path is correct for writing locally.\n    // @ts-expect-error: typed as readonly.\n    config.transformer.publicPath = `/assets?export_path=${\n      (exp.experiments?.baseUrl ?? '') + '/assets'\n    }`;\n  } else {\n    // @ts-expect-error: typed as readonly\n    config.transformer.publicPath = '/assets/?unstable_path=.';\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  if (exp.experiments?.reactCompiler) {\n    Log.warn(`Experimental React Compiler is enabled.`);\n  }\n\n  if (env.EXPO_UNSTABLE_TREE_SHAKING && !env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH) {\n    throw new CommandError(\n      'EXPO_UNSTABLE_TREE_SHAKING requires EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH to be enabled.'\n    );\n  }\n\n  if (env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH) {\n    Log.warn(`Experimental bundle optimization is enabled.`);\n  }\n  if (env.EXPO_UNSTABLE_TREE_SHAKING) {\n    Log.warn(`Experimental tree shaking is enabled.`);\n  }\n\n  if (serverActionsEnabled) {\n    Log.warn(\n      `Experimental React Server Functions are enabled. Production exports are not supported yet.`\n    );\n    if (!exp.experiments?.reactServerComponentRoutes) {\n      Log.warn(\n        `- React Server Component routes are NOT enabled. Routes will render in client mode.`\n      );\n    }\n  }\n\n  config = await withMetroMultiPlatformAsync(projectRoot, {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled: exp.experiments?.tsconfigPaths ?? true,\n    isFastResolverEnabled: env.EXPO_USE_FAST_RESOLVER,\n    isExporting,\n    isReactCanaryEnabled:\n      (exp.experiments?.reactServerComponentRoutes ||\n        serverActionsEnabled ||\n        exp.experiments?.reactCanary) ??\n      false,\n    isNamedRequiresEnabled: env.EXPO_USE_METRO_REQUIRE,\n    isReactServerComponentsEnabled: !!exp.experiments?.reactServerComponentRoutes,\n    getMetroBundler,\n  });\n\n  return {\n    config,\n    setEventReporter: (logger: (event: any) => void) => (reportEvent = logger),\n    reporter: terminalReporter,\n  };\n}\n\n/** The most generic possible setup for Metro bundler. */\nexport async function instantiateMetroAsync(\n  metroBundler: MetroBundlerDevServer,\n  options: Omit<LoadOptions, 'logger'>,\n  {\n    isExporting,\n    exp = getConfig(metroBundler.projectRoot, {\n      skipSDKVersionRequirement: true,\n    }).exp,\n  }: { isExporting: boolean; exp?: ExpoConfig }\n): Promise<{\n  metro: Metro.Server;\n  hmrServer: MetroHmrServer | null;\n  server: http.Server;\n  middleware: any;\n  messageSocket: MessageSocket;\n}> {\n  const projectRoot = metroBundler.projectRoot;\n\n  const { config: metroConfig, setEventReporter } = await loadMetroConfigAsync(\n    projectRoot,\n    options,\n    {\n      exp,\n      isExporting,\n      getMetroBundler() {\n        return metro.getBundler().getBundler();\n      },\n    }\n  );\n\n  // Create the core middleware stack for Metro, including websocket listeners\n  const { middleware, messagesSocket, eventsSocket, websocketEndpoints } =\n    createMetroMiddleware(metroConfig);\n\n  if (!isExporting) {\n    // Enable correct CORS headers for Expo Router features\n    prependMiddleware(middleware, createCorsMiddleware(exp));\n\n    // Enable debug middleware for CDP-related debugging\n    const { debugMiddleware, debugWebsocketEndpoints } = createDebugMiddleware(metroBundler);\n    Object.assign(websocketEndpoints, debugWebsocketEndpoints);\n    middleware.use(debugMiddleware);\n    middleware.use('/_expo/debugger', createJsInspectorMiddleware());\n\n    // TODO(cedric): `enhanceMiddleware` is deprecated, but is currently used to unify the middleware stacks\n    // See: https://github.com/facebook/metro/commit/22e85fde85ec454792a1b70eba4253747a2587a9\n    // See: https://github.com/facebook/metro/commit/d0d554381f119bb80ab09dbd6a1d310b54737e52\n    const customEnhanceMiddleware = metroConfig.server.enhanceMiddleware;\n    // @ts-expect-error: can't mutate readonly config\n    metroConfig.server.enhanceMiddleware = (metroMiddleware: any, server: Metro.Server) => {\n      if (customEnhanceMiddleware) {\n        metroMiddleware = customEnhanceMiddleware(metroMiddleware, server);\n      }\n      return middleware.use(metroMiddleware);\n    };\n  }\n\n  // Attach Expo Atlas if enabled\n  await attachAtlasAsync({\n    isExporting,\n    exp,\n    projectRoot,\n    middleware,\n    metroConfig,\n    // NOTE(cedric): reset the Atlas file once, and reuse it for static exports\n    resetAtlasFile: isExporting,\n  });\n\n  const { server, hmrServer, metro } = await runServer(\n    metroBundler,\n    metroConfig,\n    {\n      websocketEndpoints: {\n        ...websocketEndpoints,\n        ...createDevToolsPluginWebsocketEndpoint(),\n      },\n      watch: !isExporting && isWatchEnabled(),\n    },\n    {\n      mockServer: isExporting,\n    }\n  );\n\n  // Patch transform file to remove inconvenient customTransformOptions which are only used in single well-known files.\n  const originalTransformFile = metro\n    .getBundler()\n    .getBundler()\n    .transformFile.bind(metro.getBundler().getBundler());\n\n  metro.getBundler().getBundler().transformFile = async function (\n    filePath: string,\n    transformOptions: TransformOptions,\n    fileBuffer?: Buffer\n  ) {\n    return originalTransformFile(\n      filePath,\n      pruneCustomTransformOptions(\n        filePath,\n        // Clone the options so we don't mutate the original.\n        {\n          ...transformOptions,\n          customTransformOptions: {\n            __proto__: null,\n            ...transformOptions.customTransformOptions,\n          },\n        }\n      ),\n      fileBuffer\n    );\n  };\n\n  setEventReporter(eventsSocket.reportMetroEvent);\n\n  // This function ensures that modules in source maps are sorted in the same\n  // order as in a plain JS bundle.\n  metro._getSortedModules = function (this: Metro.Server, graph: ReadOnlyGraph) {\n    const modules = [...graph.dependencies.values()];\n\n    const ctx = {\n      platform: graph.transformOptions.platform,\n      environment: graph.transformOptions.customTransformOptions?.environment,\n    };\n    // Assign IDs to modules in a consistent order\n    for (const module of modules) {\n      // @ts-expect-error\n      this._createModuleId(module.path, ctx);\n    }\n    // Sort by IDs\n    return modules.sort(\n      // @ts-expect-error\n      (a, b) => this._createModuleId(a.path, ctx) - this._createModuleId(b.path, ctx)\n    );\n  };\n\n  if (hmrServer) {\n    // Patch HMR Server to send more info to the `_createModuleId` function for deterministic module IDs.\n    hmrServer._prepareMessage = async function (this: MetroHmrServer, group, options, changeEvent) {\n      // Fork of https://github.com/facebook/metro/blob/3b3e0aaf725cfa6907bf2c8b5fbc0da352d29efe/packages/metro/src/HmrServer.js#L327-L393\n      // with patch for `_createModuleId`.\n      const logger = !options.isInitialUpdate ? changeEvent?.logger : null;\n      try {\n        const revPromise = this._bundler.getRevision(group.revisionId);\n        if (!revPromise) {\n          return {\n            type: 'error',\n            body: formatBundlingError(new RevisionNotFoundError(group.revisionId)),\n          };\n        }\n        logger?.point('updateGraph_start');\n        const { revision, delta } = await this._bundler.updateGraph(await revPromise, false);\n        logger?.point('updateGraph_end');\n        this._clientGroups.delete(group.revisionId);\n        group.revisionId = revision.id;\n        for (const client of group.clients) {\n          client.revisionIds = client.revisionIds.filter(\n            (revisionId) => revisionId !== group.revisionId\n          );\n          client.revisionIds.push(revision.id);\n        }\n        this._clientGroups.set(group.revisionId, group);\n        logger?.point('serialize_start');\n        // NOTE(EvanBacon): This is the patch\n        const moduleIdContext = {\n          platform: revision.graph.transformOptions.platform,\n          environment: revision.graph.transformOptions.customTransformOptions?.environment,\n        };\n        const hmrUpdate = hmrJSBundle(delta, revision.graph, {\n          clientUrl: group.clientUrl,\n          // NOTE(EvanBacon): This is also the patch\n          createModuleId: (moduleId: string) => {\n            // @ts-expect-error\n            return this._createModuleId(moduleId, moduleIdContext);\n          },\n          includeAsyncPaths: group.graphOptions.lazy,\n          projectRoot: this._config.projectRoot,\n          serverRoot: this._config.server.unstable_serverRoot ?? this._config.projectRoot,\n        });\n        logger?.point('serialize_end');\n        return {\n          type: 'update',\n          body: {\n            revisionId: revision.id,\n            isInitialUpdate: options.isInitialUpdate,\n            ...hmrUpdate,\n          },\n        };\n      } catch (error: any) {\n        const formattedError = formatBundlingError(error);\n        this._config.reporter.update({\n          type: 'bundling_error',\n          error,\n        });\n        return {\n          type: 'error',\n          body: formattedError,\n        };\n      }\n    };\n  }\n\n  return {\n    metro,\n    hmrServer,\n    server,\n    middleware,\n    messageSocket: messagesSocket,\n  };\n}\n\n// TODO: Fork the entire transform function so we can simply regex the file contents for keywords instead.\nfunction pruneCustomTransformOptions(\n  filePath: string,\n  transformOptions: TransformOptions\n): TransformOptions {\n  // Normalize the filepath for cross platform checking.\n  filePath = filePath.split(path.sep).join('/');\n\n  if (\n    transformOptions.customTransformOptions?.dom &&\n    // The only generated file that needs the dom root is `expo/dom/entry.js`\n    !filePath.match(/expo\\/dom\\/entry\\.js$/)\n  ) {\n    // Clear the dom root option if we aren't transforming the magic entry file, this ensures\n    // that cached artifacts from other DOM component bundles can be reused.\n    transformOptions.customTransformOptions.dom = 'true';\n  }\n\n  if (\n    transformOptions.customTransformOptions?.routerRoot &&\n    // The router root is used all over expo-router (`process.env.EXPO_ROUTER_ABS_APP_ROOT`, `process.env.EXPO_ROUTER_APP_ROOT`) so we'll just ignore the entire package.\n    !(filePath.match(/\\/expo-router\\/_ctx/) || filePath.match(/\\/expo-router\\/build\\//))\n  ) {\n    // Set to the default value.\n    transformOptions.customTransformOptions.routerRoot = 'app';\n  }\n  if (\n    transformOptions.customTransformOptions?.asyncRoutes &&\n    // The async routes settings are also used in `expo-router/_ctx.ios.js` (and other platform variants) via `process.env.EXPO_ROUTER_IMPORT_MODE`\n    !(filePath.match(/\\/expo-router\\/_ctx/) || filePath.match(/\\/expo-router\\/build\\//))\n  ) {\n    delete transformOptions.customTransformOptions.asyncRoutes;\n  }\n\n  if (\n    transformOptions.customTransformOptions?.clientBoundaries &&\n    // The client boundaries are only used in `@expo/metro-runtime/src/virtual.js` for production RSC exports.\n    !filePath.match(/\\/@expo\\/metro-runtime\\/rsc\\/virtual\\.js$/)\n  ) {\n    delete transformOptions.customTransformOptions.clientBoundaries;\n  }\n\n  return transformOptions;\n}\n\n/**\n * Simplify and communicate if Metro is running without watching file updates,.\n * Exposed for testing.\n */\nexport function isWatchEnabled() {\n  if (env.CI) {\n    Log.log(\n      chalk`Metro is running in CI mode, reloads are disabled. Remove {bold CI=true} to enable watch mode.`\n    );\n  }\n\n  return !env.CI;\n}\n"], "names": ["loadMetroConfigAsync", "instantiateMetroAsync", "isWatchEnabled", "LogRespectingTerminal", "Terminal", "constructor", "stream", "sendLog", "args", "_logLines", "push", "util", "format", "_scheduleUpdate", "flush", "console", "log", "info", "terminal", "process", "stdout", "projectRoot", "options", "exp", "isExporting", "getMetroBundler", "config", "reportEvent", "serverActionsEnabled", "experiments", "reactServerFunctions", "env", "EXPO_UNSTABLE_SERVER_FUNCTIONS", "reactServerComponentRoutes", "EXPO_USE_METRO_REQUIRE", "EXPO_USE_FAST_RESOLVER", "serverRoot", "getMetroServerRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MetroTerminalReporter", "hasConfig", "resolveConfig", "loadConfig", "cwd", "isEmpty", "getDefaultConfig", "undefined", "reporter", "update", "event", "globalThis", "__requireCycleIgnorePatterns", "resolver", "requireCycleIgnorePatterns", "transformer", "publicPath", "baseUrl", "platformBundlers", "getPlatformBundlers", "reactCompiler", "Log", "warn", "EXPO_UNSTABLE_TREE_SHAKING", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "CommandError", "withMetroMultiPlatformAsync", "isTsconfigPathsEnabled", "tsconfigPaths", "isFastResolverEnabled", "isReactCanaryEnabled", "reactCanary", "isNamedRequiresEnabled", "isReactServerComponentsEnabled", "setEventReporter", "logger", "metroBundler", "getConfig", "skipSDKVersionRequirement", "metroConfig", "metro", "getBundler", "middleware", "messagesSocket", "eventsSocket", "websocketEndpoints", "createMetroMiddleware", "prependMiddleware", "createCorsMiddleware", "debugMiddleware", "debugWebsocketEndpoints", "createDebugMiddleware", "Object", "assign", "use", "createJsInspectorMiddleware", "customEnhanceMiddleware", "server", "enhanceMiddleware", "metroMiddleware", "attachAtlasAsync", "resetAtlasFile", "hmrServer", "runServer", "createDevToolsPluginWebsocketEndpoint", "watch", "mockServer", "originalTransformFile", "transformFile", "bind", "filePath", "transformOptions", "fileBuffer", "pruneCustomTransformOptions", "customTransformOptions", "__proto__", "reportMetroEvent", "_getSortedModules", "graph", "modules", "dependencies", "values", "ctx", "platform", "environment", "module", "_createModuleId", "path", "sort", "a", "b", "_prepareMessage", "group", "changeEvent", "isInitialUpdate", "revision", "revPromise", "_bundler", "getRevision", "revisionId", "type", "body", "formatBundlingError", "RevisionNotFoundError", "point", "delta", "updateGraph", "_clientGroups", "delete", "id", "client", "clients", "revisionIds", "filter", "set", "moduleIdContext", "hmrUpdate", "hmrJSBundle", "clientUrl", "createModuleId", "moduleId", "includeAsyncPaths", "graphOptions", "lazy", "_config", "unstable_serverRoot", "error", "formattedError", "messageSocket", "split", "sep", "join", "dom", "match", "routerRoot", "asyncRoutes", "clientBoundaries", "CI", "chalk"], "mappings": "AAAA;;;;;;;;;;;IA+DsBA,oBAAoB,MAApBA,oBAAoB;IAiHpBC,qBAAqB,MAArBA,qBAAqB;IAkQ3BC,cAAc,MAAdA,cAAc;;;yBAlbQ,cAAc;;;;;;;yBACjB,oBAAoB;;;;;;;yBACT,oBAAoB;;;;;;;8DAChD,OAAO;;;;;;;8DAKD,gDAAgD;;;;;;;8DAGtC,oDAAoD;;;;;;;8DACtD,mCAAmC;;;;;;;yBAChB,cAAc;;;;;;;yBACxC,YAAY;;;;;;;8DACpB,WAAW;;;;;;;8DACX,MAAM;;;;;;iDAE+B,mCAAmC;uCAEnD,yBAAyB;6BAC9B,yBAAyB;uCACpB,mCAAmC;uCACnC,oCAAoC;+BAChD,kBAAkB;wCACA,0BAA0B;qBAClD,cAAc;qBACd,oBAAoB;wBACX,uBAAuB;gCACf,8BAA8B;6CACvB,qDAAqD;2BAC/D,yBAAyB;kCACvB,qBAAqB;;;;;;AAOzD,uGAAuG;AACvG,MAAMC,qBAAqB,SAASC,UAAQ,EAAA,SAAA;IAC1CC,YAAYC,MAAkE,CAAE;QAC9E,KAAK,CAACA,MAAM,CAAC,CAAC;QAEd,MAAMC,OAAO,GAAG,CAAC,GAAGC,IAAI,AAAO,GAAK;YAClC,IAAI,CAACC,SAAS,CAACC,IAAI,CACjB,+BAA+B;YAC/BC,SAAI,EAAA,QAAA,CAACC,MAAM,IAAIJ,IAAI,CAAC,CACrB,CAAC;YACF,IAAI,CAACK,eAAe,EAAE,CAAC;YAEvB,6FAA6F;YAC7F,IAAI,CAACC,KAAK,EAAE,CAAC;QACf,CAAC,AAAC;QAEFC,OAAO,CAACC,GAAG,GAAGT,OAAO,CAAC;QACtBQ,OAAO,CAACE,IAAI,GAAGV,OAAO,CAAC;IACzB;CACD;AAED,6DAA6D;AAC7D,MAAMW,QAAQ,GAAG,IAAIf,qBAAqB,CAACgB,OAAO,CAACC,MAAM,CAAC,AAAC;AAEpD,eAAepB,oBAAoB,CACxCqB,WAAmB,EACnBC,OAAoB,EACpB,EACEC,GAAG,CAAA,EACHC,WAAW,CAAA,EACXC,eAAe,CAAA,EAC2D,EAC5E;QAIEF,GAAe,EAObA,IAAe,EA0BuBG,IAAe,EAerDH,IAAe,EAgCOA,IAAe,EAIpCA,IAAe,EAEdA,IAAe,EAGeA,IAAe;IA5FnD,IAAII,WAAW,AAAoC,AAAC;IAEpD,MAAMC,oBAAoB,GACxBL,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACM,WAAW,SAAsB,GAArCN,KAAAA,CAAqC,GAArCA,GAAe,CAAEO,oBAAoB,CAAA,IAAIC,IAAG,IAAA,CAACC,8BAA8B,AAAC;IAE9E,IAAIJ,oBAAoB,EAAE;QACxBT,OAAO,CAACY,GAAG,CAACC,8BAA8B,GAAG,GAAG,CAAC;IACnD,CAAC;IAED,qEAAqE;IACrE,IAAIT,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAA4B,GAA3CN,KAAAA,CAA2C,GAA3CA,IAAe,CAAEU,0BAA0B,CAAA,IAAIL,oBAAoB,EAAE;QACvET,OAAO,CAACY,GAAG,CAACG,sBAAsB,GAAG,GAAG,CAAC;QACzCf,OAAO,CAACY,GAAG,CAACI,sBAAsB,GAAG,GAAG,CAAC;IAC3C,CAAC;IAED,MAAMC,UAAU,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAAChB,WAAW,CAAC,AAAC;IACnD,MAAMiB,gBAAgB,GAAG,IAAIC,sBAAqB,sBAAA,CAACH,UAAU,EAAElB,QAAQ,CAAC,AAAC;IAEzE,MAAMsB,SAAS,GAAG,MAAMC,IAAAA,aAAa,EAAA,cAAA,EAACnB,OAAO,CAACI,MAAM,EAAEL,WAAW,CAAC,AAAC;IACnE,IAAIK,MAAM,GAAY;QACpB,GAAI,MAAMgB,IAAAA,aAAU,EAAA,WAAA,EAClB;YAAEC,GAAG,EAAEtB,WAAW;YAAEA,WAAW;YAAE,GAAGC,OAAO;SAAE,EAC7C,kFAAkF;QAClFkB,SAAS,CAACI,OAAO,GAAGC,IAAAA,YAAgB,EAAA,iBAAA,EAACxB,WAAW,CAAC,GAAGyB,SAAS,CAC9D;QACDC,QAAQ,EAAE;YACRC,MAAM,EAACC,KAAU,EAAE;gBACjBX,gBAAgB,CAACU,MAAM,CAACC,KAAK,CAAC,CAAC;gBAC/B,IAAItB,WAAW,EAAE;oBACfA,WAAW,CAACsB,KAAK,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC;SACF;KACF,AAAC;IAEF,uJAAuJ;IACvJC,UAAU,CAACC,4BAA4B,GAAGzB,CAAAA,IAAe,GAAfA,MAAM,CAAC0B,QAAQ,SAA4B,GAA3C1B,KAAAA,CAA2C,GAA3CA,IAAe,CAAE2B,0BAA0B,CAAC;IAEtF,IAAI7B,WAAW,EAAE;YAIZD,IAAe;QAHlB,iGAAiG;QACjG,uCAAuC;QACvCG,MAAM,CAAC4B,WAAW,CAACC,UAAU,GAAG,CAAC,oBAAoB,EACnD,CAAChC,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAAS,GAAxBN,KAAAA,CAAwB,GAAxBA,IAAe,CAAEiC,OAAO,CAAA,IAAI,EAAE,CAAC,GAAG,SAAS,CAC7C,CAAC,CAAC;IACL,OAAO;QACL,sCAAsC;QACtC9B,MAAM,CAAC4B,WAAW,CAACC,UAAU,GAAG,0BAA0B,CAAC;IAC7D,CAAC;IAED,MAAME,gBAAgB,GAAGC,IAAAA,iBAAmB,oBAAA,EAACrC,WAAW,EAAEE,GAAG,CAAC,AAAC;IAE/D,IAAIA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAAe,GAA9BN,KAAAA,CAA8B,GAA9BA,IAAe,CAAEoC,aAAa,EAAE;QAClCC,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,IAAI9B,IAAG,IAAA,CAAC+B,0BAA0B,IAAI,CAAC/B,IAAG,IAAA,CAACgC,kCAAkC,EAAE;QAC7E,MAAM,IAAIC,OAAY,aAAA,CACpB,uFAAuF,CACxF,CAAC;IACJ,CAAC;IAED,IAAIjC,IAAG,IAAA,CAACgC,kCAAkC,EAAE;QAC1CH,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI9B,IAAG,IAAA,CAAC+B,0BAA0B,EAAE;QAClCF,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,IAAIjC,oBAAoB,EAAE;YAInBL,IAAe;QAHpBqC,IAAG,IAAA,CAACC,IAAI,CACN,CAAC,0FAA0F,CAAC,CAC7F,CAAC;QACF,IAAI,CAACtC,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAA4B,GAA3CN,KAAAA,CAA2C,GAA3CA,IAAe,CAAEU,0BAA0B,CAAA,EAAE;YAChD2B,IAAG,IAAA,CAACC,IAAI,CACN,CAAC,mFAAmF,CAAC,CACtF,CAAC;QACJ,CAAC;IACH,CAAC;IAEDnC,MAAM,GAAG,MAAMuC,IAAAA,uBAA2B,4BAAA,EAAC5C,WAAW,EAAE;QACtDK,MAAM;QACNH,GAAG;QACHkC,gBAAgB;QAChBS,sBAAsB,EAAE3C,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAAe,GAA9BN,KAAAA,CAA8B,GAA9BA,IAAe,CAAE4C,aAAa,CAAA,IAAI,IAAI;QAC9DC,qBAAqB,EAAErC,IAAG,IAAA,CAACI,sBAAsB;QACjDX,WAAW;QACX6C,oBAAoB,EAClB,CAAC9C,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAA4B,GAA3CN,KAAAA,CAA2C,GAA3CA,IAAe,CAAEU,0BAA0B,CAAA,IAC1CL,oBAAoB,IACpBL,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAAa,GAA5BN,KAAAA,CAA4B,GAA5BA,IAAe,CAAE+C,WAAW,CAAA,CAAC,IAC/B,KAAK;QACPC,sBAAsB,EAAExC,IAAG,IAAA,CAACG,sBAAsB;QAClDsC,8BAA8B,EAAE,CAAC,CAACjD,CAAAA,CAAAA,IAAe,GAAfA,GAAG,CAACM,WAAW,SAA4B,GAA3CN,KAAAA,CAA2C,GAA3CA,IAAe,CAAEU,0BAA0B,CAAA;QAC7ER,eAAe;KAChB,CAAC,CAAC;IAEH,OAAO;QACLC,MAAM;QACN+C,gBAAgB,EAAE,CAACC,MAA4B,GAAM/C,WAAW,GAAG+C,MAAM,AAAC;QAC1E3B,QAAQ,EAAET,gBAAgB;KAC3B,CAAC;AACJ,CAAC;AAGM,eAAerC,qBAAqB,CACzC0E,YAAmC,EACnCrD,OAAoC,EACpC,EACEE,WAAW,CAAA,EACXD,GAAG,EAAGqD,IAAAA,OAAS,EAAA,UAAA,EAACD,YAAY,CAACtD,WAAW,EAAE;IACxCwD,yBAAyB,EAAE,IAAI;CAChC,CAAC,CAACtD,GAAG,CAAA,EACqC,EAO5C;IACD,MAAMF,WAAW,GAAGsD,YAAY,CAACtD,WAAW,AAAC;IAE7C,MAAM,EAAEK,MAAM,EAAEoD,WAAW,CAAA,EAAEL,gBAAgB,CAAA,EAAE,GAAG,MAAMzE,oBAAoB,CAC1EqB,WAAW,EACXC,OAAO,EACP;QACEC,GAAG;QACHC,WAAW;QACXC,eAAe,IAAG;YAChB,OAAOsD,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAAC;QACzC,CAAC;KACF,CACF,AAAC;IAEF,4EAA4E;IAC5E,MAAM,EAAEC,UAAU,CAAA,EAAEC,cAAc,CAAA,EAAEC,YAAY,CAAA,EAAEC,kBAAkB,CAAA,EAAE,GACpEC,IAAAA,sBAAqB,sBAAA,EAACP,WAAW,CAAC,AAAC;IAErC,IAAI,CAACtD,WAAW,EAAE;QAChB,uDAAuD;QACvD8D,IAAAA,UAAiB,kBAAA,EAACL,UAAU,EAAEM,IAAAA,eAAoB,qBAAA,EAAChE,GAAG,CAAC,CAAC,CAAC;QAEzD,oDAAoD;QACpD,MAAM,EAAEiE,eAAe,CAAA,EAAEC,uBAAuB,CAAA,EAAE,GAAGC,IAAAA,sBAAqB,sBAAA,EAACf,YAAY,CAAC,AAAC;QACzFgB,MAAM,CAACC,MAAM,CAACR,kBAAkB,EAAEK,uBAAuB,CAAC,CAAC;QAC3DR,UAAU,CAACY,GAAG,CAACL,eAAe,CAAC,CAAC;QAChCP,UAAU,CAACY,GAAG,CAAC,iBAAiB,EAAEC,IAAAA,4BAA2B,4BAAA,GAAE,CAAC,CAAC;QAEjE,wGAAwG;QACxG,yFAAyF;QACzF,yFAAyF;QACzF,MAAMC,uBAAuB,GAAGjB,WAAW,CAACkB,MAAM,CAACC,iBAAiB,AAAC;QACrE,iDAAiD;QACjDnB,WAAW,CAACkB,MAAM,CAACC,iBAAiB,GAAG,CAACC,eAAoB,EAAEF,MAAoB,GAAK;YACrF,IAAID,uBAAuB,EAAE;gBAC3BG,eAAe,GAAGH,uBAAuB,CAACG,eAAe,EAAEF,MAAM,CAAC,CAAC;YACrE,CAAC;YACD,OAAOf,UAAU,CAACY,GAAG,CAACK,eAAe,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAED,+BAA+B;IAC/B,MAAMC,IAAAA,YAAgB,iBAAA,EAAC;QACrB3E,WAAW;QACXD,GAAG;QACHF,WAAW;QACX4D,UAAU;QACVH,WAAW;QACX,2EAA2E;QAC3EsB,cAAc,EAAE5E,WAAW;KAC5B,CAAC,CAAC;IAEH,MAAM,EAAEwE,MAAM,CAAA,EAAEK,SAAS,CAAA,EAAEtB,KAAK,CAAA,EAAE,GAAG,MAAMuB,IAAAA,cAAS,UAAA,EAClD3B,YAAY,EACZG,WAAW,EACX;QACEM,kBAAkB,EAAE;YAClB,GAAGA,kBAAkB;YACrB,GAAGmB,IAAAA,gCAAqC,sCAAA,GAAE;SAC3C;QACDC,KAAK,EAAE,CAAChF,WAAW,IAAItB,cAAc,EAAE;KACxC,EACD;QACEuG,UAAU,EAAEjF,WAAW;KACxB,CACF,AAAC;IAEF,qHAAqH;IACrH,MAAMkF,qBAAqB,GAAG3B,KAAK,CAChCC,UAAU,EAAE,CACZA,UAAU,EAAE,CACZ2B,aAAa,CAACC,IAAI,CAAC7B,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAAC,AAAC;IAEvDD,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAAC2B,aAAa,GAAG,eAC9CE,QAAgB,EAChBC,gBAAkC,EAClCC,UAAmB,EACnB;QACA,OAAOL,qBAAqB,CAC1BG,QAAQ,EACRG,2BAA2B,CACzBH,QAAQ,EACR,qDAAqD;QACrD;YACE,GAAGC,gBAAgB;YACnBG,sBAAsB,EAAE;gBACtBC,SAAS,EAAE,IAAI;gBACf,GAAGJ,gBAAgB,CAACG,sBAAsB;aAC3C;SACF,CACF,EACDF,UAAU,CACX,CAAC;IACJ,CAAC,CAAC;IAEFtC,gBAAgB,CAACU,YAAY,CAACgC,gBAAgB,CAAC,CAAC;IAEhD,2EAA2E;IAC3E,iCAAiC;IACjCpC,KAAK,CAACqC,iBAAiB,GAAG,SAA8BC,KAAoB,EAAE;YAK7DA,GAA6C;QAJ5D,MAAMC,OAAO,GAAG;eAAID,KAAK,CAACE,YAAY,CAACC,MAAM,EAAE;SAAC,AAAC;QAEjD,MAAMC,GAAG,GAAG;YACVC,QAAQ,EAAEL,KAAK,CAACP,gBAAgB,CAACY,QAAQ;YACzCC,WAAW,EAAEN,CAAAA,GAA6C,GAA7CA,KAAK,CAACP,gBAAgB,CAACG,sBAAsB,SAAa,GAA1DI,KAAAA,CAA0D,GAA1DA,GAA6C,CAAEM,WAAW;SACxE,AAAC;QACF,8CAA8C;QAC9C,KAAK,MAAMC,MAAM,IAAIN,OAAO,CAAE;YAC5B,mBAAmB;YACnB,IAAI,CAACO,eAAe,CAACD,MAAM,CAACE,IAAI,EAAEL,GAAG,CAAC,CAAC;QACzC,CAAC;QACD,cAAc;QACd,OAAOH,OAAO,CAACS,IAAI,CACjB,mBAAmB;QACnB,CAACC,CAAC,EAAEC,CAAC,GAAK,IAAI,CAACJ,eAAe,CAACG,CAAC,CAACF,IAAI,EAAEL,GAAG,CAAC,GAAG,IAAI,CAACI,eAAe,CAACI,CAAC,CAACH,IAAI,EAAEL,GAAG,CAAC,CAChF,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIpB,SAAS,EAAE;QACb,qGAAqG;QACrGA,SAAS,CAAC6B,eAAe,GAAG,eAAsCC,KAAK,EAAE7G,OAAO,EAAE8G,WAAW,EAAE;YAC7F,oIAAoI;YACpI,oCAAoC;YACpC,MAAM1D,MAAM,GAAG,CAACpD,OAAO,CAAC+G,eAAe,GAAGD,WAAW,QAAQ,GAAnBA,KAAAA,CAAmB,GAAnBA,WAAW,CAAE1D,MAAM,GAAG,IAAI,AAAC;YACrE,IAAI;oBAwBa4D,GAAsD;gBAvBrE,MAAMC,UAAU,GAAG,IAAI,CAACC,QAAQ,CAACC,WAAW,CAACN,KAAK,CAACO,UAAU,CAAC,AAAC;gBAC/D,IAAI,CAACH,UAAU,EAAE;oBACf,OAAO;wBACLI,IAAI,EAAE,OAAO;wBACbC,IAAI,EAAEC,IAAAA,oBAAmB,EAAA,QAAA,EAAC,IAAIC,CAAAA,sBAAqB,EAAA,CAAA,QAAA,CAACX,KAAK,CAACO,UAAU,CAAC,CAAC;qBACvE,CAAC;gBACJ,CAAC;gBACDhE,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEqE,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACnC,MAAM,EAAET,QAAQ,CAAA,EAAEU,KAAK,CAAA,EAAE,GAAG,MAAM,IAAI,CAACR,QAAQ,CAACS,WAAW,CAAC,MAAMV,UAAU,EAAE,KAAK,CAAC,AAAC;gBACrF7D,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEqE,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBACjC,IAAI,CAACG,aAAa,CAACC,MAAM,CAAChB,KAAK,CAACO,UAAU,CAAC,CAAC;gBAC5CP,KAAK,CAACO,UAAU,GAAGJ,QAAQ,CAACc,EAAE,CAAC;gBAC/B,KAAK,MAAMC,MAAM,IAAIlB,KAAK,CAACmB,OAAO,CAAE;oBAClCD,MAAM,CAACE,WAAW,GAAGF,MAAM,CAACE,WAAW,CAACC,MAAM,CAC5C,CAACd,UAAU,GAAKA,UAAU,KAAKP,KAAK,CAACO,UAAU,CAChD,CAAC;oBACFW,MAAM,CAACE,WAAW,CAAC7I,IAAI,CAAC4H,QAAQ,CAACc,EAAE,CAAC,CAAC;gBACvC,CAAC;gBACD,IAAI,CAACF,aAAa,CAACO,GAAG,CAACtB,KAAK,CAACO,UAAU,EAAEP,KAAK,CAAC,CAAC;gBAChDzD,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEqE,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBACjC,qCAAqC;gBACrC,MAAMW,eAAe,GAAG;oBACtBhC,QAAQ,EAAEY,QAAQ,CAACjB,KAAK,CAACP,gBAAgB,CAACY,QAAQ;oBAClDC,WAAW,EAAEW,CAAAA,GAAsD,GAAtDA,QAAQ,CAACjB,KAAK,CAACP,gBAAgB,CAACG,sBAAsB,SAAa,GAAnEqB,KAAAA,CAAmE,GAAnEA,GAAsD,CAAEX,WAAW;iBACjF,AAAC;gBACF,MAAMgC,SAAS,GAAGC,IAAAA,YAAW,EAAA,QAAA,EAACZ,KAAK,EAAEV,QAAQ,CAACjB,KAAK,EAAE;oBACnDwC,SAAS,EAAE1B,KAAK,CAAC0B,SAAS;oBAC1B,0CAA0C;oBAC1CC,cAAc,EAAE,CAACC,QAAgB,GAAK;wBACpC,mBAAmB;wBACnB,OAAO,IAAI,CAAClC,eAAe,CAACkC,QAAQ,EAAEL,eAAe,CAAC,CAAC;oBACzD,CAAC;oBACDM,iBAAiB,EAAE7B,KAAK,CAAC8B,YAAY,CAACC,IAAI;oBAC1C7I,WAAW,EAAE,IAAI,CAAC8I,OAAO,CAAC9I,WAAW;oBACrCe,UAAU,EAAE,IAAI,CAAC+H,OAAO,CAACnE,MAAM,CAACoE,mBAAmB,IAAI,IAAI,CAACD,OAAO,CAAC9I,WAAW;iBAChF,CAAC,AAAC;gBACHqD,MAAM,QAAO,GAAbA,KAAAA,CAAa,GAAbA,MAAM,CAAEqE,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC/B,OAAO;oBACLJ,IAAI,EAAE,QAAQ;oBACdC,IAAI,EAAE;wBACJF,UAAU,EAAEJ,QAAQ,CAACc,EAAE;wBACvBf,eAAe,EAAE/G,OAAO,CAAC+G,eAAe;wBACxC,GAAGsB,SAAS;qBACb;iBACF,CAAC;YACJ,EAAE,OAAOU,KAAK,EAAO;gBACnB,MAAMC,cAAc,GAAGzB,IAAAA,oBAAmB,EAAA,QAAA,EAACwB,KAAK,CAAC,AAAC;gBAClD,IAAI,CAACF,OAAO,CAACpH,QAAQ,CAACC,MAAM,CAAC;oBAC3B2F,IAAI,EAAE,gBAAgB;oBACtB0B,KAAK;iBACN,CAAC,CAAC;gBACH,OAAO;oBACL1B,IAAI,EAAE,OAAO;oBACbC,IAAI,EAAE0B,cAAc;iBACrB,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,OAAO;QACLvF,KAAK;QACLsB,SAAS;QACTL,MAAM;QACNf,UAAU;QACVsF,aAAa,EAAErF,cAAc;KAC9B,CAAC;AACJ,CAAC;AAED,0GAA0G;AAC1G,SAAS8B,2BAA2B,CAClCH,QAAgB,EAChBC,gBAAkC,EAChB;QAKhBA,GAAuC,EAUvCA,IAAuC,EAQvCA,IAAuC,EAQvCA,IAAuC;IA9BzC,sDAAsD;IACtDD,QAAQ,GAAGA,QAAQ,CAAC2D,KAAK,CAAC1C,KAAI,EAAA,QAAA,CAAC2C,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE9C,IACE5D,CAAAA,CAAAA,GAAuC,GAAvCA,gBAAgB,CAACG,sBAAsB,SAAK,GAA5CH,KAAAA,CAA4C,GAA5CA,GAAuC,CAAE6D,GAAG,CAAA,IAC5C,yEAAyE;IACzE,CAAC9D,QAAQ,CAAC+D,KAAK,yBAAyB,EACxC;QACA,yFAAyF;QACzF,wEAAwE;QACxE9D,gBAAgB,CAACG,sBAAsB,CAAC0D,GAAG,GAAG,MAAM,CAAC;IACvD,CAAC;IAED,IACE7D,CAAAA,CAAAA,IAAuC,GAAvCA,gBAAgB,CAACG,sBAAsB,SAAY,GAAnDH,KAAAA,CAAmD,GAAnDA,IAAuC,CAAE+D,UAAU,CAAA,IACnD,qKAAqK;IACrK,CAAC,CAAChE,QAAQ,CAAC+D,KAAK,uBAAuB,IAAI/D,QAAQ,CAAC+D,KAAK,0BAA0B,CAAC,EACpF;QACA,4BAA4B;QAC5B9D,gBAAgB,CAACG,sBAAsB,CAAC4D,UAAU,GAAG,KAAK,CAAC;IAC7D,CAAC;IACD,IACE/D,CAAAA,CAAAA,IAAuC,GAAvCA,gBAAgB,CAACG,sBAAsB,SAAa,GAApDH,KAAAA,CAAoD,GAApDA,IAAuC,CAAEgE,WAAW,CAAA,IACpD,+IAA+I;IAC/I,CAAC,CAACjE,QAAQ,CAAC+D,KAAK,uBAAuB,IAAI/D,QAAQ,CAAC+D,KAAK,0BAA0B,CAAC,EACpF;QACA,OAAO9D,gBAAgB,CAACG,sBAAsB,CAAC6D,WAAW,CAAC;IAC7D,CAAC;IAED,IACEhE,CAAAA,CAAAA,IAAuC,GAAvCA,gBAAgB,CAACG,sBAAsB,SAAkB,GAAzDH,KAAAA,CAAyD,GAAzDA,IAAuC,CAAEiE,gBAAgB,CAAA,IACzD,0GAA0G;IAC1G,CAAClE,QAAQ,CAAC+D,KAAK,6CAA6C,EAC5D;QACA,OAAO9D,gBAAgB,CAACG,sBAAsB,CAAC8D,gBAAgB,CAAC;IAClE,CAAC;IAED,OAAOjE,gBAAgB,CAAC;AAC1B,CAAC;AAMM,SAAS5G,cAAc,GAAG;IAC/B,IAAI6B,IAAG,IAAA,CAACiJ,EAAE,EAAE;QACVpH,IAAG,IAAA,CAAC5C,GAAG,CACLiK,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,8FAA8F,CAAC,CACtG,CAAC;IACJ,CAAC;IAED,OAAO,CAAClJ,IAAG,IAAA,CAACiJ,EAAE,CAAC;AACjB,CAAC"}