{"name": "loscc-app", "version": "0.1.0", "private": true, "scripts": {"web": "webpack serve --config webpack.config.js", "build-web": "webpack --config webpack.config.js --mode production", "start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "axios": "^1.4.0", "expo": "^53.0.19", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.45.4", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.0.0", "react-native-web": "^0.20.0", "zod": "^3.22.2", "zustand": "^4.4.1"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@expo/metro-config": "^0.20.17", "@react-native/metro-config": "^0.80.1", "@react-native/typescript-config": "^0.80.1", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "babel-loader": "^10.0.0", "babel-preset-expo": "^13.2.3", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "metro-react-native-babel-preset": "^0.77.0", "typescript": "^5.8.3", "webpack": "^5.100.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}