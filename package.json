{"name": "loscc-app", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "^1.19.3", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "axios": "^1.4.0", "react": "18.2.0", "react-hook-form": "^7.45.4", "react-native": "0.72.4", "react-native-gesture-handler": "^2.12.1", "react-native-maps": "^1.7.1", "react-native-safe-area-context": "^4.7.1", "react-native-screens": "^3.24.0", "react-native-vector-icons": "^10.0.0", "zod": "^3.22.2", "zustand": "^4.4.1"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.27.1", "@react-native/metro-config": "^0.80.1", "@react-native/typescript-config": "^0.80.1", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "typescript": "^5.8.3"}}