{"version": 3, "sources": ["../../../../../src/start/server/metro/router.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport { sync as globSync } from 'glob';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { Log } from '../../../log';\nimport { directoryExistsSync } from '../../../utils/dir';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:server:metro:router') as typeof console.log;\n\n/**\n * Get the relative path for requiring the `/app` folder relative to the `expo-router/entry` file.\n * This mechanism does require the server to restart after the `expo-router` package is installed.\n */\nexport function getAppRouterRelativeEntryPath(\n  projectRoot: string,\n  routerDirectory: string = getRouterDirectory(projectRoot)\n): string | undefined {\n  // Auto pick App entry\n  const routerEntry =\n    resolveFrom.silent(projectRoot, 'expo-router/entry') ?? getFallbackEntryRoot(projectRoot);\n  if (!routerEntry) {\n    return undefined;\n  }\n  // It doesn't matter if the app folder exists.\n  const appFolder = path.join(projectRoot, routerDirectory);\n  const appRoot = path.relative(path.dirname(routerEntry), appFolder);\n  debug('expo-router entry', routerEntry, appFolder, appRoot);\n  return appRoot;\n}\n\n/** If the `expo-router` package is not installed, then use the `expo` package to determine where the node modules are relative to the project. */\nfunction getFallbackEntryRoot(projectRoot: string): string {\n  const expoRoot = resolveFrom.silent(projectRoot, 'expo/package.json');\n  if (expoRoot) {\n    return path.join(path.dirname(path.dirname(expoRoot)), 'expo-router/entry');\n  }\n  return path.join(projectRoot, 'node_modules/expo-router/entry');\n}\n\nexport function getRouterDirectoryModuleIdWithManifest(\n  projectRoot: string,\n  exp: ExpoConfig\n): string {\n  return toPosixPath(exp.extra?.router?.root ?? getRouterDirectory(projectRoot));\n}\n\nlet hasWarnedAboutSrcDir = false;\nconst logSrcDir = () => {\n  if (hasWarnedAboutSrcDir) return;\n  hasWarnedAboutSrcDir = true;\n  Log.log(chalk.gray('Using src/app as the root directory for Expo Router.'));\n};\n\nexport function getRouterDirectory(projectRoot: string): string {\n  // more specific directories first\n  if (directoryExistsSync(path.join(projectRoot, 'src', 'app'))) {\n    logSrcDir();\n    return path.join('src', 'app');\n  }\n\n  debug('Using app as the root directory for Expo Router.');\n  return 'app';\n}\n\nexport function isApiRouteConvention(name: string): boolean {\n  return /\\+api\\.[tj]sx?$/.test(name);\n}\n\nexport function getApiRoutesForDirectory(cwd: string) {\n  return globSync('**/*+api.@(ts|tsx|js|jsx)', {\n    cwd,\n    absolute: true,\n    dot: true,\n  });\n}\n\n// Used to emulate a context module, but way faster. TODO: May need to adjust the extensions to stay in sync with Metro.\nexport function getRoutePaths(cwd: string) {\n  return globSync('**/*.@(ts|tsx|js|jsx)', {\n    cwd,\n    dot: true,\n  }).map((p) => './' + normalizePaths(p));\n}\n\nfunction normalizePaths(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nlet hasWarnedAboutApiRouteOutput = false;\n\nexport function hasWarnedAboutApiRoutes() {\n  return hasWarnedAboutApiRouteOutput;\n}\n\nexport function warnInvalidWebOutput() {\n  if (!hasWarnedAboutApiRouteOutput) {\n    Log.warn(\n      chalk.yellow`Using API routes requires the {bold web.output} to be set to {bold \"server\"} in the project {bold app.json}. ${learnMore(\n        'https://docs.expo.dev/router/reference/api-routes/'\n      )}`\n    );\n  }\n\n  hasWarnedAboutApiRouteOutput = true;\n}\n"], "names": ["getAppRouterRelativeEntryPath", "getRouterDirectoryModuleIdWithManifest", "getRouterDirectory", "isApiRouteConvention", "getApiRoutesForDirectory", "getRoutePaths", "hasWarnedAboutApiRoutes", "warnInvalidWebOutput", "debug", "require", "projectRoot", "routerDirectory", "routerEntry", "resolveFrom", "silent", "getFallbackEntryRoot", "undefined", "appFolder", "path", "join", "appRoot", "relative", "dirname", "expoRoot", "exp", "toPosixPath", "extra", "router", "root", "hasWarnedAboutSrcDir", "logSrcDir", "Log", "log", "chalk", "gray", "directoryExistsSync", "name", "test", "cwd", "globSync", "absolute", "dot", "map", "p", "normalizePaths", "replace", "hasWarnedAboutApiRouteOutput", "warn", "yellow", "learnMore"], "mappings": "AAAA;;;;;;;;;;;IAiBgBA,6BAA6B,MAA7BA,6BAA6B;IA0B7BC,sCAAsC,MAAtCA,sCAAsC;IActCC,kBAAkB,MAAlBA,kBAAkB;IAWlBC,oBAAoB,MAApBA,oBAAoB;IAIpBC,wBAAwB,MAAxBA,wBAAwB;IASxBC,aAAa,MAAbA,aAAa;IAabC,uBAAuB,MAAvBA,uBAAuB;IAIvBC,oBAAoB,MAApBA,oBAAoB;;;8DAjGlB,OAAO;;;;;;;yBACQ,MAAM;;;;;;;8DACtB,MAAM;;;;;;;8DACC,cAAc;;;;;;qBAElB,cAAc;qBACE,oBAAoB;0BAC5B,yBAAyB;sBAC3B,qBAAqB;;;;;;AAE/C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAMhF,SAAST,6BAA6B,CAC3CU,WAAmB,EACnBC,eAAuB,GAAGT,kBAAkB,CAACQ,WAAW,CAAC,EACrC;IACpB,sBAAsB;IACtB,MAAME,WAAW,GACfC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACJ,WAAW,EAAE,mBAAmB,CAAC,IAAIK,oBAAoB,CAACL,WAAW,CAAC,AAAC;IAC5F,IAAI,CAACE,WAAW,EAAE;QAChB,OAAOI,SAAS,CAAC;IACnB,CAAC;IACD,8CAA8C;IAC9C,MAAMC,SAAS,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,WAAW,EAAEC,eAAe,CAAC,AAAC;IAC1D,MAAMS,OAAO,GAAGF,KAAI,EAAA,QAAA,CAACG,QAAQ,CAACH,KAAI,EAAA,QAAA,CAACI,OAAO,CAACV,WAAW,CAAC,EAAEK,SAAS,CAAC,AAAC;IACpET,KAAK,CAAC,mBAAmB,EAAEI,WAAW,EAAEK,SAAS,EAAEG,OAAO,CAAC,CAAC;IAC5D,OAAOA,OAAO,CAAC;AACjB,CAAC;AAED,gJAAgJ,GAChJ,SAASL,oBAAoB,CAACL,WAAmB,EAAU;IACzD,MAAMa,QAAQ,GAAGV,YAAW,EAAA,QAAA,CAACC,MAAM,CAACJ,WAAW,EAAE,mBAAmB,CAAC,AAAC;IACtE,IAAIa,QAAQ,EAAE;QACZ,OAAOL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACD,KAAI,EAAA,QAAA,CAACI,OAAO,CAACJ,KAAI,EAAA,QAAA,CAACI,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IAC9E,CAAC;IACD,OAAOL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,gCAAgC,CAAC,CAAC;AAClE,CAAC;AAEM,SAAST,sCAAsC,CACpDS,WAAmB,EACnBc,GAAe,EACP;QACWA,GAAS;IAA5B,OAAOC,IAAAA,SAAW,YAAA,EAACD,CAAAA,CAAAA,GAAS,GAATA,GAAG,CAACE,KAAK,SAAQ,GAAjBF,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEG,MAAM,SAAA,GAAjBH,KAAAA,CAAiB,QAAEI,IAAI,AAAN,CAAA,IAAU1B,kBAAkB,CAACQ,WAAW,CAAC,CAAC,CAAC;AACjF,CAAC;AAED,IAAImB,oBAAoB,GAAG,KAAK,AAAC;AACjC,MAAMC,SAAS,GAAG,IAAM;IACtB,IAAID,oBAAoB,EAAE,OAAO;IACjCA,oBAAoB,GAAG,IAAI,CAAC;IAC5BE,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,sDAAsD,CAAC,CAAC,CAAC;AAC9E,CAAC,AAAC;AAEK,SAAShC,kBAAkB,CAACQ,WAAmB,EAAU;IAC9D,kCAAkC;IAClC,IAAIyB,IAAAA,IAAmB,oBAAA,EAACjB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE;QAC7DoB,SAAS,EAAE,CAAC;QACZ,OAAOZ,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAEDX,KAAK,CAAC,kDAAkD,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,SAASL,oBAAoB,CAACiC,IAAY,EAAW;IAC1D,OAAO,kBAAkBC,IAAI,CAACD,IAAI,CAAC,CAAC;AACtC,CAAC;AAEM,SAAShC,wBAAwB,CAACkC,GAAW,EAAE;IACpD,OAAOC,IAAAA,KAAQ,EAAA,KAAA,EAAC,2BAA2B,EAAE;QAC3CD,GAAG;QACHE,QAAQ,EAAE,IAAI;QACdC,GAAG,EAAE,IAAI;KACV,CAAC,CAAC;AACL,CAAC;AAGM,SAASpC,aAAa,CAACiC,GAAW,EAAE;IACzC,OAAOC,IAAAA,KAAQ,EAAA,KAAA,EAAC,uBAAuB,EAAE;QACvCD,GAAG;QACHG,GAAG,EAAE,IAAI;KACV,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,GAAK,IAAI,GAAGC,cAAc,CAACD,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED,SAASC,cAAc,CAACD,CAAS,EAAE;IACjC,OAAOA,CAAC,CAACE,OAAO,QAAQ,GAAG,CAAC,CAAC;AAC/B,CAAC;AAED,IAAIC,4BAA4B,GAAG,KAAK,AAAC;AAElC,SAASxC,uBAAuB,GAAG;IACxC,OAAOwC,4BAA4B,CAAC;AACtC,CAAC;AAEM,SAASvC,oBAAoB,GAAG;IACrC,IAAI,CAACuC,4BAA4B,EAAE;QACjCf,IAAG,IAAA,CAACgB,IAAI,CACNd,MAAK,EAAA,QAAA,CAACe,MAAM,CAAC,6GAA6G,EAAEC,IAAAA,KAAS,UAAA,EACnI,oDAAoD,CACrD,CAAC,CAAC,CACJ,CAAC;IACJ,CAAC;IAEDH,4BAA4B,GAAG,IAAI,CAAC;AACtC,CAAC"}