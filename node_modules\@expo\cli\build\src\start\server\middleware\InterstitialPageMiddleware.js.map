{"version": 3, "sources": ["../../../../../src/start/server/middleware/InterstitialPageMiddleware.ts"], "sourcesContent": ["import { getConfig, getNameFromConfig } from '@expo/config';\nimport { getRuntimeVersionNullableAsync } from '@expo/config-plugins/build/utils/Updates';\nimport { readFile } from 'fs/promises';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\n\ntype ProjectVersion = {\n  type: 'sdk' | 'runtime';\n  version: string | null;\n};\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:interstitialPage'\n) as typeof console.log;\n\nexport const LoadingEndpoint = '/_expo/loading';\n\nexport class InterstitialPageMiddleware extends ExpoMiddleware {\n  constructor(\n    projectRoot: string,\n    protected options: { scheme: string | null } = { scheme: null }\n  ) {\n    super(projectRoot, [LoadingEndpoint]);\n  }\n\n  /** Get the template HTML page and inject values. */\n  async _getPageAsync({\n    appName,\n    projectVersion,\n  }: {\n    appName: string;\n    projectVersion: ProjectVersion;\n  }): Promise<string> {\n    const templatePath =\n      // Production: This will resolve when installed in the project.\n      resolveFrom.silent(this.projectRoot, 'expo/static/loading-page/index.html') ??\n      // Development: This will resolve when testing locally.\n      path.resolve(__dirname, '../../../../../static/loading-page/index.html');\n    let content = (await readFile(templatePath)).toString('utf-8');\n\n    content = content.replace(/{{\\s*AppName\\s*}}/, appName);\n    content = content.replace(/{{\\s*Path\\s*}}/, this.projectRoot);\n    content = content.replace(/{{\\s*Scheme\\s*}}/, this.options.scheme ?? 'Unknown');\n    content = content.replace(\n      /{{\\s*ProjectVersionType\\s*}}/,\n      `${projectVersion.type === 'sdk' ? 'SDK' : 'Runtime'} version`\n    );\n    content = content.replace(/{{\\s*ProjectVersion\\s*}}/, projectVersion.version ?? 'Undetected');\n\n    return content;\n  }\n\n  /** Get settings for the page from the project config. */\n  async _getProjectOptionsAsync(platform: RuntimePlatform): Promise<{\n    appName: string;\n    projectVersion: ProjectVersion;\n  }> {\n    assertRuntimePlatform(platform);\n\n    const { exp } = getConfig(this.projectRoot);\n    const { appName } = getNameFromConfig(exp);\n    const runtimeVersion = await getRuntimeVersionNullableAsync(this.projectRoot, exp, platform);\n    const sdkVersion = exp.sdkVersion ?? null;\n\n    return {\n      appName: appName ?? 'App',\n      projectVersion:\n        sdkVersion && !runtimeVersion\n          ? { type: 'sdk', version: sdkVersion }\n          : { type: 'runtime', version: runtimeVersion },\n    };\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    res = disableResponseCache(res);\n    res.setHeader('Content-Type', 'text/html');\n\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n\n    const { appName, projectVersion } = await this._getProjectOptionsAsync(platform);\n    debug(\n      `Create loading page. (platform: ${platform}, appName: ${appName}, projectVersion: ${projectVersion.version}, type: ${projectVersion.type})`\n    );\n    const content = await this._getPageAsync({ appName, projectVersion });\n    res.end(content);\n  }\n}\n"], "names": ["LoadingEndpoint", "InterstitialPageMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "options", "scheme", "_getPageAsync", "appName", "projectVersion", "templatePath", "resolveFrom", "silent", "path", "resolve", "__dirname", "content", "readFile", "toString", "replace", "type", "version", "_getProjectOptionsAsync", "platform", "assertRuntimePlatform", "exp", "getConfig", "getNameFromConfig", "runtimeVersion", "getRuntimeVersionNullableAsync", "sdkVersion", "handleRequestAsync", "req", "res", "disableResponseCache", "<PERSON><PERSON><PERSON><PERSON>", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "end"], "mappings": "AAAA;;;;;;;;;;;IAyBaA,eAAe,MAAfA,eAAe;IAEfC,0BAA0B,MAA1BA,0BAA0B;;;yBA3BM,cAAc;;;;;;;yBACZ,0CAA0C;;;;;;;yBAChE,aAAa;;;;;;;8DACrB,MAAM;;;;;;;8DACC,cAAc;;;;;;gCAEe,kBAAkB;iCAOhE,mBAAmB;;;;;;AAQ1B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,+CAA+C,CAChD,AAAsB,AAAC;AAEjB,MAAMH,eAAe,GAAG,gBAAgB,AAAC;AAEzC,MAAMC,0BAA0B,SAASG,eAAc,eAAA;IAC5DC,YACEC,WAAmB,EACTC,OAAkC,GAAG;QAAEC,MAAM,EAAE,IAAI;KAAE,CAC/D;QACA,KAAK,CAACF,WAAW,EAAE;YAACN,eAAe;SAAC,CAAC,CAAC;QAF5BO,eAAAA,OAAkC,CAAA;IAG9C;IAEA,kDAAkD,SAC5CE,aAAa,CAAC,EAClBC,OAAO,CAAA,EACPC,cAAc,CAAA,EAIf,EAAmB;QAClB,MAAMC,YAAY,GAChB,+DAA+D;QAC/DC,YAAW,EAAA,QAAA,CAACC,MAAM,CAAC,IAAI,CAACR,WAAW,EAAE,qCAAqC,CAAC,IAC3E,uDAAuD;QACvDS,KAAI,EAAA,QAAA,CAACC,OAAO,CAACC,SAAS,EAAE,+CAA+C,CAAC,AAAC;QAC3E,IAAIC,OAAO,GAAG,CAAC,MAAMC,IAAAA,SAAQ,EAAA,SAAA,EAACP,YAAY,CAAC,CAAC,CAACQ,QAAQ,CAAC,OAAO,CAAC,AAAC;QAE/DF,OAAO,GAAGA,OAAO,CAACG,OAAO,sBAAsBX,OAAO,CAAC,CAAC;QACxDQ,OAAO,GAAGA,OAAO,CAACG,OAAO,mBAAmB,IAAI,CAACf,WAAW,CAAC,CAAC;QAC9DY,OAAO,GAAGA,OAAO,CAACG,OAAO,qBAAqB,IAAI,CAACd,OAAO,CAACC,MAAM,IAAI,SAAS,CAAC,CAAC;QAChFU,OAAO,GAAGA,OAAO,CAACG,OAAO,iCAEvB,CAAC,EAAEV,cAAc,CAACW,IAAI,KAAK,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,CAC/D,CAAC;QACFJ,OAAO,GAAGA,OAAO,CAACG,OAAO,6BAA6BV,cAAc,CAACY,OAAO,IAAI,YAAY,CAAC,CAAC;QAE9F,OAAOL,OAAO,CAAC;IACjB;IAEA,uDAAuD,SACjDM,uBAAuB,CAACC,QAAyB,EAGpD;QACDC,IAAAA,gBAAqB,sBAAA,EAACD,QAAQ,CAAC,CAAC;QAEhC,MAAM,EAAEE,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAACtB,WAAW,CAAC,AAAC;QAC5C,MAAM,EAAEI,OAAO,CAAA,EAAE,GAAGmB,IAAAA,OAAiB,EAAA,kBAAA,EAACF,GAAG,CAAC,AAAC;QAC3C,MAAMG,cAAc,GAAG,MAAMC,IAAAA,QAA8B,EAAA,+BAAA,EAAC,IAAI,CAACzB,WAAW,EAAEqB,GAAG,EAAEF,QAAQ,CAAC,AAAC;QAC7F,MAAMO,UAAU,GAAGL,GAAG,CAACK,UAAU,IAAI,IAAI,AAAC;QAE1C,OAAO;YACLtB,OAAO,EAAEA,OAAO,IAAI,KAAK;YACzBC,cAAc,EACZqB,UAAU,IAAI,CAACF,cAAc,GACzB;gBAAER,IAAI,EAAE,KAAK;gBAAEC,OAAO,EAAES,UAAU;aAAE,GACpC;gBAAEV,IAAI,EAAE,SAAS;gBAAEC,OAAO,EAAEO,cAAc;aAAE;SACnD,CAAC;IACJ;UAEMG,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/EA,GAAG,GAAGC,IAAAA,eAAoB,qBAAA,EAACD,GAAG,CAAC,CAAC;QAChCA,GAAG,CAACE,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE3C,MAAMZ,QAAQ,GAAGa,IAAAA,gBAAmB,oBAAA,EAACJ,GAAG,CAAC,IAAIK,IAAAA,gBAAkC,mCAAA,EAACL,GAAG,CAAC,AAAC;QACrFM,IAAAA,gBAA4B,6BAAA,EAACf,QAAQ,CAAC,CAAC;QACvCC,IAAAA,gBAAqB,sBAAA,EAACD,QAAQ,CAAC,CAAC;QAEhC,MAAM,EAAEf,OAAO,CAAA,EAAEC,cAAc,CAAA,EAAE,GAAG,MAAM,IAAI,CAACa,uBAAuB,CAACC,QAAQ,CAAC,AAAC;QACjFvB,KAAK,CACH,CAAC,gCAAgC,EAAEuB,QAAQ,CAAC,WAAW,EAAEf,OAAO,CAAC,kBAAkB,EAAEC,cAAc,CAACY,OAAO,CAAC,QAAQ,EAAEZ,cAAc,CAACW,IAAI,CAAC,CAAC,CAAC,CAC7I,CAAC;QACF,MAAMJ,OAAO,GAAG,MAAM,IAAI,CAACT,aAAa,CAAC;YAAEC,OAAO;YAAEC,cAAc;SAAE,CAAC,AAAC;QACtEwB,GAAG,CAACM,GAAG,CAACvB,OAAO,CAAC,CAAC;IACnB;CACD"}