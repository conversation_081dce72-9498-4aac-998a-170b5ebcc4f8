"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "getPlatformBundlers", {
    enumerable: true,
    get: ()=>getPlatformBundlers
});
function _resolveFrom() {
    const data = /*#__PURE__*/ _interopRequireDefault(require("resolve-from"));
    _resolveFrom = function() {
        return data;
    };
    return data;
}
function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function getPlatformBundlers(projectRoot, exp) {
    var ref, ref1, ref2;
    /**
   * SDK 50+: The web bundler is dynamic based upon the presence of the `@expo/webpack-config` package.
   */ let web = (ref = exp.web) == null ? void 0 : ref.bundler;
    if (!web) {
        const resolved = _resolveFrom().default.silent(projectRoot, "@expo/webpack-config/package.json");
        web = resolved ? "webpack" : "metro";
    }
    return {
        // @ts-expect-error: not on type yet
        ios: ((ref1 = exp.ios) == null ? void 0 : ref1.bundler) ?? "metro",
        // @ts-expect-error: not on type yet
        android: ((ref2 = exp.android) == null ? void 0 : ref2.bundler) ?? "metro",
        web
    };
}

//# sourceMappingURL=platformBundlers.js.map