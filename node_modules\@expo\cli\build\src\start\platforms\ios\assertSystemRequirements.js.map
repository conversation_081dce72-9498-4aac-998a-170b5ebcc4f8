{"version": 3, "sources": ["../../../../../src/start/platforms/ios/assertSystemRequirements.ts"], "sourcesContent": ["import { profile } from '../../../utils/profile';\nimport { SimulatorAppPrerequisite } from '../../doctor/apple/SimulatorAppPrerequisite';\nimport { XcodePrerequisite } from '../../doctor/apple/XcodePrerequisite';\nimport { XcrunPrerequisite } from '../../doctor/apple/XcrunPrerequisite';\n\nexport async function assertSystemRequirementsAsync() {\n  // Order is important\n  await profile(\n    XcodePrerequisite.instance.assertAsync.bind(XcodePrerequisite.instance),\n    'XcodePrerequisite'\n  )();\n  await profile(\n    XcrunPrerequisite.instance.assertAsync.bind(XcrunPrerequisite.instance),\n    'XcrunPrerequisite'\n  )();\n  await profile(\n    SimulatorAppPrerequisite.instance.assertAsync.bind(SimulatorAppPrerequisite.instance),\n    'SimulatorAppPrerequisite'\n  )();\n}\n"], "names": ["assertSystemRequirementsAsync", "profile", "XcodePrerequisite", "instance", "assertAsync", "bind", "XcrunPrerequisite", "SimulatorAppPrerequisite"], "mappings": "AAAA;;;;+BAKsBA,+BAA6B;;aAA7BA,6BAA6B;;yBAL3B,wBAAwB;0CACP,6CAA6C;mCACpD,sCAAsC;mCACtC,sCAAsC;AAEjE,eAAeA,6BAA6B,GAAG;IACpD,qBAAqB;IACrB,MAAMC,IAAAA,QAAO,QAAA,EACXC,kBAAiB,kBAAA,CAACC,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACH,kBAAiB,kBAAA,CAACC,QAAQ,CAAC,EACvE,mBAAmB,CACpB,EAAE,CAAC;IACJ,MAAMF,IAAAA,QAAO,QAAA,EACXK,kBAAiB,kBAAA,CAACH,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACC,kBAAiB,kBAAA,CAACH,QAAQ,CAAC,EACvE,mBAAmB,CACpB,EAAE,CAAC;IACJ,MAAMF,IAAAA,QAAO,QAAA,EACXM,yBAAwB,yBAAA,CAACJ,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACE,yBAAwB,yBAAA,CAACJ,QAAQ,CAAC,EACrF,0BAA0B,CAC3B,EAAE,CAAC;AACN,CAAC"}