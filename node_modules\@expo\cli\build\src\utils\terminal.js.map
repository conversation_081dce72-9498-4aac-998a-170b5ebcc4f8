{"version": 3, "sources": ["../../../src/utils/terminal.ts"], "sourcesContent": ["/** @returns the environment variable indicating the default terminal program to use. */\nexport function getUserTerminal(): string | undefined {\n  return (\n    process.env.REACT_TERMINAL ||\n    (process.platform === 'darwin' ? process.env.TERM_PROGRAM : process.env.TERM)\n  );\n}\n"], "names": ["getUserTerminal", "process", "env", "REACT_TERMINAL", "platform", "TERM_PROGRAM", "TERM"], "mappings": "AAAA,sFAAsF,GACtF;;;;+BAAgBA,iBAAe;;aAAfA,eAAe;;AAAxB,SAASA,eAAe,GAAuB;IACpD,OACEC,OAAO,CAACC,GAAG,CAACC,cAAc,IAC1B,CAACF,OAAO,CAACG,QAAQ,KAAK,QAAQ,GAAGH,OAAO,CAACC,GAAG,CAACG,YAAY,GAAGJ,OAAO,CAACC,GAAG,CAACI,IAAI,CAAC,CAC7E;AACJ,CAAC"}