{"version": 3, "sources": ["../../../../src/run/ios/launchApp.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport path from 'path';\n\nimport * as XcodeBuild from './XcodeBuild';\nimport { BuildProps } from './XcodeBuild.types';\nimport { getAppDeltaDirectory, installOnDeviceAsync } from './appleDevice/installOnDeviceAsync';\nimport { Log } from '../../log';\nimport { AppleDeviceManager } from '../../start/platforms/ios/AppleDeviceManager';\nimport { launchBinaryOnMacAsync } from '../../start/platforms/ios/devicectl';\nimport { SimulatorLogStreamer } from '../../start/platforms/ios/simctlLogging';\nimport { DevServerManager } from '../../start/server/DevServerManager';\nimport { parsePlistAsync } from '../../utils/plist';\nimport { profile } from '../../utils/profile';\n\ntype BinaryLaunchInfo = {\n  bundleId: string;\n  schemes: string[];\n};\n\n/** Install and launch the app binary on a device. */\nexport async function launchAppAsync(\n  binaryPath: string,\n  manager: DevServerManager,\n  props: Pick<BuildProps, 'isSimulator' | 'device' | 'shouldStartBundler'>,\n  appId?: string\n) {\n  appId ??= (await profile(getLaunchInfoForBinaryAsync)(binaryPath)).bundleId;\n\n  Log.log(chalk.gray`\\u203A Installing ${binaryPath}`);\n  if (!props.isSimulator) {\n    if (props.device.osType === 'macOS') {\n      await launchBinaryOnMacAsync(appId, binaryPath);\n    } else {\n      await profile(installOnDeviceAsync)({\n        bundleIdentifier: appId,\n        bundle: binaryPath,\n        appDeltaDirectory: getAppDeltaDirectory(appId),\n        udid: props.device.udid,\n        deviceName: props.device.name,\n      });\n    }\n\n    return;\n  }\n\n  XcodeBuild.logPrettyItem(chalk`{bold Installing} on ${props.device.name}`);\n\n  const device = await AppleDeviceManager.resolveAsync({ device: props.device });\n  await device.installAppAsync(binaryPath);\n\n  XcodeBuild.logPrettyItem(chalk`{bold Opening} on ${device.name} {dim (${appId})}`);\n\n  if (props.shouldStartBundler) {\n    await SimulatorLogStreamer.getStreamer(device.device, {\n      appId,\n    }).attachAsync();\n  }\n\n  await manager.getDefaultDevServer().openCustomRuntimeAsync(\n    'simulator',\n    {\n      applicationId: appId,\n    },\n    { device }\n  );\n}\n\nexport async function getLaunchInfoForBinaryAsync(binaryPath: string): Promise<BinaryLaunchInfo> {\n  const builtInfoPlistPath = path.join(binaryPath, 'Info.plist');\n  const { CFBundleIdentifier, CFBundleURLTypes } = await parsePlistAsync(builtInfoPlistPath);\n\n  let schemes: string[] = [];\n\n  if (Array.isArray(CFBundleURLTypes)) {\n    schemes =\n      CFBundleURLTypes.reduce<string[]>((acc, urlType: unknown) => {\n        if (\n          urlType &&\n          typeof urlType === 'object' &&\n          'CFBundleURLSchemes' in urlType &&\n          Array.isArray(urlType.CFBundleURLSchemes)\n        ) {\n          return [...acc, ...urlType.CFBundleURLSchemes];\n        }\n        return acc;\n      }, []) ?? [];\n  }\n\n  return { bundleId: CFBundleIdentifier, schemes };\n}\n"], "names": ["launchAppAsync", "getLaunchInfoForBinaryAsync", "binaryPath", "manager", "props", "appId", "profile", "bundleId", "Log", "log", "chalk", "gray", "isSimulator", "device", "osType", "launchBinaryOnMacAsync", "installOnDeviceAsync", "bundleIdentifier", "bundle", "appDeltaDirectory", "getAppDeltaDirectory", "udid", "deviceName", "name", "XcodeBuild", "logPrettyItem", "AppleDeviceManager", "resolveAsync", "installAppAsync", "shouldStartBundler", "SimulatorLogStreamer", "getStreamer", "attachAsync", "getDefaultDevServer", "openCustomRuntimeAsync", "applicationId", "builtInfoPlistPath", "path", "join", "CFBundleIdentifier", "CFBundleURLTypes", "parsePlistAsync", "schemes", "Array", "isArray", "reduce", "acc", "urlType", "CFBundleURLSchemes"], "mappings": "AAAA;;;;;;;;;;;IAoBsBA,cAAc,MAAdA,cAAc;IA+CdC,2BAA2B,MAA3BA,2BAA2B;;;8DAnE/B,OAAO;;;;;;;8DACR,MAAM;;;;;;kEAEK,cAAc;sCAEiB,oCAAoC;qBAC3E,WAAW;oCACI,8CAA8C;2BAC1C,qCAAqC;+BACvC,yCAAyC;uBAE9C,mBAAmB;yBAC3B,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC,eAAeD,cAAc,CAClCE,UAAkB,EAClBC,OAAyB,EACzBC,KAAwE,EACxEC,KAAc,EACd;IACAA,KAAK,KAAK,CAAC,MAAMC,IAAAA,QAAO,QAAA,EAACL,2BAA2B,CAAC,CAACC,UAAU,CAAC,CAAC,CAACK,QAAQ,CAAC;IAE5EC,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,kBAAkB,EAAET,UAAU,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAACE,KAAK,CAACQ,WAAW,EAAE;QACtB,IAAIR,KAAK,CAACS,MAAM,CAACC,MAAM,KAAK,OAAO,EAAE;YACnC,MAAMC,IAAAA,UAAsB,uBAAA,EAACV,KAAK,EAAEH,UAAU,CAAC,CAAC;QAClD,OAAO;YACL,MAAMI,IAAAA,QAAO,QAAA,EAACU,qBAAoB,qBAAA,CAAC,CAAC;gBAClCC,gBAAgB,EAAEZ,KAAK;gBACvBa,MAAM,EAAEhB,UAAU;gBAClBiB,iBAAiB,EAAEC,IAAAA,qBAAoB,qBAAA,EAACf,KAAK,CAAC;gBAC9CgB,IAAI,EAAEjB,KAAK,CAACS,MAAM,CAACQ,IAAI;gBACvBC,UAAU,EAAElB,KAAK,CAACS,MAAM,CAACU,IAAI;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,OAAO;IACT,CAAC;IAEDC,WAAU,CAACC,aAAa,CAACf,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,qBAAqB,EAAEN,KAAK,CAACS,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC;IAE3E,MAAMV,MAAM,GAAG,MAAMa,mBAAkB,mBAAA,CAACC,YAAY,CAAC;QAAEd,MAAM,EAAET,KAAK,CAACS,MAAM;KAAE,CAAC,AAAC;IAC/E,MAAMA,MAAM,CAACe,eAAe,CAAC1B,UAAU,CAAC,CAAC;IAEzCsB,WAAU,CAACC,aAAa,CAACf,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,kBAAkB,EAAEG,MAAM,CAACU,IAAI,CAAC,OAAO,EAAElB,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAEnF,IAAID,KAAK,CAACyB,kBAAkB,EAAE;QAC5B,MAAMC,cAAoB,qBAAA,CAACC,WAAW,CAAClB,MAAM,CAACA,MAAM,EAAE;YACpDR,KAAK;SACN,CAAC,CAAC2B,WAAW,EAAE,CAAC;IACnB,CAAC;IAED,MAAM7B,OAAO,CAAC8B,mBAAmB,EAAE,CAACC,sBAAsB,CACxD,WAAW,EACX;QACEC,aAAa,EAAE9B,KAAK;KACrB,EACD;QAAEQ,MAAM;KAAE,CACX,CAAC;AACJ,CAAC;AAEM,eAAeZ,2BAA2B,CAACC,UAAkB,EAA6B;IAC/F,MAAMkC,kBAAkB,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACpC,UAAU,EAAE,YAAY,CAAC,AAAC;IAC/D,MAAM,EAAEqC,kBAAkB,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAG,MAAMC,IAAAA,MAAe,gBAAA,EAACL,kBAAkB,CAAC,AAAC;IAE3F,IAAIM,OAAO,GAAa,EAAE,AAAC;IAE3B,IAAIC,KAAK,CAACC,OAAO,CAACJ,gBAAgB,CAAC,EAAE;QACnCE,OAAO,GACLF,gBAAgB,CAACK,MAAM,CAAW,CAACC,GAAG,EAAEC,OAAgB,GAAK;YAC3D,IACEA,OAAO,IACP,OAAOA,OAAO,KAAK,QAAQ,IAC3B,oBAAoB,IAAIA,OAAO,IAC/BJ,KAAK,CAACC,OAAO,CAACG,OAAO,CAACC,kBAAkB,CAAC,EACzC;gBACA,OAAO;uBAAIF,GAAG;uBAAKC,OAAO,CAACC,kBAAkB;iBAAC,CAAC;YACjD,CAAC;YACD,OAAOF,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;IACjB,CAAC;IAED,OAAO;QAAEvC,QAAQ,EAAEgC,kBAAkB;QAAEG,OAAO;KAAE,CAAC;AACnD,CAAC"}