{"version": 3, "sources": ["../../../../../src/start/platforms/ios/getBestSimulator.ts"], "sourcesContent": ["import { execSync } from 'child_process';\n\nimport * as SimControl from './simctl';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:ios:getBestSimulator') as typeof console.log;\n\ntype DeviceContext = Partial<Pick<SimControl.Device, 'osType'>>;\n\n/**\n * Returns the default device stored in the Simulator.app settings.\n * This helps us to get the device that the user opened most recently regardless of which tool they used.\n */\nfunction getDefaultSimulatorDeviceUDID() {\n  try {\n    const defaultDeviceUDID = execSync(\n      `defaults read com.apple.iphonesimulator CurrentDeviceUDID`,\n      { stdio: 'pipe' }\n    ).toString();\n    return defaultDeviceUDID.trim();\n  } catch {\n    return null;\n  }\n}\n\nexport async function getBestBootedSimulatorAsync({\n  osType,\n}: DeviceContext = {}): Promise<SimControl.Device | null> {\n  const [simulatorOpenedByApp] = await SimControl.getBootedSimulatorsAsync();\n  // This should prevent opening a second simulator in the chance that default\n  // simulator doesn't match what the Simulator app would open by default.\n  if (\n    simulatorOpenedByApp?.udid &&\n    (!osType || (osType && simulatorOpenedByApp.osType === osType))\n  ) {\n    debug(`First booted simulator: ${simulatorOpenedByApp?.windowName}`);\n    return simulatorOpenedByApp;\n  }\n\n  debug(`No booted simulator matching requirements (osType: ${osType}).`);\n  return null;\n}\n\n/**\n * Returns the most preferred simulator UDID without booting anything.\n *\n * 1. If the simulator app defines a default simulator and the osType is not defined.\n * 2. If the osType is defined, then check if the default udid matches the osType.\n * 3. If all else fails, return the first found simulator.\n */\nexport async function getBestUnbootedSimulatorAsync({ osType }: DeviceContext = {}): Promise<\n  string | null\n> {\n  const defaultId = getDefaultSimulatorDeviceUDID();\n  debug(`Default simulator ID: ${defaultId}`);\n\n  if (defaultId && !osType) {\n    return defaultId;\n  }\n\n  const simulators = await getSelectableSimulatorsAsync({ osType });\n\n  if (!simulators.length) {\n    // TODO: Prompt to install the simulators\n    throw new CommandError(\n      'UNSUPPORTED_OS_TYPE',\n      `No ${osType || 'iOS'} devices available in Simulator.app`\n    );\n  }\n\n  // If the default udid is defined, then check to ensure its osType matches the required os.\n  if (defaultId) {\n    const defaultSimulator = simulators.find((device) => device.udid === defaultId);\n    if (defaultSimulator?.osType === osType) {\n      return defaultId;\n    }\n  }\n\n  // Return first selectable device.\n  return simulators[0]?.udid ?? null;\n}\n\n/**\n * Get all simulators supported by Expo Go (iOS only).\n */\nexport async function getSelectableSimulatorsAsync({ osType = 'iOS' }: DeviceContext = {}): Promise<\n  SimControl.Device[]\n> {\n  const simulators = await SimControl.getDevicesAsync();\n  return simulators.filter((device) => device.isAvailable && device.osType === osType);\n}\n\n/**\n * Get 'best' simulator for the user based on:\n * 1. Currently booted simulator.\n * 2. Last simulator that was opened.\n * 3. First simulator that was opened.\n */\nexport async function getBestSimulatorAsync({ osType }: DeviceContext): Promise<string | null> {\n  const simulatorOpenedByApp = await getBestBootedSimulatorAsync({ osType });\n\n  if (simulatorOpenedByApp) {\n    return simulatorOpenedByApp.udid;\n  }\n\n  return await getBestUnbootedSimulatorAsync({ osType });\n}\n"], "names": ["getBestBootedSimulatorAsync", "getBestUnbootedSimulatorAsync", "getSelectableSimulatorsAsync", "getBestSimulatorAsync", "debug", "require", "getDefaultSimulatorDeviceUDID", "defaultDeviceUDID", "execSync", "stdio", "toString", "trim", "osType", "simulatorOpenedByApp", "SimControl", "getBootedSimulatorsAsync", "udid", "windowName", "simulators", "defaultId", "length", "CommandError", "defaultSimulator", "find", "device", "getDevicesAsync", "filter", "isAvailable"], "mappings": "AAAA;;;;;;;;;;;IAyBsBA,2BAA2B,MAA3BA,2BAA2B;IAyB3BC,6BAA6B,MAA7BA,6BAA6B;IAmC7BC,4BAA4B,MAA5BA,4BAA4B;IAa5BC,qBAAqB,MAArBA,qBAAqB;;;yBAlGlB,eAAe;;;;;;8DAEZ,UAAU;wBACT,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,2CAA2C,CAAC,AAAsB,AAAC;AAIlG;;;CAGC,GACD,SAASC,6BAA6B,GAAG;IACvC,IAAI;QACF,MAAMC,iBAAiB,GAAGC,IAAAA,aAAQ,EAAA,SAAA,EAChC,CAAC,yDAAyD,CAAC,EAC3D;YAAEC,KAAK,EAAE,MAAM;SAAE,CAClB,CAACC,QAAQ,EAAE,AAAC;QACb,OAAOH,iBAAiB,CAACI,IAAI,EAAE,CAAC;IAClC,EAAE,OAAM;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAEM,eAAeX,2BAA2B,CAAC,EAChDY,MAAM,CAAA,EACQ,GAAG,EAAE,EAAqC;IACxD,MAAM,CAACC,oBAAoB,CAAC,GAAG,MAAMC,OAAU,CAACC,wBAAwB,EAAE,AAAC;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,IACEF,CAAAA,oBAAoB,QAAM,GAA1BA,KAAAA,CAA0B,GAA1BA,oBAAoB,CAAEG,IAAI,CAAA,IAC1B,CAAC,CAACJ,MAAM,IAAKA,MAAM,IAAIC,oBAAoB,CAACD,MAAM,KAAKA,MAAM,AAAC,CAAC,EAC/D;QACAR,KAAK,CAAC,CAAC,wBAAwB,EAAES,oBAAoB,QAAY,GAAhCA,KAAAA,CAAgC,GAAhCA,oBAAoB,CAAEI,UAAU,CAAC,CAAC,CAAC,CAAC;QACrE,OAAOJ,oBAAoB,CAAC;IAC9B,CAAC;IAEDT,KAAK,CAAC,CAAC,mDAAmD,EAAEQ,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE,OAAO,IAAI,CAAC;AACd,CAAC;AASM,eAAeX,6BAA6B,CAAC,EAAEW,MAAM,CAAA,EAAiB,GAAG,EAAE,EAEhF;QA2BOM,GAAa;IA1BpB,MAAMC,SAAS,GAAGb,6BAA6B,EAAE,AAAC;IAClDF,KAAK,CAAC,CAAC,sBAAsB,EAAEe,SAAS,CAAC,CAAC,CAAC,CAAC;IAE5C,IAAIA,SAAS,IAAI,CAACP,MAAM,EAAE;QACxB,OAAOO,SAAS,CAAC;IACnB,CAAC;IAED,MAAMD,UAAU,GAAG,MAAMhB,4BAA4B,CAAC;QAAEU,MAAM;KAAE,CAAC,AAAC;IAElE,IAAI,CAACM,UAAU,CAACE,MAAM,EAAE;QACtB,yCAAyC;QACzC,MAAM,IAAIC,OAAY,aAAA,CACpB,qBAAqB,EACrB,CAAC,GAAG,EAAET,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAC3D,CAAC;IACJ,CAAC;IAED,2FAA2F;IAC3F,IAAIO,SAAS,EAAE;QACb,MAAMG,gBAAgB,GAAGJ,UAAU,CAACK,IAAI,CAAC,CAACC,MAAM,GAAKA,MAAM,CAACR,IAAI,KAAKG,SAAS,CAAC,AAAC;QAChF,IAAIG,CAAAA,gBAAgB,QAAQ,GAAxBA,KAAAA,CAAwB,GAAxBA,gBAAgB,CAAEV,MAAM,CAAA,KAAKA,MAAM,EAAE;YACvC,OAAOO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED,kCAAkC;IAClC,OAAOD,CAAAA,CAAAA,GAAa,GAAbA,UAAU,CAAC,CAAC,CAAC,SAAM,GAAnBA,KAAAA,CAAmB,GAAnBA,GAAa,CAAEF,IAAI,CAAA,IAAI,IAAI,CAAC;AACrC,CAAC;AAKM,eAAed,4BAA4B,CAAC,EAAEU,MAAM,EAAG,KAAK,CAAA,EAAiB,GAAG,EAAE,EAEvF;IACA,MAAMM,UAAU,GAAG,MAAMJ,OAAU,CAACW,eAAe,EAAE,AAAC;IACtD,OAAOP,UAAU,CAACQ,MAAM,CAAC,CAACF,MAAM,GAAKA,MAAM,CAACG,WAAW,IAAIH,MAAM,CAACZ,MAAM,KAAKA,MAAM,CAAC,CAAC;AACvF,CAAC;AAQM,eAAeT,qBAAqB,CAAC,EAAES,MAAM,CAAA,EAAiB,EAA0B;IAC7F,MAAMC,oBAAoB,GAAG,MAAMb,2BAA2B,CAAC;QAAEY,MAAM;KAAE,CAAC,AAAC;IAE3E,IAAIC,oBAAoB,EAAE;QACxB,OAAOA,oBAAoB,CAACG,IAAI,CAAC;IACnC,CAAC;IAED,OAAO,MAAMf,6BAA6B,CAAC;QAAEW,MAAM;KAAE,CAAC,CAAC;AACzD,CAAC"}