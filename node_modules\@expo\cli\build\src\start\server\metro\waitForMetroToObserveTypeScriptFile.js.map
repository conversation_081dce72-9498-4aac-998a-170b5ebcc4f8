{"version": 3, "sources": ["../../../../../src/start/server/metro/waitForMetroToObserveTypeScriptFile.ts"], "sourcesContent": ["import path from 'path';\n\nimport type { ServerLike } from '../BundlerDevServer';\n\nconst debug = require('debug')('expo:start:server:metro:waitForTypescript') as typeof console.log;\n\nexport type FileChangeEvent = {\n  filePath: string;\n  metadata?: {\n    type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n  } | null;\n  type: string;\n};\n\n/**\n * Use the native file watcher / Metro ruleset to detect if a\n * TypeScript file is added to the project during development.\n */\nexport function waitForMetroToObserveTypeScriptFile(\n  projectRoot: string,\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  callback: () => Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const tsconfigPath = path.join(projectRoot, 'tsconfig.json');\n\n  const listener = ({ eventsQueue }: { eventsQueue: FileChangeEvent[] }) => {\n    for (const event of eventsQueue) {\n      if (\n        event.type === 'add' &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (\n          // If the user adds a TypeScript file to the observable files in their project.\n          /\\.tsx?$/.test(filePath) ||\n          // Or if the user adds a tsconfig.json file to the project root.\n          filePath === tsconfigPath\n        ) {\n          debug('Detected TypeScript file added to the project: ', filePath);\n          callback();\n          off();\n          return;\n        }\n      }\n    }\n  };\n\n  debug('Waiting for TypeScript files to be added to the project...');\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n\nexport function observeFileChanges(\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  files: string[],\n  callback: () => void | Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const listener = ({\n    eventsQueue,\n  }: {\n    eventsQueue: {\n      filePath: string;\n      metadata?: {\n        type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n      } | null;\n      type: string;\n    }[];\n  }) => {\n    for (const event of eventsQueue) {\n      if (\n        // event.type === 'add' &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (files.includes(filePath)) {\n          debug('Observed change:', filePath);\n          callback();\n          return;\n        }\n      }\n    }\n  };\n\n  debug('Watching file changes:', files);\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n\nexport function observeAnyFileChanges(\n  runner: {\n    metro: import('metro').Server;\n    server: ServerLike;\n  },\n  callback: (events: FileChangeEvent[]) => void | Promise<void>\n): () => void {\n  const watcher = runner.metro.getBundler().getBundler().getWatcher();\n\n  const listener = ({ eventsQueue }: { eventsQueue: FileChangeEvent[] }) => {\n    callback(eventsQueue);\n  };\n\n  watcher.addListener('change', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n  };\n\n  runner.server.addListener?.('close', off);\n  return off;\n}\n"], "names": ["waitForMetroToObserveTypeScriptFile", "observeFileChanges", "observeAnyFileChanges", "debug", "require", "projectRoot", "runner", "callback", "watcher", "metro", "getBundler", "getW<PERSON>er", "tsconfigPath", "path", "join", "listener", "eventsQueue", "event", "type", "metadata", "test", "filePath", "off", "addListener", "removeListener", "server", "files", "includes"], "mappings": "AAAA;;;;;;;;;;;IAkBgBA,mCAAmC,MAAnCA,mCAAmC;IAgDnCC,kBAAkB,MAAlBA,kBAAkB;IAkDlBC,qBAAqB,MAArBA,qBAAqB;;;8DApHpB,MAAM;;;;;;;;;;;AAIvB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,2CAA2C,CAAC,AAAsB,AAAC;AAc3F,SAASJ,mCAAmC,CACjDK,WAAmB,EACnBC,MAGC,EACDC,QAA6B,EACjB;IACZ,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAACC,UAAU,EAAE,AAAC;IAEpE,MAAMC,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,WAAW,EAAE,eAAe,CAAC,AAAC;IAE7D,MAAMU,QAAQ,GAAG,CAAC,EAAEC,WAAW,CAAA,EAAsC,GAAK;QACxE,KAAK,MAAMC,KAAK,IAAID,WAAW,CAAE;gBAG7BC,GAAc;YAFhB,IACEA,KAAK,CAACC,IAAI,KAAK,KAAK,IACpBD,CAAAA,CAAAA,GAAc,GAAdA,KAAK,CAACE,QAAQ,SAAM,GAApBF,KAAAA,CAAoB,GAApBA,GAAc,CAAEC,IAAI,CAAA,KAAK,GAAG,IAC5B,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACH,KAAK,CAACI,QAAQ,CAAC,EACpC;gBACA,MAAM,EAAEA,QAAQ,CAAA,EAAE,GAAGJ,KAAK,AAAC;gBAC3B,iBAAiB;gBACjB,IACE,+EAA+E;gBAC/E,UAAUG,IAAI,CAACC,QAAQ,CAAC,IACxB,gEAAgE;gBAChEA,QAAQ,KAAKT,YAAY,EACzB;oBACAT,KAAK,CAAC,iDAAiD,EAAEkB,QAAQ,CAAC,CAAC;oBACnEd,QAAQ,EAAE,CAAC;oBACXe,GAAG,EAAE,CAAC;oBACN,OAAO;gBACT,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,AAAC;IAEFnB,KAAK,CAAC,4DAA4D,CAAC,CAAC;IACpEK,OAAO,CAACe,WAAW,CAAC,QAAQ,EAAER,QAAQ,CAAC,CAAC;IAExC,MAAMO,GAAG,GAAG,IAAM;QAChBd,OAAO,CAACgB,cAAc,CAAC,QAAQ,EAAET,QAAQ,CAAC,CAAC;IAC7C,CAAC,AAAC;IAEFT,MAAM,CAACmB,MAAM,CAACF,WAAW,QAAgB,GAAzCjB,KAAAA,CAAyC,GAAzCA,MAAM,CAACmB,MAAM,CAACF,WAAW,CAAG,OAAO,EAAED,GAAG,CAAC,CAAC;IAC1C,OAAOA,GAAG,CAAC;AACb,CAAC;AAEM,SAASrB,kBAAkB,CAChCK,MAGC,EACDoB,KAAe,EACfnB,QAAoC,EACxB;IACZ,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAACC,UAAU,EAAE,AAAC;IAEpE,MAAMI,QAAQ,GAAG,CAAC,EAChBC,WAAW,CAAA,EASZ,GAAK;QACJ,KAAK,MAAMC,KAAK,IAAID,WAAW,CAAE;gBAE7B,0BAA0B;YAC1BC,GAAc;YAFhB,IAEEA,CAAAA,CAAAA,GAAc,GAAdA,KAAK,CAACE,QAAQ,SAAM,GAApBF,KAAAA,CAAoB,GAApBA,GAAc,CAAEC,IAAI,CAAA,KAAK,GAAG,IAC5B,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACH,KAAK,CAACI,QAAQ,CAAC,EACpC;gBACA,MAAM,EAAEA,QAAQ,CAAA,EAAE,GAAGJ,KAAK,AAAC;gBAC3B,iBAAiB;gBACjB,IAAIS,KAAK,CAACC,QAAQ,CAACN,QAAQ,CAAC,EAAE;oBAC5BlB,KAAK,CAAC,kBAAkB,EAAEkB,QAAQ,CAAC,CAAC;oBACpCd,QAAQ,EAAE,CAAC;oBACX,OAAO;gBACT,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,AAAC;IAEFJ,KAAK,CAAC,wBAAwB,EAAEuB,KAAK,CAAC,CAAC;IACvClB,OAAO,CAACe,WAAW,CAAC,QAAQ,EAAER,QAAQ,CAAC,CAAC;IAExC,MAAMO,GAAG,GAAG,IAAM;QAChBd,OAAO,CAACgB,cAAc,CAAC,QAAQ,EAAET,QAAQ,CAAC,CAAC;IAC7C,CAAC,AAAC;IAEFT,MAAM,CAACmB,MAAM,CAACF,WAAW,QAAgB,GAAzCjB,KAAAA,CAAyC,GAAzCA,MAAM,CAACmB,MAAM,CAACF,WAAW,CAAG,OAAO,EAAED,GAAG,CAAC,CAAC;IAC1C,OAAOA,GAAG,CAAC;AACb,CAAC;AAEM,SAASpB,qBAAqB,CACnCI,MAGC,EACDC,QAA6D,EACjD;IACZ,MAAMC,OAAO,GAAGF,MAAM,CAACG,KAAK,CAACC,UAAU,EAAE,CAACA,UAAU,EAAE,CAACC,UAAU,EAAE,AAAC;IAEpE,MAAMI,QAAQ,GAAG,CAAC,EAAEC,WAAW,CAAA,EAAsC,GAAK;QACxET,QAAQ,CAACS,WAAW,CAAC,CAAC;IACxB,CAAC,AAAC;IAEFR,OAAO,CAACe,WAAW,CAAC,QAAQ,EAAER,QAAQ,CAAC,CAAC;IAExC,MAAMO,GAAG,GAAG,IAAM;QAChBd,OAAO,CAACgB,cAAc,CAAC,QAAQ,EAAET,QAAQ,CAAC,CAAC;IAC7C,CAAC,AAAC;IAEFT,MAAM,CAACmB,MAAM,CAACF,WAAW,QAAgB,GAAzCjB,KAAAA,CAAyC,GAAzCA,MAAM,CAACmB,MAAM,CAACF,WAAW,CAAG,OAAO,EAAED,GAAG,CAAC,CAAC;IAC1C,OAAOA,GAAG,CAAC;AACb,CAAC"}