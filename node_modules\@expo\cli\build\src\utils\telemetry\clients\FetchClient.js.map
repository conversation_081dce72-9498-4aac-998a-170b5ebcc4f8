{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/FetchClient.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from 'node:buffer';\nimport { URL } from 'node:url';\nimport { Agent, RetryAgent, type RequestInfo, type RequestInit } from 'undici';\n\nimport { fetch } from '../../../utils/fetch';\nimport { TelemetryClient, TelemetryClientStrategy, TelemetryRecordInternal } from '../types';\nimport { TELEMETRY_ENDPOINT, TELEMETRY_TARGET } from '../utils/constants';\n\ntype FetchClientOptions = {\n  /** The fetch method for sending events, should handle retries and timeouts */\n  fetch?: typeof fetch;\n  /** The endpoint for recorded events */\n  url?: string;\n  /** The telemetry target for all events */\n  target?: string;\n};\n\ntype FetchClientEntry = Promise<void> & {\n  records: TelemetryRecordInternal[];\n  controller: AbortController;\n};\n\nexport class FetchClient implements TelemetryClient {\n  /** This client should be used for long-running commands */\n  readonly strategy: TelemetryClientStrategy = 'instant';\n  /** The fetch instance used to transport telemetry to the backend */\n  private fetch: typeof fetch;\n  /** The endpoint to send events to */\n  private url: string;\n  /** Additional headers to send with every event */\n  private headers: RequestInit['headers'];\n  /** All records that are queued and being sent */\n  private entries: Set<FetchClientEntry> = new Set();\n\n  constructor({\n    fetch = createTelemetryFetch(),\n    url = TELEMETRY_ENDPOINT,\n    target = TELEMETRY_TARGET,\n  }: FetchClientOptions = {}) {\n    this.fetch = fetch;\n    this.url = url;\n    this.headers = {\n      accept: 'application/json',\n      'content-type': 'application/json',\n      'user-agent': `expo-cli/${process.env.__EXPO_VERSION}`,\n      authorization: 'Basic ' + Buffer.from(`${target}:`).toString('base64'),\n    };\n  }\n\n  private queue(\n    records: TelemetryRecordInternal[],\n    controller: AbortController,\n    request: ReturnType<typeof fetch>\n  ) {\n    const entry: FetchClientEntry = mutePromise(request) as any;\n    entry.finally(() => this.entries.delete(entry));\n    entry.controller = controller;\n    entry.records = records;\n\n    this.entries.add(entry);\n\n    return entry;\n  }\n\n  record(record: TelemetryRecordInternal[]) {\n    const records = Array.isArray(record) ? record : [record];\n\n    if (!records.length) return;\n\n    const controller = new AbortController();\n    const body = JSON.stringify({\n      sentAt: new Date(),\n      batch: records,\n    });\n\n    return this.queue(\n      records,\n      controller,\n      this.fetch(this.url, {\n        body,\n        method: 'POST',\n        signal: controller.signal,\n        headers: this.headers,\n      })\n    );\n  }\n\n  flush() {\n    return mutePromise(Promise.all(this.entries));\n  }\n\n  abort() {\n    const records: TelemetryRecordInternal[] = [];\n\n    this.entries.forEach((entry) => {\n      try {\n        entry.controller.abort();\n        records.push(...entry.records);\n      } catch {\n        // Ignore abort errors\n      }\n    });\n\n    return records;\n  }\n}\n\nfunction createTelemetryFetch(): typeof fetch {\n  const agent = new RetryAgent(new Agent(), {\n    maxRetries: 3,\n    retryAfter: true,\n    minTimeout: 500,\n    maxTimeout: 2000,\n    timeoutFactor: 2,\n  });\n\n  return (info: RequestInfo | URL, init: RequestInit = {}) =>\n    fetch(extractUrl(info), { ...init, dispatcher: agent });\n}\n\n/** Extract the URL string from either `RequestInfo` or `URL` */\nfunction extractUrl(info: RequestInfo | URL) {\n  if (typeof info === 'string') return info;\n  if ('url' in info) return info.url;\n  return info.toString();\n}\n\n/** Mute a promise by removing the original return type and hide errors */\nfunction mutePromise(promise: Promise<any>) {\n  return promise.then(\n    () => {},\n    () => {}\n  );\n}\n"], "names": ["FetchClient", "strategy", "entries", "Set", "constructor", "fetch", "createTelemetryFetch", "url", "TELEMETRY_ENDPOINT", "target", "TELEMETRY_TARGET", "headers", "accept", "process", "env", "__EXPO_VERSION", "authorization", "<PERSON><PERSON><PERSON>", "from", "toString", "queue", "records", "controller", "request", "entry", "mutePromise", "finally", "delete", "add", "record", "Array", "isArray", "length", "AbortController", "body", "JSON", "stringify", "sentAt", "Date", "batch", "method", "signal", "flush", "Promise", "all", "abort", "for<PERSON>ach", "push", "agent", "RetryAgent", "Agent", "maxRetries", "retryAfter", "minTimeout", "maxTimeout", "timeoutFactor", "info", "init", "extractUrl", "dispatcher", "promise", "then"], "mappings": "AAAA;;;;+BAsBaA,aAAW;;aAAXA,WAAW;;;yBAtBD,aAAa;;;;;;;yBAEkC,QAAQ;;;;;;uBAExD,sBAAsB;2BAES,oBAAoB;AAgBlE,MAAMA,WAAW;IACtB,yDAAyD,GACzD,AAASC,QAAQ,GAA4B,SAAS,CAAC;IAOvD,+CAA+C,GAC/C,AAAQC,OAAO,GAA0B,IAAIC,GAAG,EAAE,CAAC;IAEnDC,YAAY,EACVC,KAAK,EAAGC,oBAAoB,EAAE,CAAA,EAC9BC,GAAG,EAAGC,UAAkB,mBAAA,CAAA,EACxBC,MAAM,EAAGC,UAAgB,iBAAA,CAAA,EACN,GAAG,EAAE,CAAE;QAC1B,IAAI,CAACL,KAAK,GAAGA,KAAK,CAAC;QACnB,IAAI,CAACE,GAAG,GAAGA,GAAG,CAAC;QACf,IAAI,CAACI,OAAO,GAAG;YACbC,MAAM,EAAE,kBAAkB;YAC1B,cAAc,EAAE,kBAAkB;YAClC,YAAY,EAAE,CAAC,SAAS,EAAEC,OAAO,CAACC,GAAG,CAACC,cAAc,CAAC,CAAC;YACtDC,aAAa,EAAE,QAAQ,GAAGC,WAAM,EAAA,OAAA,CAACC,IAAI,CAAC,CAAC,EAAET,MAAM,CAAC,CAAC,CAAC,CAAC,CAACU,QAAQ,CAAC,QAAQ,CAAC;SACvE,CAAC;IACJ;IAEQC,KAAK,CACXC,OAAkC,EAClCC,UAA2B,EAC3BC,OAAiC,EACjC;QACA,MAAMC,KAAK,GAAqBC,WAAW,CAACF,OAAO,CAAC,AAAO,AAAC;QAC5DC,KAAK,CAACE,OAAO,CAAC,IAAM,IAAI,CAACxB,OAAO,CAACyB,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC;QAChDA,KAAK,CAACF,UAAU,GAAGA,UAAU,CAAC;QAC9BE,KAAK,CAACH,OAAO,GAAGA,OAAO,CAAC;QAExB,IAAI,CAACnB,OAAO,CAAC0B,GAAG,CAACJ,KAAK,CAAC,CAAC;QAExB,OAAOA,KAAK,CAAC;IACf;IAEAK,MAAM,CAACA,MAAiC,EAAE;QACxC,MAAMR,OAAO,GAAGS,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,GAAGA,MAAM,GAAG;YAACA,MAAM;SAAC,AAAC;QAE1D,IAAI,CAACR,OAAO,CAACW,MAAM,EAAE,OAAO;QAE5B,MAAMV,UAAU,GAAG,IAAIW,eAAe,EAAE,AAAC;QACzC,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAAC;YAC1BC,MAAM,EAAE,IAAIC,IAAI,EAAE;YAClBC,KAAK,EAAElB,OAAO;SACf,CAAC,AAAC;QAEH,OAAO,IAAI,CAACD,KAAK,CACfC,OAAO,EACPC,UAAU,EACV,IAAI,CAACjB,KAAK,CAAC,IAAI,CAACE,GAAG,EAAE;YACnB2B,IAAI;YACJM,MAAM,EAAE,MAAM;YACdC,MAAM,EAAEnB,UAAU,CAACmB,MAAM;YACzB9B,OAAO,EAAE,IAAI,CAACA,OAAO;SACtB,CAAC,CACH,CAAC;IACJ;IAEA+B,KAAK,GAAG;QACN,OAAOjB,WAAW,CAACkB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1C,OAAO,CAAC,CAAC,CAAC;IAChD;IAEA2C,KAAK,GAAG;QACN,MAAMxB,OAAO,GAA8B,EAAE,AAAC;QAE9C,IAAI,CAACnB,OAAO,CAAC4C,OAAO,CAAC,CAACtB,KAAK,GAAK;YAC9B,IAAI;gBACFA,KAAK,CAACF,UAAU,CAACuB,KAAK,EAAE,CAAC;gBACzBxB,OAAO,CAAC0B,IAAI,IAAIvB,KAAK,CAACH,OAAO,CAAC,CAAC;YACjC,EAAE,OAAM;YACN,sBAAsB;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAOA,OAAO,CAAC;IACjB;CACD;AAED,SAASf,oBAAoB,GAAiB;IAC5C,MAAM0C,KAAK,GAAG,IAAIC,CAAAA,OAAU,EAAA,CAAA,WAAA,CAAC,IAAIC,CAAAA,OAAK,EAAA,CAAA,MAAA,EAAE,EAAE;QACxCC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,IAAI;QAChBC,aAAa,EAAE,CAAC;KACjB,CAAC,AAAC;IAEH,OAAO,CAACC,IAAuB,EAAEC,IAAiB,GAAG,EAAE,GACrDpD,IAAAA,MAAK,MAAA,EAACqD,UAAU,CAACF,IAAI,CAAC,EAAE;YAAE,GAAGC,IAAI;YAAEE,UAAU,EAAEX,KAAK;SAAE,CAAC,CAAC;AAC5D,CAAC;AAED,8DAA8D,GAC9D,SAASU,UAAU,CAACF,IAAuB,EAAE;IAC3C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE,OAAOA,IAAI,CAAC;IAC1C,IAAI,KAAK,IAAIA,IAAI,EAAE,OAAOA,IAAI,CAACjD,GAAG,CAAC;IACnC,OAAOiD,IAAI,CAACrC,QAAQ,EAAE,CAAC;AACzB,CAAC;AAED,wEAAwE,GACxE,SAASM,WAAW,CAACmC,OAAqB,EAAE;IAC1C,OAAOA,OAAO,CAACC,IAAI,CACjB,IAAM,CAAC,CAAC,EACR,IAAM,CAAC,CAAC,CACT,CAAC;AACJ,CAAC"}