{"version": 3, "sources": ["../../../../src/run/ios/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { XcodeConfiguration } from './XcodeBuild.types';\nimport { Command } from '../../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../../utils/args';\nimport { logCmdError } from '../../utils/errors';\n\nexport const expoRunIos: Command = async (argv) => {\n  const rawArgsMap: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--no-build-cache': Boolean,\n    '--no-install': Boolean,\n    '--no-bundler': Boolean,\n    '--configuration': String,\n    '--binary': String,\n\n    '--port': Number,\n\n    // Undocumented flag for re-bundling the app and assets for a build to try different JS code in release builds.\n    // Also updates the app.json.\n    '--unstable-rebundle': <PERSON><PERSON><PERSON>,\n    // Aliases\n    '-p': '--port',\n\n    '-h': '--help',\n  };\n  const args = assertWithOptionsArgs(rawArgsMap, {\n    argv,\n\n    permissive: true,\n  });\n\n  // '-d' -> '--device': Boolean,\n  // '--scheme': String,\n\n  if (args['--help']) {\n    printHelp(\n      `Run the iOS app binary locally`,\n      `npx expo run:ios`,\n      [\n        `--no-build-cache                 Clear the native derived data before building`,\n        `--no-install                     Skip installing dependencies`,\n        `--no-bundler                     Skip starting the Metro bundler`,\n        `--scheme [scheme]                Scheme to build`,\n        `--binary <path>                  Path to existing .app or .ipa to install.`,\n        chalk`--configuration <configuration>  Xcode configuration to use. Debug or Release. {dim Default: Debug}`,\n        `-d, --device [device]            Device name or UDID to build the app on`,\n        chalk`-p, --port <port>                Port to start the Metro bundler on. {dim Default: 8081}`,\n        `-h, --help                       Usage info`,\n      ].join('\\n'),\n      [\n        '',\n        chalk`  Build for production (unsigned) with the {bold Release} configuration:`,\n        chalk`    {dim $} npx expo run:ios --configuration Release`,\n        '',\n      ].join('\\n')\n    );\n  }\n\n  const { resolveStringOrBooleanArgsAsync } = await import('../../utils/resolveArgs.js');\n  const parsed = await resolveStringOrBooleanArgsAsync(argv ?? [], rawArgsMap, {\n    '--scheme': Boolean,\n    '--device': Boolean,\n    '-d': '--device',\n  }).catch(logCmdError);\n\n  const { runIosAsync } = await import('./runIosAsync.js');\n  return runIosAsync(path.resolve(parsed.projectRoot), {\n    // Parsed options\n    buildCache: !args['--no-build-cache'],\n    install: !args['--no-install'],\n    bundler: !args['--no-bundler'],\n    port: args['--port'],\n    binary: args['--binary'],\n    rebundle: args['--unstable-rebundle'],\n\n    // Custom parsed args\n    device: parsed.args['--device'],\n    scheme: parsed.args['--scheme'],\n    configuration: parsed.args['--configuration'] as XcodeConfiguration,\n  }).catch(logCmdError);\n};\n"], "names": ["expoRunIos", "argv", "rawArgsMap", "Boolean", "String", "Number", "args", "assertWithOptionsArgs", "permissive", "printHelp", "chalk", "join", "resolveStringOrBooleanArgsAsync", "parsed", "catch", "logCmdError", "runIosAsync", "path", "resolve", "projectRoot", "buildCache", "install", "bundler", "port", "binary", "rebundle", "device", "scheme", "configuration"], "mappings": "AAAA;;;;;+BAUaA,YAAU;;aAAVA,UAAU;;;8DARL,OAAO;;;;;;;8DACR,MAAM;;;;;;sBAI0B,kBAAkB;wBACvC,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,MAAMA,UAAU,GAAY,OAAOC,IAAI,GAAK;IACjD,MAAMC,UAAU,GAAa;QAC3B,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,kBAAkB,EAAEA,OAAO;QAC3B,cAAc,EAAEA,OAAO;QACvB,cAAc,EAAEA,OAAO;QACvB,iBAAiB,EAAEC,MAAM;QACzB,UAAU,EAAEA,MAAM;QAElB,QAAQ,EAAEC,MAAM;QAEhB,+GAA+G;QAC/G,6BAA6B;QAC7B,qBAAqB,EAAEF,OAAO;QAC9B,UAAU;QACV,IAAI,EAAE,QAAQ;QAEd,IAAI,EAAE,QAAQ;KACf,AAAC;IACF,MAAMG,IAAI,GAAGC,IAAAA,KAAqB,sBAAA,EAACL,UAAU,EAAE;QAC7CD,IAAI;QAEJO,UAAU,EAAE,IAAI;KACjB,CAAC,AAAC;IAEH,+BAA+B;IAC/B,sBAAsB;IAEtB,IAAIF,IAAI,CAAC,QAAQ,CAAC,EAAE;QAClBG,IAAAA,KAAS,UAAA,EACP,CAAC,8BAA8B,CAAC,EAChC,CAAC,gBAAgB,CAAC,EAClB;YACE,CAAC,8EAA8E,CAAC;YAChF,CAAC,6DAA6D,CAAC;YAC/D,CAAC,gEAAgE,CAAC;YAClE,CAAC,gDAAgD,CAAC;YAClD,CAAC,0EAA0E,CAAC;YAC5EC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,mGAAmG,CAAC;YAC1G,CAAC,wEAAwE,CAAC;YAC1EA,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,wFAAwF,CAAC;YAC/F,CAAC,2CAA2C,CAAC;SAC9C,CAACC,IAAI,CAAC,IAAI,CAAC,EACZ;YACE,EAAE;YACFD,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,wEAAwE,CAAC;YAC/EA,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,oDAAoD,CAAC;YAC3D,EAAE;SACH,CAACC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACJ,CAAC;IAED,MAAM,EAAEC,+BAA+B,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,4BAA4B,GAAC,AAAC;IACvF,MAAMC,MAAM,GAAG,MAAMD,+BAA+B,CAACX,IAAI,IAAI,EAAE,EAAEC,UAAU,EAAE;QAC3E,UAAU,EAAEC,OAAO;QACnB,UAAU,EAAEA,OAAO;QACnB,IAAI,EAAE,UAAU;KACjB,CAAC,CAACW,KAAK,CAACC,OAAW,YAAA,CAAC,AAAC;IAEtB,MAAM,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,kBAAkB,GAAC,AAAC;IACzD,OAAOA,WAAW,CAACC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACL,MAAM,CAACM,WAAW,CAAC,EAAE;QACnD,iBAAiB;QACjBC,UAAU,EAAE,CAACd,IAAI,CAAC,kBAAkB,CAAC;QACrCe,OAAO,EAAE,CAACf,IAAI,CAAC,cAAc,CAAC;QAC9BgB,OAAO,EAAE,CAAChB,IAAI,CAAC,cAAc,CAAC;QAC9BiB,IAAI,EAAEjB,IAAI,CAAC,QAAQ,CAAC;QACpBkB,MAAM,EAAElB,IAAI,CAAC,UAAU,CAAC;QACxBmB,QAAQ,EAAEnB,IAAI,CAAC,qBAAqB,CAAC;QAErC,qBAAqB;QACrBoB,MAAM,EAAEb,MAAM,CAACP,IAAI,CAAC,UAAU,CAAC;QAC/BqB,MAAM,EAAEd,MAAM,CAACP,IAAI,CAAC,UAAU,CAAC;QAC/BsB,aAAa,EAAEf,MAAM,CAACP,IAAI,CAAC,iBAAiB,CAAC;KAC9C,CAAC,CAACQ,KAAK,CAACC,OAAW,YAAA,CAAC,CAAC;AACxB,CAAC,AAAC"}