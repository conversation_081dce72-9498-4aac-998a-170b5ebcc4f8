{"version": 3, "sources": ["../../../../../src/start/server/webpack/compile.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { promisify } from 'util';\nimport type webpack from 'webpack';\n\nimport { formatWebpackMessages } from './formatWebpackMessages';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\n\n/** Run the `webpack` compiler and format errors/warnings. */\nexport async function compileAsync(compiler: webpack.Compiler) {\n  const stats = await promisify(compiler.run.bind(compiler))();\n  const { errors, warnings } = formatWebpackMessages(\n    stats.toJson({ all: false, warnings: true, errors: true })\n  );\n  if (errors?.length) {\n    // Only keep the first error. Others are often indicative\n    // of the same problem, but confuse the reader with noise.\n    if (errors.length > 1) {\n      errors.length = 1;\n    }\n    throw new CommandError('WEBPACK_BUNDLE', errors.join('\\n\\n'));\n  }\n  if (warnings?.length) {\n    Log.warn(chalk.yellow('Compiled with warnings\\n'));\n    Log.warn(warnings.join('\\n\\n'));\n  } else {\n    Log.log(chalk.green('Compiled successfully'));\n  }\n\n  return { errors, warnings };\n}\n"], "names": ["compileAsync", "compiler", "stats", "promisify", "run", "bind", "errors", "warnings", "formatWebpackMessages", "to<PERSON><PERSON>", "all", "length", "CommandError", "join", "Log", "warn", "chalk", "yellow", "log", "green"], "mappings": "AAAA;;;;+BASsBA,cAAY;;aAAZA,YAAY;;;8DAThB,OAAO;;;;;;;yBACC,MAAM;;;;;;uCAGM,yBAAyB;2DAC1C,cAAc;wBACN,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7C,eAAeA,YAAY,CAACC,QAA0B,EAAE;IAC7D,MAAMC,KAAK,GAAG,MAAMC,IAAAA,KAAS,EAAA,UAAA,EAACF,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC,EAAE,AAAC;IAC7D,MAAM,EAAEK,MAAM,CAAA,EAAEC,QAAQ,CAAA,EAAE,GAAGC,IAAAA,sBAAqB,sBAAA,EAChDN,KAAK,CAACO,MAAM,CAAC;QAAEC,GAAG,EAAE,KAAK;QAAEH,QAAQ,EAAE,IAAI;QAAED,MAAM,EAAE,IAAI;KAAE,CAAC,CAC3D,AAAC;IACF,IAAIA,MAAM,QAAQ,GAAdA,KAAAA,CAAc,GAAdA,MAAM,CAAEK,MAAM,EAAE;QAClB,yDAAyD;QACzD,0DAA0D;QAC1D,IAAIL,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;YACrBL,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC;QACpB,CAAC;QACD,MAAM,IAAIC,OAAY,aAAA,CAAC,gBAAgB,EAAEN,MAAM,CAACO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IACD,IAAIN,QAAQ,QAAQ,GAAhBA,KAAAA,CAAgB,GAAhBA,QAAQ,CAAEI,MAAM,EAAE;QACpBG,IAAG,CAACC,IAAI,CAACC,MAAK,EAAA,QAAA,CAACC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;QACnDH,IAAG,CAACC,IAAI,CAACR,QAAQ,CAACM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAClC,OAAO;QACLC,IAAG,CAACI,GAAG,CAACF,MAAK,EAAA,QAAA,CAACG,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,OAAO;QAAEb,MAAM;QAAEC,QAAQ;KAAE,CAAC;AAC9B,CAAC"}