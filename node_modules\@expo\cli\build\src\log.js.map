{"version": 3, "sources": ["../../src/log.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nexport function time(label?: string): void {\n  console.time(label);\n}\n\nexport function timeEnd(label?: string): void {\n  console.timeEnd(label);\n}\n\nexport function error(...message: string[]): void {\n  console.error(...message);\n}\n\n/** Print an error and provide additional info (the stack trace) in debug mode. */\nexport function exception(e: Error): void {\n  const { env } = require('./utils/env');\n  error(chalk.red(e.toString()) + (env.EXPO_DEBUG ? '\\n' + chalk.gray(e.stack) : ''));\n}\n\nexport function warn(...message: string[]): void {\n  console.warn(...message.map((value) => chalk.yellow(value)));\n}\n\nexport function log(...message: string[]): void {\n  console.log(...message);\n}\n\n/** @deprecated use `debug` package with the `expo:` prefix instead.  */\nexport function debug(...message: any[]): void {\n  if (require('./utils/env').env.EXPO_DEBUG) console.log(...message);\n}\n\n/** Clear the terminal of all text. */\nexport function clear(): void {\n  process.stdout.write(process.platform === 'win32' ? '\\x1B[2J\\x1B[0f' : '\\x1B[2J\\x1B[3J\\x1B[H');\n}\n\n/** Log a message and exit the current process. If the `code` is non-zero then `console.error` will be used instead of `console.log`. */\nexport function exit(message: string | Error, code: number = 1): never {\n  if (message instanceof Error) {\n    exception(message);\n    process.exit(code);\n  }\n\n  if (message) {\n    if (code === 0) {\n      log(message);\n    } else {\n      error(message);\n    }\n  }\n\n  process.exit(code);\n}\n\n// The re-export makes auto importing easier.\nexport const Log = {\n  time,\n  timeEnd,\n  error,\n  exception,\n  warn,\n  log,\n  debug,\n  clear,\n  exit,\n};\n"], "names": ["time", "timeEnd", "error", "exception", "warn", "log", "debug", "clear", "exit", "Log", "label", "console", "message", "e", "env", "require", "chalk", "red", "toString", "EXPO_DEBUG", "gray", "stack", "map", "value", "yellow", "process", "stdout", "write", "platform", "code", "Error"], "mappings": "AAAA;;;;;;;;;;;IAEgBA,IAAI,MAAJA,IAAI;IAIJC,OAAO,MAAPA,OAAO;IAIPC,KAAK,MAALA,KAAK;IAKLC,SAAS,MAATA,SAAS;IAKTC,IAAI,MAAJA,IAAI;IAIJC,GAAG,MAAHA,GAAG;IAKHC,KAAK,MAALA,KAAK;IAKLC,KAAK,MAALA,KAAK;IAKLC,IAAI,MAAJA,IAAI;IAkBPC,GAAG,MAAHA,GAAG;;;8DAzDE,OAAO;;;;;;;;;;;AAElB,SAAST,IAAI,CAACU,KAAc,EAAQ;IACzCC,OAAO,CAACX,IAAI,CAACU,KAAK,CAAC,CAAC;AACtB,CAAC;AAEM,SAAST,OAAO,CAACS,KAAc,EAAQ;IAC5CC,OAAO,CAACV,OAAO,CAACS,KAAK,CAAC,CAAC;AACzB,CAAC;AAEM,SAASR,KAAK,CAAC,GAAGU,OAAO,AAAU,EAAQ;IAChDD,OAAO,CAACT,KAAK,IAAIU,OAAO,CAAC,CAAC;AAC5B,CAAC;AAGM,SAAST,SAAS,CAACU,CAAQ,EAAQ;IACxC,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGC,OAAO,CAAC,aAAa,CAAC,AAAC;IACvCb,KAAK,CAACc,MAAK,EAAA,QAAA,CAACC,GAAG,CAACJ,CAAC,CAACK,QAAQ,EAAE,CAAC,GAAG,CAACJ,GAAG,CAACK,UAAU,GAAG,IAAI,GAAGH,MAAK,EAAA,QAAA,CAACI,IAAI,CAACP,CAAC,CAACQ,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACtF,CAAC;AAEM,SAASjB,IAAI,CAAC,GAAGQ,OAAO,AAAU,EAAQ;IAC/CD,OAAO,CAACP,IAAI,IAAIQ,OAAO,CAACU,GAAG,CAAC,CAACC,KAAK,GAAKP,MAAK,EAAA,QAAA,CAACQ,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;AAEM,SAASlB,GAAG,CAAC,GAAGO,OAAO,AAAU,EAAQ;IAC9CD,OAAO,CAACN,GAAG,IAAIO,OAAO,CAAC,CAAC;AAC1B,CAAC;AAGM,SAASN,KAAK,CAAC,GAAGM,OAAO,AAAO,EAAQ;IAC7C,IAAIG,OAAO,CAAC,aAAa,CAAC,CAACD,GAAG,CAACK,UAAU,EAAER,OAAO,CAACN,GAAG,IAAIO,OAAO,CAAC,CAAC;AACrE,CAAC;AAGM,SAASL,KAAK,GAAS;IAC5BkB,OAAO,CAACC,MAAM,CAACC,KAAK,CAACF,OAAO,CAACG,QAAQ,KAAK,OAAO,GAAG,gBAAgB,GAAG,sBAAsB,CAAC,CAAC;AACjG,CAAC;AAGM,SAASpB,IAAI,CAACI,OAAuB,EAAEiB,IAAY,GAAG,CAAC,EAAS;IACrE,IAAIjB,OAAO,YAAYkB,KAAK,EAAE;QAC5B3B,SAAS,CAACS,OAAO,CAAC,CAAC;QACnBa,OAAO,CAACjB,IAAI,CAACqB,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,IAAIjB,OAAO,EAAE;QACX,IAAIiB,IAAI,KAAK,CAAC,EAAE;YACdxB,GAAG,CAACO,OAAO,CAAC,CAAC;QACf,OAAO;YACLV,KAAK,CAACU,OAAO,CAAC,CAAC;QACjB,CAAC;IACH,CAAC;IAEDa,OAAO,CAACjB,IAAI,CAACqB,IAAI,CAAC,CAAC;AACrB,CAAC;AAGM,MAAMpB,GAAG,GAAG;IACjBT,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,SAAS;IACTC,IAAI;IACJC,GAAG;IACHC,KAAK;IACLC,KAAK;IACLC,IAAI;CACL,AAAC"}