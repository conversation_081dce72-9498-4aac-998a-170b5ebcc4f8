{"version": 3, "sources": ["../../../src/utils/validateApplicationId.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { env } from './env';\nimport { memoize } from './fn';\nimport { learnMore } from './link';\nimport { isUrlAvailableAsync } from './url';\nimport { Log } from '../log';\n\nconst debug = require('debug')('expo:utils:validateApplicationId') as typeof console.log;\n\n// TODO: Adjust to indicate that the bundle identifier must start with a letter, period, or hyphen.\nconst IOS_BUNDLE_ID_REGEX = /^[a-zA-Z0-9-.]+$/;\nconst ANDROID_PACKAGE_REGEX = /^[a-zA-Z][a-zA-Z0-9_]*(\\.[a-zA-Z][a-zA-Z0-9_]*)+$/;\n\n/** Validate an iOS bundle identifier. */\nexport function validateBundleId(value: string): boolean {\n  return IOS_BUNDLE_ID_REGEX.test(value);\n}\n\n/** Validate an Android package name. */\nexport function validatePackage(value: string): boolean {\n  return validatePackageWithWarning(value) === true;\n}\n\n/** Validate an Android package name and return the reason if invalid. */\nexport function validatePackageWithWarning(value: string): true | string {\n  const parts = value.split('.');\n  for (const segment of parts) {\n    if (RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS.includes(segment)) {\n      return `\"${segment}\" is a reserved Java keyword.`;\n    }\n  }\n  if (parts.length < 2) {\n    return `Package name must contain more than one segment, separated by \".\", e.g. com.${value}`;\n  }\n  if (!ANDROID_PACKAGE_REGEX.test(value)) {\n    return 'Invalid characters in Android package name. Only alphanumeric characters, \".\" and \"_\" are allowed, and each segment start with a letter.';\n  }\n\n  return true;\n}\n\nexport function getSanitizedPackage(value: string) {\n  // It's common to use dashes in your node project name, strip them from the suggested package name.\n  let output = value\n    // Oracle recommends package names are \"legalized\" by converting hyphen to an underscore and removing unsupported characters\n    // https://docs.oracle.com/javase/tutorial/java/package/namingpkgs.html\n    // However, life is much nicer when the bundle identifier and package name are the same and iOS has the inverse rule— converting underscores to hyphens.\n    // So we'll simply remove hyphens and illegal characters for Android.\n    .replace(/[^a-zA-Z0-9_.]/g, '')\n    // Prevent multiple '.' in a row (e.g. no zero-length segments).\n    .replace(/\\.+/g, '.')\n    // Prevent '.' from the start or end.\n    .replace(/^\\.|\\.$/g, '');\n\n  output ||= 'app';\n\n  // Prepend extra segments\n  let segments = output.split('.').length;\n  while (segments < 2) {\n    output = `com.${output}`;\n    segments += 1;\n  }\n\n  // Ensure each dot has a letter or number after it\n  output = output\n    .split('.')\n    .map((segment) => {\n      segment = /^[a-zA-Z]/.test(segment) ? segment : 'x' + segment;\n\n      if (RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS.includes(segment)) {\n        segment = 'x' + segment;\n      }\n      return segment;\n    })\n    .join('.');\n\n  return output;\n}\n\nexport function getSanitizedBundleIdentifier(value: string) {\n  // According to the behavior observed when using the UI in Xcode.\n  // Must start with a letter, period, or hyphen (not number).\n  // Can only contain alphanumeric characters, periods, and hyphens.\n  // Can have empty segments (e.g. com.example..app).\n  return value.replace(/(^[^a-zA-Z.-]|[^a-zA-Z0-9-.])/g, '-');\n}\n\n// https://en.wikipedia.org/wiki/List_of_Java_keywords\n// Running the following in the console and pruning the \"Reserved Identifiers\" section:\n// [...document.querySelectorAll('dl > dt > code')].map(node => node.innerText)\nconst RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS = [\n  // List of Java keywords\n  '_',\n  'abstract',\n  'assert',\n  'boolean',\n  'break',\n  'byte',\n  'case',\n  'catch',\n  'char',\n  'class',\n  'const',\n  'continue',\n  'default',\n  'do',\n  'double',\n  'else',\n  'enum',\n  'extends',\n  'final',\n  'finally',\n  'float',\n  'for',\n  'goto',\n  'if',\n  'implements',\n  'import',\n  'instanceof',\n  'int',\n  'interface',\n  'long',\n  'native',\n  'new',\n  'package',\n  'private',\n  'protected',\n  'public',\n  'return',\n  'short',\n  'static',\n  'super',\n  'switch',\n  'synchronized',\n  'this',\n  'throw',\n  'throws',\n  'transient',\n  'try',\n  'void',\n  'volatile',\n  'while',\n  // Reserved words for literal values\n  'true',\n  'false',\n  'null',\n  // Unused\n  'const',\n  'goto',\n  'strictfp',\n];\n\nexport function assertValidBundleId(value: string) {\n  assert.match(\n    value,\n    IOS_BUNDLE_ID_REGEX,\n    `The ios.bundleIdentifier defined in your Expo config is not formatted properly. Only alphanumeric characters, '.', '-', and '_' are allowed, and each '.' must be followed by a letter.`\n  );\n}\n\nexport function assertValidPackage(value: string) {\n  assert.match(\n    value,\n    ANDROID_PACKAGE_REGEX,\n    `Invalid format of Android package name. Only alphanumeric characters, '.' and '_' are allowed, and each '.' must be followed by a letter. Reserved Java keywords are not allowed.`\n  );\n}\n\n/** @private */\nexport async function getBundleIdWarningInternalAsync(bundleId: string): Promise<string | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping Apple bundle identifier reservation validation in offline-mode.');\n    return null;\n  }\n\n  if (!(await isUrlAvailableAsync('itunes.apple.com'))) {\n    debug(\n      `Couldn't connect to iTunes Store to check bundle ID ${bundleId}. itunes.apple.com may be down.`\n    );\n    // If no network, simply skip the warnings since they'll just lead to more confusion.\n    return null;\n  }\n\n  const url = `http://itunes.apple.com/lookup?bundleId=${bundleId}`;\n  try {\n    debug(`Checking iOS bundle ID '${bundleId}' at: ${url}`);\n    const response = await fetch(url);\n    const json: any = await response.json();\n    if (json.resultCount > 0) {\n      const firstApp = json.results[0];\n      return formatInUseWarning(firstApp.trackName, firstApp.sellerName, bundleId);\n    }\n  } catch (error: any) {\n    debug(`Error checking bundle ID ${bundleId}: ${error.message}`);\n    // Error fetching itunes data.\n  }\n  return null;\n}\n\n/** Returns a warning message if an iOS bundle identifier is potentially already in use. */\nexport const getBundleIdWarningAsync = memoize(getBundleIdWarningInternalAsync);\n\n/** @private */\nexport async function getPackageNameWarningInternalAsync(\n  packageName: string\n): Promise<string | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping Android package name reservation validation in offline-mode.');\n    return null;\n  }\n\n  if (!(await isUrlAvailableAsync('play.google.com'))) {\n    debug(\n      `Couldn't connect to Play Store to check package name ${packageName}. play.google.com may be down.`\n    );\n    // If no network, simply skip the warnings since they'll just lead to more confusion.\n    return null;\n  }\n\n  const url = `https://play.google.com/store/apps/details?id=${packageName}`;\n  try {\n    debug(`Checking Android package name '${packageName}' at: ${url}`);\n    const response = await fetch(url);\n    // If the page exists, then warn the user.\n    if (response.status === 200) {\n      // There is no JSON API for the Play Store so we can't concisely\n      // locate the app name and developer to match the iOS warning.\n      return `⚠️  The package ${chalk.bold(packageName)} is already in use. ${chalk.dim(\n        learnMore(url)\n      )}`;\n    }\n  } catch (error: any) {\n    // Error fetching play store data or the page doesn't exist.\n    debug(`Error checking package name ${packageName}: ${error.message}`);\n  }\n  return null;\n}\n\nfunction formatInUseWarning(appName: string, author: string, id: string): string {\n  return `⚠️  The app ${chalk.bold(appName)} by ${chalk.italic(\n    author\n  )} is already using ${chalk.bold(id)}`;\n}\n\n/** Returns a warning message if an Android package name is potentially already in use. */\nexport const getPackageNameWarningAsync = memoize(getPackageNameWarningInternalAsync);\n"], "names": ["validateBundleId", "validatePackage", "validatePackageWithWarning", "getSanitizedPackage", "getSanitizedBundleIdentifier", "assertValidBundleId", "assertValidPackage", "getBundleIdWarningInternalAsync", "getBundleIdWarningAsync", "getPackageNameWarningInternalAsync", "getPackageNameWarningAsync", "debug", "require", "IOS_BUNDLE_ID_REGEX", "ANDROID_PACKAGE_REGEX", "value", "test", "parts", "split", "segment", "RESERVED_ANDROID_PACKAGE_NAME_SEGMENTS", "includes", "length", "output", "replace", "segments", "map", "join", "assert", "match", "bundleId", "env", "EXPO_OFFLINE", "Log", "warn", "isUrlAvailableAsync", "url", "response", "fetch", "json", "resultCount", "firstApp", "results", "formatInUseWarning", "trackName", "sellerName", "error", "message", "memoize", "packageName", "status", "chalk", "bold", "dim", "learnMore", "appName", "author", "id", "italic"], "mappings": "AAAA;;;;;;;;;;;IAgBgBA,gBAAgB,MAAhBA,gBAAgB;IAKhBC,eAAe,MAAfA,eAAe;IAKfC,0BAA0B,MAA1BA,0BAA0B;IAiB1BC,mBAAmB,MAAnBA,mBAAmB;IAsCnBC,4BAA4B,MAA5BA,4BAA4B;IAyE5BC,mBAAmB,MAAnBA,mBAAmB;IAQnBC,kBAAkB,MAAlBA,kBAAkB;IASZC,+BAA+B,MAA/BA,+BAA+B;IA+BxCC,uBAAuB,MAAvBA,uBAAuB;IAGdC,kCAAkC,MAAlCA,kCAAkC;IA0C3CC,0BAA0B,MAA1BA,0BAA0B;;;8DAvPpB,QAAQ;;;;;;;8DACT,OAAO;;;;;;qBAEL,OAAO;oBACH,MAAM;sBACJ,QAAQ;qBACE,OAAO;qBACvB,QAAQ;;;;;;AAE5B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kCAAkC,CAAC,AAAsB,AAAC;AAEzF,mGAAmG;AACnG,MAAMC,mBAAmB,qBAAqB,AAAC;AAC/C,MAAMC,qBAAqB,sDAAsD,AAAC;AAG3E,SAASd,gBAAgB,CAACe,KAAa,EAAW;IACvD,OAAOF,mBAAmB,CAACG,IAAI,CAACD,KAAK,CAAC,CAAC;AACzC,CAAC;AAGM,SAASd,eAAe,CAACc,KAAa,EAAW;IACtD,OAAOb,0BAA0B,CAACa,KAAK,CAAC,KAAK,IAAI,CAAC;AACpD,CAAC;AAGM,SAASb,0BAA0B,CAACa,KAAa,EAAiB;IACvE,MAAME,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,AAAC;IAC/B,KAAK,MAAMC,OAAO,IAAIF,KAAK,CAAE;QAC3B,IAAIG,sCAAsC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YAC5D,OAAO,CAAC,CAAC,EAAEA,OAAO,CAAC,6BAA6B,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IACD,IAAIF,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;QACpB,OAAO,CAAC,4EAA4E,EAAEP,KAAK,CAAC,CAAC,CAAC;IAChG,CAAC;IACD,IAAI,CAACD,qBAAqB,CAACE,IAAI,CAACD,KAAK,CAAC,EAAE;QACtC,OAAO,0IAA0I,CAAC;IACpJ,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAASZ,mBAAmB,CAACY,KAAa,EAAE;IACjD,mGAAmG;IACnG,IAAIQ,MAAM,GAAGR,KAAK,AAChB,4HAA4H;IAC5H,uEAAuE;IACvE,wJAAwJ;IACxJ,qEAAqE;KACpES,OAAO,oBAAoB,EAAE,CAAC,AAC/B,gEAAgE;KAC/DA,OAAO,SAAS,GAAG,CAAC,AACrB,qCAAqC;KACpCA,OAAO,aAAa,EAAE,CAAC,AAAC;IAE3BD,MAAM,KAAK,KAAK,CAAC;IAEjB,yBAAyB;IACzB,IAAIE,QAAQ,GAAGF,MAAM,CAACL,KAAK,CAAC,GAAG,CAAC,CAACI,MAAM,AAAC;IACxC,MAAOG,QAAQ,GAAG,CAAC,CAAE;QACnBF,MAAM,GAAG,CAAC,IAAI,EAAEA,MAAM,CAAC,CAAC,CAAC;QACzBE,QAAQ,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,kDAAkD;IAClDF,MAAM,GAAGA,MAAM,CACZL,KAAK,CAAC,GAAG,CAAC,CACVQ,GAAG,CAAC,CAACP,OAAO,GAAK;QAChBA,OAAO,GAAG,YAAYH,IAAI,CAACG,OAAO,CAAC,GAAGA,OAAO,GAAG,GAAG,GAAGA,OAAO,CAAC;QAE9D,IAAIC,sCAAsC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;YAC5DA,OAAO,GAAG,GAAG,GAAGA,OAAO,CAAC;QAC1B,CAAC;QACD,OAAOA,OAAO,CAAC;IACjB,CAAC,CAAC,CACDQ,IAAI,CAAC,GAAG,CAAC,CAAC;IAEb,OAAOJ,MAAM,CAAC;AAChB,CAAC;AAEM,SAASnB,4BAA4B,CAACW,KAAa,EAAE;IAC1D,iEAAiE;IACjE,4DAA4D;IAC5D,kEAAkE;IAClE,mDAAmD;IACnD,OAAOA,KAAK,CAACS,OAAO,mCAAmC,GAAG,CAAC,CAAC;AAC9D,CAAC;AAED,sDAAsD;AACtD,uFAAuF;AACvF,+EAA+E;AAC/E,MAAMJ,sCAAsC,GAAG;IAC7C,wBAAwB;IACxB,GAAG;IACH,UAAU;IACV,QAAQ;IACR,SAAS;IACT,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,MAAM;IACN,MAAM;IACN,SAAS;IACT,OAAO;IACP,SAAS;IACT,OAAO;IACP,KAAK;IACL,MAAM;IACN,IAAI;IACJ,YAAY;IACZ,QAAQ;IACR,YAAY;IACZ,KAAK;IACL,WAAW;IACX,MAAM;IACN,QAAQ;IACR,KAAK;IACL,SAAS;IACT,SAAS;IACT,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,cAAc;IACd,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;IACX,KAAK;IACL,MAAM;IACN,UAAU;IACV,OAAO;IACP,oCAAoC;IACpC,MAAM;IACN,OAAO;IACP,MAAM;IACN,SAAS;IACT,OAAO;IACP,MAAM;IACN,UAAU;CACX,AAAC;AAEK,SAASf,mBAAmB,CAACU,KAAa,EAAE;IACjDa,OAAM,EAAA,QAAA,CAACC,KAAK,CACVd,KAAK,EACLF,mBAAmB,EACnB,CAAC,uLAAuL,CAAC,CAC1L,CAAC;AACJ,CAAC;AAEM,SAASP,kBAAkB,CAACS,KAAa,EAAE;IAChDa,OAAM,EAAA,QAAA,CAACC,KAAK,CACVd,KAAK,EACLD,qBAAqB,EACrB,CAAC,iLAAiL,CAAC,CACpL,CAAC;AACJ,CAAC;AAGM,eAAeP,+BAA+B,CAACuB,QAAgB,EAA0B;IAC9F,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;QACpBC,IAAG,IAAA,CAACC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAAE,MAAMC,IAAAA,IAAmB,oBAAA,EAAC,kBAAkB,CAAC,AAAC,EAAE;QACpDxB,KAAK,CACH,CAAC,oDAAoD,EAAEmB,QAAQ,CAAC,+BAA+B,CAAC,CACjG,CAAC;QACF,qFAAqF;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMM,GAAG,GAAG,CAAC,wCAAwC,EAAEN,QAAQ,CAAC,CAAC,AAAC;IAClE,IAAI;QACFnB,KAAK,CAAC,CAAC,wBAAwB,EAAEmB,QAAQ,CAAC,MAAM,EAAEM,GAAG,CAAC,CAAC,CAAC,CAAC;QACzD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,CAAC,AAAC;QAClC,MAAMG,IAAI,GAAQ,MAAMF,QAAQ,CAACE,IAAI,EAAE,AAAC;QACxC,IAAIA,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE;YACxB,MAAMC,QAAQ,GAAGF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,AAAC;YACjC,OAAOC,kBAAkB,CAACF,QAAQ,CAACG,SAAS,EAAEH,QAAQ,CAACI,UAAU,EAAEf,QAAQ,CAAC,CAAC;QAC/E,CAAC;IACH,EAAE,OAAOgB,KAAK,EAAO;QACnBnC,KAAK,CAAC,CAAC,yBAAyB,EAAEmB,QAAQ,CAAC,EAAE,EAAEgB,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAChE,8BAA8B;IAChC,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAGM,MAAMvC,uBAAuB,GAAGwC,IAAAA,GAAO,QAAA,EAACzC,+BAA+B,CAAC,AAAC;AAGzE,eAAeE,kCAAkC,CACtDwC,WAAmB,EACK;IACxB,IAAIlB,IAAG,IAAA,CAACC,YAAY,EAAE;QACpBC,IAAG,IAAA,CAACC,IAAI,CAAC,uEAAuE,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAAE,MAAMC,IAAAA,IAAmB,oBAAA,EAAC,iBAAiB,CAAC,AAAC,EAAE;QACnDxB,KAAK,CACH,CAAC,qDAAqD,EAAEsC,WAAW,CAAC,8BAA8B,CAAC,CACpG,CAAC;QACF,qFAAqF;QACrF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMb,GAAG,GAAG,CAAC,8CAA8C,EAAEa,WAAW,CAAC,CAAC,AAAC;IAC3E,IAAI;QACFtC,KAAK,CAAC,CAAC,+BAA+B,EAAEsC,WAAW,CAAC,MAAM,EAAEb,GAAG,CAAC,CAAC,CAAC,CAAC;QACnE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,CAAC,AAAC;QAClC,0CAA0C;QAC1C,IAAIC,QAAQ,CAACa,MAAM,KAAK,GAAG,EAAE;YAC3B,gEAAgE;YAChE,8DAA8D;YAC9D,OAAO,CAAC,gBAAgB,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAACH,WAAW,CAAC,CAAC,oBAAoB,EAAEE,MAAK,EAAA,QAAA,CAACE,GAAG,CAC/EC,IAAAA,KAAS,UAAA,EAAClB,GAAG,CAAC,CACf,CAAC,CAAC,CAAC;QACN,CAAC;IACH,EAAE,OAAOU,KAAK,EAAO;QACnB,4DAA4D;QAC5DnC,KAAK,CAAC,CAAC,4BAA4B,EAAEsC,WAAW,CAAC,EAAE,EAAEH,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAASJ,kBAAkB,CAACY,OAAe,EAAEC,MAAc,EAAEC,EAAU,EAAU;IAC/E,OAAO,CAAC,YAAY,EAAEN,MAAK,EAAA,QAAA,CAACC,IAAI,CAACG,OAAO,CAAC,CAAC,IAAI,EAAEJ,MAAK,EAAA,QAAA,CAACO,MAAM,CAC1DF,MAAM,CACP,CAAC,kBAAkB,EAAEL,MAAK,EAAA,QAAA,CAACC,IAAI,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC;AACzC,CAAC;AAGM,MAAM/C,0BAA0B,GAAGsC,IAAAA,GAAO,QAAA,EAACvC,kCAAkC,CAAC,AAAC"}