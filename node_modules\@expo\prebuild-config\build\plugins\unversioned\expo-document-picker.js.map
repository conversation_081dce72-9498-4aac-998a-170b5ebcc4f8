{"version": 3, "file": "expo-document-picker.js", "names": ["_configPlugins", "data", "require", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "config", "ios", "usesIcloudStorage", "WarningAggregator", "addWarningIOS"], "sources": ["../../../src/plugins/unversioned/expo-document-picker.ts"], "sourcesContent": ["import { WarningAggregator } from '@expo/config-plugins';\n\nimport { createLegacyPlugin } from './createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-document-picker',\n  fallback(config) {\n    if (config.ios?.usesIcloudStorage) {\n      WarningAggregator.addWarningIOS(\n        'ios.usesIcloudStorage',\n        'Install expo-document-picker to enable the ios.usesIcloudStorage feature'\n        // TODO: add a link to a docs page with more information on how to do this\n      );\n    }\n    return config;\n  },\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,oBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE3C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,sBAAsB;EACnCC,QAAQA,CAACC,MAAM,EAAE;IACf,IAAIA,MAAM,CAACC,GAAG,EAAEC,iBAAiB,EAAE;MACjCC,kCAAiB,CAACC,aAAa,CAC7B,uBAAuB,EACvB;MACA;MACF,CAAC;IACH;IACA,OAAOJ,MAAM;EACf;AACF,CAAC,CAAC", "ignoreList": []}