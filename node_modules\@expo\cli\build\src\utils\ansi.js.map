{"version": 3, "sources": ["../../../src/utils/ansi.ts"], "sourcesContent": ["/** Remove ansi characters from a string and return the sanitized results. */\nexport function stripAnsi(str?: string | null) {\n  if (!str) {\n    return str;\n  }\n  const pattern = [\n    '[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)',\n    '(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))',\n  ].join('|');\n\n  return str.replace(new RegExp(pattern, 'g'), '');\n}\n"], "names": ["stripAnsi", "str", "pattern", "join", "replace", "RegExp"], "mappings": "AAAA,2EAA2E,GAC3E;;;;+BAAg<PERSON>,WAAS;;aAATA,SAAS;;AAAlB,SAASA,SAAS,CAACC,GAAmB,EAAE;IAC7C,IAAI,CAACA,GAAG,EAAE;QACR,OAAOA,GAAG,CAAC;IACb,CAAC;IACD,MAAMC,OAAO,GAAG;QACd,8HAA8H;QAC9H,0DAA0D;KAC3D,CAACC,IAAI,CAAC,GAAG,CAAC,AAAC;IAEZ,OAAOF,GAAG,CAACG,OAAO,CAAC,IAAIC,MAAM,CAACH,OAAO,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AACnD,CAAC"}