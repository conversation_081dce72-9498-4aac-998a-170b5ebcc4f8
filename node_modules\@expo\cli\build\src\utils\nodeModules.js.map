{"version": 3, "sources": ["../../../src/utils/nodeModules.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { logNewSection } from './ora';\n\nexport async function clearNodeModulesAsync(projectRoot: string) {\n  // This step can take a couple seconds, if the installation logs are enabled (with EXPO_DEBUG) then it\n  // ends up looking odd to see \"Installing JavaScript dependencies\" for ~5 seconds before the logs start showing up.\n  const cleanJsDepsStep = logNewSection('Cleaning JavaScript dependencies');\n  const time = Date.now();\n  // nuke the node modules\n  // TODO: this is substantially slower, we should find a better alternative to ensuring the modules are installed.\n  await fs.promises.rm(path.join(projectRoot, 'node_modules'), { recursive: true, force: true });\n  cleanJsDepsStep.succeed(\n    `Cleaned JavaScript dependencies ${chalk.gray(Date.now() - time + 'ms')}`\n  );\n}\n"], "names": ["clearNodeModulesAsync", "projectRoot", "cleanJsDepsStep", "logNewSection", "time", "Date", "now", "fs", "promises", "rm", "path", "join", "recursive", "force", "succeed", "chalk", "gray"], "mappings": "AAAA;;;;+BA<PERSON><PERSON>,uBAAqB;;aAArBA,qBAAqB;;;8DANzB,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DACF,MAAM;;;;;;qBAEO,OAAO;;;;;;AAE9B,eAAeA,qBAAqB,CAACC,WAAmB,EAAE;IAC/D,sGAAsG;IACtG,mHAAmH;IACnH,MAAMC,eAAe,GAAGC,IAAAA,IAAa,cAAA,EAAC,kCAAkC,CAAC,AAAC;IAC1E,MAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IACxB,wBAAwB;IACxB,iHAAiH;IACjH,MAAMC,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,EAAE,CAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACV,WAAW,EAAE,cAAc,CAAC,EAAE;QAAEW,SAAS,EAAE,IAAI;QAAEC,KAAK,EAAE,IAAI;KAAE,CAAC,CAAC;IAC/FX,eAAe,CAACY,OAAO,CACrB,CAAC,gCAAgC,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAACX,IAAI,CAACC,GAAG,EAAE,GAAGF,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC"}