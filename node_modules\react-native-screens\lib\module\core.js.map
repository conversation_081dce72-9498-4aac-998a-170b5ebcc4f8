{"version": 3, "names": ["Platform", "UIManager", "isNativePlatformSupported", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "screensEnabled", "freezeEnabled"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAElD,OAAO,MAAMC,yBAAyB,GACpCF,QAAQ,CAACG,EAAE,KAAK,KAAK,IACrBH,QAAQ,CAACG,EAAE,KAAK,SAAS,IACzBH,QAAQ,CAACG,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGF,yBAAyB;AAE9C,OAAO,SAASG,aAAaA,CAACC,mBAAmB,GAAG,IAAI,EAAE;EACxDF,cAAc,GAAGE,mBAAmB;EAEpC,IAAI,CAACJ,yBAAyB,EAAE;IAC9B;EACF;EAEA,IAAIE,cAAc,IAAI,CAACH,SAAS,CAACM,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACX,wGACF,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAEzB,OAAO,SAASC,YAAYA,CAACC,uBAAuB,GAAG,IAAI,EAAE;EAC3D,IAAI,CAACV,yBAAyB,EAAE;IAC9B;EACF;EAEAQ,aAAa,GAAGE,uBAAuB;AACzC;AAEA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOT,cAAc;AACvB;AAEA,OAAO,SAASU,aAAaA,CAAA,EAAG;EAC9B,OAAOJ,aAAa;AACtB", "ignoreList": []}