export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image: string;
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PREPARING = 'PREPARING',
  READY_FOR_PICKUP = 'READY_FOR_PICKUP',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED'
}

export interface Order {
  id: string;
  items: Array<{
    productId: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  customer: {
    name: string;
    address: string;
    phone: string;
  };
  total: number;
  status: OrderStatus;
  deliveryMethod: 'DELIVERY' | 'PICKUP';
  createdAt: string;
}

export type UserRole = 'STORE' | 'DELIVERY';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
}