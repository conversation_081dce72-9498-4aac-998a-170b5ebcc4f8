{"version": 3, "sources": ["../../../../../src/start/server/metro/TerminalReporter.types.ts"], "sourcesContent": ["import type { ReportableEvent } from 'metro';\nimport type { TerminalReportableEvent } from 'metro/src/lib/TerminalReporter';\nimport type { Terminal } from 'metro-core';\nimport type { WatcherStatus } from 'metro-file-map';\n\nimport { MetroEnvironment } from '../middleware/metroOptions';\n\nexport type GlobalCacheDisabledReason = 'too_many_errors' | 'too_many_misses';\n\nexport type BundleDetails = {\n  buildID?: string;\n  bundleType: string;\n  dev: boolean;\n  entryFile: string;\n  minify: boolean;\n  platform: string | null | undefined;\n  customTransformOptions?: { environment?: MetroEnvironment; dom?: string };\n  runtimeBytecodeVersion: number | null | undefined;\n};\n\nexport type BundleProgress = {\n  bundleDetails: BundleDetails;\n  transformedFileCount: number;\n  totalFileCount: number;\n  ratio: number;\n};\n\nexport type BundleProgressUpdate = {\n  buildID: string;\n  transformedFileCount: number;\n  totalFileCount: number;\n};\n\nexport { TerminalReportableEvent };\n\nexport type BuildPhase = 'in_progress' | 'done' | 'failed';\n\n/**\n * Code across the application takes a reporter as an option and calls the\n * update whenever one of the ReportableEvent happens. Code does not directly\n * write to the standard output, because a build would be:\n *\n *   1. ad-hoc, embedded into another tool, in which case we do not want to\n *   pollute that tool's own output. The tool is free to present the\n *   warnings/progress we generate any way they want, by specifying a custom\n *   reporter.\n *   2. run as a background process from another tool, in which case we want\n *   to expose updates in a way that is easily machine-readable, for example\n *   a JSON-stream. We don't want to pollute it with textual messages.\n *\n * We centralize terminal reporting into a single place because we want the\n * output to be robust and consistent. The most common reporter is\n * TerminalReporter, that should be the only place in the application should\n * access the `terminal` module (nor the `console`).\n */\nexport type Reporter = { update(event: ReportableEvent): void };\n\nexport interface SnippetError extends Error {\n  code?: string;\n  filename?: string;\n  snippet?: string;\n\n  /** Module that failed to load ex 'fs' */\n  targetModuleName?: string;\n  originModulePath?: string;\n\n  errors?: any[];\n}\n\nexport interface TerminalReporterInterface {\n  new (terminal: Terminal): TerminalReporterInterface;\n\n  /**\n   * The bundle builds for which we are actively maintaining the status on the\n   * terminal, ie. showing a progress bar. There can be several bundles being\n   * built at the same time.\n   */\n  _activeBundles: Map<string, BundleProgress>;\n\n  _scheduleUpdateBundleProgress: {\n    (data: { buildID: string; transformedFileCount: number; totalFileCount: number }): void;\n    cancel(): void;\n  };\n\n  /** Set in super type */\n  terminal: Terminal;\n\n  /**\n   * Construct a message that represents the progress of a\n   * single bundle build, for example:\n   *\n   *     BUNDLE path/to/bundle.js ▓▓▓▓▓░░░░░░░░░░░ 36.6% (4790/7922)\n   */\n  _getBundleStatusMessage(\n    {\n      bundleDetails: { entryFile, bundleType, runtimeBytecodeVersion },\n      transformedFileCount,\n      totalFileCount,\n      ratio,\n    }: BundleProgress,\n    phase: BuildPhase\n  ): string;\n\n  _logWatcherStatus(event: WatcherStatus): void;\n\n  /**\n   * This function is only concerned with logging and should not do state\n   * or terminal status updates.\n   */\n  _log(event: TerminalReportableEvent): void;\n\n  _logCacheDisabled(reason: GlobalCacheDisabledReason): void;\n\n  _logBundleBuildDone(buildID: string): void;\n\n  _logBundleBuildFailed(buildID: string): void;\n\n  _logInitializing(port: number, hasReducedPerformance: boolean): void;\n\n  _logInitializingFailed(port: number, error: SnippetError): void;\n\n  /**\n   * We do not want to log the whole stacktrace for bundling error, because\n   * these are operational errors, not programming errors, and the stacktrace\n   * is not actionable to end users.\n   */\n  _logBundlingError(error: SnippetError): void;\n\n  /**\n   * We use Math.pow(ratio, 2) to as a conservative measure of progress because\n   * we know the `totalCount` is going to progressively increase as well. We\n   * also prevent the ratio from going backwards.\n   */\n  _updateBundleProgress({\n    buildID,\n    transformedFileCount,\n    totalFileCount,\n  }: BundleProgressUpdate): void;\n\n  /**\n   * This function is exclusively concerned with updating the internal state.\n   * No logging or status updates should be done at this point.\n   */\n  _updateState(event: TerminalReportableEvent): void;\n  /**\n   * Return a status message that is always consistent with the current state\n   * of the application. Having this single function ensures we don't have\n   * different call sites overriding each other status messages.\n   */\n  _getStatusMessage(): string;\n\n  _logHmrClientError(e: Error): void;\n\n  /**\n   * Single entry point for reporting events. That allows us to implement the\n   * corresponding JSON reporter easily and have a consistent reporting.\n   */\n  update(event: TerminalReportableEvent): void;\n}\n"], "names": [], "mappings": "AAAA"}