{"version": 3, "sources": ["../../../../src/start/server/ReactDevToolsProxy.ts"], "sourcesContent": ["import assert from 'assert';\nimport { EventEmitter } from 'events';\nimport WebSocket from 'ws';\n\nlet serverInstance: WebSocket.WebSocketServer | null = null;\n\nconst eventEmitter = new EventEmitter();\n\n/**\n * Private command to support DevTools frontend reload.\n *\n * The react-devtools maintains state between frontend(webpage) and backend(app).\n * If we reload the frontend without reloading the app, the react-devtools will stuck on incorrect state.\n * We introduce this special reload command.\n * As long as the frontend reload, we will close app's WebSocket connection and tell app to reconnect again.\n */\nconst RELOAD_COMMAND = 'Expo::RELOAD';\n\n/**\n * Start the react-devtools WebSocket proxy server\n */\nexport async function startReactDevToolsProxyAsync(options?: { port: number }) {\n  if (serverInstance != null) {\n    return;\n  }\n\n  serverInstance = new WebSocket.WebSocketServer({ port: options?.port ?? 8097 });\n\n  serverInstance.on('connection', function connection(ws) {\n    ws.on('message', function message(rawData, isBinary) {\n      assert(!isBinary);\n      const data = rawData.toString();\n\n      if (data === RELOAD_COMMAND) {\n        closeAllOtherClients(ws);\n        eventEmitter.emit(RELOAD_COMMAND);\n        return;\n      }\n\n      serverInstance?.clients.forEach(function each(client) {\n        if (client !== ws && client.readyState === WebSocket.OPEN) {\n          client.send(data, { binary: isBinary });\n        }\n      });\n    });\n  });\n\n  serverInstance.on('close', function () {\n    serverInstance = null;\n  });\n}\n\n/**\n * Close the WebSocket server\n */\nexport function closeReactDevToolsProxy() {\n  serverInstance?.close();\n  serverInstance = null;\n}\n\n/**\n * add event listener from react-devtools frontend reload\n */\nexport function addReactDevToolsReloadListener(listener: (...args: any[]) => void) {\n  eventEmitter.addListener(RELOAD_COMMAND, listener);\n}\n\n/**\n * Close all other WebSocket clients other than the current `self` client\n */\nfunction closeAllOtherClients(self: WebSocket.WebSocket) {\n  serverInstance?.clients.forEach(function each(client) {\n    if (client !== self && client.readyState === WebSocket.OPEN) {\n      client.close();\n    }\n  });\n}\n"], "names": ["startReactDevToolsProxyAsync", "closeReactDevToolsProxy", "addReactDevToolsReloadListener", "serverInstance", "eventEmitter", "EventEmitter", "RELOAD_COMMAND", "options", "WebSocket", "WebSocketServer", "port", "on", "connection", "ws", "message", "rawData", "isBinary", "assert", "data", "toString", "closeAllOtherClients", "emit", "clients", "for<PERSON>ach", "each", "client", "readyState", "OPEN", "send", "binary", "close", "listener", "addListener", "self"], "mappings": "AAAA;;;;;;;;;;;IAqBsBA,4BAA4B,MAA5BA,4BAA4B;IAkClCC,uBAAuB,MAAvBA,uBAAuB;IAQvBC,8BAA8B,MAA9BA,8BAA8B;;;8DA/D3B,QAAQ;;;;;;;yBACE,QAAQ;;;;;;;8DACf,IAAI;;;;;;;;;;;AAE1B,IAAIC,cAAc,GAAqC,IAAI,AAAC;AAE5D,MAAMC,YAAY,GAAG,IAAIC,CAAAA,OAAY,EAAA,CAAA,aAAA,EAAE,AAAC;AAExC;;;;;;;CAOC,GACD,MAAMC,cAAc,GAAG,cAAc,AAAC;AAK/B,eAAeN,4BAA4B,CAACO,OAA0B,EAAE;IAC7E,IAAIJ,cAAc,IAAI,IAAI,EAAE;QAC1B,OAAO;IACT,CAAC;IAEDA,cAAc,GAAG,IAAIK,CAAAA,GAAS,EAAA,CAAA,QAAA,CAACC,eAAe,CAAC;QAAEC,IAAI,EAAEH,CAAAA,OAAO,QAAM,GAAbA,KAAAA,CAAa,GAAbA,OAAO,CAAEG,IAAI,CAAA,IAAI,IAAI;KAAE,CAAC,CAAC;IAEhFP,cAAc,CAACQ,EAAE,CAAC,YAAY,EAAE,SAASC,UAAU,CAACC,EAAE,EAAE;QACtDA,EAAE,CAACF,EAAE,CAAC,SAAS,EAAE,SAASG,OAAO,CAACC,OAAO,EAAEC,QAAQ,EAAE;YACnDC,IAAAA,OAAM,EAAA,QAAA,EAAC,CAACD,QAAQ,CAAC,CAAC;YAClB,MAAME,IAAI,GAAGH,OAAO,CAACI,QAAQ,EAAE,AAAC;YAEhC,IAAID,IAAI,KAAKZ,cAAc,EAAE;gBAC3Bc,oBAAoB,CAACP,EAAE,CAAC,CAAC;gBACzBT,YAAY,CAACiB,IAAI,CAACf,cAAc,CAAC,CAAC;gBAClC,OAAO;YACT,CAAC;YAEDH,cAAc,QAAS,GAAvBA,KAAAA,CAAuB,GAAvBA,cAAc,CAAEmB,OAAO,CAACC,OAAO,CAAC,SAASC,IAAI,CAACC,MAAM,EAAE;gBACpD,IAAIA,MAAM,KAAKZ,EAAE,IAAIY,MAAM,CAACC,UAAU,KAAKlB,GAAS,EAAA,QAAA,CAACmB,IAAI,EAAE;oBACzDF,MAAM,CAACG,IAAI,CAACV,IAAI,EAAE;wBAAEW,MAAM,EAAEb,QAAQ;qBAAE,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEHb,cAAc,CAACQ,EAAE,CAAC,OAAO,EAAE,WAAY;QACrCR,cAAc,GAAG,IAAI,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAKM,SAASF,uBAAuB,GAAG;IACxCE,cAAc,QAAO,GAArBA,KAAAA,CAAqB,GAArBA,cAAc,CAAE2B,KAAK,EAAE,CAAC;IACxB3B,cAAc,GAAG,IAAI,CAAC;AACxB,CAAC;AAKM,SAASD,8BAA8B,CAAC6B,QAAkC,EAAE;IACjF3B,YAAY,CAAC4B,WAAW,CAAC1B,cAAc,EAAEyB,QAAQ,CAAC,CAAC;AACrD,CAAC;AAED;;CAEC,GACD,SAASX,oBAAoB,CAACa,IAAyB,EAAE;IACvD9B,cAAc,QAAS,GAAvBA,KAAAA,CAAuB,GAAvBA,cAAc,CAAEmB,OAAO,CAACC,OAAO,CAAC,SAASC,IAAI,CAACC,MAAM,EAAE;QACpD,IAAIA,MAAM,KAAKQ,IAAI,IAAIR,MAAM,CAACC,UAAU,KAAKlB,GAAS,EAAA,QAAA,CAACmB,IAAI,EAAE;YAC3DF,MAAM,CAACK,KAAK,EAAE,CAAC;QACjB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}