{"version": 3, "sources": ["../../../../../../../src/start/server/metro/dev-server/utils/socketMessages.ts"], "sourcesContent": ["import type { RawData as WebSocketRawData } from 'ws';\n\nconst debug = require('debug')('expo:metro:dev-server:messages') as typeof console.log;\n\n/** The current websocket-based communication between Metro, CLI, and client devices */\nconst PROTOCOL_VERSION = 2;\n\n/**\n * Parse the incoming raw message data and return the parsed object.\n * This returns null if the protocol version did not match expected version.\n */\nexport function parseRawMessage<T = Record<string, any>>(\n  data: WebSocketRawData,\n  isBinary: boolean\n): null | T {\n  if (isBinary) return null;\n\n  try {\n    const { version, ...message } = JSON.parse(data.toString()) ?? {};\n    if (version === PROTOCOL_VERSION) {\n      return message;\n    }\n\n    debug(\n      `Received message protocol version did not match supported \"${PROTOCOL_VERSION}\", received: ${message.version}`\n    );\n  } catch (error) {\n    debug(`Failed to parse message: ${error}`);\n  }\n\n  return null;\n}\n\n/**\n * Serialize any of the messages to send over websockets.\n * This adds the protocol version to the message.\n */\nexport function serializeMessage(message: Record<string, any>): string {\n  return JSON.stringify({ ...message, version: PROTOCOL_VERSION });\n}\n"], "names": ["parseRawMessage", "serializeMessage", "debug", "require", "PROTOCOL_VERSION", "data", "isBinary", "version", "message", "JSON", "parse", "toString", "error", "stringify"], "mappings": "AAAA;;;;;;;;;;;IAWgBA,eAAe,MAAfA,eAAe;IA0BfC,gBAAgB,MAAhBA,gBAAgB;;AAnChC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAEvF,qFAAqF,GACrF,MAAMC,gBAAgB,GAAG,CAAC,AAAC;AAMpB,SAASJ,eAAe,CAC7BK,IAAsB,EACtBC,QAAiB,EACP;IACV,IAAIA,QAAQ,EAAE,OAAO,IAAI,CAAC;IAE1B,IAAI;QACF,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAGC,OAAO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACM,QAAQ,EAAE,CAAC,IAAI,EAAE,AAAC;QAClE,IAAIJ,OAAO,KAAKH,gBAAgB,EAAE;YAChC,OAAOI,OAAO,CAAC;QACjB,CAAC;QAEDN,KAAK,CACH,CAAC,2DAA2D,EAAEE,gBAAgB,CAAC,aAAa,EAAEI,OAAO,CAACD,OAAO,CAAC,CAAC,CAChH,CAAC;IACJ,EAAE,OAAOK,KAAK,EAAE;QACdV,KAAK,CAAC,CAAC,yBAAyB,EAAEU,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAMM,SAASX,gBAAgB,CAACO,OAA4B,EAAU;IACrE,OAAOC,IAAI,CAACI,SAAS,CAAC;QAAE,GAAGL,OAAO;QAAED,OAAO,EAAEH,gBAAgB;KAAE,CAAC,CAAC;AACnE,CAAC"}