{"version": 3, "sources": ["../../../src/utils/delay.ts"], "sourcesContent": ["import { CommandError } from './errors';\n\n/** Await for a given duration of milliseconds. */\nexport function delayAsync(timeout: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, timeout));\n}\n\n/** Wait for a given action to return a truthy value. */\nexport async function waitForActionAsync<T>({\n  action,\n  interval = 100,\n  maxWaitTime = 20000,\n}: {\n  action: () => T | Promise<T>;\n  interval?: number;\n  maxWaitTime?: number;\n}): Promise<T> {\n  let complete: T;\n  const start = Date.now();\n  do {\n    const actionStartTime = Date.now();\n    complete = await action();\n\n    const actionTimeElapsed = Date.now() - actionStartTime;\n    const remainingDelayInterval = interval - actionTimeElapsed;\n    if (remainingDelayInterval > 0) {\n      await delayAsync(remainingDelayInterval);\n    }\n    if (Date.now() - start > maxWaitTime) {\n      break;\n    }\n  } while (!complete);\n\n  return complete;\n}\n\n/** Resolves a given function or rejects if the provided timeout is passed. */\nexport function resolveWithTimeout<T>(\n  action: () => Promise<T>,\n  {\n    timeout,\n    errorMessage,\n  }: {\n    /** Duration in milliseconds to wait before asserting a timeout. */\n    timeout: number;\n    /** Optional error message to use in the assertion. */\n    errorMessage?: string;\n  }\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    setTimeout(() => {\n      reject(new CommandError('TIMEOUT', errorMessage));\n    }, timeout);\n    action().then(resolve, reject);\n  });\n}\n"], "names": ["delayAsync", "waitForActionAsync", "resolveWithTimeout", "timeout", "Promise", "resolve", "setTimeout", "action", "interval", "maxWaitTime", "complete", "start", "Date", "now", "actionStartTime", "actionTimeElapsed", "remainingDelayInterval", "errorMessage", "reject", "CommandError", "then"], "mappings": "AAAA;;;;;;;;;;;IAGgBA,UAAU,MAAVA,UAAU;IAKJC,kBAAkB,MAAlBA,kBAAkB;IA6BxBC,kBAAkB,MAAlBA,kBAAkB;;wBArCL,UAAU;AAGhC,SAASF,UAAU,CAACG,OAAe,EAAiB;IACzD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,GAAKC,UAAU,CAACD,OAAO,EAAEF,OAAO,CAAC,CAAC,CAAC;AAChE,CAAC;AAGM,eAAeF,kBAAkB,CAAI,EAC1CM,MAAM,CAAA,EACNC,QAAQ,EAAG,GAAG,CAAA,EACdC,WAAW,EAAG,KAAK,CAAA,EAKpB,EAAc;IACb,IAAIC,QAAQ,AAAG,AAAC;IAChB,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,EAAE,AAAC;IACzB,GAAG;QACD,MAAMC,eAAe,GAAGF,IAAI,CAACC,GAAG,EAAE,AAAC;QACnCH,QAAQ,GAAG,MAAMH,MAAM,EAAE,CAAC;QAE1B,MAAMQ,iBAAiB,GAAGH,IAAI,CAACC,GAAG,EAAE,GAAGC,eAAe,AAAC;QACvD,MAAME,sBAAsB,GAAGR,QAAQ,GAAGO,iBAAiB,AAAC;QAC5D,IAAIC,sBAAsB,GAAG,CAAC,EAAE;YAC9B,MAAMhB,UAAU,CAACgB,sBAAsB,CAAC,CAAC;QAC3C,CAAC;QACD,IAAIJ,IAAI,CAACC,GAAG,EAAE,GAAGF,KAAK,GAAGF,WAAW,EAAE;YACpC,MAAM;QACR,CAAC;IACH,QAAS,CAACC,QAAQ,EAAE;IAEpB,OAAOA,QAAQ,CAAC;AAClB,CAAC;AAGM,SAASR,kBAAkB,CAChCK,MAAwB,EACxB,EACEJ,OAAO,CAAA,EACPc,YAAY,CAAA,EAMb,EACW;IACZ,OAAO,IAAIb,OAAO,CAAC,CAACC,OAAO,EAAEa,MAAM,GAAK;QACtCZ,UAAU,CAAC,IAAM;YACfY,MAAM,CAAC,IAAIC,OAAY,aAAA,CAAC,SAAS,EAAEF,YAAY,CAAC,CAAC,CAAC;QACpD,CAAC,EAAEd,OAAO,CAAC,CAAC;QACZI,MAAM,EAAE,CAACa,IAAI,CAACf,OAAO,EAAEa,MAAM,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;AACL,CAAC"}