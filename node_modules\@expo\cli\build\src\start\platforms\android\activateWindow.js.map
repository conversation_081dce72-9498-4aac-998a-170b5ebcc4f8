{"version": 3, "sources": ["../../../../../src/start/platforms/android/activateWindow.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport { execFileSync } from 'child_process';\n\nimport { Device } from './adb';\n\nconst debug = require('debug')('expo:start:platforms:android:activateWindow') as typeof console.log;\n\nfunction getUnixPID(port: number | string): string {\n  // Runs like `lsof -i:8081 -P -t -sTCP:LISTEN`\n  const args = [`-i:${port}`, '-P', '-t', '-sTCP:LISTEN'];\n  debug('lsof ' + args.join(' '));\n  return execFileSync('lsof', args, {\n    encoding: 'utf8',\n    stdio: ['pipe', 'pipe', 'ignore'],\n  })\n    .split('\\n')[0]\n    ?.trim?.();\n}\n\n/** Activate the Emulator window on macOS. */\nexport async function activateWindowAsync(device: Pick<Device, 'type' | 'pid'>): Promise<boolean> {\n  debug(`Activating window for device (pid: ${device.pid}, type: ${device.type})`);\n  if (\n    // only mac is supported for now.\n    process.platform !== 'darwin' ||\n    // can only focus emulators\n    device.type !== 'emulator'\n  ) {\n    return false;\n  }\n\n  // Google Emulator ID: `emulator-5554` -> `5554`\n  const androidPid = device.pid!.match(/-(\\d+)/)?.[1];\n  if (!androidPid) {\n    return false;\n  }\n  // Unix PID\n  const pid = getUnixPID(androidPid);\n\n  if (!pid) {\n    return false;\n  }\n  debug(`Activate window for pid:`, pid);\n  try {\n    await osascript.execAsync(`\n    tell application \"System Events\"\n      set frontmost of the first process whose unix id is ${pid} to true\n    end tell`);\n    return true;\n  } catch {\n    // noop -- this feature is very specific and subject to failure.\n    return false;\n  }\n}\n"], "names": ["activateWindowAsync", "debug", "require", "getUnixPID", "port", "execFileSync", "args", "join", "encoding", "stdio", "split", "trim", "device", "pid", "type", "process", "platform", "androidPid", "match", "osascript", "execAsync"], "mappings": "AAAA;;;;+BAoBsBA,qBAAmB;;aAAnBA,mBAAmB;;;+DApBd,iBAAiB;;;;;;;yBACf,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI5C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6CAA6C,CAAC,AAAsB,AAAC;AAEpG,SAASC,UAAU,CAACC,IAAqB,EAAU;QAI1CC,GAIU;IAPjB,8CAA8C;IAC9C,MAAMC,IAAI,GAAG;QAAC,CAAC,GAAG,EAAEF,IAAI,CAAC,CAAC;QAAE,IAAI;QAAE,IAAI;QAAE,cAAc;KAAC,AAAC;IACxDH,KAAK,CAAC,OAAO,GAAGK,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAChC,OAAOF,CAAAA,GAIU,GAJVA,IAAAA,aAAY,EAAA,aAAA,EAAC,MAAM,EAAEC,IAAI,EAAE;QAChCE,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE;YAAC,MAAM;YAAE,MAAM;YAAE,QAAQ;SAAC;KAClC,CAAC,CACCC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SACT,GALDL,KAAAA,CAKC,GALDA,GAIU,CACbM,IAAI,QAAI,GALLN,KAAAA,CAKK,GALLA,GAIU,CACbM,IAAI,EAAI,CAAC;AACf,CAAC;AAGM,eAAeX,mBAAmB,CAACY,MAAoC,EAAoB;QAY7EA,GAA2B;IAX9CX,KAAK,CAAC,CAAC,mCAAmC,EAAEW,MAAM,CAACC,GAAG,CAAC,QAAQ,EAAED,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,IACE,iCAAiC;IACjCC,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7B,2BAA2B;IAC3BJ,MAAM,CAACE,IAAI,KAAK,UAAU,EAC1B;QACA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gDAAgD;IAChD,MAAMG,UAAU,GAAGL,CAAAA,GAA2B,GAA3BA,MAAM,CAACC,GAAG,CAAEK,KAAK,UAAU,SAAK,GAAhCN,KAAAA,CAAgC,GAAhCA,GAA2B,AAAE,CAAC,CAAC,CAAC,AAAC;IACpD,IAAI,CAACK,UAAU,EAAE;QACf,OAAO,KAAK,CAAC;IACf,CAAC;IACD,WAAW;IACX,MAAMJ,GAAG,GAAGV,UAAU,CAACc,UAAU,CAAC,AAAC;IAEnC,IAAI,CAACJ,GAAG,EAAE;QACR,OAAO,KAAK,CAAC;IACf,CAAC;IACDZ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAEY,GAAG,CAAC,CAAC;IACvC,IAAI;QACF,MAAMM,UAAS,EAAA,CAACC,SAAS,CAAC,CAAC;;0DAE2B,EAAEP,GAAG,CAAC;YACpD,CAAC,CAAC,CAAC;QACX,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,gEAAgE;QAChE,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}