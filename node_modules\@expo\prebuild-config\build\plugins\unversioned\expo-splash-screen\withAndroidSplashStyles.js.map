{"version": 3, "file": "withAndroidSplashStyles.js", "names": ["_configPlugins", "data", "require", "_android", "_getAndroidSplashConfig", "styleResourceGroup", "name", "parent", "SPLASH_COLOR_NAME", "withAndroidSplashStyles", "config", "splashConfig", "isLegacyConfig", "withAndroidColors", "backgroundColor", "getSplashBackgroundColor", "modResults", "setSplashColorsForTheme", "withAndroidColorsNight", "getSplashDarkBackgroundColor", "withAndroidStyles", "removeOldSplashStyleGroup", "addSplashScreenStyle", "exports", "styles", "resources", "style", "item", "$", "_", "filter", "group", "head", "matches", "props", "getAndroidSplashConfig", "getAndroidDarkSplashConfig", "setSplashStylesForTheme", "AndroidConfig", "Styles", "assignStylesValue", "add", "value", "colors", "Colors", "assignColorValue"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withAndroidSplashStyles.ts"], "sourcesContent": ["import {\n  AndroidConfig,\n  ConfigPlugin,\n  withAndroidColors,\n  withAndroidColorsNight,\n  withAndroidStyles,\n} from '@expo/config-plugins';\nimport { Colors } from '@expo/config-plugins/build/android';\nimport { ExpoConfig } from '@expo/config-types';\n\nimport {\n  AndroidSplashConfig,\n  getAndroidDarkSplashConfig,\n  getAndroidSplashConfig,\n} from './getAndroidSplashConfig';\n\nconst styleResourceGroup = {\n  name: 'Theme.App.SplashScreen',\n  parent: 'Theme.SplashScreen',\n};\n\nconst SPLASH_COLOR_NAME = 'splashscreen_background';\n\nexport const withAndroidSplashStyles: ConfigPlugin<{\n  splashConfig: AndroidSplashConfig | null;\n  isLegacyConfig: boolean;\n}> = (config, { splashConfig, isLegacyConfig }) => {\n  config = withAndroidColors(config, (config) => {\n    const backgroundColor = getSplashBackgroundColor(config, splashConfig);\n    if (!backgroundColor) {\n      return config;\n    }\n    config.modResults = setSplashColorsForTheme(config.modResults, backgroundColor);\n    return config;\n  });\n  config = withAndroidColorsNight(config, (config) => {\n    const backgroundColor = getSplashDarkBackgroundColor(config, splashConfig);\n    if (!backgroundColor) {\n      return config;\n    }\n    config.modResults = setSplashColorsForTheme(config.modResults, backgroundColor);\n    return config;\n  });\n  config = withAndroidStyles(config, (config) => {\n    config.modResults = removeOldSplashStyleGroup(config.modResults);\n    config.modResults = addSplashScreenStyle(config.modResults, isLegacyConfig);\n    return config;\n  });\n  return config;\n};\n\n// Add the style that extends Theme.SplashScreen\nfunction addSplashScreenStyle(\n  styles: AndroidConfig.Resources.ResourceXML,\n  isLegacyConfig: boolean\n) {\n  const { resources } = styles;\n  const { style = [] } = resources;\n\n  let item;\n  if (isLegacyConfig) {\n    item = [\n      {\n        $: { name: 'android:windowBackground' },\n        _: '@drawable/ic_launcher_background',\n      },\n    ];\n  } else {\n    item = [\n      {\n        $: { name: 'windowSplashScreenBackground' },\n        _: '@color/splashscreen_background',\n      },\n      {\n        $: { name: 'windowSplashScreenAnimatedIcon' },\n        _: '@drawable/splashscreen_logo',\n      },\n      {\n        $: { name: 'postSplashScreenTheme' },\n        _: '@style/AppTheme',\n      },\n    ];\n  }\n\n  styles.resources.style = [\n    ...style.filter(({ $ }) => $.name !== 'Theme.App.SplashScreen'),\n    {\n      $: {\n        ...styleResourceGroup,\n        parent: isLegacyConfig ? 'AppTheme' : 'Theme.SplashScreen',\n      },\n      item,\n    },\n  ];\n\n  return styles;\n}\n\n// Remove the old style group which didn't extend the base theme properly.\nexport function removeOldSplashStyleGroup(styles: AndroidConfig.Resources.ResourceXML) {\n  const group = {\n    name: 'Theme.App.SplashScreen',\n    parent: 'Theme.AppCompat.Light.NoActionBar',\n  };\n\n  styles.resources.style = styles.resources.style?.filter?.(({ $: head }) => {\n    let matches = head.name === group.name;\n    if (group.parent != null && matches) {\n      matches = head.parent === group.parent;\n    }\n    return !matches;\n  });\n\n  return styles;\n}\n\nexport function getSplashBackgroundColor(\n  config: ExpoConfig,\n  props: AndroidSplashConfig | null\n): string | null {\n  return getAndroidSplashConfig(config, props)?.backgroundColor ?? null;\n}\n\nexport function getSplashDarkBackgroundColor(\n  config: ExpoConfig,\n  props: AndroidSplashConfig | null\n): string | null {\n  return getAndroidDarkSplashConfig(config, props)?.backgroundColor ?? null;\n}\n\nexport function setSplashStylesForTheme(styles: AndroidConfig.Resources.ResourceXML) {\n  // Add splash screen image\n  return AndroidConfig.Styles.assignStylesValue(styles, {\n    add: true,\n    value: '@drawable/splashscreen_logo',\n    name: 'android:windowSplashScreenBackground',\n    parent: styleResourceGroup,\n  });\n}\n\nexport function setSplashColorsForTheme(\n  colors: AndroidConfig.Resources.ResourceXML,\n  backgroundColor: string | null\n): AndroidConfig.Resources.ResourceXML {\n  return Colors.assignColorValue(colors, { value: backgroundColor, name: SPLASH_COLOR_NAME });\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAG,wBAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,uBAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAMA,MAAMI,kBAAkB,GAAG;EACzBC,IAAI,EAAE,wBAAwB;EAC9BC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,iBAAiB,GAAG,yBAAyB;AAE5C,MAAMC,uBAGX,GAAGA,CAACC,MAAM,EAAE;EAAEC,YAAY;EAAEC;AAAe,CAAC,KAAK;EACjDF,MAAM,GAAG,IAAAG,kCAAiB,EAACH,MAAM,EAAGA,MAAM,IAAK;IAC7C,MAAMI,eAAe,GAAGC,wBAAwB,CAACL,MAAM,EAAEC,YAAY,CAAC;IACtE,IAAI,CAACG,eAAe,EAAE;MACpB,OAAOJ,MAAM;IACf;IACAA,MAAM,CAACM,UAAU,GAAGC,uBAAuB,CAACP,MAAM,CAACM,UAAU,EAAEF,eAAe,CAAC;IAC/E,OAAOJ,MAAM;EACf,CAAC,CAAC;EACFA,MAAM,GAAG,IAAAQ,uCAAsB,EAACR,MAAM,EAAGA,MAAM,IAAK;IAClD,MAAMI,eAAe,GAAGK,4BAA4B,CAACT,MAAM,EAAEC,YAAY,CAAC;IAC1E,IAAI,CAACG,eAAe,EAAE;MACpB,OAAOJ,MAAM;IACf;IACAA,MAAM,CAACM,UAAU,GAAGC,uBAAuB,CAACP,MAAM,CAACM,UAAU,EAAEF,eAAe,CAAC;IAC/E,OAAOJ,MAAM;EACf,CAAC,CAAC;EACFA,MAAM,GAAG,IAAAU,kCAAiB,EAACV,MAAM,EAAGA,MAAM,IAAK;IAC7CA,MAAM,CAACM,UAAU,GAAGK,yBAAyB,CAACX,MAAM,CAACM,UAAU,CAAC;IAChEN,MAAM,CAACM,UAAU,GAAGM,oBAAoB,CAACZ,MAAM,CAACM,UAAU,EAAEJ,cAAc,CAAC;IAC3E,OAAOF,MAAM;EACf,CAAC,CAAC;EACF,OAAOA,MAAM;AACf,CAAC;;AAED;AAAAa,OAAA,CAAAd,uBAAA,GAAAA,uBAAA;AACA,SAASa,oBAAoBA,CAC3BE,MAA2C,EAC3CZ,cAAuB,EACvB;EACA,MAAM;IAAEa;EAAU,CAAC,GAAGD,MAAM;EAC5B,MAAM;IAAEE,KAAK,GAAG;EAAG,CAAC,GAAGD,SAAS;EAEhC,IAAIE,IAAI;EACR,IAAIf,cAAc,EAAE;IAClBe,IAAI,GAAG,CACL;MACEC,CAAC,EAAE;QAAEtB,IAAI,EAAE;MAA2B,CAAC;MACvCuB,CAAC,EAAE;IACL,CAAC,CACF;EACH,CAAC,MAAM;IACLF,IAAI,GAAG,CACL;MACEC,CAAC,EAAE;QAAEtB,IAAI,EAAE;MAA+B,CAAC;MAC3CuB,CAAC,EAAE;IACL,CAAC,EACD;MACED,CAAC,EAAE;QAAEtB,IAAI,EAAE;MAAiC,CAAC;MAC7CuB,CAAC,EAAE;IACL,CAAC,EACD;MACED,CAAC,EAAE;QAAEtB,IAAI,EAAE;MAAwB,CAAC;MACpCuB,CAAC,EAAE;IACL,CAAC,CACF;EACH;EAEAL,MAAM,CAACC,SAAS,CAACC,KAAK,GAAG,CACvB,GAAGA,KAAK,CAACI,MAAM,CAAC,CAAC;IAAEF;EAAE,CAAC,KAAKA,CAAC,CAACtB,IAAI,KAAK,wBAAwB,CAAC,EAC/D;IACEsB,CAAC,EAAE;MACD,GAAGvB,kBAAkB;MACrBE,MAAM,EAAEK,cAAc,GAAG,UAAU,GAAG;IACxC,CAAC;IACDe;EACF,CAAC,CACF;EAED,OAAOH,MAAM;AACf;;AAEA;AACO,SAASH,yBAAyBA,CAACG,MAA2C,EAAE;EACrF,MAAMO,KAAK,GAAG;IACZzB,IAAI,EAAE,wBAAwB;IAC9BC,MAAM,EAAE;EACV,CAAC;EAEDiB,MAAM,CAACC,SAAS,CAACC,KAAK,GAAGF,MAAM,CAACC,SAAS,CAACC,KAAK,EAAEI,MAAM,GAAG,CAAC;IAAEF,CAAC,EAAEI;EAAK,CAAC,KAAK;IACzE,IAAIC,OAAO,GAAGD,IAAI,CAAC1B,IAAI,KAAKyB,KAAK,CAACzB,IAAI;IACtC,IAAIyB,KAAK,CAACxB,MAAM,IAAI,IAAI,IAAI0B,OAAO,EAAE;MACnCA,OAAO,GAAGD,IAAI,CAACzB,MAAM,KAAKwB,KAAK,CAACxB,MAAM;IACxC;IACA,OAAO,CAAC0B,OAAO;EACjB,CAAC,CAAC;EAEF,OAAOT,MAAM;AACf;AAEO,SAAST,wBAAwBA,CACtCL,MAAkB,EAClBwB,KAAiC,EAClB;EACf,OAAO,IAAAC,gDAAsB,EAACzB,MAAM,EAAEwB,KAAK,CAAC,EAAEpB,eAAe,IAAI,IAAI;AACvE;AAEO,SAASK,4BAA4BA,CAC1CT,MAAkB,EAClBwB,KAAiC,EAClB;EACf,OAAO,IAAAE,oDAA0B,EAAC1B,MAAM,EAAEwB,KAAK,CAAC,EAAEpB,eAAe,IAAI,IAAI;AAC3E;AAEO,SAASuB,uBAAuBA,CAACb,MAA2C,EAAE;EACnF;EACA,OAAOc,8BAAa,CAACC,MAAM,CAACC,iBAAiB,CAAChB,MAAM,EAAE;IACpDiB,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,6BAA6B;IACpCpC,IAAI,EAAE,sCAAsC;IAC5CC,MAAM,EAAEF;EACV,CAAC,CAAC;AACJ;AAEO,SAASY,uBAAuBA,CACrC0B,MAA2C,EAC3C7B,eAA8B,EACO;EACrC,OAAO8B,iBAAM,CAACC,gBAAgB,CAACF,MAAM,EAAE;IAAED,KAAK,EAAE5B,eAAe;IAAER,IAAI,EAAEE;EAAkB,CAAC,CAAC;AAC7F", "ignoreList": []}