{"version": 3, "sources": ["../../../../../src/start/server/middleware/ExpoMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\n/** Base middleware creator for Expo dev servers. */\nexport abstract class ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected supportedPaths: string[]\n  ) {}\n\n  /**\n   * Returns true when the middleware should handle the incoming server request.\n   * Exposed for testing.\n   */\n  public shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url) {\n      return false;\n    }\n    const parsed = parse(req.url);\n    // Strip the query params\n    if (!parsed.pathname) {\n      return false;\n    }\n\n    return this.supportedPaths.includes(parsed.pathname);\n  }\n\n  abstract handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void>;\n\n  /** Create a server middleware handler. */\n  public getHandler() {\n    const internalMiddleware = async (\n      req: ServerRequest,\n      res: ServerResponse,\n      next: ServerNext\n    ) => {\n      try {\n        return await this.handleRequestAsync(req, res, next);\n      } catch (error: any) {\n        Log.exception(error);\n        // 5xx = Server Error HTTP code\n        res.statusCode = 500;\n        if (typeof error === 'object' && error !== null) {\n          res.end(\n            JSON.stringify({\n              error: error.toString(),\n            })\n          );\n        } else {\n          res.end(`Unexpected error: ${error}`);\n        }\n      }\n    };\n    const middleware = async (req: ServerRequest, res: ServerResponse, next: ServerNext) => {\n      if (!this.shouldHandleRequest(req)) {\n        return next();\n      }\n      return internalMiddleware(req, res, next);\n    };\n\n    middleware.internal = internalMiddleware;\n\n    return middleware;\n  }\n}\n\nexport function disableResponseCache(res: ServerResponse): ServerResponse {\n  res.setHeader('Cache-Control', 'private, no-cache, no-store, must-revalidate');\n  res.setHeader('Expires', '-1');\n  res.setHeader('Pragma', 'no-cache');\n  return res;\n}\n"], "names": ["ExpoMiddleware", "disableResponseCache", "constructor", "projectRoot", "supportedPaths", "shouldHandleRequest", "req", "url", "parsed", "parse", "pathname", "includes", "<PERSON><PERSON><PERSON><PERSON>", "internalMiddleware", "res", "next", "handleRequestAsync", "error", "Log", "exception", "statusCode", "end", "JSON", "stringify", "toString", "middleware", "internal", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA;;;;;;;;;;;IAMsBA,cAAc,MAAdA,cAAc;IAkEpBC,oBAAoB,MAApBA,oBAAoB;;;yBAxEd,KAAK;;;;;;2DAGN,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG5B,MAAeD,cAAc;IAClCE,YACYC,WAAmB,EACnBC,cAAwB,CAClC;QAFUD,mBAAAA,WAAmB,CAAA;QACnBC,sBAAAA,cAAwB,CAAA;IACjC;IAEH;;;GAGC,GACMC,mBAAmB,CAACC,GAAkB,EAAW;QACtD,IAAI,CAACA,GAAG,CAACC,GAAG,EAAE;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAMC,MAAM,GAAGC,IAAAA,IAAK,EAAA,MAAA,EAACH,GAAG,CAACC,GAAG,CAAC,AAAC;QAC9B,yBAAyB;QACzB,IAAI,CAACC,MAAM,CAACE,QAAQ,EAAE;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAACN,cAAc,CAACO,QAAQ,CAACH,MAAM,CAACE,QAAQ,CAAC,CAAC;IACvD;IAQA,wCAAwC,GACjCE,UAAU,GAAG;QAClB,MAAMC,kBAAkB,GAAG,OACzBP,GAAkB,EAClBQ,GAAmB,EACnBC,IAAgB,GACb;YACH,IAAI;gBACF,OAAO,MAAM,IAAI,CAACC,kBAAkB,CAACV,GAAG,EAAEQ,GAAG,EAAEC,IAAI,CAAC,CAAC;YACvD,EAAE,OAAOE,KAAK,EAAO;gBACnBC,IAAG,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC;gBACrB,+BAA+B;gBAC/BH,GAAG,CAACM,UAAU,GAAG,GAAG,CAAC;gBACrB,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;oBAC/CH,GAAG,CAACO,GAAG,CACLC,IAAI,CAACC,SAAS,CAAC;wBACbN,KAAK,EAAEA,KAAK,CAACO,QAAQ,EAAE;qBACxB,CAAC,CACH,CAAC;gBACJ,OAAO;oBACLV,GAAG,CAACO,GAAG,CAAC,CAAC,kBAAkB,EAAEJ,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;QACH,CAAC,AAAC;QACF,MAAMQ,UAAU,GAAG,OAAOnB,GAAkB,EAAEQ,GAAmB,EAAEC,IAAgB,GAAK;YACtF,IAAI,CAAC,IAAI,CAACV,mBAAmB,CAACC,GAAG,CAAC,EAAE;gBAClC,OAAOS,IAAI,EAAE,CAAC;YAChB,CAAC;YACD,OAAOF,kBAAkB,CAACP,GAAG,EAAEQ,GAAG,EAAEC,IAAI,CAAC,CAAC;QAC5C,CAAC,AAAC;QAEFU,UAAU,CAACC,QAAQ,GAAGb,kBAAkB,CAAC;QAEzC,OAAOY,UAAU,CAAC;IACpB;CACD;AAEM,SAASxB,oBAAoB,CAACa,GAAmB,EAAkB;IACxEA,GAAG,CAACa,SAAS,CAAC,eAAe,EAAE,8CAA8C,CAAC,CAAC;IAC/Eb,GAAG,CAACa,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC/Bb,GAAG,CAACa,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACpC,OAAOb,GAAG,CAAC;AACb,CAAC"}