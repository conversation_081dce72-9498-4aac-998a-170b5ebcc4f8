{"name": "webpack", "version": "5.100.1", "description": "Packs ECMAScript/CommonJs/AMD modules for the browser. Allows you to split your codebase into multiple bundles, which can be loaded on demand. Supports loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "homepage": "https://github.com/webpack/webpack", "bugs": "https://github.com/webpack/webpack/issues", "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "license": "MIT", "author": "<PERSON> @sokra", "main": "lib/index.js", "types": "types.d.ts", "bin": {"webpack": "bin/webpack.js"}, "files": ["lib/", "bin/", "hot/", "schemas/", "SECURITY.md", "module.d.ts", "types.d.ts"], "scripts": {"prepare": "husky", "setup": "node ./setup/setup.js", "prelint": "yarn setup", "lint": "yarn lint:code && yarn lint:special && yarn lint:types && yarn lint:types-test && yarn lint:types-module-test && yarn lint:yarn && yarn fmt:check && yarn lint:spellcheck", "lint:code": "node node_modules/eslint/bin/eslint.js --cache .", "lint:special": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/schemas-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-schemas && node tooling/generate-runtime-code.js && node tooling/generate-wasm-code.js && node node_modules/tooling/compile-to-definitions && node node_modules/tooling/precompile-schemas && node node_modules/tooling/generate-types --no-template-literals", "lint:types": "tsc", "lint:types-test": "tsc -p tsconfig.types.test.json", "lint:types-module-test": "tsc -p tsconfig.module.test.json", "lint:yarn": "yarn-deduplicate --fail --list -s highest yarn.lock", "lint:spellcheck": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "report:types": "rimraf coverage && yarn cover:types && yarn cover:report && open-cli coverage/lcov-report/index.html", "validate:types": "tsc -p tsconfig.validation.json", "fmt": "yarn fmt:base --log-level warn --write", "fmt:check": "yarn fmt:base --check", "fmt:base": "node node_modules/prettier/bin/prettier.cjs --cache --ignore-unknown .", "fix": "yarn fix:code && yarn fix:yarn && yarn fix:special && yarn fmt", "fix:code": "yarn lint:code --fix", "fix:yarn": "yarn-deduplicate -s highest yarn.lock", "fix:special": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-schemas --write && node tooling/generate-runtime-code.js --write && node tooling/generate-wasm-code.js --write && node node_modules/tooling/compile-to-definitions --write && node node_modules/tooling/precompile-schemas --write && node node_modules/tooling/generate-types --no-template-literals --write", "build:examples": "cd examples && node buildAll.js", "benchmark": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation --hash-seed=1 --random-seed=1 --no-opt --predictable --predictable-gc-schedule --interpreted-frames-native-stack --allow-natives-syntax --expose-gc --no-concurrent-sweeping ./test/BenchmarkTestCases.benchmark.mjs", "pretest": "yarn lint", "test": "yarn test:base", "test:update-snapshots": "yarn test:base -u", "test:basic": "yarn test:base --testMatch \"<rootDir>/test/*.basictest.js\"", "test:unit": "yarn test:base --testMatch \"<rootDir>/test/*.unittest.js\"", "test:integration": "yarn test:base --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\"", "test:base": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "cover": "yarn cover:all && yarn cover:report", "cover:clean": "rimraf .nyc_output coverage", "cover:all": "yarn cover:base --coverage", "cover:unit": "yarn cover:base --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:basic": "yarn cover:base --testMatch \"<rootDir>/test/*.basictest.js\" --coverage", "cover:integration": "yarn cover:base --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\" --coverage", "cover:integration:a": "yarn cover:base --testMatch \"<rootDir>/test/*.{basictest,test}.js\" --coverage", "cover:integration:b": "yarn cover:base --testMatch \"<rootDir>/test/*.longtest.js\" --coverage", "cover:base": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage", "cover:types": "node node_modules/tooling/type-coverage", "cover:merge": "yarn mkdirp .nyc_output && nyc merge .nyc_output coverage/coverage-nyc.json && rimraf .nyc_output", "cover:report": "nyc report --reporter=lcov  --reporter=text -t coverage"}, "lint-staged": {"*.{js,cjs,mjs}": ["node node_modules/eslint/bin/eslint.js --cache --fix"], "*": ["node node_modules/prettier/bin/prettier.cjs --cache --write --ignore-unknown", "cspell --cache --no-must-find-files"]}, "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.8", "@types/json-schema": "^7.0.15", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.15.0", "acorn-import-phases": "^1.0.3", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.2", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.3.3"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-react": "^7.27.1", "@codspeed/tinybench-plugin": "^4.0.1", "@eslint/js": "^9.29.0", "@eslint/markdown": "^6.6.0", "@stylistic/eslint-plugin": "^5.0.0", "@types/glob-to-regexp": "^0.4.4", "@types/graceful-fs": "^4.1.9", "@types/jest": "^30.0.0", "@types/mime-types": "^2.1.4", "@types/node": "^24.0.13", "@types/xxhashjs": "^0.2.4", "assemblyscript": "^0.28.2", "babel-loader": "^10.0.0", "bundle-loader": "^0.5.6", "coffee-loader": "^5.0.0", "coffeescript": "^2.5.1", "core-js": "^3.43.0", "cspell": "^9.1.1", "css-loader": "^7.1.2", "date-fns": "^4.0.0", "es5-ext": "^0.10.53", "es6-promise-polyfill": "^1.2.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.1", "eslint-config-webpack": "^4.3.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-jsdoc": "^51.2.3", "eslint-plugin-n": "^17.21.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-unicorn": "^59.0.1", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "globals": "^16.0.0", "hash-wasm": "^4.9.0", "husky": "^9.0.11", "istanbul": "^0.4.5", "jest": "^30.0.3", "jest-circus": "^30.0.3", "jest-cli": "^30.0.3", "jest-diff": "^30.0.3", "jest-environment-node": "^30.0.2", "jest-junit": "^16.0.0", "json-loader": "^0.5.7", "json5": "^2.1.3", "less": "^4.0.0", "less-loader": "^12.2.0", "lint-staged": "^16.1.2", "lodash": "^4.17.19", "lodash-es": "^4.17.15", "memfs": "^4.14.0", "mini-css-extract-plugin": "^2.9.0", "mini-svg-data-uri": "^1.2.3", "node-gyp": "^11.2.0", "nyc": "^17.1.0", "open-cli": "^8.0.0", "prettier": "^3.6.0", "prettier-2": "npm:prettier@^2", "pretty-format": "^30.0.2", "pug": "^3.0.3", "pug-loader": "^2.4.0", "raw-loader": "^4.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^3.0.2", "script-loader": "^0.7.2", "simple-git": "^3.28.0", "strip-ansi": "^6.0.0", "style-loader": "^4.0.0", "terser": "^5.43.1", "three": "^0.178.0", "tinybench": "^4.0.1", "toml": "^3.0.0", "tooling": "webpack/tooling#v1.24.3", "ts-loader": "^9.5.1", "typescript": "^5.8.2", "url-loader": "^4.1.0", "wast-loader": "^1.12.1", "webassembly-feature": "1.3.0", "webpack-cli": "^6.0.1", "xxhashjs": "^0.2.2", "yamljs": "^0.3.0", "yarn-deduplicate": "^6.0.1"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "engines": {"node": ">=10.13.0"}}