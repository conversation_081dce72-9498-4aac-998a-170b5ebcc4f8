{"version": 3, "sources": ["../../../src/run/resolveBundlerProps.ts"], "sourcesContent": ["import { Log } from '../log';\nimport { CommandError } from '../utils/errors';\nimport { resolvePortAsync } from '../utils/port';\n\nexport interface BundlerProps {\n  /** Port to start the dev server on. */\n  port: number;\n  /** Skip opening the bundler from the native script. */\n  shouldStartBundler: boolean;\n}\n\nexport async function resolveBundlerPropsAsync(\n  projectRoot: string,\n  options: {\n    port?: number;\n    bundler?: boolean;\n  }\n): Promise<BundlerProps> {\n  options.bundler = options.bundler ?? true;\n\n  if (\n    // If the user disables the bundler then they should not pass in the port property.\n    !options.bundler &&\n    options.port\n  ) {\n    throw new CommandError('BAD_ARGS', '--port and --no-bundler are mutually exclusive arguments');\n  }\n\n  // Resolve the port if the bundler is used.\n  let port = options.bundler\n    ? await resolvePortAsync(projectRoot, { reuseExistingPort: true, defaultPort: options.port })\n    : null;\n\n  // Skip bundling if the port is null -- meaning skip the bundler if the port is already running the app.\n  options.bundler = !!port;\n  if (!port) {\n    // Use explicit user-provided port, or the default port\n    port = options.port ?? 8081;\n  }\n  Log.debug(`Resolved port: ${port}, start dev server: ${options.bundler}`);\n\n  return {\n    shouldStartBundler: !!options.bundler,\n    port,\n  };\n}\n"], "names": ["resolveBundlerPropsAsync", "projectRoot", "options", "bundler", "port", "CommandError", "resolvePortAsync", "reuseExistingPort", "defaultPort", "Log", "debug", "shouldStartBundler"], "mappings": "AAAA;;;;+BAWs<PERSON>,0BAAwB;;aAAxBA,wBAAwB;;qBAX1B,QAAQ;wBACC,iBAAiB;sBACb,eAAe;AASzC,eAAeA,wBAAwB,CAC5CC,WAAmB,EACnBC,OAGC,EACsB;IACvBA,OAAO,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,IAAI,CAAC;IAE1C,IACE,mFAAmF;IACnF,CAACD,OAAO,CAACC,OAAO,IAChBD,OAAO,CAACE,IAAI,EACZ;QACA,MAAM,IAAIC,OAAY,aAAA,CAAC,UAAU,EAAE,0DAA0D,CAAC,CAAC;IACjG,CAAC;IAED,2CAA2C;IAC3C,IAAID,IAAI,GAAGF,OAAO,CAACC,OAAO,GACtB,MAAMG,IAAAA,KAAgB,iBAAA,EAACL,WAAW,EAAE;QAAEM,iBAAiB,EAAE,IAAI;QAAEC,WAAW,EAAEN,OAAO,CAACE,IAAI;KAAE,CAAC,GAC3F,IAAI,AAAC;IAET,wGAAwG;IACxGF,OAAO,CAACC,OAAO,GAAG,CAAC,CAACC,IAAI,CAAC;IACzB,IAAI,CAACA,IAAI,EAAE;QACT,uDAAuD;QACvDA,IAAI,GAAGF,OAAO,CAACE,IAAI,IAAI,IAAI,CAAC;IAC9B,CAAC;IACDK,IAAG,IAAA,CAACC,KAAK,CAAC,CAAC,eAAe,EAAEN,IAAI,CAAC,oBAAoB,EAAEF,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAE1E,OAAO;QACLQ,kBAAkB,EAAE,CAAC,CAACT,OAAO,CAACC,OAAO;QACrCC,IAAI;KACL,CAAC;AACJ,CAAC"}