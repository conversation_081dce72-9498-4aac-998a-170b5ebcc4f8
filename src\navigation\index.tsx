import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuthStore } from '../store/authStore';
import { setupInterceptors } from '../services/api';

import AuthNavigator from './AuthNavigator';
import StoreNavigator from './StoreNavigator';
import DeliveryNavigator from './DeliveryNavigator';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { user, getProfile } = useAuthStore();
  
  useEffect(() => {
    setupInterceptors();
    getProfile();
  }, []);
  
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        ) : user.role === 'STORE' ? (
          <Stack.Screen name="Store" component={StoreNavigator} />
        ) : (
          <Stack.Screen name="Delivery" component={DeliveryNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;