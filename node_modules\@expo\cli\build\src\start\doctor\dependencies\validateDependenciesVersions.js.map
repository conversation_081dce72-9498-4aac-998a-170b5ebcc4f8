{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/validateDependenciesVersions.ts"], "sourcesContent": ["import { ExpoConfig, PackageJSONConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport npmPackageArg from 'npm-package-arg';\nimport semver from 'semver';\nimport semverRangeSubset from 'semver/ranges/subset';\n\nimport { BundledNativeModules } from './bundledNativeModules';\nimport { getCombinedKnownVersionsAsync } from './getVersionedPackages';\nimport { resolveAllPackageVersionsAsync } from './resolvePackages';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:doctor:dependencies:validate') as typeof console.log;\n\ntype IncorrectDependency = {\n  packageName: string;\n  packageType: 'dependencies' | 'devDependencies';\n  expectedVersionOrRange: string;\n  actualVersion: string;\n};\n\ntype DependenciesToCheck = { known: string[]; unknown: string[] };\n\n/**\n * Print a list of incorrect dependency versions.\n * This only checks dependencies when not running in offline mode.\n *\n * @param projectRoot Expo project root.\n * @param exp Expo project config.\n * @param pkg Project's `package.json`.\n * @param packagesToCheck A list of packages to check, if undefined or empty, all will be checked.\n * @returns `true` if there are no incorrect dependencies.\n */\nexport async function validateDependenciesVersionsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'sdkVersion'>,\n  pkg: PackageJSONConfig,\n  packagesToCheck?: string[]\n): Promise<boolean | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping dependency validation in offline mode');\n    return null;\n  }\n\n  const incorrectDeps = await getVersionedDependenciesAsync(projectRoot, exp, pkg, packagesToCheck);\n  return logIncorrectDependencies(incorrectDeps);\n}\n\nfunction logInvalidDependency({\n  packageName,\n  expectedVersionOrRange,\n  actualVersion,\n}: IncorrectDependency) {\n  Log.warn(\n    chalk`  {bold ${packageName}}{cyan @}{red ${actualVersion}} - expected version: {green ${expectedVersionOrRange}}`\n  );\n}\n\nexport function logIncorrectDependencies(incorrectDeps: IncorrectDependency[]) {\n  if (!incorrectDeps.length) {\n    return true;\n  }\n\n  Log.warn(\n    chalk`The following packages should be updated for best compatibility with the installed {bold expo} version:`\n  );\n  incorrectDeps.forEach((dep) => logInvalidDependency(dep));\n\n  Log.warn(\n    'Your project may not work correctly until you install the expected versions of the packages.'\n  );\n\n  return false;\n}\n\n/**\n * Return a list of versioned dependencies for the project SDK version.\n *\n * @param projectRoot Expo project root.\n * @param exp Expo project config.\n * @param pkg Project's `package.json`.\n * @param packagesToCheck A list of packages to check, if undefined or empty, all will be checked.\n * @returns A list of incorrect dependencies.\n */\nexport async function getVersionedDependenciesAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'sdkVersion'>,\n  pkg: PackageJSONConfig,\n  packagesToCheck?: string[]\n): Promise<IncorrectDependency[]> {\n  // This should never happen under normal circumstances since\n  // the CLI is versioned in the `expo` package.\n  assert(exp.sdkVersion, 'SDK Version is missing');\n\n  // Get from both endpoints and combine the known package versions.\n  const combinedKnownPackages = await getCombinedKnownVersionsAsync({\n    projectRoot,\n    sdkVersion: exp.sdkVersion,\n  });\n  // debug(`Known dependencies: %O`, combinedKnownPackages);\n\n  const resolvedDependencies = packagesToCheck?.length\n    ? // Diff the provided packages to ensure we only check against installed packages.\n      getFilteredObject(packagesToCheck, { ...pkg.dependencies, ...pkg.devDependencies })\n    : // If no packages are provided, check against the `package.json` `dependencies` + `devDependencies` object.\n      { ...pkg.dependencies, ...pkg.devDependencies };\n  debug(`Checking dependencies for ${exp.sdkVersion}: %O`, resolvedDependencies);\n\n  // intersection of packages from package.json and bundled native modules\n  const { known: resolvedPackagesToCheck, unknown } = getPackagesToCheck(\n    combinedKnownPackages,\n    resolvedDependencies\n  );\n  debug(`Comparing known versions: %O`, resolvedPackagesToCheck);\n  debug(`Skipping packages that cannot be versioned automatically: %O`, unknown);\n  // read package versions from the file system (node_modules)\n  const packageVersions = await resolveAllPackageVersionsAsync(\n    projectRoot,\n    resolvedPackagesToCheck\n  );\n  debug(`Package versions: %O`, packageVersions);\n  // find incorrect dependencies by comparing the actual package versions with the bundled native module version ranges\n  let incorrectDeps = findIncorrectDependencies(pkg, packageVersions, combinedKnownPackages);\n  debug(`Incorrect dependencies: %O`, incorrectDeps);\n\n  if (pkg?.expo?.install?.exclude) {\n    const packagesToExclude = pkg.expo.install.exclude;\n\n    // Parse the exclude list to ensure we can factor in any specified version ranges\n    const parsedPackagesToExclude = packagesToExclude.reduce(\n      (acc: Record<string, npmPackageArg.Result>, packageName: string) => {\n        const npaResult = npmPackageArg(packageName);\n        if (typeof npaResult.name === 'string') {\n          acc[npaResult.name] = npaResult;\n        } else {\n          acc[packageName] = npaResult;\n        }\n        return acc;\n      },\n      {}\n    );\n\n    const incorrectAndExcludedDeps = incorrectDeps\n      .filter((dep) => {\n        if (parsedPackagesToExclude[dep.packageName]) {\n          const { name, raw, rawSpec, type } = parsedPackagesToExclude[dep.packageName];\n          const suggestedRange = combinedKnownPackages[name];\n\n          // If only the package name itself is specified, then we keep it in the exclude list\n          if (name === raw) {\n            return true;\n          } else if (type === 'version') {\n            return suggestedRange === rawSpec;\n          } else if (type === 'range') {\n            // Fall through exclusions if the suggested range is invalid\n            if (!semver.validRange(suggestedRange)) {\n              debug(\n                `Invalid semver range in combined known packages for package ${name} in expo.install.exclude: %O`,\n                suggestedRange\n              );\n              return false;\n            }\n\n            return semverRangeSubset(suggestedRange, rawSpec);\n          } else {\n            debug(\n              `Unsupported npm package argument type for package ${name} in expo.install.exclude: %O`,\n              type\n            );\n          }\n        }\n\n        return false;\n      })\n      .map((dep) => dep.packageName);\n\n    debug(\n      `Incorrect dependency warnings filtered out by expo.install.exclude: %O`,\n      incorrectAndExcludedDeps\n    );\n    incorrectDeps = incorrectDeps.filter(\n      (dep) => !incorrectAndExcludedDeps.includes(dep.packageName)\n    );\n  }\n\n  return incorrectDeps;\n}\n\nfunction getFilteredObject(keys: string[], object: Record<string, string>) {\n  return keys.reduce<Record<string, string>>((acc, key) => {\n    acc[key] = object[key];\n    return acc;\n  }, {});\n}\n\nfunction getPackagesToCheck(\n  bundledNativeModules: BundledNativeModules,\n  dependencies?: Record<string, string> | null\n): DependenciesToCheck {\n  const dependencyNames = Object.keys(dependencies ?? {});\n  const known: string[] = [];\n  const unknown: string[] = [];\n  for (const dependencyName of dependencyNames) {\n    if (dependencyName in bundledNativeModules) {\n      known.push(dependencyName);\n    } else {\n      unknown.push(dependencyName);\n    }\n  }\n  return { known, unknown };\n}\n\nfunction findIncorrectDependencies(\n  pkg: PackageJSONConfig,\n  packageVersions: Record<string, string>,\n  bundledNativeModules: BundledNativeModules\n): IncorrectDependency[] {\n  const packages = Object.keys(packageVersions);\n  const incorrectDeps: IncorrectDependency[] = [];\n  for (const packageName of packages) {\n    const expectedVersionOrRange = bundledNativeModules[packageName];\n    const actualVersion = packageVersions[packageName];\n    if (isDependencyVersionIncorrect(packageName, actualVersion, expectedVersionOrRange)) {\n      incorrectDeps.push({\n        packageName,\n        packageType: findDependencyType(pkg, packageName),\n        expectedVersionOrRange,\n        actualVersion,\n      });\n    }\n  }\n  return incorrectDeps;\n}\n\nexport function isDependencyVersionIncorrect(\n  packageName: string,\n  actualVersion: string,\n  expectedVersionOrRange?: string\n) {\n  if (!expectedVersionOrRange) {\n    return false;\n  }\n\n  // we never want to go backwards with the expo patch version\n  if (packageName === 'expo') {\n    return semver.ltr(actualVersion, expectedVersionOrRange);\n  }\n\n  // For all other packages, check if the actual version satisfies the expected range\n  const satisfies = semver.satisfies(actualVersion, expectedVersionOrRange, {\n    includePrerelease: true,\n  });\n\n  return !satisfies;\n}\n\nfunction findDependencyType(\n  pkg: PackageJSONConfig,\n  packageName: string\n): IncorrectDependency['packageType'] {\n  if (pkg.devDependencies && packageName in pkg.devDependencies) {\n    return 'devDependencies';\n  }\n\n  return 'dependencies';\n}\n"], "names": ["validateDependenciesVersionsAsync", "logIncorrectDependencies", "getVersionedDependenciesAsync", "isDependencyVersionIncorrect", "debug", "require", "projectRoot", "exp", "pkg", "packagesToCheck", "env", "EXPO_OFFLINE", "Log", "warn", "incorrectDeps", "logInvalidDependency", "packageName", "expectedVersionOrRange", "actualVersion", "chalk", "length", "for<PERSON>ach", "dep", "assert", "sdkVersion", "combinedKnownPackages", "getCombinedKnownVersionsAsync", "resolvedDependencies", "getFilteredObject", "dependencies", "devDependencies", "known", "resolvedPackagesToCheck", "unknown", "getPackagesToCheck", "packageVersions", "resolveAllPackageVersionsAsync", "findIncorrectDependencies", "expo", "install", "exclude", "packagesToExclude", "parsedPackagesToExclude", "reduce", "acc", "npaResult", "npmPackageArg", "name", "incorrectAndExcludedDeps", "filter", "raw", "rawSpec", "type", "<PERSON><PERSON><PERSON><PERSON>", "semver", "validRange", "semverRangeSubset", "map", "includes", "keys", "object", "key", "bundledNativeModules", "dependencyNames", "Object", "dependencyName", "push", "packages", "packageType", "findDependencyType", "ltr", "satisfies", "includePrerelease"], "mappings": "AAAA;;;;;;;;;;;IAkCsBA,iCAAiC,MAAjCA,iCAAiC;IAyBvCC,wBAAwB,MAAxBA,wBAAwB;IA0BlBC,6BAA6B,MAA7BA,6BAA6B;IAsJnCC,4BAA4B,MAA5BA,4BAA4B;;;8DA1OzB,QAAQ;;;;;;;8DACT,OAAO;;;;;;;8DACC,iBAAiB;;;;;;;8DACxB,QAAQ;;;;;;;8DACG,sBAAsB;;;;;;sCAGN,wBAAwB;iCACvB,mBAAmB;2DAC7C,cAAc;qBACf,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC,AAAsB,AAAC;AAqBnF,eAAeL,iCAAiC,CACrDM,WAAmB,EACnBC,GAAmC,EACnCC,GAAsB,EACtBC,eAA0B,EACD;IACzB,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;QACpBC,IAAG,CAACC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMC,aAAa,GAAG,MAAMZ,6BAA6B,CAACI,WAAW,EAAEC,GAAG,EAAEC,GAAG,EAAEC,eAAe,CAAC,AAAC;IAClG,OAAOR,wBAAwB,CAACa,aAAa,CAAC,CAAC;AACjD,CAAC;AAED,SAASC,oBAAoB,CAAC,EAC5BC,WAAW,CAAA,EACXC,sBAAsB,CAAA,EACtBC,aAAa,CAAA,EACO,EAAE;IACtBN,IAAG,CAACC,IAAI,CACNM,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,QAAQ,EAAEH,WAAW,CAAC,cAAc,EAAEE,aAAa,CAAC,6BAA6B,EAAED,sBAAsB,CAAC,CAAC,CAAC,CACnH,CAAC;AACJ,CAAC;AAEM,SAAShB,wBAAwB,CAACa,aAAoC,EAAE;IAC7E,IAAI,CAACA,aAAa,CAACM,MAAM,EAAE;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAEDR,IAAG,CAACC,IAAI,CACNM,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,uGAAuG,CAAC,CAC/G,CAAC;IACFL,aAAa,CAACO,OAAO,CAAC,CAACC,GAAG,GAAKP,oBAAoB,CAACO,GAAG,CAAC,CAAC,CAAC;IAE1DV,IAAG,CAACC,IAAI,CACN,8FAA8F,CAC/F,CAAC;IAEF,OAAO,KAAK,CAAC;AACf,CAAC;AAWM,eAAeX,6BAA6B,CACjDI,WAAmB,EACnBC,GAAmC,EACnCC,GAAsB,EACtBC,eAA0B,EACM;QAoC5BD,GAAS;IAnCb,4DAA4D;IAC5D,8CAA8C;IAC9Ce,IAAAA,OAAM,EAAA,QAAA,EAAChB,GAAG,CAACiB,UAAU,EAAE,wBAAwB,CAAC,CAAC;IAEjD,kEAAkE;IAClE,MAAMC,qBAAqB,GAAG,MAAMC,IAAAA,qBAA6B,8BAAA,EAAC;QAChEpB,WAAW;QACXkB,UAAU,EAAEjB,GAAG,CAACiB,UAAU;KAC3B,CAAC,AAAC;IACH,0DAA0D;IAE1D,MAAMG,oBAAoB,GAAGlB,CAAAA,eAAe,QAAQ,GAAvBA,KAAAA,CAAuB,GAAvBA,eAAe,CAAEW,MAAM,CAAA,GAEhDQ,iBAAiB,CAACnB,eAAe,EAAE;QAAE,GAAGD,GAAG,CAACqB,YAAY;QAAE,GAAGrB,GAAG,CAACsB,eAAe;KAAE,CAAC,GAEnF;QAAE,GAAGtB,GAAG,CAACqB,YAAY;QAAE,GAAGrB,GAAG,CAACsB,eAAe;KAAE,AAAC;IACpD1B,KAAK,CAAC,CAAC,0BAA0B,EAAEG,GAAG,CAACiB,UAAU,CAAC,IAAI,CAAC,EAAEG,oBAAoB,CAAC,CAAC;IAE/E,wEAAwE;IACxE,MAAM,EAAEI,KAAK,EAAEC,uBAAuB,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGC,kBAAkB,CACpET,qBAAqB,EACrBE,oBAAoB,CACrB,AAAC;IACFvB,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE4B,uBAAuB,CAAC,CAAC;IAC/D5B,KAAK,CAAC,CAAC,4DAA4D,CAAC,EAAE6B,OAAO,CAAC,CAAC;IAC/E,4DAA4D;IAC5D,MAAME,eAAe,GAAG,MAAMC,IAAAA,gBAA8B,+BAAA,EAC1D9B,WAAW,EACX0B,uBAAuB,CACxB,AAAC;IACF5B,KAAK,CAAC,CAAC,oBAAoB,CAAC,EAAE+B,eAAe,CAAC,CAAC;IAC/C,qHAAqH;IACrH,IAAIrB,aAAa,GAAGuB,yBAAyB,CAAC7B,GAAG,EAAE2B,eAAe,EAAEV,qBAAqB,CAAC,AAAC;IAC3FrB,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAEU,aAAa,CAAC,CAAC;IAEnD,IAAIN,GAAG,QAAM,GAATA,KAAAA,CAAS,GAATA,CAAAA,GAAS,GAATA,GAAG,CAAE8B,IAAI,SAAA,GAAT9B,KAAAA,CAAS,GAATA,QAAAA,GAAS,CAAE+B,OAAO,SAAT,GAAT/B,KAAAA,CAAS,QAAWgC,OAAO,AAAlB,EAAoB;QAC/B,MAAMC,iBAAiB,GAAGjC,GAAG,CAAC8B,IAAI,CAACC,OAAO,CAACC,OAAO,AAAC;QAEnD,iFAAiF;QACjF,MAAME,uBAAuB,GAAGD,iBAAiB,CAACE,MAAM,CACtD,CAACC,GAAyC,EAAE5B,WAAmB,GAAK;YAClE,MAAM6B,SAAS,GAAGC,IAAAA,cAAa,EAAA,QAAA,EAAC9B,WAAW,CAAC,AAAC;YAC7C,IAAI,OAAO6B,SAAS,CAACE,IAAI,KAAK,QAAQ,EAAE;gBACtCH,GAAG,CAACC,SAAS,CAACE,IAAI,CAAC,GAAGF,SAAS,CAAC;YAClC,OAAO;gBACLD,GAAG,CAAC5B,WAAW,CAAC,GAAG6B,SAAS,CAAC;YAC/B,CAAC;YACD,OAAOD,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CACH,AAAC;QAEF,MAAMI,wBAAwB,GAAGlC,aAAa,CAC3CmC,MAAM,CAAC,CAAC3B,GAAG,GAAK;YACf,IAAIoB,uBAAuB,CAACpB,GAAG,CAACN,WAAW,CAAC,EAAE;gBAC5C,MAAM,EAAE+B,IAAI,CAAA,EAAEG,GAAG,CAAA,EAAEC,OAAO,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGV,uBAAuB,CAACpB,GAAG,CAACN,WAAW,CAAC,AAAC;gBAC9E,MAAMqC,cAAc,GAAG5B,qBAAqB,CAACsB,IAAI,CAAC,AAAC;gBAEnD,oFAAoF;gBACpF,IAAIA,IAAI,KAAKG,GAAG,EAAE;oBAChB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAIE,IAAI,KAAK,SAAS,EAAE;oBAC7B,OAAOC,cAAc,KAAKF,OAAO,CAAC;gBACpC,OAAO,IAAIC,IAAI,KAAK,OAAO,EAAE;oBAC3B,4DAA4D;oBAC5D,IAAI,CAACE,OAAM,EAAA,QAAA,CAACC,UAAU,CAACF,cAAc,CAAC,EAAE;wBACtCjD,KAAK,CACH,CAAC,4DAA4D,EAAE2C,IAAI,CAAC,4BAA4B,CAAC,EACjGM,cAAc,CACf,CAAC;wBACF,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,OAAOG,IAAAA,OAAiB,EAAA,QAAA,EAACH,cAAc,EAAEF,OAAO,CAAC,CAAC;gBACpD,OAAO;oBACL/C,KAAK,CACH,CAAC,kDAAkD,EAAE2C,IAAI,CAAC,4BAA4B,CAAC,EACvFK,IAAI,CACL,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CACDK,GAAG,CAAC,CAACnC,GAAG,GAAKA,GAAG,CAACN,WAAW,CAAC,AAAC;QAEjCZ,KAAK,CACH,CAAC,sEAAsE,CAAC,EACxE4C,wBAAwB,CACzB,CAAC;QACFlC,aAAa,GAAGA,aAAa,CAACmC,MAAM,CAClC,CAAC3B,GAAG,GAAK,CAAC0B,wBAAwB,CAACU,QAAQ,CAACpC,GAAG,CAACN,WAAW,CAAC,CAC7D,CAAC;IACJ,CAAC;IAED,OAAOF,aAAa,CAAC;AACvB,CAAC;AAED,SAASc,iBAAiB,CAAC+B,IAAc,EAAEC,MAA8B,EAAE;IACzE,OAAOD,IAAI,CAAChB,MAAM,CAAyB,CAACC,GAAG,EAAEiB,GAAG,GAAK;QACvDjB,GAAG,CAACiB,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,CAAC;QACvB,OAAOjB,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAED,SAASV,kBAAkB,CACzB4B,oBAA0C,EAC1CjC,YAA4C,EACvB;IACrB,MAAMkC,eAAe,GAAGC,MAAM,CAACL,IAAI,CAAC9B,YAAY,IAAI,EAAE,CAAC,AAAC;IACxD,MAAME,KAAK,GAAa,EAAE,AAAC;IAC3B,MAAME,OAAO,GAAa,EAAE,AAAC;IAC7B,KAAK,MAAMgC,cAAc,IAAIF,eAAe,CAAE;QAC5C,IAAIE,cAAc,IAAIH,oBAAoB,EAAE;YAC1C/B,KAAK,CAACmC,IAAI,CAACD,cAAc,CAAC,CAAC;QAC7B,OAAO;YACLhC,OAAO,CAACiC,IAAI,CAACD,cAAc,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IACD,OAAO;QAAElC,KAAK;QAAEE,OAAO;KAAE,CAAC;AAC5B,CAAC;AAED,SAASI,yBAAyB,CAChC7B,GAAsB,EACtB2B,eAAuC,EACvC2B,oBAA0C,EACnB;IACvB,MAAMK,QAAQ,GAAGH,MAAM,CAACL,IAAI,CAACxB,eAAe,CAAC,AAAC;IAC9C,MAAMrB,aAAa,GAA0B,EAAE,AAAC;IAChD,KAAK,MAAME,WAAW,IAAImD,QAAQ,CAAE;QAClC,MAAMlD,sBAAsB,GAAG6C,oBAAoB,CAAC9C,WAAW,CAAC,AAAC;QACjE,MAAME,aAAa,GAAGiB,eAAe,CAACnB,WAAW,CAAC,AAAC;QACnD,IAAIb,4BAA4B,CAACa,WAAW,EAAEE,aAAa,EAAED,sBAAsB,CAAC,EAAE;YACpFH,aAAa,CAACoD,IAAI,CAAC;gBACjBlD,WAAW;gBACXoD,WAAW,EAAEC,kBAAkB,CAAC7D,GAAG,EAAEQ,WAAW,CAAC;gBACjDC,sBAAsB;gBACtBC,aAAa;aACd,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,OAAOJ,aAAa,CAAC;AACvB,CAAC;AAEM,SAASX,4BAA4B,CAC1Ca,WAAmB,EACnBE,aAAqB,EACrBD,sBAA+B,EAC/B;IACA,IAAI,CAACA,sBAAsB,EAAE;QAC3B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,4DAA4D;IAC5D,IAAID,WAAW,KAAK,MAAM,EAAE;QAC1B,OAAOsC,OAAM,EAAA,QAAA,CAACgB,GAAG,CAACpD,aAAa,EAAED,sBAAsB,CAAC,CAAC;IAC3D,CAAC;IAED,mFAAmF;IACnF,MAAMsD,SAAS,GAAGjB,OAAM,EAAA,QAAA,CAACiB,SAAS,CAACrD,aAAa,EAAED,sBAAsB,EAAE;QACxEuD,iBAAiB,EAAE,IAAI;KACxB,CAAC,AAAC;IAEH,OAAO,CAACD,SAAS,CAAC;AACpB,CAAC;AAED,SAASF,kBAAkB,CACzB7D,GAAsB,EACtBQ,WAAmB,EACiB;IACpC,IAAIR,GAAG,CAACsB,eAAe,IAAId,WAAW,IAAIR,GAAG,CAACsB,eAAe,EAAE;QAC7D,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC"}