{"version": 3, "sources": ["../../../src/utils/nodeEnv.ts"], "sourcesContent": ["import * as env from '@expo/env';\n\n/**\n * Set the environment to production or development\n * lots of tools use this to determine if they should run in a dev mode.\n */\nexport function setNodeEnv(mode: 'development' | 'production') {\n  process.env.NODE_ENV = process.env.NODE_ENV || mode;\n  process.env.BABEL_ENV = process.env.BABEL_ENV || process.env.NODE_ENV;\n\n  // @ts-expect-error: Add support for external React libraries being loaded in the same process.\n  globalThis.__DEV__ = process.env.NODE_ENV !== 'production';\n}\n\n/**\n * Load the dotenv files into the current `process.env` scope.\n * Note, this requires `NODE_ENV` being set through `setNodeEnv`.\n */\nexport function loadEnvFiles(projectRoot: string, options?: Parameters<typeof env.load>[1]) {\n  return env.load(projectRoot, options);\n}\n"], "names": ["setNodeEnv", "loadEnvFiles", "mode", "process", "env", "NODE_ENV", "BABEL_ENV", "globalThis", "__DEV__", "projectRoot", "options", "load"], "mappings": "AAAA;;;;;;;;;;;IAMgBA,UAAU,MAAVA,UAAU;IAYVC,YAAY,MAAZA,YAAY;;;+DAlBP,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB,SAASD,UAAU,CAACE,IAAkC,EAAE;IAC7DC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAGF,OAAO,CAACC,GAAG,CAACC,QAAQ,IAAIH,IAAI,CAAC;IACpDC,OAAO,CAACC,GAAG,CAACE,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACE,SAAS,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;IAEtE,+FAA+F;IAC/FE,UAAU,CAACC,OAAO,GAAGL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC;AAC7D,CAAC;AAMM,SAASJ,YAAY,CAACQ,WAAmB,EAAEC,OAAwC,EAAE;IAC1F,OAAON,IAAG,EAAA,CAACO,IAAI,CAACF,WAAW,EAAEC,OAAO,CAAC,CAAC;AACxC,CAAC"}