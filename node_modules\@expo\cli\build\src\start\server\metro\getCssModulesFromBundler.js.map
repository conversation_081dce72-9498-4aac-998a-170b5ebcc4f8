{"version": 3, "sources": ["../../../../../src/start/server/metro/getCssModulesFromBundler.ts"], "sourcesContent": ["import { MetroConfig } from '@expo/metro-config';\nimport crypto from 'crypto';\nimport type { Module } from 'metro';\nimport { getJsOutput, isJsModule } from 'metro/src/DeltaBundler/Serializers/helpers/js';\nimport type { ReadOnlyDependencies } from 'metro/src/DeltaBundler/types';\nimport type IncrementalBundler from 'metro/src/IncrementalBundler';\nimport splitBundleOptions from 'metro/src/lib/splitBundleOptions';\nimport path from 'path';\n\ntype Options = {\n  processModuleFilter: (modules: Module) => boolean;\n  assetPlugins: readonly string[];\n  platform?: string | null;\n  projectRoot: string;\n  publicPath: string;\n};\n\ntype MetroModuleCSSMetadata = {\n  code: string;\n  lineCount: number;\n  map: any[];\n};\n\nexport type CSSAsset = {\n  // 'styles.css'\n  originFilename: string;\n  // '_expo/static/css/bc6aa0a69dcebf8e8cac1faa76705756.css'\n  filename: string;\n  // '\\ndiv {\\n    background: cyan;\\n}\\n\\n'\n  source: string;\n};\n\n// s = static\nconst STATIC_EXPORT_DIRECTORY = '_expo/static/css';\n\n/** @returns the static CSS assets used in a given bundle. CSS assets are only enabled if the `@expo/metro-config` `transformerPath` is used. */\nexport async function getCssModulesFromBundler(\n  config: MetroConfig,\n  incrementalBundler: IncrementalBundler,\n  options: any\n): Promise<CSSAsset[]> {\n  // Static CSS is a web-only feature.\n  if (options.platform !== 'web') {\n    return [];\n  }\n\n  const { entryFile, onProgress, resolverOptions, transformOptions } = splitBundleOptions(options);\n\n  const dependencies = await incrementalBundler.getDependencies(\n    [entryFile],\n    transformOptions,\n    resolverOptions,\n    { onProgress, shallow: false, lazy: false }\n  );\n\n  return getCssModules(dependencies, {\n    processModuleFilter: config.serializer.processModuleFilter,\n    assetPlugins: config.transformer.assetPlugins,\n    platform: transformOptions.platform,\n    projectRoot: config.server.unstable_serverRoot ?? config.projectRoot,\n    publicPath: config.transformer.publicPath,\n  });\n}\n\nfunction hashString(str: string) {\n  return crypto.createHash('md5').update(str).digest('hex');\n}\n\nfunction getCssModules(\n  dependencies: ReadOnlyDependencies,\n  { processModuleFilter, projectRoot }: Options\n) {\n  const promises = [];\n\n  for (const module of dependencies.values()) {\n    if (\n      isJsModule(module) &&\n      processModuleFilter(module) &&\n      getJsOutput(module).type === 'js/module' &&\n      path.relative(projectRoot, module.path) !== 'package.json'\n    ) {\n      const cssMetadata = getCssMetadata(module);\n      if (cssMetadata) {\n        const contents = cssMetadata.code;\n        const filename = path.join(\n          // Consistent location\n          STATIC_EXPORT_DIRECTORY,\n          // Hashed file contents + name for caching\n          getFileName(module.path) + '-' + hashString(module.path + contents) + '.css'\n        );\n        promises.push({\n          originFilename: path.relative(projectRoot, module.path),\n          filename,\n          source: contents,\n        });\n      }\n    }\n  }\n\n  return promises;\n}\n\nfunction getCssMetadata(module: Module): MetroModuleCSSMetadata | null {\n  const data = module.output[0]?.data;\n  if (data && typeof data === 'object' && 'css' in data) {\n    if (typeof data.css !== 'object' || !('code' in (data as any).css)) {\n      throw new Error(\n        `Unexpected CSS metadata in Metro module (${module.path}): ${JSON.stringify(data.css)}`\n      );\n    }\n    return data.css as MetroModuleCSSMetadata;\n  }\n  return null;\n}\n\nexport function getFileName(module: string) {\n  return path.basename(module).replace(/\\.[^.]+$/, '');\n}\n"], "names": ["getCssModulesFromBundler", "getFileName", "STATIC_EXPORT_DIRECTORY", "config", "incrementalBundler", "options", "platform", "entryFile", "onProgress", "resolverOptions", "transformOptions", "splitBundleOptions", "dependencies", "getDependencies", "shallow", "lazy", "getCssModules", "processModuleFilter", "serializer", "assetPlugins", "transformer", "projectRoot", "server", "unstable_serverRoot", "publicPath", "hashString", "str", "crypto", "createHash", "update", "digest", "promises", "module", "values", "isJsModule", "getJsOutput", "type", "path", "relative", "cssMetadata", "getCssMetadata", "contents", "code", "filename", "join", "push", "originFilename", "source", "data", "output", "css", "Error", "JSON", "stringify", "basename", "replace"], "mappings": "AAAA;;;;;;;;;;;IAoCsBA,wBAAwB,MAAxBA,wBAAwB;IA+E9BC,WAAW,MAAXA,WAAW;;;8DAlHR,QAAQ;;;;;;;yBAEa,+CAA+C;;;;;;;8DAGxD,kCAAkC;;;;;;;8DAChD,MAAM;;;;;;;;;;;AAyBvB,aAAa;AACb,MAAMC,uBAAuB,GAAG,kBAAkB,AAAC;AAG5C,eAAeF,wBAAwB,CAC5CG,MAAmB,EACnBC,kBAAsC,EACtCC,OAAY,EACS;IACrB,oCAAoC;IACpC,IAAIA,OAAO,CAACC,QAAQ,KAAK,KAAK,EAAE;QAC9B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAM,EAAEC,SAAS,CAAA,EAAEC,UAAU,CAAA,EAAEC,eAAe,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAGC,IAAAA,mBAAkB,EAAA,QAAA,EAACN,OAAO,CAAC,AAAC;IAEjG,MAAMO,YAAY,GAAG,MAAMR,kBAAkB,CAACS,eAAe,CAC3D;QAACN,SAAS;KAAC,EACXG,gBAAgB,EAChBD,eAAe,EACf;QAAED,UAAU;QAAEM,OAAO,EAAE,KAAK;QAAEC,IAAI,EAAE,KAAK;KAAE,CAC5C,AAAC;IAEF,OAAOC,aAAa,CAACJ,YAAY,EAAE;QACjCK,mBAAmB,EAAEd,MAAM,CAACe,UAAU,CAACD,mBAAmB;QAC1DE,YAAY,EAAEhB,MAAM,CAACiB,WAAW,CAACD,YAAY;QAC7Cb,QAAQ,EAAEI,gBAAgB,CAACJ,QAAQ;QACnCe,WAAW,EAAElB,MAAM,CAACmB,MAAM,CAACC,mBAAmB,IAAIpB,MAAM,CAACkB,WAAW;QACpEG,UAAU,EAAErB,MAAM,CAACiB,WAAW,CAACI,UAAU;KAC1C,CAAC,CAAC;AACL,CAAC;AAED,SAASC,UAAU,CAACC,GAAW,EAAE;IAC/B,OAAOC,OAAM,EAAA,QAAA,CAACC,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED,SAASd,aAAa,CACpBJ,YAAkC,EAClC,EAAEK,mBAAmB,CAAA,EAAEI,WAAW,CAAA,EAAW,EAC7C;IACA,MAAMU,QAAQ,GAAG,EAAE,AAAC;IAEpB,KAAK,MAAMC,MAAM,IAAIpB,YAAY,CAACqB,MAAM,EAAE,CAAE;QAC1C,IACEC,IAAAA,GAAU,EAAA,WAAA,EAACF,MAAM,CAAC,IAClBf,mBAAmB,CAACe,MAAM,CAAC,IAC3BG,IAAAA,GAAW,EAAA,YAAA,EAACH,MAAM,CAAC,CAACI,IAAI,KAAK,WAAW,IACxCC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACjB,WAAW,EAAEW,MAAM,CAACK,IAAI,CAAC,KAAK,cAAc,EAC1D;YACA,MAAME,WAAW,GAAGC,cAAc,CAACR,MAAM,CAAC,AAAC;YAC3C,IAAIO,WAAW,EAAE;gBACf,MAAME,QAAQ,GAAGF,WAAW,CAACG,IAAI,AAAC;gBAClC,MAAMC,QAAQ,GAAGN,KAAI,EAAA,QAAA,CAACO,IAAI,CACxB,sBAAsB;gBACtB1C,uBAAuB,EACvB,0CAA0C;gBAC1CD,WAAW,CAAC+B,MAAM,CAACK,IAAI,CAAC,GAAG,GAAG,GAAGZ,UAAU,CAACO,MAAM,CAACK,IAAI,GAAGI,QAAQ,CAAC,GAAG,MAAM,CAC7E,AAAC;gBACFV,QAAQ,CAACc,IAAI,CAAC;oBACZC,cAAc,EAAET,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACjB,WAAW,EAAEW,MAAM,CAACK,IAAI,CAAC;oBACvDM,QAAQ;oBACRI,MAAM,EAAEN,QAAQ;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAOV,QAAQ,CAAC;AAClB,CAAC;AAED,SAASS,cAAc,CAACR,MAAc,EAAiC;QACxDA,GAAgB;IAA7B,MAAMgB,IAAI,GAAGhB,CAAAA,GAAgB,GAAhBA,MAAM,CAACiB,MAAM,CAAC,CAAC,CAAC,SAAM,GAAtBjB,KAAAA,CAAsB,GAAtBA,GAAgB,CAAEgB,IAAI,AAAC;IACpC,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAIA,IAAI,EAAE;QACrD,IAAI,OAAOA,IAAI,CAACE,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAI,AAACF,IAAI,CAASE,GAAG,CAAC,EAAE;YAClE,MAAM,IAAIC,KAAK,CACb,CAAC,yCAAyC,EAAEnB,MAAM,CAACK,IAAI,CAAC,GAAG,EAAEe,IAAI,CAACC,SAAS,CAACL,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,CACxF,CAAC;QACJ,CAAC;QACD,OAAOF,IAAI,CAACE,GAAG,CAA2B;IAC5C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAASjD,WAAW,CAAC+B,MAAc,EAAE;IAC1C,OAAOK,KAAI,EAAA,QAAA,CAACiB,QAAQ,CAACtB,MAAM,CAAC,CAACuB,OAAO,aAAa,EAAE,CAAC,CAAC;AACvD,CAAC"}