{"version": 3, "sources": ["../../../../../src/start/server/middleware/mutations.ts"], "sourcesContent": ["import type { Server as ConnectServer, HandleFunction } from 'connect';\n\n/**\n * Prepends a `middleware` to current server middleware stack.\n *\n * @param app connect app server instance\n * @param middleware target middleware to be prepended\n */\nexport function prependMiddleware(app: ConnectServer, middleware: HandleFunction) {\n  app.use(middleware);\n  app.stack.unshift(app.stack.pop()!);\n}\n\n/**\n * Replaces source middleware with a new middlware in connect app\n *\n * @param app connect app server instance\n * @param sourceMiddleware source middlware to be matched and replaces\n * @param targetMiddleware new middlware\n */\nexport function replaceMiddlewareWith(\n  app: ConnectServer,\n  sourceMiddleware: HandleFunction,\n  targetMiddleware: HandleFunction\n) {\n  const item = app.stack.find((middleware) => {\n    const handlerCode = middleware.handle.toString();\n    return !handlerCode.includes('[native code]') && handlerCode === sourceMiddleware.toString();\n  });\n  if (item) {\n    item.handle = targetMiddleware;\n  }\n}\n"], "names": ["prependMiddleware", "replaceMiddlewareWith", "app", "middleware", "use", "stack", "unshift", "pop", "sourceMiddleware", "targetMiddleware", "item", "find", "handlerCode", "handle", "toString", "includes"], "mappings": "AAAA;;;;;;;;;;;IAQgBA,iBAAiB,MAAjBA,iBAAiB;IAYjBC,qBAAqB,MAArBA,qBAAqB;;AAZ9B,SAASD,iBAAiB,CAACE,GAAkB,EAAEC,UAA0B,EAAE;IAChFD,GAAG,CAACE,GAAG,CAACD,UAAU,CAAC,CAAC;IACpBD,GAAG,CAACG,KAAK,CAACC,OAAO,CAACJ,GAAG,CAACG,KAAK,CAACE,GAAG,EAAE,CAAE,CAAC;AACtC,CAAC;AASM,SAASN,qBAAqB,CACnCC,GAAkB,EAClBM,gBAAgC,EAChCC,gBAAgC,EAChC;IACA,MAAMC,IAAI,GAAGR,GAAG,CAACG,KAAK,CAACM,IAAI,CAAC,CAACR,UAAU,GAAK;QAC1C,MAAMS,WAAW,GAAGT,UAAU,CAACU,MAAM,CAACC,QAAQ,EAAE,AAAC;QACjD,OAAO,CAACF,WAAW,CAACG,QAAQ,CAAC,eAAe,CAAC,IAAIH,WAAW,KAAKJ,gBAAgB,CAACM,QAAQ,EAAE,CAAC;IAC/F,CAAC,CAAC,AAAC;IACH,IAAIJ,IAAI,EAAE;QACRA,IAAI,CAACG,MAAM,GAAGJ,gBAAgB,CAAC;IACjC,CAAC;AACH,CAAC"}