{"version": 3, "sources": ["../../../../../src/utils/telemetry/utils/constants.ts"], "sourcesContent": ["import { env } from '../../env';\n\nexport const TELEMETRY_ENDPOINT = 'https://cdp.expo.dev/v1/batch';\n\nexport const TELEMETRY_TARGET =\n  env.EXPO_STAGING || env.EXPO_LOCAL\n    ? '24TKICqYKilXM480mA7ktgVDdea'\n    : '24TKR7CQAaGgIrLTgu3Fp4OdOkI'; // expo unified\n"], "names": ["TELEMETRY_ENDPOINT", "TELEMETRY_TARGET", "env", "EXPO_STAGING", "EXPO_LOCAL"], "mappings": "AAAA;;;;;;;;;;;IAEaA,kBAAkB,MAAlBA,kBAAkB;IAElBC,gBAAgB,MAAhBA,gBAAgB;;qBAJT,WAAW;AAExB,MAAMD,kBAAkB,GAAG,+BAA+B,AAAC;AAE3D,MAAMC,gBAAgB,GAC3BC,IAAG,IAAA,CAACC,YAAY,IAAID,IAAG,IAAA,CAACE,UAAU,GAC9B,6BAA6B,GAC7B,6BAA6B,AAAC,EAAC,eAAe"}