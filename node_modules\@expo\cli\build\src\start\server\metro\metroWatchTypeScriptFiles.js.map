{"version": 3, "sources": ["../../../../../src/start/server/metro/metroWatchTypeScriptFiles.ts"], "sourcesContent": ["import path from 'path';\n\nimport type { ServerLike } from '../BundlerDevServer';\n\nconst debug = require('debug')(\n  'expo:start:server:metro:metroWatchTypeScriptFiles'\n) as typeof console.log;\n\nexport interface MetroWatchTypeScriptFilesOptions {\n  projectRoot: string;\n  metro: import('metro').Server;\n  server: ServerLike;\n  /* Include tsconfig.json in the watcher */\n  tsconfig?: boolean;\n  callback: (event: WatchEvent) => void;\n  /* Array of eventTypes to watch. Defaults to all events */\n  eventTypes?: string[];\n  /* Throlle the callback. When true and  a group of events are recieved, callback it will only be called with the\n   * first event */\n  throttle?: boolean;\n}\n\ninterface WatchEvent {\n  filePath: string;\n  metadata?: {\n    type: 'f' | 'd' | 'l'; // Regular file / Directory / Symlink\n  } | null;\n  type: string;\n}\n\n/**\n * Use the native file watcher / Metro ruleset to detect if a\n * TypeScript file is added to the project during development.\n */\nexport function metroWatchTypeScriptFiles({\n  metro,\n  server,\n  projectRoot,\n  callback,\n  tsconfig = false,\n  throttle = false,\n  eventTypes = ['add', 'change', 'delete'],\n}: MetroWatchTypeScriptFilesOptions): () => void {\n  const watcher = metro.getBundler().getBundler().getWatcher();\n\n  const tsconfigPath = path.join(projectRoot, 'tsconfig.json');\n\n  const listener = ({ eventsQueue }: { eventsQueue: WatchEvent[] }) => {\n    for (const event of eventsQueue) {\n      if (\n        eventTypes.includes(event.type) &&\n        event.metadata?.type !== 'd' &&\n        // We need to ignore node_modules because Metro will add all of the files in node_modules to the watcher.\n        !/node_modules/.test(event.filePath) &&\n        // Ignore declaration files\n        !/\\.d\\.ts$/.test(event.filePath)\n      ) {\n        const { filePath } = event;\n        // Is TypeScript?\n        if (\n          // If the user adds a TypeScript file to the observable files in their project.\n          /\\.tsx?$/.test(filePath) ||\n          // Or if the user adds a tsconfig.json file to the project root.\n          (tsconfig && filePath === tsconfigPath)\n        ) {\n          debug('Detected TypeScript file changed in the project: ', filePath);\n          callback(event);\n\n          if (throttle) {\n            return;\n          }\n        }\n      }\n    }\n  };\n\n  debug('Waiting for TypeScript files to be added to the project...');\n  watcher.addListener('change', listener);\n  watcher.addListener('add', listener);\n\n  const off = () => {\n    watcher.removeListener('change', listener);\n    watcher.removeListener('add', listener);\n  };\n\n  server.addListener?.('close', off);\n  return off;\n}\n"], "names": ["metroWatchTypeScriptFiles", "debug", "require", "metro", "server", "projectRoot", "callback", "tsconfig", "throttle", "eventTypes", "watcher", "getBundler", "getW<PERSON>er", "tsconfigPath", "path", "join", "listener", "eventsQueue", "event", "includes", "type", "metadata", "test", "filePath", "addListener", "off", "removeListener"], "mappings": "AAAA;;;;+BAkCgBA,2BAAyB;;aAAzBA,yBAAyB;;;8DAlCxB,MAAM;;;;;;;;;;;AAIvB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,mDAAmD,CACpD,AAAsB,AAAC;AA4BjB,SAASF,yBAAyB,CAAC,EACxCG,KAAK,CAAA,EACLC,MAAM,CAAA,EACNC,WAAW,CAAA,EACXC,QAAQ,CAAA,EACRC,QAAQ,EAAG,KAAK,CAAA,EAChBC,QAAQ,EAAG,KAAK,CAAA,EAChBC,UAAU,EAAG;IAAC,KAAK;IAAE,QAAQ;IAAE,QAAQ;CAAC,CAAA,EACP,EAAc;IAC/C,MAAMC,OAAO,GAAGP,KAAK,CAACQ,UAAU,EAAE,CAACA,UAAU,EAAE,CAACC,UAAU,EAAE,AAAC;IAE7D,MAAMC,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACV,WAAW,EAAE,eAAe,CAAC,AAAC;IAE7D,MAAMW,QAAQ,GAAG,CAAC,EAAEC,WAAW,CAAA,EAAiC,GAAK;QACnE,KAAK,MAAMC,KAAK,IAAID,WAAW,CAAE;gBAG7BC,GAAc;YAFhB,IACET,UAAU,CAACU,QAAQ,CAACD,KAAK,CAACE,IAAI,CAAC,IAC/BF,CAAAA,CAAAA,GAAc,GAAdA,KAAK,CAACG,QAAQ,SAAM,GAApBH,KAAAA,CAAoB,GAApBA,GAAc,CAAEE,IAAI,CAAA,KAAK,GAAG,IAC5B,yGAAyG;YACzG,CAAC,eAAeE,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAAC,IACpC,2BAA2B;YAC3B,CAAC,WAAWD,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAAC,EAChC;gBACA,MAAM,EAAEA,QAAQ,CAAA,EAAE,GAAGL,KAAK,AAAC;gBAC3B,iBAAiB;gBACjB,IACE,+EAA+E;gBAC/E,UAAUI,IAAI,CAACC,QAAQ,CAAC,IACxB,gEAAgE;gBAChE,CAAChB,QAAQ,IAAIgB,QAAQ,KAAKV,YAAY,CAAC,EACvC;oBACAZ,KAAK,CAAC,mDAAmD,EAAEsB,QAAQ,CAAC,CAAC;oBACrEjB,QAAQ,CAACY,KAAK,CAAC,CAAC;oBAEhB,IAAIV,QAAQ,EAAE;wBACZ,OAAO;oBACT,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,AAAC;IAEFP,KAAK,CAAC,4DAA4D,CAAC,CAAC;IACpES,OAAO,CAACc,WAAW,CAAC,QAAQ,EAAER,QAAQ,CAAC,CAAC;IACxCN,OAAO,CAACc,WAAW,CAAC,KAAK,EAAER,QAAQ,CAAC,CAAC;IAErC,MAAMS,GAAG,GAAG,IAAM;QAChBf,OAAO,CAACgB,cAAc,CAAC,QAAQ,EAAEV,QAAQ,CAAC,CAAC;QAC3CN,OAAO,CAACgB,cAAc,CAAC,KAAK,EAAEV,QAAQ,CAAC,CAAC;IAC1C,CAAC,AAAC;IAEFZ,MAAM,CAACoB,WAAW,QAAgB,GAAlCpB,KAAAA,CAAkC,GAAlCA,MAAM,CAACoB,WAAW,CAAG,OAAO,EAAEC,GAAG,CAAC,CAAC;IACnC,OAAOA,GAAG,CAAC;AACb,CAAC"}