{"version": 3, "sources": ["../../../src/utils/errors.ts"], "sourcesContent": ["import { AssertionError } from 'assert';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\n\nimport { exit, exception, warn } from '../log';\n\nconst ERROR_PREFIX = 'Error: ';\n\n/**\n * General error, formatted as a message in red text when caught by expo-cli (no stack trace is printed). Should be used in favor of `log.error()` in most cases.\n */\nexport class CommandError extends Error {\n  name = 'CommandError';\n  readonly isCommandError = true;\n\n  constructor(\n    public code: string,\n    message: string = ''\n  ) {\n    super('');\n    // If e.toString() was called to get `message` we don't want it to look\n    // like \"Error: Error:\".\n    if (message.startsWith(ERROR_PREFIX)) {\n      message = message.substring(ERROR_PREFIX.length);\n    }\n\n    this.message = message || code;\n  }\n}\n\nexport class AbortCommandError extends CommandError {\n  constructor() {\n    super('ABORTED', 'Interactive prompt was cancelled.');\n  }\n}\n\n/**\n * Used to end a CLI process without printing a stack trace in the Expo CLI. Should be used in favor of `process.exit`.\n */\nexport class SilentError extends CommandError {\n  constructor(messageOrError?: string | Error) {\n    const message =\n      (typeof messageOrError === 'string' ? messageOrError : messageOrError?.message) ??\n      'This error should fail silently in the CLI';\n    super('SILENT', message);\n    if (typeof messageOrError !== 'string') {\n      // forward the props of the incoming error for tests or processes outside of expo-cli that use expo cli internals.\n      this.stack = messageOrError?.stack ?? this.stack;\n      this.name = messageOrError?.name ?? this.name;\n    }\n  }\n}\n\nexport function logCmdError(error: any): never {\n  if (!(error instanceof Error)) {\n    throw error;\n  }\n  if (error instanceof AbortCommandError || error instanceof SilentError) {\n    // Do nothing, this is used for prompts or other cases that were custom logged.\n    process.exit(0);\n  } else if (\n    error instanceof CommandError ||\n    error instanceof AssertionError ||\n    error.name === 'ApiV2Error' ||\n    error.name === 'ConfigError'\n  ) {\n    // Print the stack trace in debug mode only.\n    exit(error);\n  }\n\n  const errorDetails = error.stack ? '\\n' + chalk.gray(error.stack) : '';\n\n  exit(chalk.red(error.toString()) + errorDetails);\n}\n\n/** This should never be thrown in production. */\nexport class UnimplementedError extends Error {\n  constructor() {\n    super('Unimplemented');\n    this.name = 'UnimplementedError';\n  }\n}\n\n/**\n * Add additional information when EMFILE errors are encountered.\n * These errors originate from Metro's FSEventsWatcher due to `fsevents` going over MacOS system limit.\n * Unfortunately, these limits in macOS are relatively low compared to an average React Native project.\n *\n * @see https://github.com/expo/expo/issues/29083\n * @see https://github.com/facebook/metro/issues/834\n * @see https://github.com/fsevents/fsevents/issues/42#issuecomment-62632234\n */\nfunction handleTooManyOpenFileErrors(error: any) {\n  // Only enable special logging when running on macOS and are running into the `EMFILE` error\n  if ('code' in error && error.code === 'EMFILE' && process.platform === 'darwin') {\n    try {\n      // Try to recover watchman, if it's not installed this will throw\n      execSync('watchman shutdown-server', { stdio: 'ignore' });\n      // NOTE(cedric): this both starts the watchman server and resets all watchers\n      execSync('watchman watch-del-all', { stdio: 'ignore' });\n\n      warn(\n        'Watchman is installed but was likely not enabled when starting Metro, try starting your project again.\\nIf this problem persists, follow the troubleshooting guide of Watchman: https://facebook.github.io/watchman/docs/troubleshooting'\n      );\n    } catch {\n      warn(\n        `Your macOS system limit does not allow enough watchers for Metro, install Watchman instead. Learn more: https://facebook.github.io/watchman/docs/install`\n      );\n    }\n\n    exception(error);\n    process.exit(1);\n  }\n\n  throw error;\n}\n\nprocess.on('uncaughtException', handleTooManyOpenFileErrors);\n"], "names": ["CommandError", "AbortCommandError", "SilentError", "logCmdError", "UnimplementedError", "ERROR_PREFIX", "Error", "constructor", "code", "message", "name", "isCommandError", "startsWith", "substring", "length", "messageOrError", "stack", "error", "process", "exit", "AssertionError", "errorDetails", "chalk", "gray", "red", "toString", "handleTooManyOpenFileErrors", "platform", "execSync", "stdio", "warn", "exception", "on"], "mappings": "AAAA;;;;;;;;;;;IAWaA,YAAY,MAAZA,YAAY;IAmBZC,iBAAiB,MAAjBA,iBAAiB;IASjBC,WAAW,MAAXA,WAAW;IAcRC,WAAW,MAAXA,WAAW;IAuBdC,kBAAkB,MAAlBA,kBAAkB;;;yBA5EA,QAAQ;;;;;;;8DACrB,OAAO;;;;;;;yBACA,eAAe;;;;;;qBAEF,QAAQ;;;;;;AAE9C,MAAMC,YAAY,GAAG,SAAS,AAAC;AAKxB,MAAML,YAAY,SAASM,KAAK;IAIrCC,YACSC,IAAY,EACnBC,OAAe,GAAG,EAAE,CACpB;QACA,KAAK,CAAC,EAAE,CAAC,CAAC;QAHHD,YAAAA,IAAY,CAAA;aAJrBE,IAAI,GAAG,cAAc;aACZC,cAAc,GAAG,IAAI;QAO5B,uEAAuE;QACvE,wBAAwB;QACxB,IAAIF,OAAO,CAACG,UAAU,CAACP,YAAY,CAAC,EAAE;YACpCI,OAAO,GAAGA,OAAO,CAACI,SAAS,CAACR,YAAY,CAACS,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAACL,OAAO,GAAGA,OAAO,IAAID,IAAI,CAAC;IACjC;CACD;AAEM,MAAMP,iBAAiB,SAASD,YAAY;IACjDO,aAAc;QACZ,KAAK,CAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC;IACxD;CACD;AAKM,MAAML,WAAW,SAASF,YAAY;IAC3CO,YAAYQ,cAA+B,CAAE;QAC3C,MAAMN,OAAO,GACX,CAAC,OAAOM,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAGA,cAAc,QAAS,GAAvBA,KAAAA,CAAuB,GAAvBA,cAAc,CAAEN,OAAO,CAAC,IAC/E,4CAA4C,AAAC;QAC/C,KAAK,CAAC,QAAQ,EAAEA,OAAO,CAAC,CAAC;QACzB,IAAI,OAAOM,cAAc,KAAK,QAAQ,EAAE;YACtC,kHAAkH;YAClH,IAAI,CAACC,KAAK,GAAGD,CAAAA,cAAc,QAAO,GAArBA,KAAAA,CAAqB,GAArBA,cAAc,CAAEC,KAAK,CAAA,IAAI,IAAI,CAACA,KAAK,CAAC;YACjD,IAAI,CAACN,IAAI,GAAGK,CAAAA,cAAc,QAAM,GAApBA,KAAAA,CAAoB,GAApBA,cAAc,CAAEL,IAAI,CAAA,IAAI,IAAI,CAACA,IAAI,CAAC;QAChD,CAAC;IACH;CACD;AAEM,SAASP,WAAW,CAACc,KAAU,EAAS;IAC7C,IAAI,CAAC,CAACA,KAAK,YAAYX,KAAK,CAAC,EAAE;QAC7B,MAAMW,KAAK,CAAC;IACd,CAAC;IACD,IAAIA,KAAK,YAAYhB,iBAAiB,IAAIgB,KAAK,YAAYf,WAAW,EAAE;QACtE,+EAA+E;QAC/EgB,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,OAAO,IACLF,KAAK,YAAYjB,YAAY,IAC7BiB,KAAK,YAAYG,OAAc,EAAA,eAAA,IAC/BH,KAAK,CAACP,IAAI,KAAK,YAAY,IAC3BO,KAAK,CAACP,IAAI,KAAK,aAAa,EAC5B;QACA,4CAA4C;QAC5CS,IAAAA,IAAI,KAAA,EAACF,KAAK,CAAC,CAAC;IACd,CAAC;IAED,MAAMI,YAAY,GAAGJ,KAAK,CAACD,KAAK,GAAG,IAAI,GAAGM,MAAK,EAAA,QAAA,CAACC,IAAI,CAACN,KAAK,CAACD,KAAK,CAAC,GAAG,EAAE,AAAC;IAEvEG,IAAAA,IAAI,KAAA,EAACG,MAAK,EAAA,QAAA,CAACE,GAAG,CAACP,KAAK,CAACQ,QAAQ,EAAE,CAAC,GAAGJ,YAAY,CAAC,CAAC;AACnD,CAAC;AAGM,MAAMjB,kBAAkB,SAASE,KAAK;IAC3CC,aAAc;QACZ,KAAK,CAAC,eAAe,CAAC,CAAC;QACvB,IAAI,CAACG,IAAI,GAAG,oBAAoB,CAAC;IACnC;CACD;AAED;;;;;;;;CAQC,GACD,SAASgB,2BAA2B,CAACT,KAAU,EAAE;IAC/C,4FAA4F;IAC5F,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACT,IAAI,KAAK,QAAQ,IAAIU,OAAO,CAACS,QAAQ,KAAK,QAAQ,EAAE;QAC/E,IAAI;YACF,iEAAiE;YACjEC,IAAAA,aAAQ,EAAA,SAAA,EAAC,0BAA0B,EAAE;gBAAEC,KAAK,EAAE,QAAQ;aAAE,CAAC,CAAC;YAC1D,6EAA6E;YAC7ED,IAAAA,aAAQ,EAAA,SAAA,EAAC,wBAAwB,EAAE;gBAAEC,KAAK,EAAE,QAAQ;aAAE,CAAC,CAAC;YAExDC,IAAAA,IAAI,KAAA,EACF,0OAA0O,CAC3O,CAAC;QACJ,EAAE,OAAM;YACNA,IAAAA,IAAI,KAAA,EACF,CAAC,wJAAwJ,CAAC,CAC3J,CAAC;QACJ,CAAC;QAEDC,IAAAA,IAAS,UAAA,EAACd,KAAK,CAAC,CAAC;QACjBC,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,MAAMF,KAAK,CAAC;AACd,CAAC;AAEDC,OAAO,CAACc,EAAE,CAAC,mBAAmB,EAAEN,2BAA2B,CAAC,CAAC"}