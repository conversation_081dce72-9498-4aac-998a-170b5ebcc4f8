"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    formatProjectFilePath: ()=>formatProjectFilePath,
    getStackFormattedLocation: ()=>getStackFormattedLocation
});
function formatProjectFilePath(projectRoot, file) {
    if (file == null) {
        return "<unknown>";
    }
    return pathRelativeToPath(file.replace(/\\/g, "/"), projectRoot.replace(/\\/g, "/")).replace(/\?.*$/, "");
}
function pathRelativeToPath(path, relativeTo, sep = "/") {
    const relativeToParts = relativeTo.split(sep);
    const pathParts = path.split(sep);
    let i = 0;
    while(i < relativeToParts.length && i < pathParts.length){
        if (relativeToParts[i] !== pathParts[i]) {
            break;
        }
        i++;
    }
    return pathParts.slice(i).join(sep);
}
function getStackFormattedLocation(projectRoot, frame) {
    const column = frame.column != null && parseInt(String(frame.column), 10);
    const location = formatProjectFilePath(projectRoot, frame.file) + (frame.lineNumber != null ? ":" + frame.lineNumber + (column && !isNaN(column) ? ":" + (column + 1) : "") : "");
    return location;
}

//# sourceMappingURL=formatProjectFilePath.js.map