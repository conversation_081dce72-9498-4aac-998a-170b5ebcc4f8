{"version": 3, "sources": ["../../../../src/utils/tsconfig/evaluateTsConfig.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nexport function evaluateTsConfig(ts: typeof import('typescript'), tsConfigPath: string) {\n  const formatDiagnosticsHost: import('typescript').FormatDiagnosticsHost = {\n    getNewLine: () => require('os').EOL,\n    getCurrentDirectory: ts.sys.getCurrentDirectory,\n    getCanonicalFileName: (fileName: string) => fileName,\n  };\n\n  try {\n    const { config, error } = ts.readConfigFile(tsConfigPath, ts.sys.readFile);\n\n    if (error) {\n      throw new Error(ts.formatDiagnostic(error, formatDiagnosticsHost));\n    }\n\n    const jsonFileContents = ts.parseJsonConfigFileContent(\n      config,\n      {\n        ...ts.sys,\n        readDirectory: (_, ext) => [ext ? `file${ext[0]}` : `file.ts`],\n      },\n      path.dirname(tsConfigPath)\n    );\n\n    if (jsonFileContents.errors) {\n      // filter out \"no inputs were found in config file\" error\n      jsonFileContents.errors = jsonFileContents.errors.filter(({ code }) => code !== 18003);\n    }\n\n    if (jsonFileContents.errors?.length) {\n      throw new Error(ts.formatDiagnostic(jsonFileContents.errors[0], formatDiagnosticsHost));\n    }\n\n    return { compilerOptions: jsonFileContents.options, raw: config.raw };\n  } catch (error: any) {\n    if (error?.name === 'SyntaxError') {\n      throw new Error('tsconfig.json is invalid:\\n' + (error.message ?? ''));\n    }\n    throw error;\n  }\n}\n\nexport function importTypeScriptFromProjectOptionally(\n  projectRoot: string\n): typeof import('typescript') | null {\n  const resolvedPath = resolveFrom.silent(projectRoot, 'typescript');\n  if (!resolvedPath) {\n    return null;\n  }\n  return require(resolvedPath);\n}\n"], "names": ["evaluateTsConfig", "importTypeScriptFromProjectOptionally", "ts", "tsConfigPath", "formatDiagnosticsHost", "getNewLine", "require", "EOL", "getCurrentDirectory", "sys", "getCanonicalFileName", "fileName", "jsonFileContents", "config", "error", "readConfigFile", "readFile", "Error", "formatDiagnostic", "parseJsonConfigFileContent", "readDirectory", "_", "ext", "path", "dirname", "errors", "filter", "code", "length", "compilerOptions", "options", "raw", "name", "message", "projectRoot", "<PERSON><PERSON><PERSON>", "resolveFrom", "silent"], "mappings": "AAAA;;;;;;;;;;;IAGgBA,gBAAgB,MAAhBA,gBAAgB;IAyChBC,qCAAqC,MAArCA,qCAAqC;;;8DA5CpC,MAAM;;;;;;;8DACC,cAAc;;;;;;;;;;;AAE/B,SAASD,gBAAgB,CAACE,EAA+B,EAAEC,YAAoB,EAAE;IACtF,MAAMC,qBAAqB,GAA+C;QACxEC,UAAU,EAAE,IAAMC,OAAO,CAAC,IAAI,CAAC,CAACC,GAAG;QACnCC,mBAAmB,EAAEN,EAAE,CAACO,GAAG,CAACD,mBAAmB;QAC/CE,oBAAoB,EAAE,CAACC,QAAgB,GAAKA,QAAQ;KACrD,AAAC;IAEF,IAAI;YAqBEC,GAAuB;QApB3B,MAAM,EAAEC,MAAM,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGZ,EAAE,CAACa,cAAc,CAACZ,YAAY,EAAED,EAAE,CAACO,GAAG,CAACO,QAAQ,CAAC,AAAC;QAE3E,IAAIF,KAAK,EAAE;YACT,MAAM,IAAIG,KAAK,CAACf,EAAE,CAACgB,gBAAgB,CAACJ,KAAK,EAAEV,qBAAqB,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,MAAMQ,gBAAgB,GAAGV,EAAE,CAACiB,0BAA0B,CACpDN,MAAM,EACN;YACE,GAAGX,EAAE,CAACO,GAAG;YACTW,aAAa,EAAE,CAACC,CAAC,EAAEC,GAAG,GAAK;oBAACA,GAAG,GAAG,CAAC,IAAI,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;iBAAC;SAC/D,EACDC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACrB,YAAY,CAAC,CAC3B,AAAC;QAEF,IAAIS,gBAAgB,CAACa,MAAM,EAAE;YAC3B,yDAAyD;YACzDb,gBAAgB,CAACa,MAAM,GAAGb,gBAAgB,CAACa,MAAM,CAACC,MAAM,CAAC,CAAC,EAAEC,IAAI,CAAA,EAAE,GAAKA,IAAI,KAAK,KAAK,CAAC,CAAC;QACzF,CAAC;QAED,IAAIf,CAAAA,GAAuB,GAAvBA,gBAAgB,CAACa,MAAM,SAAQ,GAA/Bb,KAAAA,CAA+B,GAA/BA,GAAuB,CAAEgB,MAAM,EAAE;YACnC,MAAM,IAAIX,KAAK,CAACf,EAAE,CAACgB,gBAAgB,CAACN,gBAAgB,CAACa,MAAM,CAAC,CAAC,CAAC,EAAErB,qBAAqB,CAAC,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO;YAAEyB,eAAe,EAAEjB,gBAAgB,CAACkB,OAAO;YAAEC,GAAG,EAAElB,MAAM,CAACkB,GAAG;SAAE,CAAC;IACxE,EAAE,OAAOjB,MAAK,EAAO;QACnB,IAAIA,CAAAA,MAAK,QAAM,GAAXA,KAAAA,CAAW,GAAXA,MAAK,CAAEkB,IAAI,CAAA,KAAK,aAAa,EAAE;YACjC,MAAM,IAAIf,KAAK,CAAC,6BAA6B,GAAG,CAACH,MAAK,CAACmB,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,MAAMnB,MAAK,CAAC;IACd,CAAC;AACH,CAAC;AAEM,SAASb,qCAAqC,CACnDiC,WAAmB,EACiB;IACpC,MAAMC,YAAY,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,YAAY,CAAC,AAAC;IACnE,IAAI,CAACC,YAAY,EAAE;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO7B,OAAO,CAAC6B,YAAY,CAAC,CAAC;AAC/B,CAAC"}