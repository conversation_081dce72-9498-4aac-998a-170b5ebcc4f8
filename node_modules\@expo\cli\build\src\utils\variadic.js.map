{"version": 3, "sources": ["../../../src/utils/variadic.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\n\nconst debug = require('debug')('expo:utils:variadic') as typeof console.log;\n\n/** Given a list of CLI args, return a sorted set of args based on categories used in a complex command. */\nexport function parseVariadicArguments(argv: string[]): {\n  variadic: string[];\n  extras: string[];\n  flags: Record<string, boolean | undefined>;\n} {\n  const variadic: string[] = [];\n  const flags: Record<string, boolean> = {};\n\n  for (const arg of argv) {\n    if (!arg.startsWith('-')) {\n      variadic.push(arg);\n    } else if (arg === '--') {\n      break;\n    } else {\n      flags[arg] = true;\n    }\n  }\n\n  // Everything after `--` that is not an option is passed to the underlying install command.\n  const extras: string[] = [];\n\n  const extraOperator = argv.indexOf('--');\n  if (extraOperator > -1 && argv.length > extraOperator + 1) {\n    const extraArgs = argv.slice(extraOperator + 1);\n    if (extraArgs.includes('--')) {\n      throw new CommandError('BAD_ARGS', 'Unexpected multiple --');\n    }\n    extras.push(...extraArgs);\n    debug('Extra arguments: ' + extras.join(', '));\n  }\n\n  debug(`Parsed arguments (variadic: %O, flags: %O, extra: %O)`, variadic, flags, extras);\n\n  return {\n    variadic,\n    flags,\n    extras,\n  };\n}\n\nexport function assertUnexpectedObjectKeys(keys: string[], obj: Record<string, any>): void {\n  const unexpectedKeys = Object.keys(obj).filter((key) => !keys.includes(key));\n  if (unexpectedKeys.length > 0) {\n    throw new CommandError('BAD_ARGS', `Unexpected: ${unexpectedKeys.join(', ')}`);\n  }\n}\n\nexport function assertUnexpectedVariadicFlags(\n  expectedFlags: string[],\n  { extras, flags, variadic }: ReturnType<typeof parseVariadicArguments>,\n  prefixCommand = ''\n) {\n  const unexpectedFlags = Object.keys(flags).filter((key) => !expectedFlags.includes(key));\n\n  if (unexpectedFlags.length > 0) {\n    const intendedFlags = Object.entries(flags)\n      .filter(([key]) => expectedFlags.includes(key))\n      .map(([key]) => key);\n\n    const cmd = [\n      prefixCommand,\n      ...variadic,\n      ...intendedFlags,\n      '--',\n      ...extras.concat(unexpectedFlags),\n    ].join(' ');\n\n    throw new CommandError(\n      'BAD_ARGS',\n      `Unexpected: ${unexpectedFlags.join(', ')}\\nDid you mean: ${cmd.trim()}`\n    );\n  }\n}\n"], "names": ["parseVariadicArguments", "assertUnexpectedObjectKeys", "assertUnexpectedVariadicFlags", "debug", "require", "argv", "variadic", "flags", "arg", "startsWith", "push", "extras", "extraOperator", "indexOf", "length", "extraArgs", "slice", "includes", "CommandError", "join", "keys", "obj", "<PERSON><PERSON><PERSON><PERSON>", "Object", "filter", "key", "expectedFlags", "prefixCommand", "unexpectedFlags", "intendedFlags", "entries", "map", "cmd", "concat", "trim"], "mappings": "AAAA;;;;;;;;;;;IAKgBA,sBAAsB,MAAtBA,sBAAsB;IAwCtBC,0BAA0B,MAA1BA,0BAA0B;IAO1BC,6BAA6B,MAA7BA,6BAA6B;;wBApDhB,iBAAiB;AAE9C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,AAAsB,AAAC;AAGrE,SAASJ,sBAAsB,CAACK,IAAc,EAInD;IACA,MAAMC,QAAQ,GAAa,EAAE,AAAC;IAC9B,MAAMC,KAAK,GAA4B,EAAE,AAAC;IAE1C,KAAK,MAAMC,GAAG,IAAIH,IAAI,CAAE;QACtB,IAAI,CAACG,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxBH,QAAQ,CAACI,IAAI,CAACF,GAAG,CAAC,CAAC;QACrB,OAAO,IAAIA,GAAG,KAAK,IAAI,EAAE;YACvB,MAAM;QACR,OAAO;YACLD,KAAK,CAACC,GAAG,CAAC,GAAG,IAAI,CAAC;QACpB,CAAC;IACH,CAAC;IAED,2FAA2F;IAC3F,MAAMG,MAAM,GAAa,EAAE,AAAC;IAE5B,MAAMC,aAAa,GAAGP,IAAI,CAACQ,OAAO,CAAC,IAAI,CAAC,AAAC;IACzC,IAAID,aAAa,GAAG,CAAC,CAAC,IAAIP,IAAI,CAACS,MAAM,GAAGF,aAAa,GAAG,CAAC,EAAE;QACzD,MAAMG,SAAS,GAAGV,IAAI,CAACW,KAAK,CAACJ,aAAa,GAAG,CAAC,CAAC,AAAC;QAChD,IAAIG,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,IAAIC,OAAY,aAAA,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QAC/D,CAAC;QACDP,MAAM,CAACD,IAAI,IAAIK,SAAS,CAAC,CAAC;QAC1BZ,KAAK,CAAC,mBAAmB,GAAGQ,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjD,CAAC;IAEDhB,KAAK,CAAC,CAAC,qDAAqD,CAAC,EAAEG,QAAQ,EAAEC,KAAK,EAAEI,MAAM,CAAC,CAAC;IAExF,OAAO;QACLL,QAAQ;QACRC,KAAK;QACLI,MAAM;KACP,CAAC;AACJ,CAAC;AAEM,SAASV,0BAA0B,CAACmB,IAAc,EAAEC,GAAwB,EAAQ;IACzF,MAAMC,cAAc,GAAGC,MAAM,CAACH,IAAI,CAACC,GAAG,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,GAAK,CAACL,IAAI,CAACH,QAAQ,CAACQ,GAAG,CAAC,CAAC,AAAC;IAC7E,IAAIH,cAAc,CAACR,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAM,IAAII,OAAY,aAAA,CAAC,UAAU,EAAE,CAAC,YAAY,EAAEI,cAAc,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;AACH,CAAC;AAEM,SAASjB,6BAA6B,CAC3CwB,aAAuB,EACvB,EAAEf,MAAM,CAAA,EAAEJ,KAAK,CAAA,EAAED,QAAQ,CAAA,EAA6C,EACtEqB,aAAa,GAAG,EAAE,EAClB;IACA,MAAMC,eAAe,GAAGL,MAAM,CAACH,IAAI,CAACb,KAAK,CAAC,CAACiB,MAAM,CAAC,CAACC,GAAG,GAAK,CAACC,aAAa,CAACT,QAAQ,CAACQ,GAAG,CAAC,CAAC,AAAC;IAEzF,IAAIG,eAAe,CAACd,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAMe,aAAa,GAAGN,MAAM,CAACO,OAAO,CAACvB,KAAK,CAAC,CACxCiB,MAAM,CAAC,CAAC,CAACC,GAAG,CAAC,GAAKC,aAAa,CAACT,QAAQ,CAACQ,GAAG,CAAC,CAAC,CAC9CM,GAAG,CAAC,CAAC,CAACN,GAAG,CAAC,GAAKA,GAAG,CAAC,AAAC;QAEvB,MAAMO,GAAG,GAAG;YACVL,aAAa;eACVrB,QAAQ;eACRuB,aAAa;YAChB,IAAI;eACDlB,MAAM,CAACsB,MAAM,CAACL,eAAe,CAAC;SAClC,CAACT,IAAI,CAAC,GAAG,CAAC,AAAC;QAEZ,MAAM,IAAID,OAAY,aAAA,CACpB,UAAU,EACV,CAAC,YAAY,EAAEU,eAAe,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAEa,GAAG,CAACE,IAAI,EAAE,CAAC,CAAC,CACzE,CAAC;IACJ,CAAC;AACH,CAAC"}