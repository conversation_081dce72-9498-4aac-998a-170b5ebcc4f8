{"version": 3, "names": ["React", "GHContext", "createContext", "props", "createElement", "Fragment", "children", "RNSScreensRefContext"], "sourceRoot": "../../src", "sources": ["contexts.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAA6B,OAAO;AAGhD,OAAO,MAAMC,SAAS,gBAAGD,KAAK,CAACE,aAAa,CACzCC,KAA8C,iBAAKH,KAAA,CAAAI,aAAA,CAAAJ,KAAA,CAAAK,QAAA,QAAGF,KAAK,CAACG,QAAW,CAC1E,CAAC;AAED,OAAO,MAAMC,oBAAoB,gBAC/BP,KAAK,CAACE,aAAa,CAAmD,IAAI,CAAC", "ignoreList": []}