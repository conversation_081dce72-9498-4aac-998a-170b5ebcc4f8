{"version": 3, "file": "createLegacyPlugin.js", "names": ["_configPlugins", "data", "require", "toCamelCase", "s", "replace", "x", "toUpperCase", "isModuleExcluded", "config", "packageName", "_internal", "autolinkedModules", "includes", "createLegacyPlugin", "fallback", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "with<PERSON><PERSON><PERSON>", "withUnknown", "createRunOncePlugin", "withStaticPlugin", "_isLegacyPlugin", "plugin", "methodName", "Object", "defineProperty", "value"], "sources": ["../../../src/plugins/unversioned/createLegacyPlugin.ts"], "sourcesContent": ["import {\n  ConfigPlugin,\n  createRunOncePlugin,\n  PluginParameters,\n  withPlugins,\n  withStaticPlugin,\n} from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nconst toCamelCase = (s: string) => s.replace(/-./g, (x) => x.toUpperCase()[1]);\n\nfunction isModuleExcluded(config: Pick<ExpoConfig, '_internal'>, packageName: string): boolean {\n  // Skip using the versioned plugin when autolinking is enabled\n  // and doesn't link the native module.\n  return (\n    config._internal?.autolinkedModules && !config._internal.autolinkedModules.includes(packageName)\n  );\n}\n\nexport function createLegacyPlugin({\n  packageName,\n  fallback,\n}: {\n  packageName: string;\n  fallback: ConfigPlugin | PluginParameters<typeof withPlugins>;\n}): ConfigPlugin {\n  let withFallback: ConfigPlugin;\n\n  if (Array.isArray(fallback)) {\n    withFallback = (config) => withPlugins(config, fallback);\n  } else {\n    withFallback = fallback;\n  }\n\n  const withUnknown: ConfigPlugin = (config) => {\n    // Skip using the versioned plugin when autolinking is enabled\n    // and doesn't link the native module.\n    if (isModuleExcluded(config, packageName)) {\n      return createRunOncePlugin(withFallback, packageName)(config);\n    }\n\n    return withStaticPlugin(config, {\n      _isLegacyPlugin: true,\n      plugin: packageName,\n      // If the static plugin isn't found, use the unversioned one.\n      fallback: createRunOncePlugin(withFallback, packageName),\n    });\n  };\n\n  const methodName = toCamelCase(`with-${packageName}`);\n  Object.defineProperty(withUnknown, 'name', {\n    value: methodName,\n  });\n\n  return withUnknown;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASA,MAAME,WAAW,GAAIC,CAAS,IAAKA,CAAC,CAACC,OAAO,CAAC,KAAK,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAE9E,SAASC,gBAAgBA,CAACC,MAAqC,EAAEC,WAAmB,EAAW;EAC7F;EACA;EACA,OACED,MAAM,CAACE,SAAS,EAAEC,iBAAiB,IAAI,CAACH,MAAM,CAACE,SAAS,CAACC,iBAAiB,CAACC,QAAQ,CAACH,WAAW,CAAC;AAEpG;AAEO,SAASI,kBAAkBA,CAAC;EACjCJ,WAAW;EACXK;AAIF,CAAC,EAAgB;EACf,IAAIC,YAA0B;EAE9B,IAAIC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;IAC3BC,YAAY,GAAIP,MAAM,IAAK,IAAAU,4BAAW,EAACV,MAAM,EAAEM,QAAQ,CAAC;EAC1D,CAAC,MAAM;IACLC,YAAY,GAAGD,QAAQ;EACzB;EAEA,MAAMK,WAAyB,GAAIX,MAAM,IAAK;IAC5C;IACA;IACA,IAAID,gBAAgB,CAACC,MAAM,EAAEC,WAAW,CAAC,EAAE;MACzC,OAAO,IAAAW,oCAAmB,EAACL,YAAY,EAAEN,WAAW,CAAC,CAACD,MAAM,CAAC;IAC/D;IAEA,OAAO,IAAAa,iCAAgB,EAACb,MAAM,EAAE;MAC9Bc,eAAe,EAAE,IAAI;MACrBC,MAAM,EAAEd,WAAW;MACnB;MACAK,QAAQ,EAAE,IAAAM,oCAAmB,EAACL,YAAY,EAAEN,WAAW;IACzD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,UAAU,GAAGtB,WAAW,CAAC,QAAQO,WAAW,EAAE,CAAC;EACrDgB,MAAM,CAACC,cAAc,CAACP,WAAW,EAAE,MAAM,EAAE;IACzCQ,KAAK,EAAEH;EACT,CAAC,CAAC;EAEF,OAAOL,WAAW;AACpB", "ignoreList": []}