{"version": 3, "sources": ["../../../../../src/start/server/middleware/HistoryFallbackMiddleware.ts"], "sourcesContent": ["import { parsePlatformHeader } from './resolvePlatform';\nimport { ServerNext, ServerRequest, ServerResponse } from './server.types';\n\n/**\n * Create a web-only middleware which redirects to the index middleware without losing the path component.\n * This is useful for things like React Navigation which need to render the index.html and then direct the user in-memory.\n */\nexport class HistoryFallbackMiddleware {\n  constructor(\n    private indexMiddleware: (\n      req: ServerRequest,\n      res: ServerResponse,\n      next: ServerNext\n    ) => Promise<void>\n  ) {}\n  getHandler() {\n    return (req: ServerRequest, res: ServerResponse, next: any) => {\n      const platform = parsePlatformHeader(req);\n\n      if (!platform || platform === 'web') {\n        // Redirect unknown to the manifest handler while preserving the path.\n        // This implements the HTML5 history fallback API.\n        return this.indexMiddleware(req, res, next);\n      }\n\n      return next();\n    };\n  }\n}\n"], "names": ["HistoryFallbackMiddleware", "constructor", "indexMiddleware", "<PERSON><PERSON><PERSON><PERSON>", "req", "res", "next", "platform", "parsePlatformHeader"], "mappings": "AAAA;;;;+BAOaA,2BAAyB;;aAAzBA,yBAAyB;;iCAPF,mBAAmB;AAOhD,MAAMA,yBAAyB;IACpCC,YACUC,eAIU,CAClB;QALQA,uBAAAA,eAIU,CAAA;IACjB;IACHC,UAAU,GAAG;QACX,OAAO,CAACC,GAAkB,EAAEC,GAAmB,EAAEC,IAAS,GAAK;YAC7D,MAAMC,QAAQ,GAAGC,IAAAA,gBAAmB,oBAAA,EAACJ,GAAG,CAAC,AAAC;YAE1C,IAAI,CAACG,QAAQ,IAAIA,QAAQ,KAAK,KAAK,EAAE;gBACnC,sEAAsE;gBACtE,kDAAkD;gBAClD,OAAO,IAAI,CAACL,eAAe,CAACE,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC;YAC9C,CAAC;YAED,OAAOA,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC;IACJ;CACD"}