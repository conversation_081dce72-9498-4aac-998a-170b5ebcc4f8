{"version": 3, "sources": ["../../../../src/start/server/DevServerManager.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\n\nimport { BundlerDevServer, BundlerStartOptions } from './BundlerDevServer';\nimport DevToolsPluginManager from './DevToolsPluginManager';\nimport { getPlatformBundlers } from './platformBundlers';\nimport { Log } from '../../log';\nimport { FileNotifier } from '../../utils/FileNotifier';\nimport { env } from '../../utils/env';\nimport { ProjectPrerequisite } from '../doctor/Prerequisite';\nimport { TypeScriptProjectPrerequisite } from '../doctor/typescript/TypeScriptProjectPrerequisite';\nimport { printItem } from '../interface/commandsTable';\nimport * as AndroidDebugBridge from '../platforms/android/adb';\nimport { resolveSchemeAsync } from '../resolveOptions';\n\nconst debug = require('debug')('expo:start:server:devServerManager') as typeof console.log;\n\nexport type MultiBundlerStartOptions = {\n  type: keyof typeof BUNDLERS;\n  options?: BundlerStartOptions;\n}[];\n\nconst BUNDLERS = {\n  webpack: () =>\n    require('./webpack/WebpackBundlerDevServer')\n      .WebpackBundlerDevServer as typeof import('./webpack/WebpackBundlerDevServer').WebpackBundlerDevServer,\n  metro: () =>\n    require('./metro/MetroBundlerDevServer')\n      .MetroBundlerDevServer as typeof import('./metro/MetroBundlerDevServer').MetroBundlerDevServer,\n};\n\n/** Manages interacting with multiple dev servers. */\nexport class DevServerManager {\n  private devServers: BundlerDevServer[] = [];\n\n  static async startMetroAsync(projectRoot: string, startOptions: BundlerStartOptions) {\n    const devServerManager = new DevServerManager(projectRoot, startOptions);\n\n    await devServerManager.startAsync([\n      {\n        type: 'metro',\n        options: startOptions,\n      },\n    ]);\n    return devServerManager;\n  }\n\n  private projectPrerequisites: ProjectPrerequisite<any, void>[] = [];\n  public readonly devtoolsPluginManager: DevToolsPluginManager;\n\n  private notifier: FileNotifier | null = null;\n\n  constructor(\n    public projectRoot: string,\n    /** Keep track of the original CLI options for bundlers that are started interactively. */\n    public options: BundlerStartOptions\n  ) {\n    if (!options.isExporting) {\n      this.notifier = this.watchBabelConfig();\n    }\n    this.devtoolsPluginManager = new DevToolsPluginManager(projectRoot);\n  }\n\n  private watchBabelConfig() {\n    const notifier = new FileNotifier(\n      this.projectRoot,\n      [\n        './babel.config.js',\n        './babel.config.json',\n        './.babelrc.json',\n        './.babelrc',\n        './.babelrc.js',\n      ],\n      {\n        additionalWarning: chalk` You may need to clear the bundler cache with the {bold --clear} flag for your changes to take effect.`,\n      }\n    );\n\n    notifier.startObserving();\n\n    return notifier;\n  }\n\n  /** Lazily load and assert a project-level prerequisite. */\n  async ensureProjectPrerequisiteAsync(PrerequisiteClass: typeof ProjectPrerequisite<any, any>) {\n    let prerequisite = this.projectPrerequisites.find(\n      (prerequisite) => prerequisite instanceof PrerequisiteClass\n    );\n    if (!prerequisite) {\n      prerequisite = new PrerequisiteClass(this.projectRoot);\n      this.projectPrerequisites.push(prerequisite);\n    }\n    return await prerequisite.assertAsync();\n  }\n\n  /**\n   * Sends a message over web sockets to all connected devices,\n   * does nothing when the dev server is not running.\n   *\n   * @param method name of the command. In RN projects `reload`, and `devMenu` are available. In Expo Go, `sendDevCommand` is available.\n   * @param params extra event info to send over the socket.\n   */\n  broadcastMessage(method: 'reload' | 'devMenu' | 'sendDevCommand', params?: Record<string, any>) {\n    this.devServers.forEach((server) => {\n      server.broadcastMessage(method, params);\n    });\n  }\n\n  /** Get the port for the dev server (either Webpack or Metro) that is hosting code for React Native runtimes. */\n  getNativeDevServerPort() {\n    const server = this.devServers.find((server) => server.isTargetingNative());\n    return server?.getInstance()?.location.port ?? null;\n  }\n\n  /** Get the first server that targets web. */\n  getWebDevServer() {\n    const server = this.devServers.find((server) => server.isTargetingWeb());\n    return server ?? null;\n  }\n\n  getDefaultDevServer(): BundlerDevServer {\n    // Return the first native dev server otherwise return the first dev server.\n    const server = this.devServers.find((server) => server.isTargetingNative());\n    const defaultServer = server ?? this.devServers[0];\n    assert(defaultServer, 'No dev servers are running');\n    return defaultServer;\n  }\n\n  async ensureWebDevServerRunningAsync() {\n    const [server] = this.devServers.filter((server) => server.isTargetingWeb());\n    if (server) {\n      return;\n    }\n    const { exp } = getConfig(this.projectRoot, {\n      skipPlugins: true,\n      skipSDKVersionRequirement: true,\n    });\n    const bundler = getPlatformBundlers(this.projectRoot, exp).web;\n    debug(`Starting ${bundler} dev server for web`);\n    return this.startAsync([\n      {\n        type: bundler,\n        options: this.options,\n      },\n    ]);\n  }\n\n  /** Switch between Expo Go and Expo Dev Clients. */\n  async toggleRuntimeMode(isUsingDevClient: boolean = !this.options.devClient): Promise<boolean> {\n    const nextMode = isUsingDevClient ? '--dev-client' : '--go';\n    Log.log(printItem(chalk`Switching to {bold ${nextMode}}`));\n\n    const nextScheme = await resolveSchemeAsync(this.projectRoot, {\n      devClient: isUsingDevClient,\n      // NOTE: The custom `--scheme` argument is lost from this point on.\n    });\n\n    this.options.location.scheme = nextScheme;\n    this.options.devClient = isUsingDevClient;\n    for (const devServer of this.devServers) {\n      devServer.isDevClient = isUsingDevClient;\n      const urlCreator = devServer.getUrlCreator();\n      urlCreator.defaults ??= {};\n      urlCreator.defaults.scheme = nextScheme;\n    }\n\n    debug(`New runtime options (runtime: ${nextMode}):`, this.options);\n    return true;\n  }\n\n  /** Start all dev servers. */\n  async startAsync(startOptions: MultiBundlerStartOptions): Promise<ExpoConfig> {\n    const { exp } = getConfig(this.projectRoot, { skipSDKVersionRequirement: true });\n    const platformBundlers = getPlatformBundlers(this.projectRoot, exp);\n\n    // Start all dev servers...\n    for (const { type, options } of startOptions) {\n      const BundlerDevServerClass = await BUNDLERS[type]();\n      const server = new BundlerDevServerClass(this.projectRoot, platformBundlers, {\n        devToolsPluginManager: this.devtoolsPluginManager,\n        isDevClient: !!options?.devClient,\n      });\n      await server.startAsync(options ?? this.options);\n      this.devServers.push(server);\n    }\n\n    return exp;\n  }\n\n  async bootstrapTypeScriptAsync() {\n    const typescriptPrerequisite = await this.ensureProjectPrerequisiteAsync(\n      TypeScriptProjectPrerequisite\n    );\n\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      return;\n    }\n\n    // Optionally, wait for the user to add TypeScript during the\n    // development cycle.\n    const server = this.devServers.find((server) => server.name === 'metro');\n    if (!server) {\n      return;\n    }\n\n    // The dev server shouldn't wait for the typescript services\n    if (!typescriptPrerequisite) {\n      server.waitForTypeScriptAsync().then(async (success) => {\n        if (success) {\n          server.startTypeScriptServices();\n        }\n      });\n    } else {\n      server.startTypeScriptServices();\n    }\n  }\n\n  async watchEnvironmentVariables() {\n    await this.devServers.find((server) => server.name === 'metro')?.watchEnvironmentVariables();\n  }\n\n  /** Stop all servers including ADB. */\n  async stopAsync(): Promise<void> {\n    await Promise.allSettled([\n      this.notifier?.stopObserving(),\n      // Stop ADB\n      AndroidDebugBridge.getServer().stopAsync(),\n      // Stop all dev servers\n      ...this.devServers.map((server) =>\n        server.stopAsync().catch((error) => {\n          Log.error(`Failed to stop dev server (bundler: ${server.name})`);\n          Log.exception(error);\n        })\n      ),\n    ]);\n  }\n}\n"], "names": ["DevServerManager", "debug", "require", "BUNDLERS", "webpack", "WebpackBundlerDevServer", "metro", "MetroBundlerDevServer", "startMetroAsync", "projectRoot", "startOptions", "devServerManager", "startAsync", "type", "options", "constructor", "devServers", "projectPrerequisites", "notifier", "isExporting", "watchBabelConfig", "devtoolsPluginManager", "DevToolsPluginManager", "FileNotifier", "additionalWarning", "chalk", "startObserving", "ensureProjectPrerequisiteAsync", "PrerequisiteClass", "prerequisite", "find", "push", "assertAsync", "broadcastMessage", "method", "params", "for<PERSON>ach", "server", "getNativeDevServerPort", "isTargetingNative", "getInstance", "location", "port", "getWebDevServer", "isTargetingWeb", "getDefaultDevServer", "defaultServer", "assert", "ensureWebDevServerRunningAsync", "filter", "exp", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "bundler", "getPlatformBundlers", "web", "toggleRuntimeMode", "isUsingDevClient", "devClient", "nextMode", "Log", "log", "printItem", "nextScheme", "resolveSchemeAsync", "scheme", "devServer", "isDevClient", "urlCreator", "getUrlCreator", "defaults", "platformBundlers", "BundlerDevServerClass", "devToolsPluginManager", "bootstrapTypeScriptAsync", "typescriptPrerequisite", "TypeScriptProjectPrerequisite", "env", "EXPO_NO_TYPESCRIPT_SETUP", "name", "waitForTypeScriptAsync", "then", "success", "startTypeScriptServices", "watchEnvironmentVariables", "stopAsync", "Promise", "allSettled", "stopObserving", "AndroidDebugBridge", "getServer", "map", "catch", "error", "exception"], "mappings": "AAAA;;;;+BAiCaA,kBAAgB;;aAAhBA,gBAAgB;;;yBAjCS,cAAc;;;;;;;8DACjC,QAAQ;;;;;;;8DACT,OAAO;;;;;;4EAGS,yBAAyB;kCACvB,oBAAoB;qBACpC,WAAW;8BACF,0BAA0B;qBACnC,iBAAiB;+CAES,oDAAoD;+BACxE,4BAA4B;2DAClB,0BAA0B;gCAC3B,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oCAAoC,CAAC,AAAsB,AAAC;AAO3F,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,IACPF,OAAO,CAAC,mCAAmC,CAAC,CACzCG,uBAAuB,AAA8E;IAC1GC,KAAK,EAAE,IACLJ,OAAO,CAAC,+BAA+B,CAAC,CACrCK,qBAAqB,AAAwE;CACnG,AAAC;AAGK,MAAMP,gBAAgB;iBAGdQ,eAAe,CAACC,WAAmB,EAAEC,YAAiC,EAAE;QACnF,MAAMC,gBAAgB,GAAG,IAAIX,gBAAgB,CAACS,WAAW,EAAEC,YAAY,CAAC,AAAC;QAEzE,MAAMC,gBAAgB,CAACC,UAAU,CAAC;YAChC;gBACEC,IAAI,EAAE,OAAO;gBACbC,OAAO,EAAEJ,YAAY;aACtB;SACF,CAAC,CAAC;QACH,OAAOC,gBAAgB,CAAC;IAC1B;IAOAI,YACSN,WAAmB,EAEnBK,OAA4B,CACnC;QAHOL,mBAAAA,WAAmB,CAAA;QAEnBK,eAAAA,OAA4B,CAAA;aAtB7BE,UAAU,GAAuB,EAAE;aAcnCC,oBAAoB,GAAqC,EAAE;aAG3DC,QAAQ,GAAwB,IAAI;QAO1C,IAAI,CAACJ,OAAO,CAACK,WAAW,EAAE;YACxB,IAAI,CAACD,QAAQ,GAAG,IAAI,CAACE,gBAAgB,EAAE,CAAC;QAC1C,CAAC;QACD,IAAI,CAACC,qBAAqB,GAAG,IAAIC,sBAAqB,QAAA,CAACb,WAAW,CAAC,CAAC;IACtE;IAEQW,gBAAgB,GAAG;QACzB,MAAMF,QAAQ,GAAG,IAAIK,aAAY,aAAA,CAC/B,IAAI,CAACd,WAAW,EAChB;YACE,mBAAmB;YACnB,qBAAqB;YACrB,iBAAiB;YACjB,YAAY;YACZ,eAAe;SAChB,EACD;YACEe,iBAAiB,EAAEC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sGAAsG,CAAC;SACjI,CACF,AAAC;QAEFP,QAAQ,CAACQ,cAAc,EAAE,CAAC;QAE1B,OAAOR,QAAQ,CAAC;IAClB;IAEA,yDAAyD,SACnDS,8BAA8B,CAACC,iBAAuD,EAAE;QAC5F,IAAIC,YAAY,GAAG,IAAI,CAACZ,oBAAoB,CAACa,IAAI,CAC/C,CAACD,YAAY,GAAKA,YAAY,YAAYD,iBAAiB,CAC5D,AAAC;QACF,IAAI,CAACC,YAAY,EAAE;YACjBA,YAAY,GAAG,IAAID,iBAAiB,CAAC,IAAI,CAACnB,WAAW,CAAC,CAAC;YACvD,IAAI,CAACQ,oBAAoB,CAACc,IAAI,CAACF,YAAY,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,MAAMA,YAAY,CAACG,WAAW,EAAE,CAAC;IAC1C;IAEA;;;;;;GAMC,GACDC,gBAAgB,CAACC,MAA+C,EAAEC,MAA4B,EAAE;QAC9F,IAAI,CAACnB,UAAU,CAACoB,OAAO,CAAC,CAACC,MAAM,GAAK;YAClCA,MAAM,CAACJ,gBAAgB,CAACC,MAAM,EAAEC,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL;IAEA,8GAA8G,GAC9GG,sBAAsB,GAAG;;QACvB,MAAMD,MAAM,GAAG,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACE,iBAAiB,EAAE,CAAC,AAAC;QAC5E,OAAOF,CAAAA,OAAAA,MAAM,QAAa,GAAnBA,KAAAA,CAAmB,GAAnBA,MAAM,CAAEG,WAAW,EAAE,SAAU,GAA/BH,KAAAA,CAA+B,GAA/BA,IAAuBI,QAAQ,CAACC,IAAI,KAAI,IAAI,CAAC;IACtD;IAEA,2CAA2C,GAC3CC,eAAe,GAAG;QAChB,MAAMN,MAAM,GAAG,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACO,cAAc,EAAE,CAAC,AAAC;QACzE,OAAOP,MAAM,IAAI,IAAI,CAAC;IACxB;IAEAQ,mBAAmB,GAAqB;QACtC,4EAA4E;QAC5E,MAAMR,MAAM,GAAG,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAACE,iBAAiB,EAAE,CAAC,AAAC;QAC5E,MAAMO,aAAa,GAAGT,MAAM,IAAI,IAAI,CAACrB,UAAU,CAAC,CAAC,CAAC,AAAC;QACnD+B,IAAAA,OAAM,EAAA,QAAA,EAACD,aAAa,EAAE,4BAA4B,CAAC,CAAC;QACpD,OAAOA,aAAa,CAAC;IACvB;UAEME,8BAA8B,GAAG;QACrC,MAAM,CAACX,MAAM,CAAC,GAAG,IAAI,CAACrB,UAAU,CAACiC,MAAM,CAAC,CAACZ,MAAM,GAAKA,MAAM,CAACO,cAAc,EAAE,CAAC,AAAC;QAC7E,IAAIP,MAAM,EAAE;YACV,OAAO;QACT,CAAC;QACD,MAAM,EAAEa,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAC1C,WAAW,EAAE;YAC1C2C,WAAW,EAAE,IAAI;YACjBC,yBAAyB,EAAE,IAAI;SAChC,CAAC,AAAC;QACH,MAAMC,OAAO,GAAGC,IAAAA,iBAAmB,oBAAA,EAAC,IAAI,CAAC9C,WAAW,EAAEyC,GAAG,CAAC,CAACM,GAAG,AAAC;QAC/DvD,KAAK,CAAC,CAAC,SAAS,EAAEqD,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC1C,UAAU,CAAC;YACrB;gBACEC,IAAI,EAAEyC,OAAO;gBACbxC,OAAO,EAAE,IAAI,CAACA,OAAO;aACtB;SACF,CAAC,CAAC;IACL;IAEA,iDAAiD,SAC3C2C,iBAAiB,CAACC,gBAAyB,GAAG,CAAC,IAAI,CAAC5C,OAAO,CAAC6C,SAAS,EAAoB;QAC7F,MAAMC,QAAQ,GAAGF,gBAAgB,GAAG,cAAc,GAAG,MAAM,AAAC;QAC5DG,IAAG,IAAA,CAACC,GAAG,CAACC,IAAAA,cAAS,UAAA,EAACtC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,mBAAmB,EAAEmC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,MAAMI,UAAU,GAAG,MAAMC,IAAAA,eAAkB,mBAAA,EAAC,IAAI,CAACxD,WAAW,EAAE;YAC5DkD,SAAS,EAAED,gBAAgB;SAE5B,CAAC,AAAC;QAEH,IAAI,CAAC5C,OAAO,CAAC2B,QAAQ,CAACyB,MAAM,GAAGF,UAAU,CAAC;QAC1C,IAAI,CAAClD,OAAO,CAAC6C,SAAS,GAAGD,gBAAgB,CAAC;QAC1C,KAAK,MAAMS,SAAS,IAAI,IAAI,CAACnD,UAAU,CAAE;YACvCmD,SAAS,CAACC,WAAW,GAAGV,gBAAgB,CAAC;YACzC,MAAMW,UAAU,GAAGF,SAAS,CAACG,aAAa,EAAE,AAAC;YAC7CD,UAAU,CAACE,QAAQ,KAAK,EAAE,CAAC;YAC3BF,UAAU,CAACE,QAAQ,CAACL,MAAM,GAAGF,UAAU,CAAC;QAC1C,CAAC;QAED/D,KAAK,CAAC,CAAC,8BAA8B,EAAE2D,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC9C,OAAO,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd;IAEA,2BAA2B,SACrBF,UAAU,CAACF,YAAsC,EAAuB;QAC5E,MAAM,EAAEwC,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAC1C,WAAW,EAAE;YAAE4C,yBAAyB,EAAE,IAAI;SAAE,CAAC,AAAC;QACjF,MAAMmB,gBAAgB,GAAGjB,IAAAA,iBAAmB,oBAAA,EAAC,IAAI,CAAC9C,WAAW,EAAEyC,GAAG,CAAC,AAAC;QAEpE,2BAA2B;QAC3B,KAAK,MAAM,EAAErC,IAAI,CAAA,EAAEC,OAAO,CAAA,EAAE,IAAIJ,YAAY,CAAE;YAC5C,MAAM+D,qBAAqB,GAAG,MAAMtE,QAAQ,CAACU,IAAI,CAAC,EAAE,AAAC;YACrD,MAAMwB,MAAM,GAAG,IAAIoC,qBAAqB,CAAC,IAAI,CAAChE,WAAW,EAAE+D,gBAAgB,EAAE;gBAC3EE,qBAAqB,EAAE,IAAI,CAACrD,qBAAqB;gBACjD+C,WAAW,EAAE,CAAC,CAACtD,CAAAA,OAAO,QAAW,GAAlBA,KAAAA,CAAkB,GAAlBA,OAAO,CAAE6C,SAAS,CAAA;aAClC,CAAC,AAAC;YACH,MAAMtB,MAAM,CAACzB,UAAU,CAACE,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,CAAC;YACjD,IAAI,CAACE,UAAU,CAACe,IAAI,CAACM,MAAM,CAAC,CAAC;QAC/B,CAAC;QAED,OAAOa,GAAG,CAAC;IACb;UAEMyB,wBAAwB,GAAG;QAC/B,MAAMC,sBAAsB,GAAG,MAAM,IAAI,CAACjD,8BAA8B,CACtEkD,8BAA6B,8BAAA,CAC9B,AAAC;QAEF,IAAIC,IAAG,IAAA,CAACC,wBAAwB,EAAE;YAChC,OAAO;QACT,CAAC;QAED,6DAA6D;QAC7D,qBAAqB;QACrB,MAAM1C,MAAM,GAAG,IAAI,CAACrB,UAAU,CAACc,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAAC2C,IAAI,KAAK,OAAO,CAAC,AAAC;QACzE,IAAI,CAAC3C,MAAM,EAAE;YACX,OAAO;QACT,CAAC;QAED,4DAA4D;QAC5D,IAAI,CAACuC,sBAAsB,EAAE;YAC3BvC,MAAM,CAAC4C,sBAAsB,EAAE,CAACC,IAAI,CAAC,OAAOC,OAAO,GAAK;gBACtD,IAAIA,OAAO,EAAE;oBACX9C,MAAM,CAAC+C,uBAAuB,EAAE,CAAC;gBACnC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,OAAO;YACL/C,MAAM,CAAC+C,uBAAuB,EAAE,CAAC;QACnC,CAAC;IACH;UAEMC,yBAAyB,GAAG;YAC1B,GAAyD;QAA/D,OAAM,CAAA,GAAyD,GAAzD,IAAI,CAACrE,UAAU,CAACc,IAAI,CAAC,CAACO,MAAM,GAAKA,MAAM,CAAC2C,IAAI,KAAK,OAAO,CAAC,SAA2B,GAApF,KAAA,CAAoF,GAApF,GAAyD,CAAEK,yBAAyB,EAAE,CAAA,CAAC;IAC/F;IAEA,oCAAoC,SAC9BC,SAAS,GAAkB;YAE7B,GAAa;QADf,MAAMC,OAAO,CAACC,UAAU,CAAC;YACvB,CAAA,GAAa,GAAb,IAAI,CAACtE,QAAQ,SAAe,GAA5B,KAAA,CAA4B,GAA5B,GAAa,CAAEuE,aAAa,EAAE;YAC9B,WAAW;YACXC,IAAkB,CAACC,SAAS,EAAE,CAACL,SAAS,EAAE;YAC1C,uBAAuB;eACpB,IAAI,CAACtE,UAAU,CAAC4E,GAAG,CAAC,CAACvD,MAAM,GAC5BA,MAAM,CAACiD,SAAS,EAAE,CAACO,KAAK,CAAC,CAACC,KAAK,GAAK;oBAClCjC,IAAG,IAAA,CAACiC,KAAK,CAAC,CAAC,oCAAoC,EAAEzD,MAAM,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjEnB,IAAG,IAAA,CAACkC,SAAS,CAACD,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC,CACH;SACF,CAAC,CAAC;IACL;CACD"}