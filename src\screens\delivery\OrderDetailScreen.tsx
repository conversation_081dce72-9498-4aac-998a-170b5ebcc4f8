import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView,
  SafeAreaView,
  Alert,
  Linking
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { deliveryService } from '../../services/delivery';
import { Order, OrderStatus } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Icon from 'react-native-vector-icons/MaterialIcons';

const OrderDetailScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { order } = route.params as { order: Order };
  
  const [currentOrder, setCurrentOrder] = useState<Order>(order);
  const [isLoading, setIsLoading] = useState(false);

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PICKED_UP:
        return '#9C27B0';
      case OrderStatus.IN_TRANSIT:
        return '#607D8B';
      case OrderStatus.DELIVERED:
        return COLORS.success;
      case OrderStatus.CANCELLED:
        return COLORS.error;
      default:
        return COLORS.secondary;
    }
  };

  const getNextStatus = (currentStatus: OrderStatus): OrderStatus | null => {
    switch (currentStatus) {
      case OrderStatus.PICKED_UP:
        return OrderStatus.IN_TRANSIT;
      case OrderStatus.IN_TRANSIT:
        return OrderStatus.DELIVERED;
      default:
        return null;
    }
  };

  const getStatusActionText = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.PICKED_UP:
        return 'Start Delivery';
      case OrderStatus.IN_TRANSIT:
        return 'Mark Delivered';
      default:
        return '';
    }
  };

  const updateOrderStatus = async (newStatus: OrderStatus) => {
    setIsLoading(true);
    try {
      const updatedOrder = await deliveryService.updateOrderStatus(currentOrder.id, newStatus);
      setCurrentOrder(updatedOrder);
      Alert.alert('Success', 'Order status updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to update order status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = () => {
    const nextStatus = getNextStatus(currentOrder.status);
    if (nextStatus) {
      Alert.alert(
        'Update Status',
        `Are you sure you want to ${getStatusActionText(currentOrder.status).toLowerCase()}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Confirm', onPress: () => updateOrderStatus(nextStatus) }
        ]
      );
    }
  };

  const handleCallCustomer = () => {
    const phoneNumber = currentOrder.customer.phone;
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const handleOpenMaps = () => {
    const address = encodeURIComponent(currentOrder.customer.address);
    const url = `https://www.google.com/maps/search/?api=1&query=${address}`;
    Linking.openURL(url);
  };

  const nextStatus = getNextStatus(currentOrder.status);
  const canUpdateStatus = nextStatus !== null && currentOrder.status !== OrderStatus.DELIVERED && currentOrder.status !== OrderStatus.CANCELLED;

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Button
            title=""
            onPress={() => navigation.goBack()}
            variant="outline"
            size="small"
            style={styles.backButton}
          />
          <Text style={styles.title}>Delivery Details</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Order Status */}
          <Card style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <Text style={styles.orderId}>Order #{currentOrder.id.slice(-6)}</Text>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(currentOrder.status) + '20' }]}>
                <Text style={[styles.statusText, { color: getStatusColor(currentOrder.status) }]}>
                  {currentOrder.status.replace('_', ' ')}
                </Text>
              </View>
            </View>
            <Text style={styles.orderDate}>
              {new Date(currentOrder.createdAt).toLocaleDateString()} at {new Date(currentOrder.createdAt).toLocaleTimeString()}
            </Text>
          </Card>

          {/* Customer Information */}
          <Card style={styles.customerCard}>
            <Text style={styles.sectionTitle}>Customer Information</Text>
            <View style={styles.customerInfo}>
              <Icon name="person" size={20} color={COLORS.secondary} />
              <Text style={styles.customerText}>{currentOrder.customer.name}</Text>
            </View>
            <View style={styles.customerInfo}>
              <Icon name="location-on" size={20} color={COLORS.secondary} />
              <Text style={styles.customerText}>{currentOrder.customer.address}</Text>
              <Button
                title=""
                onPress={handleOpenMaps}
                variant="outline"
                size="small"
                style={styles.mapButton}
              />
            </View>
            <View style={styles.customerInfo}>
              <Icon name="phone" size={20} color={COLORS.secondary} />
              <Text style={styles.customerText}>{currentOrder.customer.phone}</Text>
              <Button
                title=""
                onPress={handleCallCustomer}
                variant="outline"
                size="small"
                style={styles.callButton}
              />
            </View>
          </Card>

          {/* Order Items */}
          <Card style={styles.itemsCard}>
            <Text style={styles.sectionTitle}>Order Items</Text>
            {currentOrder.items.map((item, index) => (
              <View key={index} style={styles.orderItem}>
                <View style={styles.itemInfo}>
                  <Text style={styles.itemName}>{item.name}</Text>
                  <Text style={styles.itemPrice}>${item.price.toFixed(2)} each</Text>
                </View>
                <View style={styles.itemQuantity}>
                  <Text style={styles.quantityText}>x{item.quantity}</Text>
                  <Text style={styles.itemTotal}>${(item.price * item.quantity).toFixed(2)}</Text>
                </View>
              </View>
            ))}
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalAmount}>${currentOrder.total.toFixed(2)}</Text>
            </View>
          </Card>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {canUpdateStatus && (
              <Button
                title={getStatusActionText(currentOrder.status)}
                onPress={handleStatusUpdate}
                isLoading={isLoading}
                style={styles.updateButton}
              />
            )}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  statusCard: {
    marginBottom: SPACING.md,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  orderId: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
  },
  statusText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.sm,
    textTransform: 'capitalize',
  },
  orderDate: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    opacity: 0.6,
  },
  customerCard: {
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.md,
    color: COLORS.secondary,
    marginBottom: SPACING.sm,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  customerText: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.md,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    flex: 1,
  },
  mapButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  itemsCard: {
    marginBottom: SPACING.lg,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  itemPrice: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    opacity: 0.7,
  },
  itemQuantity: {
    alignItems: 'flex-end',
  },
  quantityText: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    marginBottom: SPACING.xs,
  },
  itemTotal: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.md,
    color: COLORS.accent,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: SPACING.md,
    marginTop: SPACING.sm,
    borderTopWidth: 2,
    borderTopColor: COLORS.border,
  },
  totalLabel: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
  },
  totalAmount: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.accent,
  },
  actionButtons: {
    marginBottom: SPACING.xl,
  },
  updateButton: {
    marginBottom: SPACING.md,
  },
});

export default OrderDetailScreen;
