{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidSdk.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\n/**\n * The default Android SDK locations per platform.\n * @see https://developer.android.com/studio/run/emulator-commandline#filedir\n * @see https://developer.android.com/studio/intro/studio-config#optimize-studio-windows\n */\nconst ANDROID_DEFAULT_LOCATION: Readonly<Partial<Record<NodeJS.Platform, string>>> = {\n  darwin: path.join(os.homedir(), 'Library', 'Android', 'sdk'),\n  linux: path.join(os.homedir(), 'Android', 'sdk'),\n  win32: path.join(os.homedir(), 'AppData', 'Local', 'Android', 'Sdk'),\n};\n\n/**\n * Resolve and validate the root folder where the Android SDK has been installed.\n * This checks both `ANDROID_HOME`, `ANDROID_SDK_ROOT`, and the default path for the current platform.\n * @see https://developer.android.com/studio/command-line/variables\n */\nexport function assertSdkRoot() {\n  if (process.env.ANDROID_HOME) {\n    assert(\n      fs.existsSync(process.env.ANDROID_HOME),\n      `Failed to resolve the Android SDK path. ANDROID_HOME is set to a non-existing path: ${process.env.ANDROID_HOME}`\n    );\n    return process.env.ANDROID_HOME;\n  }\n\n  if (process.env.ANDROID_SDK_ROOT) {\n    assert(\n      fs.existsSync(process.env.ANDROID_SDK_ROOT),\n      `Failed to resolve the Android SDK path. Deprecated ANDROID_SDK_ROOT is set to a non-existing path: ${process.env.ANDROID_SDK_ROOT}. Use ANDROID_HOME instead.`\n    );\n    return process.env.ANDROID_SDK_ROOT;\n  }\n\n  const defaultLocation = ANDROID_DEFAULT_LOCATION[process.platform];\n  if (defaultLocation) {\n    assert(\n      fs.existsSync(defaultLocation),\n      `Failed to resolve the Android SDK path. Default install location not found: ${defaultLocation}. Use ANDROID_HOME to set the Android SDK location.`\n    );\n    return defaultLocation;\n  }\n\n  return null;\n}\n"], "names": ["assertSdkRoot", "ANDROID_DEFAULT_LOCATION", "darwin", "path", "join", "os", "homedir", "linux", "win32", "process", "env", "ANDROID_HOME", "assert", "fs", "existsSync", "ANDROID_SDK_ROOT", "defaultLocation", "platform"], "mappings": "AAAA;;;;+BAqBgBA,eAAa;;aAAbA,aAAa;;;8DArBV,QAAQ;;;;;;;8DACZ,IAAI;;;;;;;8DACJ,IAAI;;;;;;;8DACF,MAAM;;;;;;;;;;;AAEvB;;;;CAIC,GACD,MAAMC,wBAAwB,GAAuD;IACnFC,MAAM,EAAEC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,GAAE,EAAA,QAAA,CAACC,OAAO,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC;IAC5DC,KAAK,EAAEJ,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,GAAE,EAAA,QAAA,CAACC,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC;IAChDE,KAAK,EAAEL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,GAAE,EAAA,QAAA,CAACC,OAAO,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;CACrE,AAAC;AAOK,SAASN,aAAa,GAAG;IAC9B,IAAIS,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE;QAC5BC,IAAAA,OAAM,EAAA,QAAA,EACJC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACL,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC,EACvC,CAAC,oFAAoF,EAAEF,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC,CAAC,CAClH,CAAC;QACF,OAAOF,OAAO,CAACC,GAAG,CAACC,YAAY,CAAC;IAClC,CAAC;IAED,IAAIF,OAAO,CAACC,GAAG,CAACK,gBAAgB,EAAE;QAChCH,IAAAA,OAAM,EAAA,QAAA,EACJC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACL,OAAO,CAACC,GAAG,CAACK,gBAAgB,CAAC,EAC3C,CAAC,mGAAmG,EAAEN,OAAO,CAACC,GAAG,CAACK,gBAAgB,CAAC,2BAA2B,CAAC,CAChK,CAAC;QACF,OAAON,OAAO,CAACC,GAAG,CAACK,gBAAgB,CAAC;IACtC,CAAC;IAED,MAAMC,eAAe,GAAGf,wBAAwB,CAACQ,OAAO,CAACQ,QAAQ,CAAC,AAAC;IACnE,IAAID,eAAe,EAAE;QACnBJ,IAAAA,OAAM,EAAA,QAAA,EACJC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACE,eAAe,CAAC,EAC9B,CAAC,4EAA4E,EAAEA,eAAe,CAAC,mDAAmD,CAAC,CACpJ,CAAC;QACF,OAAOA,eAAe,CAAC;IACzB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}