{"version": 3, "file": "withConfigureEdgeToEdgeEnforcement.js", "names": ["_configPlugins", "data", "require", "OPT_OUT_EDGE_TO_EDGE_ATTRIBUTE", "withConfigureEdgeToEdgeEnforcement", "config", "disableEdgeToEdgeEnforcement", "withAndroidStyles", "configureEdgeToEdgeEnforcement", "exports", "style", "modResults", "resources", "disableEdgeToEdgeEnforcementItem", "_", "$", "name", "mainThemeIndex", "findIndex", "existingItem", "item", "filter", "push"], "sources": ["../../../../src/plugins/unversioned/react-native-edge-to-edge/withConfigureEdgeToEdgeEnforcement.ts"], "sourcesContent": ["import { ConfigPlugin, withAndroidStyles } from '@expo/config-plugins';\n\nimport { ResourceXMLConfig } from './withEdgeToEdge';\n\nconst OPT_OUT_EDGE_TO_EDGE_ATTRIBUTE = 'android:windowOptOutEdgeToEdgeEnforcement';\n\nexport const withConfigureEdgeToEdgeEnforcement: ConfigPlugin<{\n  disableEdgeToEdgeEnforcement: boolean;\n}> = (config, { disableEdgeToEdgeEnforcement }) => {\n  return withAndroidStyles(config, (config) => {\n    return configureEdgeToEdgeEnforcement(config, disableEdgeToEdgeEnforcement);\n  });\n};\n\nexport function configureEdgeToEdgeEnforcement(\n  config: ResourceXMLConfig,\n  disableEdgeToEdgeEnforcement: boolean\n): ResourceXMLConfig {\n  const { style = [] } = config.modResults.resources;\n\n  const disableEdgeToEdgeEnforcementItem = {\n    _: 'true',\n    $: {\n      name: OPT_OUT_EDGE_TO_EDGE_ATTRIBUTE,\n      'tools:targetApi': '35',\n    },\n  };\n\n  const mainThemeIndex = style.findIndex(({ $ }) => $.name === 'AppTheme');\n\n  if (mainThemeIndex === -1) {\n    return config;\n  }\n\n  const existingItem = style[mainThemeIndex].item.filter(\n    ({ $ }) => $.name !== OPT_OUT_EDGE_TO_EDGE_ATTRIBUTE\n  );\n\n  if (disableEdgeToEdgeEnforcement) {\n    existingItem.push(disableEdgeToEdgeEnforcementItem);\n  }\n  if (!config.modResults.resources.style) {\n    return config;\n  }\n\n  config.modResults.resources.style[mainThemeIndex].item = existingItem;\n  return config;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,MAAME,8BAA8B,GAAG,2CAA2C;AAE3E,MAAMC,kCAEX,GAAGA,CAACC,MAAM,EAAE;EAAEC;AAA6B,CAAC,KAAK;EACjD,OAAO,IAAAC,kCAAiB,EAACF,MAAM,EAAGA,MAAM,IAAK;IAC3C,OAAOG,8BAA8B,CAACH,MAAM,EAAEC,4BAA4B,CAAC;EAC7E,CAAC,CAAC;AACJ,CAAC;AAACG,OAAA,CAAAL,kCAAA,GAAAA,kCAAA;AAEK,SAASI,8BAA8BA,CAC5CH,MAAyB,EACzBC,4BAAqC,EAClB;EACnB,MAAM;IAAEI,KAAK,GAAG;EAAG,CAAC,GAAGL,MAAM,CAACM,UAAU,CAACC,SAAS;EAElD,MAAMC,gCAAgC,GAAG;IACvCC,CAAC,EAAE,MAAM;IACTC,CAAC,EAAE;MACDC,IAAI,EAAEb,8BAA8B;MACpC,iBAAiB,EAAE;IACrB;EACF,CAAC;EAED,MAAMc,cAAc,GAAGP,KAAK,CAACQ,SAAS,CAAC,CAAC;IAAEH;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAK,UAAU,CAAC;EAExE,IAAIC,cAAc,KAAK,CAAC,CAAC,EAAE;IACzB,OAAOZ,MAAM;EACf;EAEA,MAAMc,YAAY,GAAGT,KAAK,CAACO,cAAc,CAAC,CAACG,IAAI,CAACC,MAAM,CACpD,CAAC;IAAEN;EAAE,CAAC,KAAKA,CAAC,CAACC,IAAI,KAAKb,8BACxB,CAAC;EAED,IAAIG,4BAA4B,EAAE;IAChCa,YAAY,CAACG,IAAI,CAACT,gCAAgC,CAAC;EACrD;EACA,IAAI,CAACR,MAAM,CAACM,UAAU,CAACC,SAAS,CAACF,KAAK,EAAE;IACtC,OAAOL,MAAM;EACf;EAEAA,MAAM,CAACM,UAAU,CAACC,SAAS,CAACF,KAAK,CAACO,cAAc,CAAC,CAACG,IAAI,GAAGD,YAAY;EACrE,OAAOd,MAAM;AACf", "ignoreList": []}