# \[Experimental\] Metro File Map

🚇 File system crawling, watching and mapping for [Metro](https://metrobundler.dev/).

Originally a fork of [`jest-haste-map`](https://github.com/facebook/jest/tree/main/packages/jest-haste-map).

This entire package should be considered "experimental" for the time being -
the API is considered internal and changes will not be semver-breaking.

If you need to rely on `metro-file-map` APIs directly please
[raise an issue](https://github.com/facebook/metro/issues/new) to discuss your
use case.
