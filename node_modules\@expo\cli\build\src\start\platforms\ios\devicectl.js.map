{"version": 3, "sources": ["../../../../../src/start/platforms/ios/devicectl.ts"], "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { getExpoHomeDirectory } from '@expo/config/build/getUserState';\nimport JsonFile from '@expo/json-file';\nimport spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { spawn, execSync } from 'child_process';\nimport fs from 'fs';\nimport assert from 'node:assert';\nimport { Ora } from 'ora';\nimport { EOL } from 'os';\nimport path from 'path';\n\nimport { xcrunAsync } from './xcrun';\nimport * as Log from '../../../log';\nimport { createTempFilePath } from '../../../utils/createTempPath';\nimport { CommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\nimport { isInteractive } from '../../../utils/interactive';\nimport { ora } from '../../../utils/ora';\nimport { confirmAsync } from '../../../utils/prompts';\n\nconst DEVICE_CTL_EXISTS_PATH = path.join(getExpoHomeDirectory(), 'devicectl-exists');\n\nconst debug = require('debug')('expo:devicectl') as typeof console.log;\n\ntype AnyEnum<T extends string = string> = T | (string & object);\n\ntype DeviceCtlDevice = {\n  capabilities: DeviceCtlDeviceCapability[];\n  connectionProperties: DeviceCtlConnectionProperties;\n  deviceProperties: DeviceCtlDeviceProperties;\n  hardwareProperties: DeviceCtlHardwareProperties;\n  /** \"A1A1AAA1-0011-1AA1-11A1-10A1111AA11A\" */\n  identifier: string;\n  visibilityClass: AnyEnum<'default'>;\n};\n\ntype DeviceCtlHardwareProperties = {\n  cpuType: DeviceCtlCpuType;\n  deviceType: AnyEnum<'iPhone'>;\n  /** 1114404411111111 */\n  ecid: number;\n  /** \"D74AP\" */\n  hardwareModel: string;\n  /** 512000000000 */\n  internalStorageCapacity: number;\n  /** true */\n  isProductionFused: boolean;\n  /** \"iPhone 14 Pro Max\" */\n  marketingName: string;\n  /** \"iOS\" */\n  platform: AnyEnum<'iOS' | 'xrOS'>;\n  /** \"iPhone15,3\" */\n  productType: AnyEnum<'iPhone13,4' | 'iPhone15,3'>;\n  reality: AnyEnum<'physical'>;\n  /** \"X2X1CC1XXX\" */\n  serialNumber: string;\n  supportedCPUTypes: DeviceCtlCpuType[];\n  /** [1] */\n  supportedDeviceFamilies: number[];\n  thinningProductType: AnyEnum<'iPhone15,3'>;\n  /** \"00001110-001111110110101A\" */\n  udid: string;\n};\n\ntype DeviceCtlDeviceProperties = {\n  /** true */\n  bootedFromSnapshot: boolean;\n  /** \"com.apple.os.update-AD0CF111ACFF11A11111A76A3D1262AE42A3F56F305AF5AE1135393A7A14A7D1\" */\n  bootedSnapshotName: string;\n  /** false */\n  ddiServicesAvailable: boolean;\n\n  developerModeStatus: AnyEnum<'enabled'>;\n  /** false */\n  hasInternalOSBuild: boolean;\n  /** \"Evan's phone\" */\n  name: string;\n  /** \"21E236\" */\n  osBuildUpdate: string;\n  /** \"17.4.1\" */\n  osVersionNumber: string;\n  /** false */\n  rootFileSystemIsWritable: boolean;\n};\n\ntype DeviceCtlDeviceCapability =\n  | {\n      name: AnyEnum;\n      featureIdentifier: AnyEnum;\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.connectdevice';\n      name: 'Connect to Device';\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.unpairdevice';\n      name: 'Unpair Device';\n    }\n  | {\n      featureIdentifier: 'com.apple.coredevice.feature.acquireusageassertion';\n      name: 'Acquire Usage Assertion';\n    };\n\ntype DeviceCtlConnectionProperties = {\n  authenticationType: AnyEnum<'manualPairing'>;\n  isMobileDeviceOnly: boolean;\n  /** \"2024-04-20T22:50:04.244Z\" */\n  lastConnectionDate: string;\n  pairingState: AnyEnum<'paired'>;\n  /** [\"00001111-001111110110101A.coredevice.local\", \"A1A1AAA1-0011-1AA1-11A1-10A1111AA11A.coredevice.local\"] */\n  potentialHostnames: string[];\n  transportType: AnyEnum<'localNetwork' | 'wired'>;\n  tunnelState: AnyEnum<'disconnected' | 'unavailable'>;\n  tunnelTransportProtocol: AnyEnum<'tcp'>;\n};\n\ntype DeviceCtlCpuType = {\n  name: AnyEnum<'arm64e' | 'arm64' | 'arm64_32'>;\n  subType: number;\n  /** 16777228 */\n  type: number;\n};\n\n/** Run a `devicectl` command. */\nexport async function devicectlAsync(\n  args: (string | undefined)[],\n  options?: SpawnOptions\n): Promise<SpawnResult> {\n  try {\n    return await xcrunAsync(['devicectl', ...args], options);\n  } catch (error: any) {\n    if (error instanceof CommandError) {\n      throw error;\n    }\n    if ('stderr' in error) {\n      const errorCodes = getDeviceCtlErrorCodes(error.stderr);\n      if (errorCodes.includes('Locked')) {\n        throw new CommandError('APPLE_DEVICE_LOCKED', 'Device is locked, unlock and try again.');\n      }\n    }\n    throw error;\n  }\n}\n\nexport async function getConnectedAppleDevicesAsync() {\n  if (!hasDevicectlEverBeenInstalled()) {\n    debug('devicectl not found, skipping remote Apple devices.');\n    return [];\n  }\n\n  const tmpPath = createTempFilePath();\n  const devices = await devicectlAsync([\n    'list',\n    'devices',\n    '--json-output',\n    tmpPath,\n    // Give two seconds before timing out: between 5 and 9223372036854775807\n    '--timeout',\n    '5',\n  ]);\n  debug(devices.stdout);\n  const devicesJson = await JsonFile.readAsync(tmpPath);\n\n  if ((devicesJson as any)?.info?.jsonVersion !== 2) {\n    Log.warn(\n      'Unexpected devicectl JSON version output from devicectl. Connecting to physical Apple devices may not work as expected.'\n    );\n  }\n\n  assertDevicesJson(devicesJson);\n\n  return devicesJson.result.devices as DeviceCtlDevice[];\n}\n\nfunction assertDevicesJson(\n  results: any\n): asserts results is { result: { devices: DeviceCtlDevice[] } } {\n  assert(\n    results != null && 'result' in results && Array.isArray(results?.result?.devices),\n    'Malformed JSON output from devicectl: ' + JSON.stringify(results, null, 2)\n  );\n}\n\nexport async function launchBinaryOnMacAsync(\n  bundleId: string,\n  appBinaryPath: string\n): Promise<void> {\n  const args = ['-b', bundleId, appBinaryPath];\n  try {\n    await spawnAsync('open', args);\n  } catch (error: any) {\n    if ('code' in error) {\n      if (error.code === 1) {\n        throw new CommandError(\n          'MACOS_LAUNCH',\n          'Failed to launch the compatible binary on macOS: open ' +\n            args.join(' ') +\n            '\\n\\n' +\n            error.message\n        );\n      }\n    }\n    throw error;\n  }\n}\n\nasync function installAppWithDeviceCtlAsync(\n  uuid: string,\n  bundleIdOrAppPath: string,\n  onProgress: (event: { status: string; isComplete: boolean; progress: number }) => void\n): Promise<void> {\n  // 𝝠 xcrun devicectl device install app --device 00001110-001111110110101A /Users/<USER>/Library/Developer/Xcode/DerivedData/Router-hgbqaxzhrhkiftfweydvhgttadvn/Build/Products/Debug-iphoneos/Router.app --verbose\n  return new Promise((resolve, reject) => {\n    const args: string[] = [\n      'devicectl',\n      'device',\n      'install',\n      'app',\n      '--device',\n      uuid,\n      bundleIdOrAppPath,\n    ];\n    const childProcess = spawn('xcrun', args);\n    debug('xcrun ' + args.join(' '));\n\n    let currentProgress = 0;\n    let hasStarted = false;\n\n    function updateProgress(progress: number) {\n      hasStarted = true;\n      if (progress <= currentProgress) {\n        return;\n      }\n      currentProgress = progress;\n      onProgress({\n        progress,\n        isComplete: progress === 100,\n        status: 'Installing',\n      });\n    }\n\n    childProcess.stdout.on('data', (data: Buffer) => {\n      // Sometimes more than one chunk comes at a time, here we split by system newline,\n      // then trim and filter.\n      const strings = data\n        .toString()\n        .split(EOL)\n        .map((value) => value.trim());\n\n      strings.forEach((str) => {\n        // Match the progress percentage:\n        // - '34%... 35%...' -> 34\n        // - '31%...' -> 31\n        // - 'Complete!' -> 100\n\n        const match = str.match(/(\\d+)%\\.\\.\\./);\n        if (match) {\n          updateProgress(parseInt(match[1], 10));\n        } else if (hasStarted) {\n          updateProgress(100);\n        }\n      });\n\n      debug('[stdout]:', strings);\n    });\n\n    childProcess.on('close', (code) => {\n      debug('[close]: ' + code);\n      if (code === 0) {\n        resolve();\n      } else {\n        const stderr = childProcess.stderr.read();\n        const err = new Error(stderr);\n        (err as any).code = code;\n        detach(err);\n      }\n    });\n\n    const detach = async (err?: Error) => {\n      off?.();\n      if (childProcess) {\n        return new Promise<void>((resolve) => {\n          childProcess?.on('close', resolve);\n          childProcess?.kill();\n          // childProcess = null;\n          reject(err ?? new CommandError('detached'));\n        });\n      }\n    };\n\n    const off = installExitHooks(() => detach());\n  });\n}\n\nexport async function launchAppWithDeviceCtl(deviceId: string, bundleId: string) {\n  await devicectlAsync(['device', 'process', 'launch', '--device', deviceId, bundleId]);\n}\n\n/** Find all error codes from the output log */\nfunction getDeviceCtlErrorCodes(log: string): string[] {\n  return [...log.matchAll(/BSErrorCodeDescription\\s+=\\s+(.*)$/gim)].map(([_line, code]) => code);\n}\n\nlet hasEverBeenInstalled: boolean | undefined;\n\nexport function hasDevicectlEverBeenInstalled() {\n  if (hasEverBeenInstalled) return hasEverBeenInstalled;\n  // It doesn't appear possible for devicectl to ever be uninstalled. We can just check once and store this result forever\n  // to prevent cold boots of devicectl from slowing down all invocations of `expo run ios`\n  if (fs.existsSync(DEVICE_CTL_EXISTS_PATH)) {\n    hasEverBeenInstalled = true;\n    return true;\n  }\n\n  const isInstalled = isDevicectlInstalled();\n\n  if (isInstalled) {\n    fs.writeFileSync(DEVICE_CTL_EXISTS_PATH, '1');\n  }\n  hasEverBeenInstalled = isInstalled;\n  return isInstalled;\n}\n\nfunction isDevicectlInstalled() {\n  try {\n    execSync('xcrun devicectl --version', { stdio: 'ignore' });\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Wraps the apple device method for installing and running an app,\n * adds indicator and retry loop for when the device is locked.\n */\nexport async function installAndLaunchAppAsync(props: {\n  bundle: string;\n  bundleIdentifier: string;\n  udid: string;\n  deviceName: string;\n}): Promise<void> {\n  debug('Running on device:', props);\n  const { bundle, bundleIdentifier, udid, deviceName } = props;\n  let indicator: Ora | undefined;\n\n  try {\n    if (!indicator) {\n      indicator = ora(`Connecting to: ${props.deviceName}`).start();\n    }\n\n    await installAppWithDeviceCtlAsync(\n      udid,\n      bundle,\n      ({\n        status,\n        isComplete,\n        progress,\n      }: {\n        status: string;\n        isComplete: boolean;\n        progress: number;\n      }) => {\n        if (!indicator) {\n          indicator = ora(status).start();\n        }\n        indicator.text = `${chalk.bold(status)} ${progress}%`;\n        if (isComplete) {\n          indicator.succeed();\n        }\n      }\n    );\n  } catch (error: any) {\n    if (indicator) {\n      indicator.fail();\n    }\n    throw error;\n  }\n\n  async function launchAppOptionally() {\n    try {\n      await launchAppWithDeviceCtl(udid, bundleIdentifier);\n    } catch (error: any) {\n      if (indicator) {\n        indicator.fail();\n      }\n      if (error.code === 'APPLE_DEVICE_LOCKED') {\n        // Get the app name from the binary path.\n        const appName = path.basename(bundle).split('.')[0] ?? 'app';\n        if (\n          isInteractive() &&\n          (await confirmAsync({\n            message: `Cannot launch ${appName} because the device is locked. Unlock ${deviceName} to continue...`,\n            initial: true,\n          }))\n        ) {\n          return launchAppOptionally();\n        }\n        throw new CommandError(\n          `Cannot launch ${appName} on ${deviceName} because the device is locked.`\n        );\n      }\n      throw error;\n    }\n  }\n\n  await launchAppOptionally();\n}\n"], "names": ["devicectlAsync", "getConnectedAppleDevicesAsync", "launchBinaryOnMacAsync", "launchAppWithDeviceCtl", "hasDevicectlEverBeenInstalled", "installAndLaunchAppAsync", "DEVICE_CTL_EXISTS_PATH", "path", "join", "getExpoHomeDirectory", "debug", "require", "args", "options", "xcrunAsync", "error", "CommandError", "errorCodes", "getDeviceCtlErrorCodes", "stderr", "includes", "tmpPath", "createTempFilePath", "devices", "stdout", "devicesJson", "JsonFile", "readAsync", "info", "jsonVersion", "Log", "warn", "assertDevicesJson", "result", "results", "assert", "Array", "isArray", "JSON", "stringify", "bundleId", "appBinaryPath", "spawnAsync", "code", "message", "installAppWithDeviceCtlAsync", "uuid", "bundleIdOrAppPath", "onProgress", "Promise", "resolve", "reject", "childProcess", "spawn", "currentProgress", "hasStarted", "updateProgress", "progress", "isComplete", "status", "on", "data", "strings", "toString", "split", "EOL", "map", "value", "trim", "for<PERSON>ach", "str", "match", "parseInt", "read", "err", "Error", "detach", "off", "kill", "installExitHooks", "deviceId", "log", "matchAll", "_line", "hasEverBeenInstalled", "fs", "existsSync", "isInstalled", "isDevicectlInstalled", "writeFileSync", "execSync", "stdio", "props", "bundle", "bundleIdentifier", "udid", "deviceName", "indicator", "ora", "start", "text", "chalk", "bold", "succeed", "fail", "launchAppOptionally", "appName", "basename", "isInteractive", "<PERSON><PERSON><PERSON>", "initial"], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;;;IA4HsBA,cAAc,MAAdA,cAAc;IAoBdC,6BAA6B,MAA7BA,6BAA6B;IAuC7BC,sBAAsB,MAAtBA,sBAAsB;IA+GtBC,sBAAsB,MAAtBA,sBAAsB;IAW5BC,6BAA6B,MAA7BA,6BAA6B;IA+BvBC,wBAAwB,MAAxBA,wBAAwB;;;yBAhVT,iCAAiC;;;;;;;8DACjD,iBAAiB;;;;;;;8DACgB,mBAAmB;;;;;;;8DACvD,OAAO;;;;;;;yBACO,eAAe;;;;;;;8DAChC,IAAI;;;;;;;8DACA,aAAa;;;;;;;yBAEZ,IAAI;;;;;;;8DACP,MAAM;;;;;;uBAEI,SAAS;2DACf,cAAc;gCACA,+BAA+B;wBACrC,uBAAuB;sBACnB,qBAAqB;6BACxB,4BAA4B;qBACtC,oBAAoB;yBACX,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErD,MAAMC,sBAAsB,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,IAAAA,aAAoB,EAAA,qBAAA,GAAE,EAAE,kBAAkB,CAAC,AAAC;AAErF,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,AAAsB,AAAC;AAsGhE,eAAeX,cAAc,CAClCY,IAA4B,EAC5BC,OAAsB,EACA;IACtB,IAAI;QACF,OAAO,MAAMC,IAAAA,MAAU,WAAA,EAAC;YAAC,WAAW;eAAKF,IAAI;SAAC,EAAEC,OAAO,CAAC,CAAC;IAC3D,EAAE,OAAOE,KAAK,EAAO;QACnB,IAAIA,KAAK,YAAYC,OAAY,aAAA,EAAE;YACjC,MAAMD,KAAK,CAAC;QACd,CAAC;QACD,IAAI,QAAQ,IAAIA,KAAK,EAAE;YACrB,MAAME,UAAU,GAAGC,sBAAsB,CAACH,KAAK,CAACI,MAAM,CAAC,AAAC;YACxD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACjC,MAAM,IAAIJ,OAAY,aAAA,CAAC,qBAAqB,EAAE,yCAAyC,CAAC,CAAC;YAC3F,CAAC;QACH,CAAC;QACD,MAAMD,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAEM,eAAed,6BAA6B,GAAG;QAmBhD,GAAoB;IAlBxB,IAAI,CAACG,6BAA6B,EAAE,EAAE;QACpCM,KAAK,CAAC,qDAAqD,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAMW,OAAO,GAAGC,IAAAA,eAAkB,mBAAA,GAAE,AAAC;IACrC,MAAMC,OAAO,GAAG,MAAMvB,cAAc,CAAC;QACnC,MAAM;QACN,SAAS;QACT,eAAe;QACfqB,OAAO;QACP,wEAAwE;QACxE,WAAW;QACX,GAAG;KACJ,CAAC,AAAC;IACHX,KAAK,CAACa,OAAO,CAACC,MAAM,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAG,MAAMC,SAAQ,EAAA,QAAA,CAACC,SAAS,CAACN,OAAO,CAAC,AAAC;IAEtD,IAAI,CAAA,CAAA,GAAoB,GAAnBI,WAAW,SAAc,GAA1B,KAAA,CAA0B,GAA1B,QAAA,GAAoB,CAAEG,IAAI,SAAA,GAA1B,KAAA,CAA0B,QAAEC,WAAW,AAAb,CAAA,KAAkB,CAAC,EAAE;QACjDC,IAAG,CAACC,IAAI,CACN,yHAAyH,CAC1H,CAAC;IACJ,CAAC;IAEDC,iBAAiB,CAACP,WAAW,CAAC,CAAC;IAE/B,OAAOA,WAAW,CAACQ,MAAM,CAACV,OAAO,CAAsB;AACzD,CAAC;AAED,SAASS,iBAAiB,CACxBE,OAAY,EACmD;QAELA,GAAe;IADzEC,IAAAA,WAAM,EAAA,QAAA,EACJD,OAAO,IAAI,IAAI,IAAI,QAAQ,IAAIA,OAAO,IAAIE,KAAK,CAACC,OAAO,CAACH,OAAO,QAAQ,GAAfA,KAAAA,CAAe,GAAfA,CAAAA,GAAe,GAAfA,OAAO,CAAED,MAAM,SAAA,GAAfC,KAAAA,CAAe,GAAfA,GAAe,CAAEX,OAAO,AAAT,CAAU,EACjF,wCAAwC,GAAGe,IAAI,CAACC,SAAS,CAACL,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAC5E,CAAC;AACJ,CAAC;AAEM,eAAehC,sBAAsB,CAC1CsC,QAAgB,EAChBC,aAAqB,EACN;IACf,MAAM7B,IAAI,GAAG;QAAC,IAAI;QAAE4B,QAAQ;QAAEC,aAAa;KAAC,AAAC;IAC7C,IAAI;QACF,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,MAAM,EAAE9B,IAAI,CAAC,CAAC;IACjC,EAAE,OAAOG,KAAK,EAAO;QACnB,IAAI,MAAM,IAAIA,KAAK,EAAE;YACnB,IAAIA,KAAK,CAAC4B,IAAI,KAAK,CAAC,EAAE;gBACpB,MAAM,IAAI3B,OAAY,aAAA,CACpB,cAAc,EACd,wDAAwD,GACtDJ,IAAI,CAACJ,IAAI,CAAC,GAAG,CAAC,GACd,MAAM,GACNO,KAAK,CAAC6B,OAAO,CAChB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,MAAM7B,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,eAAe8B,4BAA4B,CACzCC,IAAY,EACZC,iBAAyB,EACzBC,UAAsF,EACvE;IACf,sNAAsN;IACtN,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,GAAK;QACtC,MAAMvC,IAAI,GAAa;YACrB,WAAW;YACX,QAAQ;YACR,SAAS;YACT,KAAK;YACL,UAAU;YACVkC,IAAI;YACJC,iBAAiB;SAClB,AAAC;QACF,MAAMK,YAAY,GAAGC,IAAAA,aAAK,EAAA,MAAA,EAAC,OAAO,EAAEzC,IAAI,CAAC,AAAC;QAC1CF,KAAK,CAAC,QAAQ,GAAGE,IAAI,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAEjC,IAAI8C,eAAe,GAAG,CAAC,AAAC;QACxB,IAAIC,UAAU,GAAG,KAAK,AAAC;QAEvB,SAASC,cAAc,CAACC,QAAgB,EAAE;YACxCF,UAAU,GAAG,IAAI,CAAC;YAClB,IAAIE,QAAQ,IAAIH,eAAe,EAAE;gBAC/B,OAAO;YACT,CAAC;YACDA,eAAe,GAAGG,QAAQ,CAAC;YAC3BT,UAAU,CAAC;gBACTS,QAAQ;gBACRC,UAAU,EAAED,QAAQ,KAAK,GAAG;gBAC5BE,MAAM,EAAE,YAAY;aACrB,CAAC,CAAC;QACL,CAAC;QAEDP,YAAY,CAAC5B,MAAM,CAACoC,EAAE,CAAC,MAAM,EAAE,CAACC,IAAY,GAAK;YAC/C,kFAAkF;YAClF,wBAAwB;YACxB,MAAMC,OAAO,GAAGD,IAAI,CACjBE,QAAQ,EAAE,CACVC,KAAK,CAACC,GAAG,EAAA,IAAA,CAAC,CACVC,GAAG,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,IAAI,EAAE,CAAC,AAAC;YAEhCN,OAAO,CAACO,OAAO,CAAC,CAACC,GAAG,GAAK;gBACvB,iCAAiC;gBACjC,0BAA0B;gBAC1B,mBAAmB;gBACnB,uBAAuB;gBAEvB,MAAMC,KAAK,GAAGD,GAAG,CAACC,KAAK,gBAAgB,AAAC;gBACxC,IAAIA,KAAK,EAAE;oBACTf,cAAc,CAACgB,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBACzC,OAAO,IAAIhB,UAAU,EAAE;oBACrBC,cAAc,CAAC,GAAG,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH9C,KAAK,CAAC,WAAW,EAAEoD,OAAO,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEHV,YAAY,CAACQ,EAAE,CAAC,OAAO,EAAE,CAACjB,IAAI,GAAK;YACjCjC,KAAK,CAAC,WAAW,GAAGiC,IAAI,CAAC,CAAC;YAC1B,IAAIA,IAAI,KAAK,CAAC,EAAE;gBACdO,OAAO,EAAE,CAAC;YACZ,OAAO;gBACL,MAAM/B,MAAM,GAAGiC,YAAY,CAACjC,MAAM,CAACsD,IAAI,EAAE,AAAC;gBAC1C,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAACxD,MAAM,CAAC,AAAC;gBAC9B,AAACuD,GAAG,CAAS/B,IAAI,GAAGA,IAAI,CAAC;gBACzBiC,MAAM,CAACF,GAAG,CAAC,CAAC;YACd,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAME,MAAM,GAAG,OAAOF,GAAW,GAAK;YACpCG,GAAG,QAAI,GAAPA,KAAAA,CAAO,GAAPA,GAAG,EAAI,CAAC;YACR,IAAIzB,YAAY,EAAE;gBAChB,OAAO,IAAIH,OAAO,CAAO,CAACC,OAAO,GAAK;oBACpCE,YAAY,QAAI,GAAhBA,KAAAA,CAAgB,GAAhBA,YAAY,CAAEQ,EAAE,CAAC,OAAO,EAAEV,OAAO,CAAC,CAAC;oBACnCE,YAAY,QAAM,GAAlBA,KAAAA,CAAkB,GAAlBA,YAAY,CAAE0B,IAAI,EAAE,CAAC;oBACrB,uBAAuB;oBACvB3B,MAAM,CAACuB,GAAG,IAAI,IAAI1D,OAAY,aAAA,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,AAAC;QAEF,MAAM6D,GAAG,GAAGE,IAAAA,KAAgB,iBAAA,EAAC,IAAMH,MAAM,EAAE,CAAC,AAAC;IAC/C,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,eAAezE,sBAAsB,CAAC6E,QAAgB,EAAExC,QAAgB,EAAE;IAC/E,MAAMxC,cAAc,CAAC;QAAC,QAAQ;QAAE,SAAS;QAAE,QAAQ;QAAE,UAAU;QAAEgF,QAAQ;QAAExC,QAAQ;KAAC,CAAC,CAAC;AACxF,CAAC;AAED,6CAA6C,GAC7C,SAAStB,sBAAsB,CAAC+D,GAAW,EAAY;IACrD,OAAO;WAAIA,GAAG,CAACC,QAAQ,yCAAyC;KAAC,CAAChB,GAAG,CAAC,CAAC,CAACiB,KAAK,EAAExC,IAAI,CAAC,GAAKA,IAAI,CAAC,CAAC;AACjG,CAAC;AAED,IAAIyC,oBAAoB,AAAqB,AAAC;AAEvC,SAAShF,6BAA6B,GAAG;IAC9C,IAAIgF,oBAAoB,EAAE,OAAOA,oBAAoB,CAAC;IACtD,wHAAwH;IACxH,yFAAyF;IACzF,IAAIC,GAAE,EAAA,QAAA,CAACC,UAAU,CAAChF,sBAAsB,CAAC,EAAE;QACzC8E,oBAAoB,GAAG,IAAI,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMG,WAAW,GAAGC,oBAAoB,EAAE,AAAC;IAE3C,IAAID,WAAW,EAAE;QACfF,GAAE,EAAA,QAAA,CAACI,aAAa,CAACnF,sBAAsB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IACD8E,oBAAoB,GAAGG,WAAW,CAAC;IACnC,OAAOA,WAAW,CAAC;AACrB,CAAC;AAED,SAASC,oBAAoB,GAAG;IAC9B,IAAI;QACFE,IAAAA,aAAQ,EAAA,SAAA,EAAC,2BAA2B,EAAE;YAAEC,KAAK,EAAE,QAAQ;SAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAMM,eAAetF,wBAAwB,CAACuF,KAK9C,EAAiB;IAChBlF,KAAK,CAAC,oBAAoB,EAAEkF,KAAK,CAAC,CAAC;IACnC,MAAM,EAAEC,MAAM,CAAA,EAAEC,gBAAgB,CAAA,EAAEC,IAAI,CAAA,EAAEC,UAAU,CAAA,EAAE,GAAGJ,KAAK,AAAC;IAC7D,IAAIK,SAAS,AAAiB,AAAC;IAE/B,IAAI;QACF,IAAI,CAACA,SAAS,EAAE;YACdA,SAAS,GAAGC,IAAAA,IAAG,IAAA,EAAC,CAAC,eAAe,EAAEN,KAAK,CAACI,UAAU,CAAC,CAAC,CAAC,CAACG,KAAK,EAAE,CAAC;QAChE,CAAC;QAED,MAAMtD,4BAA4B,CAChCkD,IAAI,EACJF,MAAM,EACN,CAAC,EACClC,MAAM,CAAA,EACND,UAAU,CAAA,EACVD,QAAQ,CAAA,EAKT,GAAK;YACJ,IAAI,CAACwC,SAAS,EAAE;gBACdA,SAAS,GAAGC,IAAAA,IAAG,IAAA,EAACvC,MAAM,CAAC,CAACwC,KAAK,EAAE,CAAC;YAClC,CAAC;YACDF,SAAS,CAACG,IAAI,GAAG,CAAC,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtD,IAAIC,UAAU,EAAE;gBACduC,SAAS,CAACM,OAAO,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CACF,CAAC;IACJ,EAAE,OAAOxF,KAAK,EAAO;QACnB,IAAIkF,SAAS,EAAE;YACbA,SAAS,CAACO,IAAI,EAAE,CAAC;QACnB,CAAC;QACD,MAAMzF,KAAK,CAAC;IACd,CAAC;IAED,eAAe0F,mBAAmB,GAAG;QACnC,IAAI;YACF,MAAMtG,sBAAsB,CAAC4F,IAAI,EAAED,gBAAgB,CAAC,CAAC;QACvD,EAAE,OAAO/E,KAAK,EAAO;YACnB,IAAIkF,SAAS,EAAE;gBACbA,SAAS,CAACO,IAAI,EAAE,CAAC;YACnB,CAAC;YACD,IAAIzF,KAAK,CAAC4B,IAAI,KAAK,qBAAqB,EAAE;gBACxC,yCAAyC;gBACzC,MAAM+D,OAAO,GAAGnG,KAAI,EAAA,QAAA,CAACoG,QAAQ,CAACd,MAAM,CAAC,CAAC7B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,AAAC;gBAC7D,IACE4C,IAAAA,YAAa,cAAA,GAAE,IACd,MAAMC,IAAAA,QAAY,aAAA,EAAC;oBAClBjE,OAAO,EAAE,CAAC,cAAc,EAAE8D,OAAO,CAAC,sCAAsC,EAAEV,UAAU,CAAC,eAAe,CAAC;oBACrGc,OAAO,EAAE,IAAI;iBACd,CAAC,AAAC,EACH;oBACA,OAAOL,mBAAmB,EAAE,CAAC;gBAC/B,CAAC;gBACD,MAAM,IAAIzF,OAAY,aAAA,CACpB,CAAC,cAAc,EAAE0F,OAAO,CAAC,IAAI,EAAEV,UAAU,CAAC,8BAA8B,CAAC,CAC1E,CAAC;YACJ,CAAC;YACD,MAAMjF,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM0F,mBAAmB,EAAE,CAAC;AAC9B,CAAC"}