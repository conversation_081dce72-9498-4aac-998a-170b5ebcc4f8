{"version": 3, "sources": ["../../../../../src/start/server/middleware/metroOptions.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport type { BundleOptions as MetroBundleOptions } from 'metro/src/shared/types';\nimport resolveFrom from 'resolve-from';\n\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nconst debug = require('debug')('expo:metro:options') as typeof console.log;\n\nexport type MetroEnvironment = 'node' | 'react-server' | 'client';\n\nexport type ExpoMetroOptions = {\n  platform: string;\n  mainModuleName: string;\n  mode: string;\n  minify?: boolean;\n  environment?: MetroEnvironment;\n  serializerOutput?: 'static';\n  serializerIncludeMaps?: boolean;\n  lazy?: boolean;\n  engine?: 'hermes';\n  preserveEnvVars?: boolean;\n  bytecode: boolean;\n  /** Enable async routes (route-based bundle splitting) in Expo Router. */\n  asyncRoutes?: boolean;\n  /** Module ID relative to the projectRoot for the Expo Router app directory. */\n  routerRoot: string;\n  /** Enable React compiler support in Babel. */\n  reactCompiler: boolean;\n  baseUrl?: string;\n  isExporting: boolean;\n  /** Is bundling a DOM Component (\"use dom\"). Requires the entry dom component file path. */\n  domRoot?: string;\n  /** Exporting MD5 filename based on file contents, for EAS Update.  */\n  useMd5Filename?: boolean;\n  inlineSourceMap?: boolean;\n  clientBoundaries?: string[];\n  splitChunks?: boolean;\n  usedExports?: boolean;\n  /** Enable optimized bundling (required for tree shaking). */\n  optimize?: boolean;\n\n  modulesOnly?: boolean;\n  runModule?: boolean;\n};\n\nexport type SerializerOptions = {\n  includeSourceMaps?: boolean;\n  output?: 'static';\n  splitChunks?: boolean;\n  usedExports?: boolean;\n};\n\nexport type ExpoMetroBundleOptions = MetroBundleOptions & {\n  serializerOptions?: SerializerOptions;\n};\n\nexport function isServerEnvironment(environment?: any): boolean {\n  return environment === 'node' || environment === 'react-server';\n}\n\nexport function shouldEnableAsyncImports(projectRoot: string): boolean {\n  if (env.EXPO_NO_METRO_LAZY) {\n    return false;\n  }\n\n  // `@expo/metro-runtime` includes support for the fetch + eval runtime code required\n  // to support async imports. If it's not installed, we can't support async imports.\n  // If it is installed, the user MUST import it somewhere in their project.\n  // Expo Router automatically pulls this in, so we can check for it.\n  return resolveFrom.silent(projectRoot, '@expo/metro-runtime') != null;\n}\n\nfunction withDefaults({\n  mode = 'development',\n  minify = mode === 'production',\n  preserveEnvVars = mode !== 'development' && env.EXPO_NO_CLIENT_ENV_VARS,\n  lazy,\n  environment,\n  ...props\n}: ExpoMetroOptions): ExpoMetroOptions {\n  if (props.bytecode) {\n    if (props.platform === 'web') {\n      throw new CommandError('Cannot use bytecode with the web platform');\n    }\n    if (props.engine !== 'hermes') {\n      throw new CommandError('Bytecode is only supported with the Hermes engine');\n    }\n  }\n\n  const optimize =\n    props.optimize ??\n    (environment !== 'node' && mode === 'production' && env.EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH);\n\n  return {\n    mode,\n    minify,\n    preserveEnvVars,\n    optimize,\n    usedExports: optimize && env.EXPO_UNSTABLE_TREE_SHAKING,\n    lazy: !props.isExporting && lazy,\n    environment: environment === 'client' ? undefined : environment,\n    ...props,\n  };\n}\n\nexport function getBaseUrlFromExpoConfig(exp: ExpoConfig) {\n  return exp.experiments?.baseUrl?.trim().replace(/\\/+$/, '') ?? '';\n}\n\nexport function getAsyncRoutesFromExpoConfig(exp: ExpoConfig, mode: string, platform: string) {\n  let asyncRoutesSetting;\n\n  if (exp.extra?.router?.asyncRoutes) {\n    const asyncRoutes = exp.extra?.router?.asyncRoutes;\n    if (['boolean', 'string'].includes(typeof asyncRoutes)) {\n      asyncRoutesSetting = asyncRoutes;\n    } else if (typeof asyncRoutes === 'object') {\n      asyncRoutesSetting = asyncRoutes[platform] ?? asyncRoutes.default;\n    }\n  }\n\n  return [mode, true].includes(asyncRoutesSetting);\n}\n\nexport function getMetroDirectBundleOptionsForExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'baseUrl' | 'reactCompiler' | 'routerRoot' | 'asyncRoutes'>\n): Partial<ExpoMetroBundleOptions> {\n  return getMetroDirectBundleOptions({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n    asyncRoutes: getAsyncRoutesFromExpoConfig(exp, options.mode, options.platform),\n  });\n}\n\nexport function getMetroDirectBundleOptions(\n  options: ExpoMetroOptions\n): Partial<ExpoMetroBundleOptions> {\n  const {\n    mainModuleName,\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    bytecode,\n    lazy,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    isExporting,\n    inlineSourceMap,\n    splitChunks,\n    usedExports,\n    reactCompiler,\n    optimize,\n    domRoot,\n    clientBoundaries,\n    runModule,\n    modulesOnly,\n    useMd5Filename,\n  } = withDefaults(options);\n\n  const dev = mode !== 'production';\n  const isHermes = engine === 'hermes';\n\n  if (isExporting) {\n    debug('Disabling lazy bundling for export build');\n    options.lazy = false;\n  }\n\n  let fakeSourceUrl: string | undefined;\n  let fakeSourceMapUrl: string | undefined;\n\n  // TODO: Upstream support to Metro for passing custom serializer options.\n  if (serializerIncludeMaps != null || serializerOutput != null) {\n    fakeSourceUrl = new URL(\n      createBundleUrlPath(options).replace(/^\\//, ''),\n      'http://localhost:8081'\n    ).toString();\n    if (serializerIncludeMaps) {\n      fakeSourceMapUrl = fakeSourceUrl.replace('.bundle?', '.map?');\n    }\n  }\n\n  const customTransformOptions: ExpoMetroBundleOptions['customTransformOptions'] = {\n    __proto__: null,\n    optimize: optimize || undefined,\n    engine,\n    clientBoundaries,\n    preserveEnvVars: preserveEnvVars || undefined,\n    // Use string to match the query param behavior.\n    asyncRoutes: asyncRoutes ? String(asyncRoutes) : undefined,\n    environment,\n    baseUrl: baseUrl || undefined,\n    routerRoot,\n    bytecode: bytecode ? '1' : undefined,\n    reactCompiler: reactCompiler ? String(reactCompiler) : undefined,\n    dom: domRoot,\n    useMd5Filename: useMd5Filename || undefined,\n  };\n\n  // Iterate and delete undefined values\n  for (const key in customTransformOptions) {\n    if (customTransformOptions[key] === undefined) {\n      delete customTransformOptions[key];\n    }\n  }\n\n  const bundleOptions: Partial<ExpoMetroBundleOptions> = {\n    platform,\n    entryFile: mainModuleName,\n    dev,\n    minify: minify ?? !dev,\n    inlineSourceMap: inlineSourceMap ?? false,\n    lazy: (!isExporting && lazy) || undefined,\n    unstable_transformProfile: isHermes ? 'hermes-stable' : 'default',\n    customTransformOptions,\n    runModule,\n    modulesOnly,\n    customResolverOptions: {\n      __proto__: null,\n      environment,\n      exporting: isExporting || undefined,\n    },\n    sourceMapUrl: fakeSourceMapUrl,\n    sourceUrl: fakeSourceUrl,\n    serializerOptions: {\n      splitChunks,\n      usedExports: usedExports || undefined,\n      output: serializerOutput,\n      includeSourceMaps: serializerIncludeMaps,\n    },\n  };\n\n  return bundleOptions;\n}\n\nexport function createBundleUrlPathFromExpoConfig(\n  projectRoot: string,\n  exp: ExpoConfig,\n  options: Omit<ExpoMetroOptions, 'reactCompiler' | 'baseUrl' | 'routerRoot'>\n): string {\n  return createBundleUrlPath({\n    ...options,\n    reactCompiler: !!exp.experiments?.reactCompiler,\n    baseUrl: getBaseUrlFromExpoConfig(exp),\n    routerRoot: getRouterDirectoryModuleIdWithManifest(projectRoot, exp),\n  });\n}\n\nexport function createBundleUrlPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  return `/${encodeURI(options.mainModuleName.replace(/^\\/+/, ''))}.bundle?${queryParams.toString()}`;\n}\n\n/**\n * Create a bundle URL, containing all required query parameters, using a valid \"os path\".\n * On POSIX systems, this would look something like `/Users/<USER>/project/file.js?dev=false&..`.\n * On UNIX systems, this would look something like `C:\\Users\\<USER>\\project\\file.js?dev=false&..`.\n * This path can safely be used with `path.*` modifiers and resolved.\n */\nexport function createBundleUrlOsPath(options: ExpoMetroOptions): string {\n  const queryParams = createBundleUrlSearchParams(options);\n  const mainModuleName = encodeURI(toPosixPath(options.mainModuleName));\n  return `${mainModuleName}.bundle?${queryParams.toString()}`;\n}\n\nexport function createBundleUrlSearchParams(options: ExpoMetroOptions): URLSearchParams {\n  const {\n    platform,\n    mode,\n    minify,\n    environment,\n    serializerOutput,\n    serializerIncludeMaps,\n    lazy,\n    bytecode,\n    engine,\n    preserveEnvVars,\n    asyncRoutes,\n    baseUrl,\n    routerRoot,\n    reactCompiler,\n    inlineSourceMap,\n    isExporting,\n    clientBoundaries,\n    splitChunks,\n    usedExports,\n    optimize,\n    domRoot,\n    modulesOnly,\n    runModule,\n  } = withDefaults(options);\n\n  const dev = String(mode !== 'production');\n  const queryParams = new URLSearchParams({\n    platform: encodeURIComponent(platform),\n    dev,\n    // TODO: Is this still needed?\n    hot: String(false),\n  });\n\n  // Lazy bundling must be disabled for bundle splitting to work.\n  if (!isExporting && lazy) {\n    queryParams.append('lazy', String(lazy));\n  }\n\n  if (inlineSourceMap) {\n    queryParams.append('inlineSourceMap', String(inlineSourceMap));\n  }\n\n  if (minify) {\n    queryParams.append('minify', String(minify));\n  }\n\n  // We split bytecode from the engine since you could technically use Hermes without bytecode.\n  // Hermes indicates the type of language features you want to transform out of the JS, whereas bytecode\n  // indicates whether you want to use the Hermes bytecode format.\n  if (engine) {\n    queryParams.append('transform.engine', engine);\n  }\n  if (bytecode) {\n    queryParams.append('transform.bytecode', '1');\n  }\n  if (asyncRoutes) {\n    queryParams.append('transform.asyncRoutes', String(asyncRoutes));\n  }\n  if (preserveEnvVars) {\n    queryParams.append('transform.preserveEnvVars', String(preserveEnvVars));\n  }\n  if (baseUrl) {\n    queryParams.append('transform.baseUrl', baseUrl);\n  }\n  if (clientBoundaries?.length) {\n    queryParams.append('transform.clientBoundaries', JSON.stringify(clientBoundaries));\n  }\n  if (routerRoot != null) {\n    queryParams.append('transform.routerRoot', routerRoot);\n  }\n  if (reactCompiler) {\n    queryParams.append('transform.reactCompiler', String(reactCompiler));\n  }\n  if (domRoot) {\n    queryParams.append('transform.dom', domRoot);\n  }\n\n  if (environment) {\n    queryParams.append('resolver.environment', environment);\n    queryParams.append('transform.environment', environment);\n  }\n\n  if (isExporting) {\n    queryParams.append('resolver.exporting', String(isExporting));\n  }\n\n  if (splitChunks) {\n    queryParams.append('serializer.splitChunks', String(splitChunks));\n  }\n  if (usedExports) {\n    queryParams.append('serializer.usedExports', String(usedExports));\n  }\n  if (optimize) {\n    queryParams.append('transform.optimize', String(optimize));\n  }\n  if (serializerOutput) {\n    queryParams.append('serializer.output', serializerOutput);\n  }\n  if (serializerIncludeMaps) {\n    queryParams.append('serializer.map', String(serializerIncludeMaps));\n  }\n  if (engine === 'hermes') {\n    queryParams.append('unstable_transformProfile', 'hermes-stable');\n  }\n\n  if (modulesOnly != null) {\n    queryParams.set('modulesOnly', String(modulesOnly));\n  }\n  if (runModule != null) {\n    queryParams.set('runModule', String(runModule));\n  }\n\n  return queryParams;\n}\n\n/**\n * Convert all path separators to `/`, including on Windows.\n * Metro asumes that all module specifiers are posix paths.\n * References to directories can still be Windows-style paths in Metro.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules#importing_features_into_your_script\n * @see https://github.com/facebook/metro/pull/1286\n */\nexport function convertPathToModuleSpecifier(pathLike: string) {\n  return toPosixPath(pathLike);\n}\n\nexport function getMetroOptionsFromUrl(urlFragment: string) {\n  const url = new URL(urlFragment, 'http://localhost:0');\n  const getStringParam = (key: string) => {\n    const param = url.searchParams.get(key);\n    if (Array.isArray(param)) {\n      throw new Error(`Expected single value for ${key}`);\n    }\n    return param;\n  };\n\n  let pathname = url.pathname;\n  if (pathname.endsWith('.bundle')) {\n    pathname = pathname.slice(0, -'.bundle'.length);\n  }\n\n  const options: ExpoMetroOptions = {\n    mode: isTruthy(getStringParam('dev') ?? 'true') ? 'development' : 'production',\n    minify: isTruthy(getStringParam('minify') ?? 'false'),\n    lazy: isTruthy(getStringParam('lazy') ?? 'false'),\n    routerRoot: getStringParam('transform.routerRoot') ?? 'app',\n    isExporting: isTruthy(getStringParam('resolver.exporting') ?? 'false'),\n    environment: assertEnvironment(getStringParam('transform.environment') ?? 'node'),\n    platform: url.searchParams.get('platform') ?? 'web',\n    bytecode: isTruthy(getStringParam('transform.bytecode') ?? 'false'),\n    mainModuleName: convertPathToModuleSpecifier(pathname),\n    reactCompiler: isTruthy(getStringParam('transform.reactCompiler') ?? 'false'),\n    asyncRoutes: isTruthy(getStringParam('transform.asyncRoutes') ?? 'false'),\n    baseUrl: getStringParam('transform.baseUrl') ?? undefined,\n    // clientBoundaries: JSON.parse(getStringParam('transform.clientBoundaries') ?? '[]'),\n    engine: assertEngine(getStringParam('transform.engine')),\n    runModule: isTruthy(getStringParam('runModule') ?? 'true'),\n    modulesOnly: isTruthy(getStringParam('modulesOnly') ?? 'false'),\n  };\n\n  return options;\n}\n\nfunction isTruthy(value: string | null): boolean {\n  return value === 'true' || value === '1';\n}\n\nfunction assertEnvironment(environment: string | undefined): MetroEnvironment | undefined {\n  if (!environment) {\n    return undefined;\n  }\n  if (!['node', 'react-server', 'client'].includes(environment)) {\n    throw new Error(`Expected transform.environment to be one of: node, react-server, client`);\n  }\n  return environment as MetroEnvironment;\n}\nfunction assertEngine(engine: string | undefined | null): 'hermes' | undefined {\n  if (!engine) {\n    return undefined;\n  }\n  if (!['hermes'].includes(engine)) {\n    throw new Error(`Expected transform.engine to be one of: hermes`);\n  }\n  return engine as 'hermes';\n}\n"], "names": ["isServerEnvironment", "shouldEnableAsyncImports", "getBaseUrlFromExpoConfig", "getAsyncRoutesFromExpoConfig", "getMetroDirectBundleOptionsForExpoConfig", "getMetroDirectBundleOptions", "createBundleUrlPathFromExpoConfig", "createBundleUrlPath", "createBundleUrlOsPath", "createBundleUrlSearchParams", "convertPathToModuleSpecifier", "getMetroOptionsFromUrl", "debug", "require", "environment", "projectRoot", "env", "EXPO_NO_METRO_LAZY", "resolveFrom", "silent", "with<PERSON><PERSON><PERSON><PERSON>", "mode", "minify", "preserveEnvVars", "EXPO_NO_CLIENT_ENV_VARS", "lazy", "props", "bytecode", "platform", "CommandError", "engine", "optimize", "EXPO_UNSTABLE_METRO_OPTIMIZE_GRAPH", "usedExports", "EXPO_UNSTABLE_TREE_SHAKING", "isExporting", "undefined", "exp", "experiments", "baseUrl", "trim", "replace", "asyncRoutesSetting", "extra", "router", "asyncRoutes", "includes", "default", "options", "reactCompiler", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "mainModuleName", "serializerOutput", "serializerIncludeMaps", "inlineSourceMap", "splitChunks", "domRoot", "clientBoundaries", "runModule", "modulesOnly", "useMd5Filename", "dev", "isHermes", "fakeSourceUrl", "fakeSourceMapUrl", "URL", "toString", "customTransformOptions", "__proto__", "String", "dom", "key", "bundleOptions", "entryFile", "unstable_transformProfile", "customResolverOptions", "exporting", "sourceMapUrl", "sourceUrl", "serializerOptions", "output", "includeSourceMaps", "queryParams", "encodeURI", "toPosixPath", "URLSearchParams", "encodeURIComponent", "hot", "append", "length", "JSON", "stringify", "set", "pathLike", "urlFragment", "url", "getStringParam", "param", "searchParams", "get", "Array", "isArray", "Error", "pathname", "endsWith", "slice", "<PERSON><PERSON><PERSON><PERSON>", "assertEnvironment", "assertEngine", "value"], "mappings": "AAAA;;;;;;;;;;;IA2DgBA,mBAAmB,MAAnBA,mBAAmB;IAInBC,wBAAwB,MAAxBA,wBAAwB;IA6CxBC,wBAAwB,MAAxBA,wBAAwB;IAIxBC,4BAA4B,MAA5BA,4BAA4B;IAe5BC,wCAAwC,MAAxCA,wCAAwC;IAcxCC,2BAA2B,MAA3BA,2BAA2B;IA0G3BC,iCAAiC,MAAjCA,iCAAiC;IAajCC,mBAAmB,MAAnBA,mBAAmB;IAWnBC,qBAAqB,MAArBA,qBAAqB;IAMrBC,2BAA2B,MAA3BA,2BAA2B;IA6H3BC,4BAA4B,MAA5BA,4BAA4B;IAI5BC,sBAAsB,MAAtBA,sBAAsB;;;8DApZd,cAAc;;;;;;qBAElB,oBAAoB;wBACX,uBAAuB;0BACxB,yBAAyB;wBACE,iBAAiB;;;;;;AAExE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAkDpE,SAASb,mBAAmB,CAACc,WAAiB,EAAW;IAC9D,OAAOA,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,cAAc,CAAC;AAClE,CAAC;AAEM,SAASb,wBAAwB,CAACc,WAAmB,EAAW;IACrE,IAAIC,IAAG,IAAA,CAACC,kBAAkB,EAAE;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oFAAoF;IACpF,mFAAmF;IACnF,0EAA0E;IAC1E,mEAAmE;IACnE,OAAOC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACJ,WAAW,EAAE,qBAAqB,CAAC,IAAI,IAAI,CAAC;AACxE,CAAC;AAED,SAASK,YAAY,CAAC,EACpBC,IAAI,EAAG,aAAa,CAAA,EACpBC,MAAM,EAAGD,IAAI,KAAK,YAAY,CAAA,EAC9BE,eAAe,EAAGF,IAAI,KAAK,aAAa,IAAIL,IAAG,IAAA,CAACQ,uBAAuB,CAAA,EACvEC,IAAI,CAAA,EACJX,WAAW,CAAA,EACX,GAAGY,KAAK,EACS,EAAoB;IACrC,IAAIA,KAAK,CAACC,QAAQ,EAAE;QAClB,IAAID,KAAK,CAACE,QAAQ,KAAK,KAAK,EAAE;YAC5B,MAAM,IAAIC,OAAY,aAAA,CAAC,2CAA2C,CAAC,CAAC;QACtE,CAAC;QACD,IAAIH,KAAK,CAACI,MAAM,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAID,OAAY,aAAA,CAAC,mDAAmD,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAED,MAAME,QAAQ,GACZL,KAAK,CAACK,QAAQ,IACd,CAACjB,WAAW,KAAK,MAAM,IAAIO,IAAI,KAAK,YAAY,IAAIL,IAAG,IAAA,CAACgB,kCAAkC,CAAC,AAAC;IAE9F,OAAO;QACLX,IAAI;QACJC,MAAM;QACNC,eAAe;QACfQ,QAAQ;QACRE,WAAW,EAAEF,QAAQ,IAAIf,IAAG,IAAA,CAACkB,0BAA0B;QACvDT,IAAI,EAAE,CAACC,KAAK,CAACS,WAAW,IAAIV,IAAI;QAChCX,WAAW,EAAEA,WAAW,KAAK,QAAQ,GAAGsB,SAAS,GAAGtB,WAAW;QAC/D,GAAGY,KAAK;KACT,CAAC;AACJ,CAAC;AAEM,SAASxB,wBAAwB,CAACmC,GAAe,EAAE;QACjDA,GAAe;IAAtB,OAAOA,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAS,GAAxBD,KAAAA,CAAwB,GAAxBA,QAAAA,GAAe,CAAEE,OAAO,SAAA,GAAxBF,KAAAA,CAAwB,GAAxBA,KAA0BG,IAAI,EAAE,CAACC,OAAO,SAAS,EAAE,CAAC,CAAA,IAAI,EAAE,CAAC;AACpE,CAAC;AAEM,SAAStC,4BAA4B,CAACkC,GAAe,EAAEhB,IAAY,EAAEO,QAAgB,EAAE;QAGxFS,GAAS;IAFb,IAAIK,kBAAkB,AAAC;IAEvB,IAAIL,CAAAA,GAAS,GAATA,GAAG,CAACM,KAAK,SAAQ,GAAjBN,KAAAA,CAAiB,GAAjBA,QAAAA,GAAS,CAAEO,MAAM,SAAA,GAAjBP,KAAAA,CAAiB,QAAEQ,WAAW,AAAb,EAAe;YACdR,IAAS;QAA7B,MAAMQ,WAAW,GAAGR,CAAAA,IAAS,GAATA,GAAG,CAACM,KAAK,SAAQ,GAAjBN,KAAAA,CAAiB,GAAjBA,QAAAA,IAAS,CAAEO,MAAM,SAAA,GAAjBP,KAAAA,CAAiB,QAAEQ,WAAW,AAAb,AAAc;QACnD,IAAI;YAAC,SAAS;YAAE,QAAQ;SAAC,CAACC,QAAQ,CAAC,OAAOD,WAAW,CAAC,EAAE;YACtDH,kBAAkB,GAAGG,WAAW,CAAC;QACnC,OAAO,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;YAC1CH,kBAAkB,GAAGG,WAAW,CAACjB,QAAQ,CAAC,IAAIiB,WAAW,CAACE,OAAO,CAAC;QACpE,CAAC;IACH,CAAC;IAED,OAAO;QAAC1B,IAAI;QAAE,IAAI;KAAC,CAACyB,QAAQ,CAACJ,kBAAkB,CAAC,CAAC;AACnD,CAAC;AAEM,SAAStC,wCAAwC,CACtDW,WAAmB,EACnBsB,GAAe,EACfW,OAA2F,EAC1D;QAGdX,GAAe;IAFlC,OAAOhC,2BAA2B,CAAC;QACjC,GAAG2C,OAAO;QACVC,aAAa,EAAE,CAAC,CAACZ,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAe,GAA9BD,KAAAA,CAA8B,GAA9BA,GAAe,CAAEY,aAAa,CAAA;QAC/CV,OAAO,EAAErC,wBAAwB,CAACmC,GAAG,CAAC;QACtCa,UAAU,EAAEC,IAAAA,OAAsC,uCAAA,EAACpC,WAAW,EAAEsB,GAAG,CAAC;QACpEQ,WAAW,EAAE1C,4BAA4B,CAACkC,GAAG,EAAEW,OAAO,CAAC3B,IAAI,EAAE2B,OAAO,CAACpB,QAAQ,CAAC;KAC/E,CAAC,CAAC;AACL,CAAC;AAEM,SAASvB,2BAA2B,CACzC2C,OAAyB,EACQ;IACjC,MAAM,EACJI,cAAc,CAAA,EACdxB,QAAQ,CAAA,EACRP,IAAI,CAAA,EACJC,MAAM,CAAA,EACNR,WAAW,CAAA,EACXuC,gBAAgB,CAAA,EAChBC,qBAAqB,CAAA,EACrB3B,QAAQ,CAAA,EACRF,IAAI,CAAA,EACJK,MAAM,CAAA,EACNP,eAAe,CAAA,EACfsB,WAAW,CAAA,EACXN,OAAO,CAAA,EACPW,UAAU,CAAA,EACVf,WAAW,CAAA,EACXoB,eAAe,CAAA,EACfC,WAAW,CAAA,EACXvB,WAAW,CAAA,EACXgB,aAAa,CAAA,EACblB,QAAQ,CAAA,EACR0B,OAAO,CAAA,EACPC,gBAAgB,CAAA,EAChBC,SAAS,CAAA,EACTC,WAAW,CAAA,EACXC,cAAc,CAAA,IACf,GAAGzC,YAAY,CAAC4B,OAAO,CAAC,AAAC;IAE1B,MAAMc,GAAG,GAAGzC,IAAI,KAAK,YAAY,AAAC;IAClC,MAAM0C,QAAQ,GAAGjC,MAAM,KAAK,QAAQ,AAAC;IAErC,IAAIK,WAAW,EAAE;QACfvB,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAClDoC,OAAO,CAACvB,IAAI,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,IAAIuC,aAAa,AAAoB,AAAC;IACtC,IAAIC,gBAAgB,AAAoB,AAAC;IAEzC,yEAAyE;IACzE,IAAIX,qBAAqB,IAAI,IAAI,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC7DW,aAAa,GAAG,IAAIE,GAAG,CACrB3D,mBAAmB,CAACyC,OAAO,CAAC,CAACP,OAAO,QAAQ,EAAE,CAAC,EAC/C,uBAAuB,CACxB,CAAC0B,QAAQ,EAAE,CAAC;QACb,IAAIb,qBAAqB,EAAE;YACzBW,gBAAgB,GAAGD,aAAa,CAACvB,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,MAAM2B,sBAAsB,GAAqD;QAC/EC,SAAS,EAAE,IAAI;QACftC,QAAQ,EAAEA,QAAQ,IAAIK,SAAS;QAC/BN,MAAM;QACN4B,gBAAgB;QAChBnC,eAAe,EAAEA,eAAe,IAAIa,SAAS;QAC7C,gDAAgD;QAChDS,WAAW,EAAEA,WAAW,GAAGyB,MAAM,CAACzB,WAAW,CAAC,GAAGT,SAAS;QAC1DtB,WAAW;QACXyB,OAAO,EAAEA,OAAO,IAAIH,SAAS;QAC7Bc,UAAU;QACVvB,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAGS,SAAS;QACpCa,aAAa,EAAEA,aAAa,GAAGqB,MAAM,CAACrB,aAAa,CAAC,GAAGb,SAAS;QAChEmC,GAAG,EAAEd,OAAO;QACZI,cAAc,EAAEA,cAAc,IAAIzB,SAAS;KAC5C,AAAC;IAEF,sCAAsC;IACtC,IAAK,MAAMoC,GAAG,IAAIJ,sBAAsB,CAAE;QACxC,IAAIA,sBAAsB,CAACI,GAAG,CAAC,KAAKpC,SAAS,EAAE;YAC7C,OAAOgC,sBAAsB,CAACI,GAAG,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,MAAMC,aAAa,GAAoC;QACrD7C,QAAQ;QACR8C,SAAS,EAAEtB,cAAc;QACzBU,GAAG;QACHxC,MAAM,EAAEA,MAAM,IAAI,CAACwC,GAAG;QACtBP,eAAe,EAAEA,eAAe,IAAI,KAAK;QACzC9B,IAAI,EAAE,AAAC,CAACU,WAAW,IAAIV,IAAI,IAAKW,SAAS;QACzCuC,yBAAyB,EAAEZ,QAAQ,GAAG,eAAe,GAAG,SAAS;QACjEK,sBAAsB;QACtBT,SAAS;QACTC,WAAW;QACXgB,qBAAqB,EAAE;YACrBP,SAAS,EAAE,IAAI;YACfvD,WAAW;YACX+D,SAAS,EAAE1C,WAAW,IAAIC,SAAS;SACpC;QACD0C,YAAY,EAAEb,gBAAgB;QAC9Bc,SAAS,EAAEf,aAAa;QACxBgB,iBAAiB,EAAE;YACjBxB,WAAW;YACXvB,WAAW,EAAEA,WAAW,IAAIG,SAAS;YACrC6C,MAAM,EAAE5B,gBAAgB;YACxB6B,iBAAiB,EAAE5B,qBAAqB;SACzC;KACF,AAAC;IAEF,OAAOmB,aAAa,CAAC;AACvB,CAAC;AAEM,SAASnE,iCAAiC,CAC/CS,WAAmB,EACnBsB,GAAe,EACfW,OAA2E,EACnE;QAGWX,GAAe;IAFlC,OAAO9B,mBAAmB,CAAC;QACzB,GAAGyC,OAAO;QACVC,aAAa,EAAE,CAAC,CAACZ,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACC,WAAW,SAAe,GAA9BD,KAAAA,CAA8B,GAA9BA,GAAe,CAAEY,aAAa,CAAA;QAC/CV,OAAO,EAAErC,wBAAwB,CAACmC,GAAG,CAAC;QACtCa,UAAU,EAAEC,IAAAA,OAAsC,uCAAA,EAACpC,WAAW,EAAEsB,GAAG,CAAC;KACrE,CAAC,CAAC;AACL,CAAC;AAEM,SAAS9B,mBAAmB,CAACyC,OAAyB,EAAU;IACrE,MAAMmC,WAAW,GAAG1E,2BAA2B,CAACuC,OAAO,CAAC,AAAC;IACzD,OAAO,CAAC,CAAC,EAAEoC,SAAS,CAACpC,OAAO,CAACI,cAAc,CAACX,OAAO,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE0C,WAAW,CAAChB,QAAQ,EAAE,CAAC,CAAC,CAAC;AACtG,CAAC;AAQM,SAAS3D,qBAAqB,CAACwC,OAAyB,EAAU;IACvE,MAAMmC,WAAW,GAAG1E,2BAA2B,CAACuC,OAAO,CAAC,AAAC;IACzD,MAAMI,cAAc,GAAGgC,SAAS,CAACC,IAAAA,SAAW,YAAA,EAACrC,OAAO,CAACI,cAAc,CAAC,CAAC,AAAC;IACtE,OAAO,CAAC,EAAEA,cAAc,CAAC,QAAQ,EAAE+B,WAAW,CAAChB,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AAEM,SAAS1D,2BAA2B,CAACuC,OAAyB,EAAmB;IACtF,MAAM,EACJpB,QAAQ,CAAA,EACRP,IAAI,CAAA,EACJC,MAAM,CAAA,EACNR,WAAW,CAAA,EACXuC,gBAAgB,CAAA,EAChBC,qBAAqB,CAAA,EACrB7B,IAAI,CAAA,EACJE,QAAQ,CAAA,EACRG,MAAM,CAAA,EACNP,eAAe,CAAA,EACfsB,WAAW,CAAA,EACXN,OAAO,CAAA,EACPW,UAAU,CAAA,EACVD,aAAa,CAAA,EACbM,eAAe,CAAA,EACfpB,WAAW,CAAA,EACXuB,gBAAgB,CAAA,EAChBF,WAAW,CAAA,EACXvB,WAAW,CAAA,EACXF,QAAQ,CAAA,EACR0B,OAAO,CAAA,EACPG,WAAW,CAAA,EACXD,SAAS,CAAA,IACV,GAAGvC,YAAY,CAAC4B,OAAO,CAAC,AAAC;IAE1B,MAAMc,GAAG,GAAGQ,MAAM,CAACjD,IAAI,KAAK,YAAY,CAAC,AAAC;IAC1C,MAAM8D,WAAW,GAAG,IAAIG,eAAe,CAAC;QACtC1D,QAAQ,EAAE2D,kBAAkB,CAAC3D,QAAQ,CAAC;QACtCkC,GAAG;QACH,8BAA8B;QAC9B0B,GAAG,EAAElB,MAAM,CAAC,KAAK,CAAC;KACnB,CAAC,AAAC;IAEH,+DAA+D;IAC/D,IAAI,CAACnC,WAAW,IAAIV,IAAI,EAAE;QACxB0D,WAAW,CAACM,MAAM,CAAC,MAAM,EAAEnB,MAAM,CAAC7C,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI8B,eAAe,EAAE;QACnB4B,WAAW,CAACM,MAAM,CAAC,iBAAiB,EAAEnB,MAAM,CAACf,eAAe,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,IAAIjC,MAAM,EAAE;QACV6D,WAAW,CAACM,MAAM,CAAC,QAAQ,EAAEnB,MAAM,CAAChD,MAAM,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,6FAA6F;IAC7F,uGAAuG;IACvG,gEAAgE;IAChE,IAAIQ,MAAM,EAAE;QACVqD,WAAW,CAACM,MAAM,CAAC,kBAAkB,EAAE3D,MAAM,CAAC,CAAC;IACjD,CAAC;IACD,IAAIH,QAAQ,EAAE;QACZwD,WAAW,CAACM,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IACD,IAAI5C,WAAW,EAAE;QACfsC,WAAW,CAACM,MAAM,CAAC,uBAAuB,EAAEnB,MAAM,CAACzB,WAAW,CAAC,CAAC,CAAC;IACnE,CAAC;IACD,IAAItB,eAAe,EAAE;QACnB4D,WAAW,CAACM,MAAM,CAAC,2BAA2B,EAAEnB,MAAM,CAAC/C,eAAe,CAAC,CAAC,CAAC;IAC3E,CAAC;IACD,IAAIgB,OAAO,EAAE;QACX4C,WAAW,CAACM,MAAM,CAAC,mBAAmB,EAAElD,OAAO,CAAC,CAAC;IACnD,CAAC;IACD,IAAImB,gBAAgB,QAAQ,GAAxBA,KAAAA,CAAwB,GAAxBA,gBAAgB,CAAEgC,MAAM,EAAE;QAC5BP,WAAW,CAACM,MAAM,CAAC,4BAA4B,EAAEE,IAAI,CAACC,SAAS,CAAClC,gBAAgB,CAAC,CAAC,CAAC;IACrF,CAAC;IACD,IAAIR,UAAU,IAAI,IAAI,EAAE;QACtBiC,WAAW,CAACM,MAAM,CAAC,sBAAsB,EAAEvC,UAAU,CAAC,CAAC;IACzD,CAAC;IACD,IAAID,aAAa,EAAE;QACjBkC,WAAW,CAACM,MAAM,CAAC,yBAAyB,EAAEnB,MAAM,CAACrB,aAAa,CAAC,CAAC,CAAC;IACvE,CAAC;IACD,IAAIQ,OAAO,EAAE;QACX0B,WAAW,CAACM,MAAM,CAAC,eAAe,EAAEhC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI3C,WAAW,EAAE;QACfqE,WAAW,CAACM,MAAM,CAAC,sBAAsB,EAAE3E,WAAW,CAAC,CAAC;QACxDqE,WAAW,CAACM,MAAM,CAAC,uBAAuB,EAAE3E,WAAW,CAAC,CAAC;IAC3D,CAAC;IAED,IAAIqB,WAAW,EAAE;QACfgD,WAAW,CAACM,MAAM,CAAC,oBAAoB,EAAEnB,MAAM,CAACnC,WAAW,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,IAAIqB,WAAW,EAAE;QACf2B,WAAW,CAACM,MAAM,CAAC,wBAAwB,EAAEnB,MAAM,CAACd,WAAW,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,IAAIvB,WAAW,EAAE;QACfkD,WAAW,CAACM,MAAM,CAAC,wBAAwB,EAAEnB,MAAM,CAACrC,WAAW,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,IAAIF,QAAQ,EAAE;QACZoD,WAAW,CAACM,MAAM,CAAC,oBAAoB,EAAEnB,MAAM,CAACvC,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD,IAAIsB,gBAAgB,EAAE;QACpB8B,WAAW,CAACM,MAAM,CAAC,mBAAmB,EAAEpC,gBAAgB,CAAC,CAAC;IAC5D,CAAC;IACD,IAAIC,qBAAqB,EAAE;QACzB6B,WAAW,CAACM,MAAM,CAAC,gBAAgB,EAAEnB,MAAM,CAAChB,qBAAqB,CAAC,CAAC,CAAC;IACtE,CAAC;IACD,IAAIxB,MAAM,KAAK,QAAQ,EAAE;QACvBqD,WAAW,CAACM,MAAM,CAAC,2BAA2B,EAAE,eAAe,CAAC,CAAC;IACnE,CAAC;IAED,IAAI7B,WAAW,IAAI,IAAI,EAAE;QACvBuB,WAAW,CAACU,GAAG,CAAC,aAAa,EAAEvB,MAAM,CAACV,WAAW,CAAC,CAAC,CAAC;IACtD,CAAC;IACD,IAAID,SAAS,IAAI,IAAI,EAAE;QACrBwB,WAAW,CAACU,GAAG,CAAC,WAAW,EAAEvB,MAAM,CAACX,SAAS,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,OAAOwB,WAAW,CAAC;AACrB,CAAC;AAUM,SAASzE,4BAA4B,CAACoF,QAAgB,EAAE;IAC7D,OAAOT,IAAAA,SAAW,YAAA,EAACS,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAEM,SAASnF,sBAAsB,CAACoF,WAAmB,EAAE;IAC1D,MAAMC,GAAG,GAAG,IAAI9B,GAAG,CAAC6B,WAAW,EAAE,oBAAoB,CAAC,AAAC;IACvD,MAAME,cAAc,GAAG,CAACzB,GAAW,GAAK;QACtC,MAAM0B,KAAK,GAAGF,GAAG,CAACG,YAAY,CAACC,GAAG,CAAC5B,GAAG,CAAC,AAAC;QACxC,IAAI6B,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;YACxB,MAAM,IAAIK,KAAK,CAAC,CAAC,0BAA0B,EAAE/B,GAAG,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;QACD,OAAO0B,KAAK,CAAC;IACf,CAAC,AAAC;IAEF,IAAIM,QAAQ,GAAGR,GAAG,CAACQ,QAAQ,AAAC;IAC5B,IAAIA,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAChCD,QAAQ,GAAGA,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,SAAS,CAAChB,MAAM,CAAC,CAAC;IAClD,CAAC;IAED,MAAM1C,OAAO,GAAqB;QAChC3B,IAAI,EAAEsF,QAAQ,CAACV,cAAc,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,aAAa,GAAG,YAAY;QAC9E3E,MAAM,EAAEqF,QAAQ,CAACV,cAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC;QACrDxE,IAAI,EAAEkF,QAAQ,CAACV,cAAc,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC;QACjD/C,UAAU,EAAE+C,cAAc,CAAC,sBAAsB,CAAC,IAAI,KAAK;QAC3D9D,WAAW,EAAEwE,QAAQ,CAACV,cAAc,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC;QACtEnF,WAAW,EAAE8F,iBAAiB,CAACX,cAAc,CAAC,uBAAuB,CAAC,IAAI,MAAM,CAAC;QACjFrE,QAAQ,EAAEoE,GAAG,CAACG,YAAY,CAACC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK;QACnDzE,QAAQ,EAAEgF,QAAQ,CAACV,cAAc,CAAC,oBAAoB,CAAC,IAAI,OAAO,CAAC;QACnE7C,cAAc,EAAE1C,4BAA4B,CAAC8F,QAAQ,CAAC;QACtDvD,aAAa,EAAE0D,QAAQ,CAACV,cAAc,CAAC,yBAAyB,CAAC,IAAI,OAAO,CAAC;QAC7EpD,WAAW,EAAE8D,QAAQ,CAACV,cAAc,CAAC,uBAAuB,CAAC,IAAI,OAAO,CAAC;QACzE1D,OAAO,EAAE0D,cAAc,CAAC,mBAAmB,CAAC,IAAI7D,SAAS;QACzD,sFAAsF;QACtFN,MAAM,EAAE+E,YAAY,CAACZ,cAAc,CAAC,kBAAkB,CAAC,CAAC;QACxDtC,SAAS,EAAEgD,QAAQ,CAACV,cAAc,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC;QAC1DrC,WAAW,EAAE+C,QAAQ,CAACV,cAAc,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC;KAChE,AAAC;IAEF,OAAOjD,OAAO,CAAC;AACjB,CAAC;AAED,SAAS2D,QAAQ,CAACG,KAAoB,EAAW;IAC/C,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,GAAG,CAAC;AAC3C,CAAC;AAED,SAASF,iBAAiB,CAAC9F,WAA+B,EAAgC;IACxF,IAAI,CAACA,WAAW,EAAE;QAChB,OAAOsB,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,CAAC;QAAC,MAAM;QAAE,cAAc;QAAE,QAAQ;KAAC,CAACU,QAAQ,CAAChC,WAAW,CAAC,EAAE;QAC7D,MAAM,IAAIyF,KAAK,CAAC,CAAC,uEAAuE,CAAC,CAAC,CAAC;IAC7F,CAAC;IACD,OAAOzF,WAAW,CAAqB;AACzC,CAAC;AACD,SAAS+F,YAAY,CAAC/E,MAAiC,EAAwB;IAC7E,IAAI,CAACA,MAAM,EAAE;QACX,OAAOM,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,CAAC;QAAC,QAAQ;KAAC,CAACU,QAAQ,CAAChB,MAAM,CAAC,EAAE;QAChC,MAAM,IAAIyE,KAAK,CAAC,CAAC,8CAA8C,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,OAAOzE,MAAM,CAAa;AAC5B,CAAC"}