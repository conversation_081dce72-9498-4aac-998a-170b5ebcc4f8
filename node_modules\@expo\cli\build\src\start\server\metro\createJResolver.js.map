{"version": 3, "sources": ["../../../../../src/start/server/metro/createJResolver.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Fork of the jest resolver but with additional settings for Metro and pnp removed.\n * https://github.com/jestjs/jest/blob/d1a2ed7fea4bdc19836274cd810c8360e3ab62f3/packages/jest-resolve/src/defaultResolver.ts#L1\n */\nimport type { JSONObject as PackageJSON } from '@expo/json-file';\nimport assert from 'assert';\nimport { dirname, isAbsolute, resolve as pathResolve } from 'path';\nimport { sync as resolveSync, SyncOpts as UpstreamResolveOptions } from 'resolve';\nimport * as resolve from 'resolve.exports';\n\n/**\n * Allows transforming parsed `package.json` contents.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param file - Path to `package.json` file.\n * @param dir - Directory that contains the `package.json`.\n *\n * @returns Transformed `package.json` contents.\n */\ntype PackageFilter = (pkg: PackageJSON, file: string, dir: string) => PackageJSON;\n\n/**\n * Allows transforming a path within a package.\n *\n * @param pkg - Parsed `package.json` contents.\n * @param path - Path being resolved.\n * @param relativePath - Path relative from the `package.json` location.\n *\n * @returns Relative path that will be joined from the `package.json` location.\n */\ntype PathFilter = (pkg: PackageJSON, path: string, relativePath: string) => string;\n\ntype ResolverOptions = {\n  /** Directory to begin resolving from. */\n  basedir: string;\n  /** List of export conditions. */\n  conditions?: string[];\n  /** Instance of default resolver. */\n  defaultResolver: typeof defaultResolver;\n  /** List of file extensions to search in order. */\n  extensions?: string[];\n  /**\n   * List of directory names to be looked up for modules recursively.\n   *\n   * @defaultValue\n   * The default is `['node_modules']`.\n   */\n  moduleDirectory?: string[];\n  /**\n   * List of `require.paths` to use if nothing is found in `node_modules`.\n   *\n   * @defaultValue\n   * The default is `undefined`.\n   */\n  paths?: string[];\n  /** Allows transforming parsed `package.json` contents. */\n  packageFilter?: PackageFilter;\n  /** Allows transforms a path within a package. */\n  pathFilter?: PathFilter;\n  /** Current root directory. */\n  rootDir?: string;\n\n  enablePackageExports?: boolean;\n\n  blockList: RegExp[];\n\n  getPackageForModule: import('metro-resolver').CustomResolutionContext['getPackageForModule'];\n} & Pick<\n  UpstreamResolveOptions,\n  | 'readPackageSync'\n  | 'realpathSync'\n  | 'moduleDirectory'\n  | 'extensions'\n  | 'preserveSymlinks'\n  | 'includeCoreModules'\n>;\n\ntype UpstreamResolveOptionsWithConditions = UpstreamResolveOptions &\n  ResolverOptions & {\n    pathExists: (file: string) => boolean;\n  };\n\nconst defaultResolver = (\n  path: string,\n  {\n    enablePackageExports,\n    blockList = [],\n    ...options\n  }: Omit<ResolverOptions, 'defaultResolver' | 'getPackageForModule'> & {\n    isDirectory: (file: string) => boolean;\n    isFile: (file: string) => boolean;\n    pathExists: (file: string) => boolean;\n  }\n): string => {\n  // @ts-expect-error\n  const resolveOptions: UpstreamResolveOptionsWithConditions = {\n    ...options,\n    preserveSymlinks: options.preserveSymlinks,\n    defaultResolver,\n  };\n\n  // resolveSync dereferences symlinks to ensure we don't create a separate\n  // module instance depending on how it was referenced.\n  const result = resolveSync(enablePackageExports ? getPathInModule(path, resolveOptions) : path, {\n    ...resolveOptions,\n    preserveSymlinks: !options.preserveSymlinks,\n  });\n\n  return result;\n};\n\nexport default defaultResolver;\n\n/*\n * helper functions\n */\n\nfunction getPathInModule(path: string, options: UpstreamResolveOptionsWithConditions): string {\n  if (shouldIgnoreRequestForExports(path)) {\n    return path;\n  }\n\n  const segments = path.split('/');\n\n  let moduleName = segments.shift();\n\n  if (!moduleName) {\n    return path;\n  }\n\n  if (moduleName.startsWith('@')) {\n    moduleName = `${moduleName}/${segments.shift()}`;\n  }\n\n  // Disable package exports for babel/runtime for https://github.com/facebook/metro/issues/984/\n  if (moduleName === '@babel/runtime') {\n    return path;\n  }\n\n  // self-reference\n  const closestPackageJson = findClosestPackageJson(options.basedir, options);\n  if (closestPackageJson) {\n    const pkg = options.readPackageSync!(options.readFileSync!, closestPackageJson);\n    assert(pkg, 'package.json should be read by `readPackageSync`');\n\n    // Added support for the package.json \"imports\" field (#-prefixed paths)\n    if (path.startsWith('#')) {\n      const resolved = resolve.imports(pkg, path, createResolveOptions(options.conditions));\n      if (resolved) {\n        // TODO: Should we attempt to resolve every path in the array?\n        return pathResolve(dirname(closestPackageJson), resolved[0]);\n      }\n      // NOTE: resolve.imports would have thrown by this point.\n      return path;\n    }\n\n    if (pkg.name === moduleName) {\n      const resolved = resolve.exports(\n        pkg,\n        (segments.join('/') || '.') as resolve.Exports.Entry,\n        createResolveOptions(options.conditions)\n      );\n\n      if (resolved) {\n        return pathResolve(dirname(closestPackageJson), resolved[0]);\n      }\n\n      if (pkg.exports) {\n        throw new Error(\n          \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n        );\n      }\n    }\n  }\n\n  let packageJsonPath = '';\n\n  try {\n    packageJsonPath = resolveSync(`${moduleName}/package.json`, options);\n  } catch {\n    // ignore if package.json cannot be found\n  }\n\n  if (!packageJsonPath) {\n    return path;\n  }\n\n  const pkg = options.readPackageSync!(options.readFileSync!, packageJsonPath);\n  assert(pkg, 'package.json should be read by `readPackageSync`');\n\n  const resolved = resolve.exports(\n    pkg,\n    (segments.join('/') || '.') as resolve.Exports.Entry,\n    createResolveOptions(options.conditions)\n  );\n\n  if (resolved) {\n    return pathResolve(dirname(packageJsonPath), resolved[0]);\n  }\n\n  if (pkg.exports) {\n    throw new Error(\n      \"`exports` exists, but no results - this is a bug in Expo CLI's Metro resolver. Please report an issue\"\n    );\n  }\n\n  return path;\n}\n\nfunction createResolveOptions(conditions: string[] | undefined): resolve.Options {\n  return conditions\n    ? { conditions, unsafe: true }\n    : // no conditions were passed - let's assume this is Jest internal and it should be `require`\n      { browser: false, require: true };\n}\n\n// if it's a relative import or an absolute path, imports/exports are ignored\nconst shouldIgnoreRequestForExports = (path: string) => path.startsWith('.') || isAbsolute(path);\n\n// adapted from\n// https://github.com/lukeed/escalade/blob/2477005062cdbd8407afc90d3f48f4930354252b/src/sync.js\nfunction findClosestPackageJson(\n  start: string,\n  options: UpstreamResolveOptionsWithConditions\n): string | undefined {\n  let dir = pathResolve('.', start);\n  if (!options.isDirectory!(dir)) {\n    dir = dirname(dir);\n  }\n\n  while (true) {\n    const pkgJsonFile = pathResolve(dir, './package.json');\n    const hasPackageJson = options.pathExists!(pkgJsonFile);\n\n    if (hasPackageJson) {\n      return pkgJsonFile;\n    }\n\n    const prevDir = dir;\n    dir = dirname(dir);\n\n    if (prevDir === dir) {\n      return undefined;\n    }\n  }\n}\n"], "names": ["defaultResolver", "path", "enablePackageExports", "blockList", "options", "resolveOptions", "preserveSymlinks", "result", "resolveSync", "getPathInModule", "shouldIgnoreRequestForExports", "segments", "split", "moduleName", "shift", "startsWith", "closestPackageJson", "findClosestPackageJson", "basedir", "pkg", "readPackageSync", "readFileSync", "assert", "resolved", "resolve", "imports", "createResolveOptions", "conditions", "pathResolve", "dirname", "name", "exports", "join", "Error", "packageJsonPath", "unsafe", "browser", "require", "isAbsolute", "start", "dir", "isDirectory", "pkgJsonFile", "hasPackageJson", "pathExists", "prevDir", "undefined"], "mappings": "AAAA;;;;;;;;;CASC,GACD;;;;+BA2GA,SAA+B;;aAA/B,QAA+B;;;8DA1GZ,QAAQ;;;;;;;yBACiC,MAAM;;;;;;;yBACM,SAAS;;;;;;;+DACxD,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0E1C,MAAMA,eAAe,GAAG,CACtBC,IAAY,EACZ,EACEC,oBAAoB,CAAA,EACpBC,SAAS,EAAG,EAAE,CAAA,EACd,GAAGC,OAAO,EAKX,GACU;IACX,mBAAmB;IACnB,MAAMC,cAAc,GAAyC;QAC3D,GAAGD,OAAO;QACVE,gBAAgB,EAAEF,OAAO,CAACE,gBAAgB;QAC1CN,eAAe;KAChB,AAAC;IAEF,yEAAyE;IACzE,sDAAsD;IACtD,MAAMO,MAAM,GAAGC,IAAAA,QAAW,EAAA,KAAA,EAACN,oBAAoB,GAAGO,eAAe,CAACR,IAAI,EAAEI,cAAc,CAAC,GAAGJ,IAAI,EAAE;QAC9F,GAAGI,cAAc;QACjBC,gBAAgB,EAAE,CAACF,OAAO,CAACE,gBAAgB;KAC5C,CAAC,AAAC;IAEH,OAAOC,MAAM,CAAC;AAChB,CAAC,AAAC;MAEF,QAA+B,GAAhBP,eAAe;AAE9B;;CAEC,GAED,SAASS,eAAe,CAACR,IAAY,EAAEG,OAA6C,EAAU;IAC5F,IAAIM,6BAA6B,CAACT,IAAI,CAAC,EAAE;QACvC,OAAOA,IAAI,CAAC;IACd,CAAC;IAED,MAAMU,QAAQ,GAAGV,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,AAAC;IAEjC,IAAIC,UAAU,GAAGF,QAAQ,CAACG,KAAK,EAAE,AAAC;IAElC,IAAI,CAACD,UAAU,EAAE;QACf,OAAOZ,IAAI,CAAC;IACd,CAAC;IAED,IAAIY,UAAU,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QAC9BF,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC,CAAC,EAAEF,QAAQ,CAACG,KAAK,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,8FAA8F;IAC9F,IAAID,UAAU,KAAK,gBAAgB,EAAE;QACnC,OAAOZ,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB;IACjB,MAAMe,kBAAkB,GAAGC,sBAAsB,CAACb,OAAO,CAACc,OAAO,EAAEd,OAAO,CAAC,AAAC;IAC5E,IAAIY,kBAAkB,EAAE;QACtB,MAAMG,GAAG,GAAGf,OAAO,CAACgB,eAAe,CAAEhB,OAAO,CAACiB,YAAY,EAAGL,kBAAkB,CAAC,AAAC;QAChFM,IAAAA,OAAM,EAAA,QAAA,EAACH,GAAG,EAAE,kDAAkD,CAAC,CAAC;QAEhE,wEAAwE;QACxE,IAAIlB,IAAI,CAACc,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,MAAMQ,QAAQ,GAAGC,eAAO,EAAA,CAACC,OAAO,CAACN,GAAG,EAAElB,IAAI,EAAEyB,oBAAoB,CAACtB,OAAO,CAACuB,UAAU,CAAC,CAAC,AAAC;YACtF,IAAIJ,QAAQ,EAAE;gBACZ,8DAA8D;gBAC9D,OAAOK,IAAAA,KAAW,EAAA,QAAA,EAACC,IAAAA,KAAO,EAAA,QAAA,EAACb,kBAAkB,CAAC,EAAEO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,yDAAyD;YACzD,OAAOtB,IAAI,CAAC;QACd,CAAC;QAED,IAAIkB,GAAG,CAACW,IAAI,KAAKjB,UAAU,EAAE;YAC3B,MAAMU,SAAQ,GAAGC,eAAO,EAAA,CAACO,OAAO,CAC9BZ,GAAG,EACFR,QAAQ,CAACqB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAC1BN,oBAAoB,CAACtB,OAAO,CAACuB,UAAU,CAAC,CACzC,AAAC;YAEF,IAAIJ,SAAQ,EAAE;gBACZ,OAAOK,IAAAA,KAAW,EAAA,QAAA,EAACC,IAAAA,KAAO,EAAA,QAAA,EAACb,kBAAkB,CAAC,EAAEO,SAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;YAED,IAAIJ,GAAG,CAACY,OAAO,EAAE;gBACf,MAAM,IAAIE,KAAK,CACb,uGAAuG,CACxG,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAIC,eAAe,GAAG,EAAE,AAAC;IAEzB,IAAI;QACFA,eAAe,GAAG1B,IAAAA,QAAW,EAAA,KAAA,EAAC,CAAC,EAAEK,UAAU,CAAC,aAAa,CAAC,EAAET,OAAO,CAAC,CAAC;IACvE,EAAE,OAAM;IACN,yCAAyC;IAC3C,CAAC;IAED,IAAI,CAAC8B,eAAe,EAAE;QACpB,OAAOjC,IAAI,CAAC;IACd,CAAC;IAED,MAAMkB,IAAG,GAAGf,OAAO,CAACgB,eAAe,CAAEhB,OAAO,CAACiB,YAAY,EAAGa,eAAe,CAAC,AAAC;IAC7EZ,IAAAA,OAAM,EAAA,QAAA,EAACH,IAAG,EAAE,kDAAkD,CAAC,CAAC;IAEhE,MAAMI,SAAQ,GAAGC,eAAO,EAAA,CAACO,OAAO,CAC9BZ,IAAG,EACFR,QAAQ,CAACqB,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,EAC1BN,oBAAoB,CAACtB,OAAO,CAACuB,UAAU,CAAC,CACzC,AAAC;IAEF,IAAIJ,SAAQ,EAAE;QACZ,OAAOK,IAAAA,KAAW,EAAA,QAAA,EAACC,IAAAA,KAAO,EAAA,QAAA,EAACK,eAAe,CAAC,EAAEX,SAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,IAAIJ,IAAG,CAACY,OAAO,EAAE;QACf,MAAM,IAAIE,KAAK,CACb,uGAAuG,CACxG,CAAC;IACJ,CAAC;IAED,OAAOhC,IAAI,CAAC;AACd,CAAC;AAED,SAASyB,oBAAoB,CAACC,UAAgC,EAAmB;IAC/E,OAAOA,UAAU,GACb;QAAEA,UAAU;QAAEQ,MAAM,EAAE,IAAI;KAAE,GAE5B;QAAEC,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE,IAAI;KAAE,CAAC;AACxC,CAAC;AAED,6EAA6E;AAC7E,MAAM3B,6BAA6B,GAAG,CAACT,IAAY,GAAKA,IAAI,CAACc,UAAU,CAAC,GAAG,CAAC,IAAIuB,IAAAA,KAAU,EAAA,WAAA,EAACrC,IAAI,CAAC,AAAC;AAEjG,eAAe;AACf,+FAA+F;AAC/F,SAASgB,sBAAsB,CAC7BsB,KAAa,EACbnC,OAA6C,EACzB;IACpB,IAAIoC,GAAG,GAAGZ,IAAAA,KAAW,EAAA,QAAA,EAAC,GAAG,EAAEW,KAAK,CAAC,AAAC;IAClC,IAAI,CAACnC,OAAO,CAACqC,WAAW,CAAED,GAAG,CAAC,EAAE;QAC9BA,GAAG,GAAGX,IAAAA,KAAO,EAAA,QAAA,EAACW,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,MAAO,IAAI,CAAE;QACX,MAAME,WAAW,GAAGd,IAAAA,KAAW,EAAA,QAAA,EAACY,GAAG,EAAE,gBAAgB,CAAC,AAAC;QACvD,MAAMG,cAAc,GAAGvC,OAAO,CAACwC,UAAU,CAAEF,WAAW,CAAC,AAAC;QAExD,IAAIC,cAAc,EAAE;YAClB,OAAOD,WAAW,CAAC;QACrB,CAAC;QAED,MAAMG,OAAO,GAAGL,GAAG,AAAC;QACpBA,GAAG,GAAGX,IAAAA,KAAO,EAAA,QAAA,EAACW,GAAG,CAAC,CAAC;QAEnB,IAAIK,OAAO,KAAKL,GAAG,EAAE;YACnB,OAAOM,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;AACH,CAAC"}