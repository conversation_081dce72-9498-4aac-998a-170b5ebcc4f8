{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidPlatformManager.ts"], "sourcesContent": ["import { AndroidAppIdResolver } from './AndroidAppIdResolver';\nimport { AndroidDeviceManager } from './AndroidDeviceManager';\nimport { Device } from './adb';\nimport { startAdbReverseAsync } from './adbReverse';\nimport { CommandError } from '../../../utils/errors';\nimport { memoize } from '../../../utils/fn';\nimport { learnMore } from '../../../utils/link';\nimport { hasDirectDevClientDependency } from '../../detectDevClient';\nimport { AppIdResolver } from '../AppIdResolver';\nimport { BaseOpenInCustomProps, BaseResolveDeviceProps, PlatformManager } from '../PlatformManager';\n\nconst debug = require('debug')(\n  'expo:start:platforms:platformManager:android'\n) as typeof console.log;\n\nexport interface AndroidOpenInCustomProps extends BaseOpenInCustomProps {\n  /**\n   * The Android app intent to launch through `adb shell am start -n <launchActivity>`.\n   */\n  launchActivity?: string;\n  /**\n   * The custom app id to launch, provided through `--app-id`.\n   * By default, the app id is identical to the package name.\n   * When using product flavors, the app id might be customized.\n   */\n  customAppId?: string;\n}\n\nexport class AndroidPlatformManager extends PlatformManager<Device, AndroidOpenInCustomProps> {\n  /** The last used custom launch props, should be reused whenever launching custom runtime without launch props */\n  private lastCustomRuntimeLaunchProps?: AndroidOpenInCustomProps;\n  /** Memoized method to detect if dev client is installed */\n  private hasDevClientInstalled: () => boolean;\n\n  constructor(\n    protected projectRoot: string,\n    protected port: number,\n    options: {\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL */\n      getExpoGoUrl: () => string;\n      /** Get redirect URL for native disambiguation. */\n      getRedirectUrl: () => string | null;\n      /** Dev Client URL. */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n    }\n  ) {\n    super(projectRoot, {\n      platform: 'android',\n      ...options,\n      resolveDeviceAsync: AndroidDeviceManager.resolveAsync,\n    });\n\n    this.hasDevClientInstalled = memoize(hasDirectDevClientDependency.bind(this, projectRoot));\n  }\n\n  async openAsync(\n    options:\n      | { runtime: 'expo' | 'web' }\n      | { runtime: 'custom'; props?: Partial<AndroidOpenInCustomProps> },\n    resolveSettings?: Partial<BaseResolveDeviceProps<Device>>\n  ): Promise<{ url: string }> {\n    await startAdbReverseAsync([this.port]);\n\n    if (options.runtime === 'custom') {\n      // Store the resolved launch properties for future \"openAsync\" request.\n      // This reuses the same launch properties when opening through the CLI interface (pressing `a`).\n      if (options.props) {\n        this.lastCustomRuntimeLaunchProps = options.props;\n      } else if (!options.props && this.lastCustomRuntimeLaunchProps) {\n        options.props = this.lastCustomRuntimeLaunchProps;\n      }\n\n      // Handle projects that need to launch with a custom app id and launch activity\n      return this.openProjectInCustomRuntimeWithCustomAppIdAsync(options, resolveSettings);\n    }\n\n    return super.openAsync(options, resolveSettings);\n  }\n\n  /**\n   * Launch the custom runtime project, using the provided custom app id and launch activity.\n   * Instead of \"open url\", this will launch the activity directly.\n   * If dev client is installed, it will also pass the dev client URL to the activity.\n   */\n  async openProjectInCustomRuntimeWithCustomAppIdAsync(\n    options: { runtime: 'custom'; props?: Partial<AndroidOpenInCustomProps> },\n    resolveSettings?: Partial<BaseResolveDeviceProps<Device>>\n  ) {\n    // Fall back to default dev client URL open behavior if no custom app id or launch activity is provided\n    if (!options.props?.customAppId || !options.props?.launchActivity) {\n      return super.openProjectInCustomRuntimeAsync(resolveSettings, options.props);\n    }\n\n    const { customAppId, launchActivity } = options.props;\n    const url = this.hasDevClientInstalled()\n      ? (this.props.getCustomRuntimeUrl({ scheme: options.props.scheme }) ?? undefined)\n      : undefined;\n\n    debug(`Opening custom runtime using launch activity: ${launchActivity} --`, options.props);\n\n    const deviceManager = (await this.props.resolveDeviceAsync(\n      resolveSettings\n    )) as AndroidDeviceManager;\n\n    if (!(await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(customAppId))) {\n      throw new CommandError(\n        `No development build (${customAppId}) for this project is installed. ` +\n          `Please make and install a development build on the device first.\\n${learnMore(\n            'https://docs.expo.dev/development/build/'\n          )}`\n      );\n    }\n\n    deviceManager.logOpeningUrl(url ?? launchActivity);\n    await deviceManager.activateWindowAsync();\n    await deviceManager.launchActivityAsync(launchActivity, url);\n\n    return { url: url ?? launchActivity };\n  }\n\n  _getAppIdResolver(): AppIdResolver {\n    return new AndroidAppIdResolver(this.projectRoot);\n  }\n\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props?: Partial<AndroidOpenInCustomProps>\n  ): string {\n    return props?.launchActivity ?? `${applicationId}/.MainActivity`;\n  }\n}\n"], "names": ["AndroidPlatformManager", "debug", "require", "PlatformManager", "constructor", "projectRoot", "port", "options", "platform", "resolveDeviceAsync", "AndroidDeviceManager", "resolveAsync", "hasDevClientInstalled", "memoize", "hasDirectDevClientDependency", "bind", "openAsync", "resolveSettings", "startAdbReverseAsync", "runtime", "props", "lastCustomRuntimeLaunchProps", "openProjectInCustomRuntimeWithCustomAppIdAsync", "customAppId", "launchActivity", "openProjectInCustomRuntimeAsync", "url", "getCustomRuntimeUrl", "scheme", "undefined", "deviceManager", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "CommandError", "learnMore", "logOpeningUrl", "activateWindowAsync", "launchActivityAsync", "_getAppIdResolver", "AndroidAppIdResolver", "_resolveAlternativeLaunchUrl", "applicationId"], "mappings": "AAAA;;;;+BA4BaA,wBAAsB;;aAAtBA,sBAAsB;;sCA5BE,wBAAwB;sCACxB,wBAAwB;4BAExB,cAAc;wBACtB,uBAAuB;oBAC5B,mBAAmB;sBACjB,qBAAqB;iCACF,uBAAuB;iCAEW,oBAAoB;AAEnG,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,8CAA8C,CAC/C,AAAsB,AAAC;AAejB,MAAMF,sBAAsB,SAASG,gBAAe,gBAAA;IAMzDC,YACYC,WAAmB,EACnBC,IAAY,EACtBC,OASC,CACD;QACA,KAAK,CAACF,WAAW,EAAE;YACjBG,QAAQ,EAAE,SAAS;YACnB,GAAGD,OAAO;YACVE,kBAAkB,EAAEC,qBAAoB,qBAAA,CAACC,YAAY;SACtD,CAAC,CAAC;QAjBON,mBAAAA,WAAmB,CAAA;QACnBC,YAAAA,IAAY,CAAA;QAkBtB,IAAI,CAACM,qBAAqB,GAAGC,IAAAA,GAAO,QAAA,EAACC,gBAA4B,6BAAA,CAACC,IAAI,CAAC,IAAI,EAAEV,WAAW,CAAC,CAAC,CAAC;IAC7F;UAEMW,SAAS,CACbT,OAEoE,EACpEU,eAAyD,EAC/B;QAC1B,MAAMC,IAAAA,WAAoB,qBAAA,EAAC;YAAC,IAAI,CAACZ,IAAI;SAAC,CAAC,CAAC;QAExC,IAAIC,OAAO,CAACY,OAAO,KAAK,QAAQ,EAAE;YAChC,uEAAuE;YACvE,gGAAgG;YAChG,IAAIZ,OAAO,CAACa,KAAK,EAAE;gBACjB,IAAI,CAACC,4BAA4B,GAAGd,OAAO,CAACa,KAAK,CAAC;YACpD,OAAO,IAAI,CAACb,OAAO,CAACa,KAAK,IAAI,IAAI,CAACC,4BAA4B,EAAE;gBAC9Dd,OAAO,CAACa,KAAK,GAAG,IAAI,CAACC,4BAA4B,CAAC;YACpD,CAAC;YAED,+EAA+E;YAC/E,OAAO,IAAI,CAACC,8CAA8C,CAACf,OAAO,EAAEU,eAAe,CAAC,CAAC;QACvF,CAAC;QAED,OAAO,KAAK,CAACD,SAAS,CAACT,OAAO,EAAEU,eAAe,CAAC,CAAC;IACnD;IAEA;;;;GAIC,SACKK,8CAA8C,CAClDf,OAAyE,EACzEU,eAAyD,EACzD;YAEKV,GAAa,EAAkBA,IAAa;QADjD,uGAAuG;QACvG,IAAI,CAACA,CAAAA,CAAAA,GAAa,GAAbA,OAAO,CAACa,KAAK,SAAa,GAA1Bb,KAAAA,CAA0B,GAA1BA,GAAa,CAAEgB,WAAW,CAAA,IAAI,CAAChB,CAAAA,CAAAA,IAAa,GAAbA,OAAO,CAACa,KAAK,SAAgB,GAA7Bb,KAAAA,CAA6B,GAA7BA,IAAa,CAAEiB,cAAc,CAAA,EAAE;YACjE,OAAO,KAAK,CAACC,+BAA+B,CAACR,eAAe,EAAEV,OAAO,CAACa,KAAK,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,EAAEG,WAAW,CAAA,EAAEC,cAAc,CAAA,EAAE,GAAGjB,OAAO,CAACa,KAAK,AAAC;QACtD,MAAMM,GAAG,GAAG,IAAI,CAACd,qBAAqB,EAAE,GACnC,IAAI,CAACQ,KAAK,CAACO,mBAAmB,CAAC;YAAEC,MAAM,EAAErB,OAAO,CAACa,KAAK,CAACQ,MAAM;SAAE,CAAC,IAAIC,SAAS,GAC9EA,SAAS,AAAC;QAEd5B,KAAK,CAAC,CAAC,8CAA8C,EAAEuB,cAAc,CAAC,GAAG,CAAC,EAAEjB,OAAO,CAACa,KAAK,CAAC,CAAC;QAE3F,MAAMU,aAAa,GAAI,MAAM,IAAI,CAACV,KAAK,CAACX,kBAAkB,CACxDQ,eAAe,CAChB,AAAyB,AAAC;QAE3B,IAAI,CAAE,MAAMa,aAAa,CAACC,mDAAmD,CAACR,WAAW,CAAC,AAAC,EAAE;YAC3F,MAAM,IAAIS,OAAY,aAAA,CACpB,CAAC,sBAAsB,EAAET,WAAW,CAAC,iCAAiC,CAAC,GACrE,CAAC,kEAAkE,EAAEU,IAAAA,KAAS,UAAA,EAC5E,0CAA0C,CAC3C,CAAC,CAAC,CACN,CAAC;QACJ,CAAC;QAEDH,aAAa,CAACI,aAAa,CAACR,GAAG,IAAIF,cAAc,CAAC,CAAC;QACnD,MAAMM,aAAa,CAACK,mBAAmB,EAAE,CAAC;QAC1C,MAAML,aAAa,CAACM,mBAAmB,CAACZ,cAAc,EAAEE,GAAG,CAAC,CAAC;QAE7D,OAAO;YAAEA,GAAG,EAAEA,GAAG,IAAIF,cAAc;SAAE,CAAC;IACxC;IAEAa,iBAAiB,GAAkB;QACjC,OAAO,IAAIC,qBAAoB,qBAAA,CAAC,IAAI,CAACjC,WAAW,CAAC,CAAC;IACpD;IAEAkC,4BAA4B,CAC1BC,aAAqB,EACrBpB,KAAyC,EACjC;QACR,OAAOA,CAAAA,KAAK,QAAgB,GAArBA,KAAAA,CAAqB,GAArBA,KAAK,CAAEI,cAAc,CAAA,IAAI,CAAC,EAAEgB,aAAa,CAAC,cAAc,CAAC,CAAC;IACnE;CACD"}