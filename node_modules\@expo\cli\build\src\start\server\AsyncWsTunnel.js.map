{"version": 3, "sources": ["../../../../src/start/server/AsyncWsTunnel.ts"], "sourcesContent": ["import * as tunnel from '@expo/ws-tunnel';\nimport chalk from 'chalk';\nimport * as fs from 'node:fs';\nimport { hostname } from 'node:os';\nimport * as path from 'node:path';\nimport tempDir from 'temp-dir';\n\nimport * as Log from '../../log';\nimport { env, envIsWebcontainer } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\n\nconst debug = require('debug')('expo:start:server:ws-tunnel') as typeof console.log;\n\nexport class AsyncWsTunnel {\n  /** Info about the currently running instance of tunnel. */\n  private serverUrl: string | null = null;\n\n  constructor(_projectRoot: string, port: number) {\n    if (port !== 8081) {\n      throw new CommandError(\n        'WS_TUNNEL_PORT',\n        `WS-tunnel only supports tunneling over port 8081, attempted to use port ${port}`\n      );\n    }\n  }\n\n  public getActiveUrl(): string | null {\n    return this.serverUrl;\n  }\n\n  async startAsync(): Promise<void> {\n    this.serverUrl = await tunnel.startAsync({\n      ...getTunnelOptions(),\n      onStatusChange(status) {\n        if (status === 'disconnected') {\n          Log.error(\n            chalk.red(\n              'Tunnel connection has been closed. This is often related to intermittent connection problems with the ws proxy servers. Restart the dev server to try connecting again.'\n            ) + chalk.gray('\\nCheck the Expo status page for outages: https://status.expo.dev/')\n          );\n        }\n      },\n    });\n\n    debug('Tunnel URL:', this.serverUrl);\n  }\n\n  async stopAsync(): Promise<void> {\n    debug('Stopping Tunnel');\n    await tunnel.stopAsync();\n    this.serverUrl = null;\n  }\n}\n\n// Generate a base-36 string of 5 characters (from 32 bits of randomness)\nfunction randomStr() {\n  return (Math.random().toString(36) + '00000').slice(2, 7);\n}\n\nfunction getTunnelSession(): string {\n  let session = randomStr() + randomStr() + randomStr();\n  if (envIsWebcontainer()) {\n    const leaseId = Buffer.from(hostname()).toString('base64url');\n    const leaseFile = path.join(tempDir, `_ws_tunnel_lease_${leaseId}`);\n    try {\n      session = fs.readFileSync(leaseFile, 'utf8').trim() || session;\n    } catch {}\n    try {\n      fs.writeFileSync(leaseFile, session, 'utf8');\n    } catch {}\n  }\n  return session;\n}\n\nfunction getTunnelOptions() {\n  const userDefinedSubdomain = env.EXPO_TUNNEL_SUBDOMAIN;\n  if (userDefinedSubdomain && typeof userDefinedSubdomain === 'string') {\n    debug('Session:', userDefinedSubdomain);\n    return { session: userDefinedSubdomain };\n  } else {\n    const session = getTunnelSession();\n    return { session };\n  }\n}\n"], "names": ["AsyncWsTunnel", "debug", "require", "serverUrl", "constructor", "_projectRoot", "port", "CommandError", "getActiveUrl", "startAsync", "tunnel", "getTunnelOptions", "onStatusChange", "status", "Log", "error", "chalk", "red", "gray", "stopAsync", "randomStr", "Math", "random", "toString", "slice", "getTunnelSession", "session", "envIsWebcontainer", "leaseId", "<PERSON><PERSON><PERSON>", "from", "hostname", "leaseFile", "path", "join", "tempDir", "fs", "readFileSync", "trim", "writeFileSync", "userDefinedSubdomain", "env", "EXPO_TUNNEL_SUBDOMAIN"], "mappings": "AAAA;;;;+BAaaA,eAAa;;aAAbA,aAAa;;;+DAbF,iBAAiB;;;;;;;8DACvB,OAAO;;;;;;;+DAC<PERSON>,SAAS;;;;;;;yBACJ,SAAS;;;;;;;+DAC<PERSON>,WAAW;;;;;;;8DACb,UAAU;;;;;;2DAET,WAAW;qBACO,iBAAiB;wBAC3B,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AAE7E,MAAMF,aAAa;IACxB,yDAAyD,GACzD,AAAQG,SAAS,GAAkB,IAAI,CAAC;IAExCC,YAAYC,YAAoB,EAAEC,IAAY,CAAE;QAC9C,IAAIA,IAAI,KAAK,IAAI,EAAE;YACjB,MAAM,IAAIC,OAAY,aAAA,CACpB,gBAAgB,EAChB,CAAC,wEAAwE,EAAED,IAAI,CAAC,CAAC,CAClF,CAAC;QACJ,CAAC;IACH;IAEOE,YAAY,GAAkB;QACnC,OAAO,IAAI,CAACL,SAAS,CAAC;IACxB;UAEMM,UAAU,GAAkB;QAChC,IAAI,CAACN,SAAS,GAAG,MAAMO,SAAM,EAAA,CAACD,UAAU,CAAC;YACvC,GAAGE,gBAAgB,EAAE;YACrBC,cAAc,EAACC,MAAM,EAAE;gBACrB,IAAIA,MAAM,KAAK,cAAc,EAAE;oBAC7BC,IAAG,CAACC,KAAK,CACPC,MAAK,EAAA,QAAA,CAACC,GAAG,CACP,yKAAyK,CAC1K,GAAGD,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,oEAAoE,CAAC,CACrF,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEHjB,KAAK,CAAC,aAAa,EAAE,IAAI,CAACE,SAAS,CAAC,CAAC;IACvC;UAEMgB,SAAS,GAAkB;QAC/BlB,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACzB,MAAMS,SAAM,EAAA,CAACS,SAAS,EAAE,CAAC;QACzB,IAAI,CAAChB,SAAS,GAAG,IAAI,CAAC;IACxB;CACD;AAED,yEAAyE;AACzE,SAASiB,SAAS,GAAG;IACnB,OAAO,CAACC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,SAASC,gBAAgB,GAAW;IAClC,IAAIC,OAAO,GAAGN,SAAS,EAAE,GAAGA,SAAS,EAAE,GAAGA,SAAS,EAAE,AAAC;IACtD,IAAIO,IAAAA,IAAiB,kBAAA,GAAE,EAAE;QACvB,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACC,IAAAA,OAAQ,EAAA,SAAA,GAAE,CAAC,CAACR,QAAQ,CAAC,WAAW,CAAC,AAAC;QAC9D,MAAMS,SAAS,GAAGC,SAAI,EAAA,CAACC,IAAI,CAACC,QAAO,EAAA,QAAA,EAAE,CAAC,iBAAiB,EAAEP,OAAO,CAAC,CAAC,CAAC,AAAC;QACpE,IAAI;YACFF,OAAO,GAAGU,OAAE,EAAA,CAACC,YAAY,CAACL,SAAS,EAAE,MAAM,CAAC,CAACM,IAAI,EAAE,IAAIZ,OAAO,CAAC;QACjE,EAAE,OAAM,CAAC,CAAC;QACV,IAAI;YACFU,OAAE,EAAA,CAACG,aAAa,CAACP,SAAS,EAAEN,OAAO,EAAE,MAAM,CAAC,CAAC;QAC/C,EAAE,OAAM,CAAC,CAAC;IACZ,CAAC;IACD,OAAOA,OAAO,CAAC;AACjB,CAAC;AAED,SAASf,gBAAgB,GAAG;IAC1B,MAAM6B,oBAAoB,GAAGC,IAAG,IAAA,CAACC,qBAAqB,AAAC;IACvD,IAAIF,oBAAoB,IAAI,OAAOA,oBAAoB,KAAK,QAAQ,EAAE;QACpEvC,KAAK,CAAC,UAAU,EAAEuC,oBAAoB,CAAC,CAAC;QACxC,OAAO;YAAEd,OAAO,EAAEc,oBAAoB;SAAE,CAAC;IAC3C,OAAO;QACL,MAAMd,OAAO,GAAGD,gBAAgB,EAAE,AAAC;QACnC,OAAO;YAAEC,OAAO;SAAE,CAAC;IACrB,CAAC;AACH,CAAC"}