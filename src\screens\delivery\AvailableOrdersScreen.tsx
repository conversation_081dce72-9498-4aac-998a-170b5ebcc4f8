import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity,
  RefreshControl,
  SafeAreaView,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { deliveryService } from '../../services/delivery';
import { Order, OrderStatus } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Icon from 'react-native-vector-icons/MaterialIcons';

const AvailableOrdersScreen = () => {
  const navigation = useNavigation<any>();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await deliveryService.getAvailableOrders();
      setOrders(data);
    } catch (err) {
      setError('Failed to load available orders');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const handleAcceptOrder = async (orderId: string) => {
    try {
      await deliveryService.acceptOrder(orderId);
      Alert.alert('Success', 'Order accepted successfully');
      fetchOrders(); // Refresh the list
    } catch (error) {
      Alert.alert('Error', 'Failed to accept order');
    }
  };

  const handleOrderPress = (order: Order) => {
    navigation.navigate('OrderDetail', { order });
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <Card style={styles.orderCard}>
      <TouchableOpacity 
        style={styles.orderContent}
        onPress={() => handleOrderPress(item)}
      >
        <View style={styles.orderHeader}>
          <Text style={styles.orderId}>Order #{item.id.slice(-6)}</Text>
          <Text style={styles.orderTotal}>${item.total.toFixed(2)}</Text>
        </View>

        <View style={styles.customerInfo}>
          <Icon name="person" size={16} color={COLORS.secondary} />
          <Text style={styles.customerName}>{item.customer.name}</Text>
        </View>

        <View style={styles.addressInfo}>
          <Icon name="location-on" size={16} color={COLORS.secondary} />
          <Text style={styles.customerAddress}>{item.customer.address}</Text>
        </View>

        <View style={styles.orderFooter}>
          <Text style={styles.itemCount}>
            {item.items.length} item{item.items.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.orderDate}>
            {new Date(item.createdAt).toLocaleTimeString()}
          </Text>
        </View>

        <Button
          title="Accept Order"
          onPress={() => handleAcceptOrder(item.id)}
          variant="primary"
          size="small"
          style={styles.acceptButton}
        />
      </TouchableOpacity>
    </Card>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Available Orders</Text>
        </View>
        
        {error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <Button 
              title="Try Again" 
              onPress={fetchOrders}
              variant="outline"
              size="small"
              style={styles.retryButton}
            />
          </Card>
        ) : (
          <FlatList
            data={orders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={fetchOrders} />
            }
            ListEmptyComponent={
              !isLoading ? (
                <View style={styles.emptyContainer}>
                  <Icon name="local-shipping" size={48} color={COLORS.secondary + '40'} />
                  <Text style={styles.emptyText}>No available orders</Text>
                  <Text style={styles.emptySubtext}>
                    Check back later for new delivery opportunities
                  </Text>
                </View>
              ) : null
            }
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  header: {
    marginBottom: SPACING.md,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.secondary,
  },
  listContent: {
    paddingBottom: SPACING.lg,
  },
  orderCard: {
    marginBottom: SPACING.sm,
  },
  orderContent: {
    padding: 0,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  orderId: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.md,
    color: COLORS.secondary,
  },
  orderTotal: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.accent,
  },
  customerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  customerName: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.text,
    marginLeft: SPACING.xs,
  },
  addressInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.sm,
  },
  customerAddress: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.text,
    opacity: 0.7,
    marginLeft: SPACING.xs,
    flex: 1,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  itemCount: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.text,
    opacity: 0.7,
  },
  orderDate: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary,
    opacity: 0.6,
  },
  acceptButton: {
    marginTop: SPACING.sm,
  },
  errorCard: {
    backgroundColor: COLORS.error + '10',
    borderColor: COLORS.error,
    borderWidth: 1,
    marginBottom: SPACING.md,
  },
  errorText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.error,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  retryButton: {
    alignSelf: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptySubtext: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.md,
    color: COLORS.secondary,
    opacity: 0.6,
    textAlign: 'center',
  },
});

export default AvailableOrdersScreen;
