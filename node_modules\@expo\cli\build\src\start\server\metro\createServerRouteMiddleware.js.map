{"version": 3, "sources": ["../../../../../src/start/server/metro/createServerRouteMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type { ProjectConfig } from '@expo/config';\nimport resolve from 'resolve';\nimport resolveFrom from 'resolve-from';\nimport { promisify } from 'util';\n\nimport { fetchManifest } from './fetchRouterManifest';\nimport { getErrorOverlayHtmlAsync, logMetroError } from './metroErrorInterface';\nimport { warnInvalidWebOutput } from './router';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:server:metro') as typeof console.log;\n\nconst resolveAsync = promisify(resolve) as any as (\n  id: string,\n  opts: resolve.AsyncOpts\n) => Promise<string | null>;\n\nexport function createRouteHandlerMiddleware(\n  projectRoot: string,\n  options: {\n    appDir: string;\n    routerRoot: string;\n    getStaticPageAsync: (pathname: string) => Promise<{ content: string }>;\n    bundleApiRoute: (\n      functionFilePath: string\n    ) => Promise<null | Record<string, Function> | Response>;\n    config: ProjectConfig;\n  } & import('expo-router/build/routes-manifest').Options\n) {\n  if (!resolveFrom.silent(projectRoot, 'expo-router')) {\n    throw new CommandError(\n      'static and server rendering requires the expo-router package to be installed in your project.'\n    );\n  }\n\n  const { createRequestHandler } =\n    require('@expo/server/build/vendor/http') as typeof import('@expo/server/build/vendor/http');\n\n  return createRequestHandler(\n    { build: '' },\n    {\n      async getRoutesManifest() {\n        const manifest = await fetchManifest<RegExp>(projectRoot, options);\n        debug('manifest', manifest);\n        // NOTE: no app dir if null\n        // TODO: Redirect to 404 page\n        return (\n          manifest ?? {\n            // Support the onboarding screen if there's no manifest\n            htmlRoutes: [\n              {\n                file: 'index.js',\n                page: '/index',\n                routeKeys: {},\n                namedRegex: /^\\/(?:index)?\\/?$/i,\n              },\n            ],\n            apiRoutes: [],\n            notFoundRoutes: [],\n          }\n        );\n      },\n      async getHtml(request) {\n        try {\n          const { content } = await options.getStaticPageAsync(request.url);\n          return content;\n        } catch (error: any) {\n          // Forward the Metro server response as-is. It won't be pretty, but at least it will be accurate.\n\n          try {\n            return new Response(\n              await getErrorOverlayHtmlAsync({\n                error,\n                projectRoot,\n                routerRoot: options.routerRoot,\n              }),\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          } catch (staticError: any) {\n            debug('Failed to render static error overlay:', staticError);\n            // Fallback error for when Expo Router is misconfigured in the project.\n            return new Response(\n              '<span><h3>Internal Error:</h3><b>Project is not setup correctly for static rendering (check terminal for more info):</b><br/>' +\n                error.message +\n                '<br/><br/>' +\n                staticError.message +\n                '</span>',\n              {\n                status: 500,\n                headers: {\n                  'Content-Type': 'text/html',\n                },\n              }\n            );\n          }\n        }\n      },\n      logApiRouteExecutionError(error) {\n        logMetroError(projectRoot, { error });\n      },\n      async handleApiRouteError(error) {\n        const htmlServerError = await getErrorOverlayHtmlAsync({\n          error,\n          projectRoot,\n          routerRoot: options.routerRoot!,\n        });\n\n        return new Response(htmlServerError, {\n          status: 500,\n          headers: {\n            'Content-Type': 'text/html',\n          },\n        });\n      },\n      async getApiRoute(route) {\n        const { exp } = options.config;\n        if (exp.web?.output !== 'server') {\n          warnInvalidWebOutput();\n        }\n\n        const resolvedFunctionPath = await resolveAsync(route.file, {\n          extensions: ['.js', '.jsx', '.ts', '.tsx'],\n          basedir: options.appDir,\n        })!;\n\n        try {\n          debug(`Bundling middleware at: ${resolvedFunctionPath}`);\n          return await options.bundleApiRoute(resolvedFunctionPath!);\n        } catch (error: any) {\n          return new Response(\n            'Failed to load API Route: ' + resolvedFunctionPath + '\\n\\n' + error.message,\n            {\n              status: 500,\n              headers: {\n                'Content-Type': 'text/html',\n              },\n            }\n          );\n        }\n      },\n    }\n  );\n}\n"], "names": ["createRouteHandlerMiddleware", "debug", "require", "resolveAsync", "promisify", "resolve", "projectRoot", "options", "resolveFrom", "silent", "CommandError", "createRequestHandler", "build", "getRoutesManifest", "manifest", "fetchManifest", "htmlRoutes", "file", "page", "routeKeys", "namedRegex", "apiRoutes", "notFoundRoutes", "getHtml", "request", "content", "getStaticPageAsync", "url", "error", "Response", "getErrorOverlayHtmlAsync", "routerRoot", "status", "headers", "staticError", "message", "logApiRouteExecutionError", "logMetroError", "handleApiRouteError", "htmlServerError", "getApiRoute", "route", "exp", "config", "web", "output", "warnInvalidWebOutput", "resolvedFunctionPath", "extensions", "basedir", "appDir", "bundleApiRoute"], "mappings": "AAAA;;;;;CAKC,GAED;;;;+BAiBgBA,8BAA4B;;aAA5BA,4BAA4B;;;8DAhBxB,SAAS;;;;;;;8DACL,cAAc;;;;;;;yBACZ,MAAM;;;;;;qCAEF,uBAAuB;qCACG,uBAAuB;wBAC1C,UAAU;wBAClB,uBAAuB;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEhF,MAAMC,YAAY,GAAGC,IAAAA,KAAS,EAAA,UAAA,EAACC,QAAO,EAAA,QAAA,CAAC,AAGZ,AAAC;AAErB,SAASL,4BAA4B,CAC1CM,WAAmB,EACnBC,OAQuD,EACvD;IACA,IAAI,CAACC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACH,WAAW,EAAE,aAAa,CAAC,EAAE;QACnD,MAAM,IAAII,OAAY,aAAA,CACpB,+FAA+F,CAChG,CAAC;IACJ,CAAC;IAED,MAAM,EAAEC,oBAAoB,CAAA,EAAE,GAC5BT,OAAO,CAAC,gCAAgC,CAAC,AAAmD,AAAC;IAE/F,OAAOS,oBAAoB,CACzB;QAAEC,KAAK,EAAE,EAAE;KAAE,EACb;QACE,MAAMC,iBAAiB,IAAG;YACxB,MAAMC,QAAQ,GAAG,MAAMC,IAAAA,oBAAa,cAAA,EAAST,WAAW,EAAEC,OAAO,CAAC,AAAC;YACnEN,KAAK,CAAC,UAAU,EAAEa,QAAQ,CAAC,CAAC;YAC5B,2BAA2B;YAC3B,6BAA6B;YAC7B,OACEA,QAAQ,IAAI;gBACV,uDAAuD;gBACvDE,UAAU,EAAE;oBACV;wBACEC,IAAI,EAAE,UAAU;wBAChBC,IAAI,EAAE,QAAQ;wBACdC,SAAS,EAAE,EAAE;wBACbC,UAAU,sBAAsB;qBACjC;iBACF;gBACDC,SAAS,EAAE,EAAE;gBACbC,cAAc,EAAE,EAAE;aACnB,CACD;QACJ,CAAC;QACD,MAAMC,OAAO,EAACC,OAAO,EAAE;YACrB,IAAI;gBACF,MAAM,EAAEC,OAAO,CAAA,EAAE,GAAG,MAAMlB,OAAO,CAACmB,kBAAkB,CAACF,OAAO,CAACG,GAAG,CAAC,AAAC;gBAClE,OAAOF,OAAO,CAAC;YACjB,EAAE,OAAOG,KAAK,EAAO;gBACnB,iGAAiG;gBAEjG,IAAI;oBACF,OAAO,IAAIC,QAAQ,CACjB,MAAMC,IAAAA,oBAAwB,yBAAA,EAAC;wBAC7BF,KAAK;wBACLtB,WAAW;wBACXyB,UAAU,EAAExB,OAAO,CAACwB,UAAU;qBAC/B,CAAC,EACF;wBACEC,MAAM,EAAE,GAAG;wBACXC,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CACF,CAAC;gBACJ,EAAE,OAAOC,WAAW,EAAO;oBACzBjC,KAAK,CAAC,wCAAwC,EAAEiC,WAAW,CAAC,CAAC;oBAC7D,uEAAuE;oBACvE,OAAO,IAAIL,QAAQ,CACjB,+HAA+H,GAC7HD,KAAK,CAACO,OAAO,GACb,YAAY,GACZD,WAAW,CAACC,OAAO,GACnB,SAAS,EACX;wBACEH,MAAM,EAAE,GAAG;wBACXC,OAAO,EAAE;4BACP,cAAc,EAAE,WAAW;yBAC5B;qBACF,CACF,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QACDG,yBAAyB,EAACR,KAAK,EAAE;YAC/BS,IAAAA,oBAAa,cAAA,EAAC/B,WAAW,EAAE;gBAAEsB,KAAK;aAAE,CAAC,CAAC;QACxC,CAAC;QACD,MAAMU,mBAAmB,EAACV,KAAK,EAAE;YAC/B,MAAMW,eAAe,GAAG,MAAMT,IAAAA,oBAAwB,yBAAA,EAAC;gBACrDF,KAAK;gBACLtB,WAAW;gBACXyB,UAAU,EAAExB,OAAO,CAACwB,UAAU;aAC/B,CAAC,AAAC;YAEH,OAAO,IAAIF,QAAQ,CAACU,eAAe,EAAE;gBACnCP,MAAM,EAAE,GAAG;gBACXC,OAAO,EAAE;oBACP,cAAc,EAAE,WAAW;iBAC5B;aACF,CAAC,CAAC;QACL,CAAC;QACD,MAAMO,WAAW,EAACC,KAAK,EAAE;gBAEnBC,GAAO;YADX,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGnC,OAAO,CAACoC,MAAM,AAAC;YAC/B,IAAID,CAAAA,CAAAA,GAAO,GAAPA,GAAG,CAACE,GAAG,SAAQ,GAAfF,KAAAA,CAAe,GAAfA,GAAO,CAAEG,MAAM,CAAA,KAAK,QAAQ,EAAE;gBAChCC,IAAAA,OAAoB,qBAAA,GAAE,CAAC;YACzB,CAAC;YAED,MAAMC,oBAAoB,GAAG,MAAM5C,YAAY,CAACsC,KAAK,CAACxB,IAAI,EAAE;gBAC1D+B,UAAU,EAAE;oBAAC,KAAK;oBAAE,MAAM;oBAAE,KAAK;oBAAE,MAAM;iBAAC;gBAC1CC,OAAO,EAAE1C,OAAO,CAAC2C,MAAM;aACxB,CAAC,AAAC,AAAC;YAEJ,IAAI;gBACFjD,KAAK,CAAC,CAAC,wBAAwB,EAAE8C,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACzD,OAAO,MAAMxC,OAAO,CAAC4C,cAAc,CAACJ,oBAAoB,CAAE,CAAC;YAC7D,EAAE,OAAOnB,KAAK,EAAO;gBACnB,OAAO,IAAIC,QAAQ,CACjB,4BAA4B,GAAGkB,oBAAoB,GAAG,MAAM,GAAGnB,KAAK,CAACO,OAAO,EAC5E;oBACEH,MAAM,EAAE,GAAG;oBACXC,OAAO,EAAE;wBACP,cAAc,EAAE,WAAW;qBAC5B;iBACF,CACF,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CACF,CAAC;AACJ,CAAC"}