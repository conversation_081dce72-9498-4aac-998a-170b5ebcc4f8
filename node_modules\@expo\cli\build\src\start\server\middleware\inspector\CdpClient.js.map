{"version": 3, "sources": ["../../../../../../src/start/server/middleware/inspector/CdpClient.ts"], "sourcesContent": ["import { WebSocket } from 'ws';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:inspector:CdpClient'\n) as typeof console.log;\n\nexport function evaluateJsFromCdpAsync(\n  webSocketDebuggerUrl: string,\n  source: string,\n  timeoutMs: number = 2000\n): Promise<string | undefined> {\n  const REQUEST_ID = 0;\n  let timeoutHandle: NodeJS.Timeout;\n\n  return new Promise((resolve, reject) => {\n    let settled = false;\n    const ws = new WebSocket(webSocketDebuggerUrl);\n\n    timeoutHandle = setTimeout(() => {\n      debug(`[evaluateJsFromCdpAsync] Request timeout from ${webSocketDebuggerUrl}`);\n      reject(new Error('Request timeout'));\n      settled = true;\n      ws.close();\n    }, timeoutMs);\n\n    ws.on('open', () => {\n      ws.send(\n        JSON.stringify({\n          id: REQUEST_ID,\n          method: 'Runtime.evaluate',\n          params: { expression: source },\n        })\n      );\n    });\n\n    ws.on('error', (e) => {\n      debug(`[evaluateJsFromCdpAsync] Failed to connect ${webSocketDebuggerUrl}`, e);\n      reject(e);\n      settled = true;\n      clearTimeout(timeoutHandle);\n      ws.close();\n    });\n\n    ws.on('close', () => {\n      if (!settled) {\n        reject(new Error('WebSocket closed before response was received.'));\n        clearTimeout(timeoutHandle);\n      }\n    });\n\n    ws.on('message', (data) => {\n      debug(\n        `[evaluateJsFromCdpAsync] message received from ${webSocketDebuggerUrl}: ${data.toString()}`\n      );\n      try {\n        const response = JSON.parse(data.toString());\n        if (response.id === REQUEST_ID) {\n          if (response.error) {\n            reject(new Error(response.error.message));\n          } else if (response.result.result.type === 'string') {\n            resolve(response.result.result.value);\n          } else {\n            resolve(undefined);\n          }\n          settled = true;\n          clearTimeout(timeoutHandle);\n          ws.close();\n        }\n      } catch (e) {\n        reject(e);\n        settled = true;\n        clearTimeout(timeoutHandle);\n        ws.close();\n      }\n    });\n  });\n}\n"], "names": ["evaluateJsFromCdpAsync", "debug", "require", "webSocketDebuggerUrl", "source", "timeoutMs", "REQUEST_ID", "timeoutH<PERSON>le", "Promise", "resolve", "reject", "settled", "ws", "WebSocket", "setTimeout", "Error", "close", "on", "send", "JSON", "stringify", "id", "method", "params", "expression", "e", "clearTimeout", "data", "toString", "response", "parse", "error", "message", "result", "type", "value", "undefined"], "mappings": "AAAA;;;;+BAMgBA,wBAAsB;;aAAtBA,sBAAsB;;;yBANZ,IAAI;;;;;;AAE9B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,kDAAkD,CACnD,AAAsB,AAAC;AAEjB,SAASF,sBAAsB,CACpCG,oBAA4B,EAC5BC,MAAc,EACdC,SAAiB,GAAG,IAAI,EACK;IAC7B,MAAMC,UAAU,GAAG,CAAC,AAAC;IACrB,IAAIC,aAAa,AAAgB,AAAC;IAElC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,GAAK;QACtC,IAAIC,OAAO,GAAG,KAAK,AAAC;QACpB,MAAMC,EAAE,GAAG,IAAIC,CAAAA,GAAS,EAAA,CAAA,UAAA,CAACV,oBAAoB,CAAC,AAAC;QAE/CI,aAAa,GAAGO,UAAU,CAAC,IAAM;YAC/Bb,KAAK,CAAC,CAAC,8CAA8C,EAAEE,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAC/EO,MAAM,CAAC,IAAIK,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;YACrCJ,OAAO,GAAG,IAAI,CAAC;YACfC,EAAE,CAACI,KAAK,EAAE,CAAC;QACb,CAAC,EAAEX,SAAS,CAAC,CAAC;QAEdO,EAAE,CAACK,EAAE,CAAC,MAAM,EAAE,IAAM;YAClBL,EAAE,CAACM,IAAI,CACLC,IAAI,CAACC,SAAS,CAAC;gBACbC,EAAE,EAAEf,UAAU;gBACdgB,MAAM,EAAE,kBAAkB;gBAC1BC,MAAM,EAAE;oBAAEC,UAAU,EAAEpB,MAAM;iBAAE;aAC/B,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEHQ,EAAE,CAACK,EAAE,CAAC,OAAO,EAAE,CAACQ,CAAC,GAAK;YACpBxB,KAAK,CAAC,CAAC,2CAA2C,EAAEE,oBAAoB,CAAC,CAAC,EAAEsB,CAAC,CAAC,CAAC;YAC/Ef,MAAM,CAACe,CAAC,CAAC,CAAC;YACVd,OAAO,GAAG,IAAI,CAAC;YACfe,YAAY,CAACnB,aAAa,CAAC,CAAC;YAC5BK,EAAE,CAACI,KAAK,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;QAEHJ,EAAE,CAACK,EAAE,CAAC,OAAO,EAAE,IAAM;YACnB,IAAI,CAACN,OAAO,EAAE;gBACZD,MAAM,CAAC,IAAIK,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAC;gBACpEW,YAAY,CAACnB,aAAa,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEHK,EAAE,CAACK,EAAE,CAAC,SAAS,EAAE,CAACU,IAAI,GAAK;YACzB1B,KAAK,CACH,CAAC,+CAA+C,EAAEE,oBAAoB,CAAC,EAAE,EAAEwB,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC,CAC7F,CAAC;YACF,IAAI;gBACF,MAAMC,QAAQ,GAAGV,IAAI,CAACW,KAAK,CAACH,IAAI,CAACC,QAAQ,EAAE,CAAC,AAAC;gBAC7C,IAAIC,QAAQ,CAACR,EAAE,KAAKf,UAAU,EAAE;oBAC9B,IAAIuB,QAAQ,CAACE,KAAK,EAAE;wBAClBrB,MAAM,CAAC,IAAIK,KAAK,CAACc,QAAQ,CAACE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;oBAC5C,OAAO,IAAIH,QAAQ,CAACI,MAAM,CAACA,MAAM,CAACC,IAAI,KAAK,QAAQ,EAAE;wBACnDzB,OAAO,CAACoB,QAAQ,CAACI,MAAM,CAACA,MAAM,CAACE,KAAK,CAAC,CAAC;oBACxC,OAAO;wBACL1B,OAAO,CAAC2B,SAAS,CAAC,CAAC;oBACrB,CAAC;oBACDzB,OAAO,GAAG,IAAI,CAAC;oBACfe,YAAY,CAACnB,aAAa,CAAC,CAAC;oBAC5BK,EAAE,CAACI,KAAK,EAAE,CAAC;gBACb,CAAC;YACH,EAAE,OAAOS,CAAC,EAAE;gBACVf,MAAM,CAACe,CAAC,CAAC,CAAC;gBACVd,OAAO,GAAG,IAAI,CAAC;gBACfe,YAAY,CAACnB,aAAa,CAAC,CAAC;gBAC5BK,EAAE,CAACI,KAAK,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC"}