{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidDeviceManager.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { activateWindowAsync } from './activateWindow';\nimport * as AndroidDebugBridge from './adb';\nimport { startDeviceAsync } from './emulator';\nimport { getDevicesAsync } from './getDevices';\nimport { promptForDeviceAsync } from './promptAndroidDevice';\nimport * as Log from '../../../log';\nimport { AbortCommandError, CommandError } from '../../../utils/errors';\nimport { validateUrl } from '../../../utils/url';\nimport { DeviceManager } from '../DeviceManager';\nimport { ExpoGoInstaller } from '../ExpoGoInstaller';\nimport { BaseResolveDeviceProps } from '../PlatformManager';\n\nconst EXPO_GO_APPLICATION_IDENTIFIER = 'host.exp.exponent';\n\nexport class AndroidDeviceManager extends DeviceManager<AndroidDebugBridge.Device> {\n  static async resolveFromNameAsync(name: string): Promise<AndroidDeviceManager> {\n    const devices = await getDevicesAsync();\n    const device = devices.find((device) => device.name === name);\n\n    if (!device) {\n      throw new CommandError('Could not find device with name: ' + name);\n    }\n    return AndroidDeviceManager.resolveAsync({ device, shouldPrompt: false });\n  }\n\n  static async resolveAsync({\n    device,\n    shouldPrompt,\n  }: BaseResolveDeviceProps<AndroidDebugBridge.Device> = {}): Promise<AndroidDeviceManager> {\n    if (device) {\n      const manager = new AndroidDeviceManager(device);\n      if (!(await manager.attemptToStartAsync())) {\n        throw new AbortCommandError();\n      }\n      return manager;\n    }\n\n    const devices = await getDevicesAsync();\n    const _device = shouldPrompt ? await promptForDeviceAsync(devices) : devices[0];\n    return AndroidDeviceManager.resolveAsync({ device: _device, shouldPrompt: false });\n  }\n\n  get name() {\n    // TODO: Maybe strip `_` from the device name?\n    return this.device.name;\n  }\n\n  get identifier(): string {\n    return this.device.pid ?? 'unknown';\n  }\n\n  async getAppVersionAsync(applicationId: string): Promise<string | null> {\n    const info = await AndroidDebugBridge.getPackageInfoAsync(this.device, {\n      appId: applicationId,\n    });\n\n    const regex = /versionName=([0-9.]+)/;\n    return regex.exec(info)?.[1] ?? null;\n  }\n\n  protected async attemptToStartAsync(): Promise<AndroidDebugBridge.Device | null> {\n    // TODO: Add a light-weight method for checking since a device could disconnect.\n    if (!(await AndroidDebugBridge.isDeviceBootedAsync(this.device))) {\n      this.device = await startDeviceAsync(this.device);\n    }\n\n    if (this.device.isAuthorized === false) {\n      AndroidDebugBridge.logUnauthorized(this.device);\n      return null;\n    }\n\n    return this.device;\n  }\n\n  async startAsync(): Promise<AndroidDebugBridge.Device> {\n    const device = await this.attemptToStartAsync();\n    assert(device, `Failed to boot emulator.`);\n    return this.device;\n  }\n\n  async installAppAsync(binaryPath: string) {\n    await AndroidDebugBridge.installAsync(this.device, {\n      filePath: binaryPath,\n    });\n  }\n\n  async uninstallAppAsync(appId: string) {\n    // we need to check if the app is installed, else we might bump into \"Failure [DELETE_FAILED_INTERNAL_ERROR]\"\n    const isInstalled = await this.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(appId);\n    if (!isInstalled) {\n      return;\n    }\n\n    try {\n      await AndroidDebugBridge.uninstallAsync(this.device, {\n        appId,\n      });\n    } catch (e) {\n      Log.error(\n        `Could not uninstall app \"${appId}\" from your device, please uninstall it manually and try again.`\n      );\n      throw e;\n    }\n  }\n\n  /**\n   * @param launchActivity Activity to launch `[application identifier]/.[main activity name]`, ex: `com.bacon.app/.MainActivity`\n   */\n  async launchActivityAsync(launchActivity: string, url?: string): Promise<string> {\n    try {\n      return await AndroidDebugBridge.launchActivityAsync(this.device, {\n        launchActivity,\n        url,\n      });\n    } catch (error: any) {\n      let errorMessage = `Couldn't open Android app with activity \"${launchActivity}\" on device \"${this.name}\".`;\n      if (error instanceof CommandError && error.code === 'APP_NOT_INSTALLED') {\n        errorMessage += `\\nThe app might not be installed, try installing it with: ${chalk.bold(\n          `npx expo run:android -d ${this.name}`\n        )}`;\n      }\n      errorMessage += chalk.gray(`\\n${error.message}`);\n      error.message = errorMessage;\n      throw error;\n    }\n  }\n\n  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId: string) {\n    return await AndroidDebugBridge.isPackageInstalledAsync(this.device, applicationId);\n  }\n\n  async openUrlAsync(url: string) {\n    // Non-compliant URLs will be treated as application identifiers.\n    if (!validateUrl(url, { requireProtocol: true })) {\n      await this.launchActivityAsync(url);\n      return;\n    }\n\n    const parsed = new URL(url);\n\n    if (parsed.protocol === 'exp:') {\n      // NOTE(brentvatne): temporary workaround! launch Expo Go first, then\n      // launch the project!\n      // https://github.com/expo/expo/issues/7772\n      // adb shell monkey -p host.exp.exponent -c android.intent.category.LAUNCHER 1\n      // Note: this is not needed in Expo Development Client, it only applies to Expo Go\n      await AndroidDebugBridge.openAppIdAsync(\n        { pid: this.device.pid },\n        { applicationId: EXPO_GO_APPLICATION_IDENTIFIER }\n      );\n    }\n\n    await AndroidDebugBridge.openUrlAsync({ pid: this.device.pid }, { url });\n  }\n\n  async activateWindowAsync() {\n    // Bring the emulator window to the front on macos devices.\n    await activateWindowAsync(this.device);\n  }\n\n  getExpoGoAppId(): string {\n    return EXPO_GO_APPLICATION_IDENTIFIER;\n  }\n\n  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {\n    const installer = new ExpoGoInstaller('android', EXPO_GO_APPLICATION_IDENTIFIER, sdkVersion);\n    return installer.ensureAsync(this);\n  }\n}\n"], "names": ["AndroidDeviceManager", "EXPO_GO_APPLICATION_IDENTIFIER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resolveFromNameAsync", "name", "devices", "getDevicesAsync", "device", "find", "CommandError", "resolveAsync", "should<PERSON>rompt", "manager", "attemptToStartAsync", "AbortCommandError", "_device", "promptForDeviceAsync", "identifier", "pid", "getAppVersionAsync", "applicationId", "regex", "info", "AndroidDebugBridge", "getPackageInfoAsync", "appId", "exec", "isDeviceBootedAsync", "startDeviceAsync", "isAuthorized", "logUnauthorized", "startAsync", "assert", "installAppAsync", "binaryPath", "installAsync", "filePath", "uninstallAppAsync", "isInstalled", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "uninstallAsync", "e", "Log", "error", "launchActivityAsync", "launchActivity", "url", "errorMessage", "code", "chalk", "bold", "gray", "message", "isPackageInstalledAsync", "openUrlAsync", "validateUrl", "requireProtocol", "parsed", "URL", "protocol", "openAppIdAsync", "activateWindowAsync", "getExpoGoAppId", "ensureExpoGoAsync", "sdkVersion", "installer", "ExpoGoInstaller", "ensureAsync"], "mappings": "AAAA;;;;+BAiBaA,sBAAoB;;aAApBA,oBAAoB;;;8DAjBd,QAAQ;;;;;;;8DACT,OAAO;;;;;;gCAEW,kBAAkB;2DAClB,OAAO;0BACV,YAAY;4BACb,cAAc;qCACT,uBAAuB;2DACvC,cAAc;wBACa,uBAAuB;qBAC3C,oBAAoB;+BAClB,kBAAkB;iCAChB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGpD,MAAMC,8BAA8B,GAAG,mBAAmB,AAAC;AAEpD,MAAMD,oBAAoB,SAASE,cAAa,cAAA;iBACxCC,oBAAoB,CAACC,IAAY,EAAiC;QAC7E,MAAMC,OAAO,GAAG,MAAMC,IAAAA,WAAe,gBAAA,GAAE,AAAC;QACxC,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,CAAC,CAACD,MAAM,GAAKA,MAAM,CAACH,IAAI,KAAKA,IAAI,CAAC,AAAC;QAE9D,IAAI,CAACG,MAAM,EAAE;YACX,MAAM,IAAIE,OAAY,aAAA,CAAC,mCAAmC,GAAGL,IAAI,CAAC,CAAC;QACrE,CAAC;QACD,OAAOJ,oBAAoB,CAACU,YAAY,CAAC;YAAEH,MAAM;YAAEI,YAAY,EAAE,KAAK;SAAE,CAAC,CAAC;IAC5E;iBAEaD,YAAY,CAAC,EACxBH,MAAM,CAAA,EACNI,YAAY,CAAA,EACsC,GAAG,EAAE,EAAiC;QACxF,IAAIJ,MAAM,EAAE;YACV,MAAMK,OAAO,GAAG,IAAIZ,oBAAoB,CAACO,MAAM,CAAC,AAAC;YACjD,IAAI,CAAE,MAAMK,OAAO,CAACC,mBAAmB,EAAE,AAAC,EAAE;gBAC1C,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;YAChC,CAAC;YACD,OAAOF,OAAO,CAAC;QACjB,CAAC;QAED,MAAMP,OAAO,GAAG,MAAMC,IAAAA,WAAe,gBAAA,GAAE,AAAC;QACxC,MAAMS,OAAO,GAAGJ,YAAY,GAAG,MAAMK,IAAAA,oBAAoB,qBAAA,EAACX,OAAO,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,AAAC;QAChF,OAAOL,oBAAoB,CAACU,YAAY,CAAC;YAAEH,MAAM,EAAEQ,OAAO;YAAEJ,YAAY,EAAE,KAAK;SAAE,CAAC,CAAC;IACrF;QAEIP,IAAI,GAAG;QACT,8CAA8C;QAC9C,OAAO,IAAI,CAACG,MAAM,CAACH,IAAI,CAAC;IAC1B;QAEIa,UAAU,GAAW;QACvB,OAAO,IAAI,CAACV,MAAM,CAACW,GAAG,IAAI,SAAS,CAAC;IACtC;UAEMC,kBAAkB,CAACC,aAAqB,EAA0B;YAM/DC,GAAgB;QALvB,MAAMC,IAAI,GAAG,MAAMC,IAAkB,CAACC,mBAAmB,CAAC,IAAI,CAACjB,MAAM,EAAE;YACrEkB,KAAK,EAAEL,aAAa;SACrB,CAAC,AAAC;QAEH,MAAMC,KAAK,0BAA0B,AAAC;QACtC,OAAOA,CAAAA,CAAAA,GAAgB,GAAhBA,KAAK,CAACK,IAAI,CAACJ,IAAI,CAAC,SAAK,GAArBD,KAAAA,CAAqB,GAArBA,GAAgB,AAAE,CAAC,CAAC,CAAC,CAAA,IAAI,IAAI,CAAC;IACvC;UAEgBR,mBAAmB,GAA8C;QAC/E,gFAAgF;QAChF,IAAI,CAAE,MAAMU,IAAkB,CAACI,mBAAmB,CAAC,IAAI,CAACpB,MAAM,CAAC,AAAC,EAAE;YAChE,IAAI,CAACA,MAAM,GAAG,MAAMqB,IAAAA,SAAgB,iBAAA,EAAC,IAAI,CAACrB,MAAM,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAACA,MAAM,CAACsB,YAAY,KAAK,KAAK,EAAE;YACtCN,IAAkB,CAACO,eAAe,CAAC,IAAI,CAACvB,MAAM,CAAC,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAACA,MAAM,CAAC;IACrB;UAEMwB,UAAU,GAAuC;QACrD,MAAMxB,MAAM,GAAG,MAAM,IAAI,CAACM,mBAAmB,EAAE,AAAC;QAChDmB,IAAAA,OAAM,EAAA,QAAA,EAACzB,MAAM,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAC3C,OAAO,IAAI,CAACA,MAAM,CAAC;IACrB;UAEM0B,eAAe,CAACC,UAAkB,EAAE;QACxC,MAAMX,IAAkB,CAACY,YAAY,CAAC,IAAI,CAAC5B,MAAM,EAAE;YACjD6B,QAAQ,EAAEF,UAAU;SACrB,CAAC,CAAC;IACL;UAEMG,iBAAiB,CAACZ,KAAa,EAAE;QACrC,6GAA6G;QAC7G,MAAMa,WAAW,GAAG,MAAM,IAAI,CAACC,mDAAmD,CAACd,KAAK,CAAC,AAAC;QAC1F,IAAI,CAACa,WAAW,EAAE;YAChB,OAAO;QACT,CAAC;QAED,IAAI;YACF,MAAMf,IAAkB,CAACiB,cAAc,CAAC,IAAI,CAACjC,MAAM,EAAE;gBACnDkB,KAAK;aACN,CAAC,CAAC;QACL,EAAE,OAAOgB,CAAC,EAAE;YACVC,IAAG,CAACC,KAAK,CACP,CAAC,yBAAyB,EAAElB,KAAK,CAAC,+DAA+D,CAAC,CACnG,CAAC;YACF,MAAMgB,CAAC,CAAC;QACV,CAAC;IACH;IAEA;;GAEC,SACKG,mBAAmB,CAACC,cAAsB,EAAEC,GAAY,EAAmB;QAC/E,IAAI;YACF,OAAO,MAAMvB,IAAkB,CAACqB,mBAAmB,CAAC,IAAI,CAACrC,MAAM,EAAE;gBAC/DsC,cAAc;gBACdC,GAAG;aACJ,CAAC,CAAC;QACL,EAAE,OAAOH,KAAK,EAAO;YACnB,IAAII,YAAY,GAAG,CAAC,yCAAyC,EAAEF,cAAc,CAAC,aAAa,EAAE,IAAI,CAACzC,IAAI,CAAC,EAAE,CAAC,AAAC;YAC3G,IAAIuC,KAAK,YAAYlC,OAAY,aAAA,IAAIkC,KAAK,CAACK,IAAI,KAAK,mBAAmB,EAAE;gBACvED,YAAY,IAAI,CAAC,0DAA0D,EAAEE,MAAK,EAAA,QAAA,CAACC,IAAI,CACrF,CAAC,wBAAwB,EAAE,IAAI,CAAC9C,IAAI,CAAC,CAAC,CACvC,CAAC,CAAC,CAAC;YACN,CAAC;YACD2C,YAAY,IAAIE,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,CAAC,EAAE,EAAER,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC;YACjDT,KAAK,CAACS,OAAO,GAAGL,YAAY,CAAC;YAC7B,MAAMJ,KAAK,CAAC;QACd,CAAC;IACH;UAEMJ,mDAAmD,CAACnB,aAAqB,EAAE;QAC/E,OAAO,MAAMG,IAAkB,CAAC8B,uBAAuB,CAAC,IAAI,CAAC9C,MAAM,EAAEa,aAAa,CAAC,CAAC;IACtF;UAEMkC,YAAY,CAACR,GAAW,EAAE;QAC9B,iEAAiE;QACjE,IAAI,CAACS,IAAAA,IAAW,YAAA,EAACT,GAAG,EAAE;YAAEU,eAAe,EAAE,IAAI;SAAE,CAAC,EAAE;YAChD,MAAM,IAAI,CAACZ,mBAAmB,CAACE,GAAG,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,MAAMW,MAAM,GAAG,IAAIC,GAAG,CAACZ,GAAG,CAAC,AAAC;QAE5B,IAAIW,MAAM,CAACE,QAAQ,KAAK,MAAM,EAAE;YAC9B,qEAAqE;YACrE,sBAAsB;YACtB,2CAA2C;YAC3C,8EAA8E;YAC9E,kFAAkF;YAClF,MAAMpC,IAAkB,CAACqC,cAAc,CACrC;gBAAE1C,GAAG,EAAE,IAAI,CAACX,MAAM,CAACW,GAAG;aAAE,EACxB;gBAAEE,aAAa,EAAEnB,8BAA8B;aAAE,CAClD,CAAC;QACJ,CAAC;QAED,MAAMsB,IAAkB,CAAC+B,YAAY,CAAC;YAAEpC,GAAG,EAAE,IAAI,CAACX,MAAM,CAACW,GAAG;SAAE,EAAE;YAAE4B,GAAG;SAAE,CAAC,CAAC;IAC3E;UAEMe,mBAAmB,GAAG;QAC1B,2DAA2D;QAC3D,MAAMA,IAAAA,eAAmB,oBAAA,EAAC,IAAI,CAACtD,MAAM,CAAC,CAAC;IACzC;IAEAuD,cAAc,GAAW;QACvB,OAAO7D,8BAA8B,CAAC;IACxC;UAEM8D,iBAAiB,CAACC,UAAkB,EAAoB;QAC5D,MAAMC,SAAS,GAAG,IAAIC,gBAAe,gBAAA,CAAC,SAAS,EAAEjE,8BAA8B,EAAE+D,UAAU,CAAC,AAAC;QAC7F,OAAOC,SAAS,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC;IACrC;CACD"}