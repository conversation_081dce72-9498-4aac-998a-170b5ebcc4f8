{"version": 3, "sources": ["../../../../src/utils/telemetry/events.ts"], "sourcesContent": ["import type { TelemetryRecord } from './types';\n\n/** A single command invocation, with the invoked command name */\nexport function commandEvent(commandName: string): TelemetryRecord {\n  return {\n    event: 'action',\n    properties: {\n      action: `expo ${commandName}`,\n    },\n  };\n}\n"], "names": ["commandEvent", "commandName", "event", "properties", "action"], "mappings": "AAAA;;;;+BAGgBA,cAAY;;aAAZA,YAAY;;AAArB,SAASA,YAAY,CAACC,WAAmB,EAAmB;IACjE,OAAO;QACLC,KAAK,EAAE,QAAQ;QACfC,UAAU,EAAE;YACVC,MAAM,EAAE,CAAC,KAAK,EAAEH,WAAW,CAAC,CAAC;SAC9B;KACF,CAAC;AACJ,CAAC"}