{"version": 3, "sources": ["../../../src/utils/createFileTransform.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport path from 'path';\nimport picomatch from 'picomatch';\nimport { ReadEntry } from 'tar';\n\nconst debug = require('debug')('expo:file-transform') as typeof console.log;\n\nexport function createEntryResolver(name: string) {\n  return (entry: ReadEntry) => {\n    if (name) {\n      // Rewrite paths for bare workflow\n      entry.path = entry.path\n        .replace(\n          /HelloWorld/g,\n          entry.path.includes('android')\n            ? IOSConfig.XcodeUtils.sanitizedName(name.toLowerCase())\n            : IOSConfig.XcodeUtils.sanitizedName(name)\n        )\n        .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(name).toLowerCase());\n    }\n    if (entry.type && /^file$/i.test(entry.type) && path.basename(entry.path) === 'gitignore') {\n      // Rename `gitignore` because npm ignores files named `.gitignore` when publishing.\n      // See: https://github.com/npm/npm/issues/1862\n      entry.path = entry.path.replace(/gitignore$/, '.gitignore');\n    }\n  };\n}\n\nexport function createGlobFilter(\n  globPattern: picomatch.Glob,\n  options?: picomatch.PicomatchOptions\n) {\n  const matcher = picomatch(globPattern, options);\n\n  debug(\n    'filter: created for pattern(s) \"%s\" (%s)',\n    Array.isArray(globPattern) ? globPattern.join('\", \"') : globPattern,\n    options\n  );\n\n  return (path: string) => {\n    const included = matcher(path);\n    debug('filter: %s - %s', included ? 'include' : 'exclude', path);\n    return included;\n  };\n}\n"], "names": ["createEntryResolver", "createGlobFilter", "debug", "require", "name", "entry", "path", "replace", "includes", "IOSConfig", "XcodeUtils", "sanitizedName", "toLowerCase", "type", "test", "basename", "globPattern", "options", "matcher", "picomatch", "Array", "isArray", "join", "included"], "mappings": "AAAA;;;;;;;;;;;IAOgBA,mBAAmB,MAAnBA,mBAAmB;IAqBnBC,gBAAgB,MAAhBA,gBAAgB;;;yBA5BN,sBAAsB;;;;;;;8DAC/B,MAAM;;;;;;;8DACD,WAAW;;;;;;;;;;;AAGjC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qBAAqB,CAAC,AAAsB,AAAC;AAErE,SAASH,mBAAmB,CAACI,IAAY,EAAE;IAChD,OAAO,CAACC,KAAgB,GAAK;QAC3B,IAAID,IAAI,EAAE;YACR,kCAAkC;YAClCC,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CACpBC,OAAO,gBAENF,KAAK,CAACC,IAAI,CAACE,QAAQ,CAAC,SAAS,CAAC,GAC1BC,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,aAAa,CAACP,IAAI,CAACQ,WAAW,EAAE,CAAC,GACtDH,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,aAAa,CAACP,IAAI,CAAC,CAC7C,CACAG,OAAO,gBAAgBE,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,aAAa,CAACP,IAAI,CAAC,CAACQ,WAAW,EAAE,CAAC,CAAC;QACpF,CAAC;QACD,IAAIP,KAAK,CAACQ,IAAI,IAAI,UAAUC,IAAI,CAACT,KAAK,CAACQ,IAAI,CAAC,IAAIP,KAAI,EAAA,QAAA,CAACS,QAAQ,CAACV,KAAK,CAACC,IAAI,CAAC,KAAK,WAAW,EAAE;YACzF,mFAAmF;YACnF,8CAA8C;YAC9CD,KAAK,CAACC,IAAI,GAAGD,KAAK,CAACC,IAAI,CAACC,OAAO,eAAe,YAAY,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAEM,SAASN,gBAAgB,CAC9Be,WAA2B,EAC3BC,OAAoC,EACpC;IACA,MAAMC,OAAO,GAAGC,IAAAA,UAAS,EAAA,QAAA,EAACH,WAAW,EAAEC,OAAO,CAAC,AAAC;IAEhDf,KAAK,CACH,0CAA0C,EAC1CkB,KAAK,CAACC,OAAO,CAACL,WAAW,CAAC,GAAGA,WAAW,CAACM,IAAI,CAAC,MAAM,CAAC,GAAGN,WAAW,EACnEC,OAAO,CACR,CAAC;IAEF,OAAO,CAACX,IAAY,GAAK;QACvB,MAAMiB,QAAQ,GAAGL,OAAO,CAACZ,IAAI,CAAC,AAAC;QAC/BJ,KAAK,CAAC,iBAAiB,EAAEqB,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAEjB,IAAI,CAAC,CAAC;QACjE,OAAOiB,QAAQ,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC"}