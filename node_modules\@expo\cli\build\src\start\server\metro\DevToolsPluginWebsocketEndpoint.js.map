{"version": 3, "sources": ["../../../../../src/start/server/metro/DevToolsPluginWebsocketEndpoint.ts"], "sourcesContent": ["import { WebSocket, WebSocketServer } from 'ws';\n\nexport function createDevToolsPluginWebsocketEndpoint(): Record<string, WebSocketServer> {\n  const wss = new WebSocketServer({ noServer: true });\n\n  wss.on('connection', (ws: WebSocket) => {\n    ws.on('message', (message, isBinary) => {\n      // Broadcast the received message to all other connected clients\n      wss.clients.forEach((client) => {\n        if (client !== ws && client.readyState === WebSocket.OPEN) {\n          client.send(message, { binary: isBinary });\n        }\n      });\n    });\n  });\n\n  return { '/expo-dev-plugins/broadcast': wss };\n}\n"], "names": ["createDevToolsPluginWebsocketEndpoint", "wss", "WebSocketServer", "noServer", "on", "ws", "message", "isBinary", "clients", "for<PERSON>ach", "client", "readyState", "WebSocket", "OPEN", "send", "binary"], "mappings": "AAAA;;;;+BAEg<PERSON>,uCAAqC;;aAArCA,qCAAqC;;;yBAFV,IAAI;;;;;;AAExC,SAASA,qCAAqC,GAAoC;IACvF,MAAMC,GAAG,GAAG,IAAIC,CAAAA,GAAe,EAAA,CAAA,gBAAA,CAAC;QAAEC,QAAQ,EAAE,IAAI;KAAE,CAAC,AAAC;IAEpDF,GAAG,CAACG,EAAE,CAAC,YAAY,EAAE,CAACC,EAAa,GAAK;QACtCA,EAAE,CAACD,EAAE,CAAC,SAAS,EAAE,CAACE,OAAO,EAAEC,QAAQ,GAAK;YACtC,gEAAgE;YAChEN,GAAG,CAACO,OAAO,CAACC,OAAO,CAAC,CAACC,MAAM,GAAK;gBAC9B,IAAIA,MAAM,KAAKL,EAAE,IAAIK,MAAM,CAACC,UAAU,KAAKC,GAAS,EAAA,UAAA,CAACC,IAAI,EAAE;oBACzDH,MAAM,CAACI,IAAI,CAACR,OAAO,EAAE;wBAAES,MAAM,EAAER,QAAQ;qBAAE,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO;QAAE,6BAA6B,EAAEN,GAAG;KAAE,CAAC;AAChD,CAAC"}