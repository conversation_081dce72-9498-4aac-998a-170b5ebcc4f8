import { ordersApi } from './api';
import { Product, Order } from '../types';

interface DashboardStats {
  totalSales: number;
  pendingOrders: number;
  completedOrders: number;
  revenue: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export const storeService = {
  async getProducts(): Promise<Product[]> {
    const response = await ordersApi.get<Product[]>('/products');
    return response.data;
  },
  
  async createProduct(product: Omit<Product, 'id'>): Promise<Product> {
    const response = await ordersApi.post<Product>('/products', product);
    return response.data;
  },
  
  async updateProduct(id: string, product: Partial<Product>): Promise<Product> {
    const response = await ordersApi.put<Product>(`/products/${id}`, product);
    return response.data;
  },
  
  async getOrders(): Promise<Order[]> {
    const response = await ordersApi.get<Order[]>('/orders/store');
    return response.data;
  },
  
  async updateOrderStatus(id: string, status: string): Promise<Order> {
    const response = await ordersApi.put<Order>(`/orders/${id}/status`, { status });
    return response.data;
  },
  
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await ordersApi.get<DashboardStats>('/dashboard');
    return response.data;
  }
};