{"version": 3, "sources": ["../../../../../src/start/server/metro/externals.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport { builtinModules } from 'module';\nimport path from 'path';\n\n// A list of the Node.js standard library modules that are currently\n// available,\nexport const NODE_STDLIB_MODULES: string[] = [\n  // Add all nested imports...\n  'assert/strict',\n  'dns/promises',\n  'inspector/promises',\n  'fs/promises',\n  'stream/web',\n  'stream/promises',\n  'path/posix',\n  'path/win32',\n  'readline/promises',\n  'stream/consumers',\n  'timers/promises',\n  'util/types',\n  // Collect all builtin modules...\n  ...(\n    builtinModules ||\n    // @ts-expect-error\n    (process.binding ? Object.keys(process.binding('natives')) : []) ||\n    []\n  ).filter((x) => !/^_|^(internal|v8|node-inspect)\\/|\\//.test(x) && !['sys'].includes(x)),\n].sort();\n\nconst shimsFolder = path.join(require.resolve('@expo/cli/package.json'), '../static/shims');\nconst canaryFolder = path.join(require.resolve('@expo/cli/package.json'), '../static/canary');\n\nexport function shouldCreateVirtualShim(normalName: string) {\n  const shimPath = path.join(shimsFolder, normalName);\n  if (fs.existsSync(shimPath)) {\n    return shimPath;\n  }\n  return null;\n}\nexport function shouldCreateVirtualCanary(normalName: string): string | null {\n  const canaryPath = path.join(canaryFolder, normalName);\n  if (fs.existsSync(canaryPath)) {\n    return canaryPath;\n  }\n  return null;\n}\n\nexport function isNodeExternal(moduleName: string): string | null {\n  const moduleId = moduleName.replace(/^node:/, '');\n  if (NODE_STDLIB_MODULES.includes(moduleId)) {\n    return moduleId;\n  }\n  return null;\n}\n"], "names": ["NODE_STDLIB_MODULES", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldCreateVirtualCanary", "isNodeExternal", "builtinModules", "process", "binding", "Object", "keys", "filter", "x", "test", "includes", "sort", "shimsFolder", "path", "join", "require", "resolve", "canaryFolder", "normalName", "s<PERSON><PERSON><PERSON>", "fs", "existsSync", "canaryPath", "moduleName", "moduleId", "replace"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAMaA,mBAAmB,MAAnBA,mBAAmB;IA0BhBC,uBAAuB,MAAvBA,uBAAuB;IAOvBC,yBAAyB,MAAzBA,yBAAyB;IAQzBC,cAAc,MAAdA,cAAc;;;8DA/Cf,IAAI;;;;;;;yBACY,QAAQ;;;;;;;8DACtB,MAAM;;;;;;;;;;;AAIhB,MAAMH,mBAAmB,GAAa;IAC3C,4BAA4B;IAC5B,eAAe;IACf,cAAc;IACd,oBAAoB;IACpB,aAAa;IACb,YAAY;IACZ,iBAAiB;IACjB,YAAY;IACZ,YAAY;IACZ,mBAAmB;IACnB,kBAAkB;IAClB,iBAAiB;IACjB,YAAY;IACZ,iCAAiC;OAC9B,CACDI,OAAc,EAAA,eAAA,IACd,mBAAmB;IACnB,CAACC,OAAO,CAACC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACH,OAAO,CAACC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,IAChE,EAAE,CACH,CAACG,MAAM,CAAC,CAACC,CAAC,GAAK,CAAC,sCAAsCC,IAAI,CAACD,CAAC,CAAC,IAAI,CAAC;YAAC,KAAK;SAAC,CAACE,QAAQ,CAACF,CAAC,CAAC,CAAC;CACxF,CAACG,IAAI,EAAE,AAAC;AAET,MAAMC,WAAW,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE,iBAAiB,CAAC,AAAC;AAC5F,MAAMC,YAAY,GAAGJ,KAAI,EAAA,QAAA,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE,kBAAkB,CAAC,AAAC;AAEvF,SAASjB,uBAAuB,CAACmB,UAAkB,EAAE;IAC1D,MAAMC,QAAQ,GAAGN,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,WAAW,EAAEM,UAAU,CAAC,AAAC;IACpD,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;QAC3B,OAAOA,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AACM,SAASnB,yBAAyB,CAACkB,UAAkB,EAAiB;IAC3E,MAAMI,UAAU,GAAGT,KAAI,EAAA,QAAA,CAACC,IAAI,CAACG,YAAY,EAAEC,UAAU,CAAC,AAAC;IACvD,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACC,UAAU,CAAC,EAAE;QAC7B,OAAOA,UAAU,CAAC;IACpB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAASrB,cAAc,CAACsB,UAAkB,EAAiB;IAChE,MAAMC,QAAQ,GAAGD,UAAU,CAACE,OAAO,WAAW,EAAE,CAAC,AAAC;IAClD,IAAI3B,mBAAmB,CAACY,QAAQ,CAACc,QAAQ,CAAC,EAAE;QAC1C,OAAOA,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}