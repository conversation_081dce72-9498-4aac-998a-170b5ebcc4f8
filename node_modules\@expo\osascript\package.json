{"name": "@expo/osascript", "version": "2.2.5", "description": "Tools for running an osascripts in Node", "license": "MIT", "keywords": ["osascript", "mac", "osx", "spawn", "exec"], "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/osascript#readme", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/osascript"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "main": "build/index.js", "files": ["build"], "scripts": {"build": "expo-module tsc", "prepare": "yarn run clean && yarn run build", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "watch": "yarn run build --watch --preserveWatchOutput", "prepublishOnly": "expo-module prepublishOnly"}, "engines": {"node": ">=12"}, "dependencies": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}, "devDependencies": {"expo-module-scripts": "^4.1.9"}, "publishConfig": {"access": "public"}, "gitHead": "1c4a89b0c0adebb53ef84b4a6ac25864e4652917"}