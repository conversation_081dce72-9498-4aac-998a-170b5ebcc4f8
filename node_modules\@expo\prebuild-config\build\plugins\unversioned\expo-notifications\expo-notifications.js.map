{"version": 3, "file": "expo-notifications.js", "names": ["_withAndroidNotifications", "data", "require", "_createLegacyPlugin", "_default", "exports", "default", "createLegacyPlugin", "packageName", "fallback", "withNotificationManifest", "withNotificationIconColor", "withNotificationIcons"], "sources": ["../../../../src/plugins/unversioned/expo-notifications/expo-notifications.ts"], "sourcesContent": ["import {\n  withNotificationIconColor,\n  withNotificationIcons,\n  withNotificationManifest,\n} from './withAndroidNotifications';\nimport { createLegacyPlugin } from '../createLegacyPlugin';\n\nexport default createLegacyPlugin({\n  packageName: 'expo-notifications',\n  fallback: [\n    // Android\n    withNotificationManifest,\n    withNotificationIconColor,\n    withNotificationIcons,\n    // iOS\n    // Automatic setting of APNS entitlement is no longer needed\n  ],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,0BAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,yBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,SAAAE,oBAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,mBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2D,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE5C,IAAAC,wCAAkB,EAAC;EAChCC,WAAW,EAAE,oBAAoB;EACjCC,QAAQ,EAAE;EACR;EACAC,oDAAwB,EACxBC,qDAAyB,EACzBC;EACA;EACA;EAAA;AAEJ,CAAC,CAAC", "ignoreList": []}