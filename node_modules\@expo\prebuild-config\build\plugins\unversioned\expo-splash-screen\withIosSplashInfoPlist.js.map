{"version": 3, "file": "withIosSplashInfoPlist.js", "names": ["_configPlugins", "data", "require", "_debug", "_interopRequireDefault", "e", "__esModule", "default", "debug", "Debug", "withIosSplashInfoPlist", "config", "splash", "withInfoPlist", "modResults", "setSplashInfoPlist", "exports", "infoPlist", "isDarkModeEnabled", "dark", "image", "tabletImage", "backgroundColor", "tabletBackgroundColor", "existing", "ios", "userInterfaceStyle", "WarningAggregator", "addWarningIOS", "UIUserInterfaceStyle", "UILaunchStoryboardName"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashInfoPlist.ts"], "sourcesContent": ["import { ConfigPlugin, InfoPlist, WarningAggregator, withInfoPlist } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport Debug from 'debug';\n\nimport { IOSSplashConfig } from './getIosSplashConfig';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios:infoPlist');\n\nexport const withIosSplashInfoPlist: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setSplashInfoPlist(config, config.modResults, splash);\n    return config;\n  });\n};\n\nexport function setSplashInfoPlist(\n  config: ExpoConfig,\n  infoPlist: InfoPlist,\n  splash: IOSSplashConfig\n): InfoPlist {\n  const isDarkModeEnabled = !!(\n    splash?.dark?.image ||\n    splash?.dark?.tabletImage ||\n    splash?.dark?.backgroundColor ||\n    splash?.dark?.tabletBackgroundColor\n  );\n  debug(`isDarkModeEnabled: `, isDarkModeEnabled);\n\n  if (isDarkModeEnabled) {\n    // IOSConfig.UserInterfaceStyle.getUserInterfaceStyle(config);\n    // Determine if the user manually defined the userInterfaceStyle incorrectly\n    const existing = config.ios?.userInterfaceStyle ?? config.userInterfaceStyle;\n    // Add a warning to prevent the dark mode splash screen from not being shown -- this was learned the hard way.\n    if (existing && existing !== 'automatic') {\n      WarningAggregator.addWarningIOS(\n        'userInterfaceStyle',\n        'The existing `userInterfaceStyle` property is preventing splash screen from working properly. Remove it or disable dark mode splash screens.'\n      );\n    }\n    // assigning it to auto anyways, but this is fragile because the order of operations matter now\n    infoPlist.UIUserInterfaceStyle = 'Automatic';\n  } else {\n    // NOTE(brentvatne): Commented out this line because it causes https://github.com/expo/expo-cli/issues/3935\n    // We should revisit this approach.\n    // delete infoPlist.UIUserInterfaceStyle;\n  }\n\n  if (splash) {\n    // TODO: What to do here ??\n    infoPlist.UILaunchStoryboardName = 'SplashScreen';\n  } else {\n    debug(`Disabling UILaunchStoryboardName`);\n    delete infoPlist.UILaunchStoryboardName;\n  }\n\n  return infoPlist;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,OAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,MAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0B,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAI1B,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,uDAAuD,CAAC;AAErE,MAAMC,sBAAqD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACvF,OAAO,IAAAC,8BAAa,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACG,UAAU,GAAGC,kBAAkB,CAACJ,MAAM,EAAEA,MAAM,CAACG,UAAU,EAAEF,MAAM,CAAC;IACzE,OAAOD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,sBAAA,GAAAA,sBAAA;AAEK,SAASK,kBAAkBA,CAChCJ,MAAkB,EAClBM,SAAoB,EACpBL,MAAuB,EACZ;EACX,MAAMM,iBAAiB,GAAG,CAAC,EACzBN,MAAM,EAAEO,IAAI,EAAEC,KAAK,IACnBR,MAAM,EAAEO,IAAI,EAAEE,WAAW,IACzBT,MAAM,EAAEO,IAAI,EAAEG,eAAe,IAC7BV,MAAM,EAAEO,IAAI,EAAEI,qBAAqB,CACpC;EACDf,KAAK,CAAC,qBAAqB,EAAEU,iBAAiB,CAAC;EAE/C,IAAIA,iBAAiB,EAAE;IACrB;IACA;IACA,MAAMM,QAAQ,GAAGb,MAAM,CAACc,GAAG,EAAEC,kBAAkB,IAAIf,MAAM,CAACe,kBAAkB;IAC5E;IACA,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACxCG,kCAAiB,CAACC,aAAa,CAC7B,oBAAoB,EACpB,8IACF,CAAC;IACH;IACA;IACAX,SAAS,CAACY,oBAAoB,GAAG,WAAW;EAC9C,CAAC,MAAM;IACL;IACA;IACA;EAAA;EAGF,IAAIjB,MAAM,EAAE;IACV;IACAK,SAAS,CAACa,sBAAsB,GAAG,cAAc;EACnD,CAAC,MAAM;IACLtB,KAAK,CAAC,kCAAkC,CAAC;IACzC,OAAOS,SAAS,CAACa,sBAAsB;EACzC;EAEA,OAAOb,SAAS;AAClB", "ignoreList": []}