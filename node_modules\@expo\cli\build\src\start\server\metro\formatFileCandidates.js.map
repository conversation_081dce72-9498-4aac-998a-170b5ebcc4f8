{"version": 3, "sources": ["../../../../../src/start/server/metro/formatFileCandidates.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * A fork of my proposed Metro change https://github.com/facebook/metro/pull/1036/files\n */\n\nimport type { FileCandidates } from 'metro-resolver';\nimport path from 'path';\n\nfunction groupExtensions(exts: readonly string[]): string[][] {\n  // Reverse the extensions and split into parts\n  const extParts = exts.map((ext) => ext.split('.').reverse());\n\n  // Find the maximum depth of extension parts\n  const maxDepth = Math.max(...extParts.map((parts) => parts.length));\n\n  // Initialize groups based on the max depth\n  const groups = Array.from({ length: maxDepth }, () => new Set<string>());\n\n  extParts.forEach((parts) => {\n    parts.forEach((part, i) => {\n      // Add parts to the corresponding group based on their depth\n      groups[i].add(part);\n    });\n  });\n\n  // Cycle groups and remove duplicates that appear forwards\n  groups.forEach((group, index) => {\n    // Remove duplicates that appear forwards\n    // NOTE: This doesn't support extensions like `.native.native.js`\n    groups.forEach((otherGroup, otherIndex) => {\n      if (index < otherIndex) {\n        otherGroup.forEach((part) => group.delete(part));\n      }\n    });\n  });\n\n  // Convert sets back to arrays and reverse groups to correct order\n  return groups.map((group) => Array.from(group)).reverse();\n}\n\nfunction createMatcherPatternForExtensions(exts: readonly string[]): string {\n  let formatted = '';\n\n  if (exts.length) {\n    // Apply grouping function\n    const groups = groupExtensions(exts);\n\n    formatted += groups\n      .map((group, index) => {\n        return index < groups.length - 1\n          ? `(${group.map((ext) => `.${ext}`).join('|')})?`\n          : `.(${group.join('|')})`;\n      })\n      .join('');\n  }\n\n  return formatted;\n}\n\nexport function formatFileCandidates(\n  candidates: FileCandidates,\n  allowIndex: boolean = false\n): string {\n  if (candidates.type === 'asset') {\n    return candidates.name;\n  }\n\n  let formatted = candidates.filePathPrefix;\n\n  if (allowIndex) {\n    formatted += `(${path.sep}index)?`;\n  }\n\n  const extensions = candidates.candidateExts\n    // Drop additional dots, the first character if it is a dot, and remove empty strings.\n    .map((ext) => ext.replace(/\\.+/g, '.').replace(/^\\./g, ''))\n    .filter(Boolean);\n\n  formatted += createMatcherPatternForExtensions(extensions);\n\n  return formatted;\n}\n"], "names": ["formatFileCandidates", "groupExtensions", "exts", "extParts", "map", "ext", "split", "reverse", "max<PERSON><PERSON><PERSON>", "Math", "max", "parts", "length", "groups", "Array", "from", "Set", "for<PERSON>ach", "part", "i", "add", "group", "index", "otherGroup", "otherIndex", "delete", "createMatcherPatternForExtensions", "formatted", "join", "candidates", "allowIndex", "type", "name", "filePathPrefix", "path", "sep", "extensions", "candidate<PERSON><PERSON><PERSON>", "replace", "filter", "Boolean"], "mappings": "AAAA;;;;;;;;CAQC,GAED;;;;+BAsDgBA,sBAAoB;;aAApBA,oBAAoB;;;8DArDnB,MAAM;;;;;;;;;;;AAEvB,SAASC,eAAe,CAACC,IAAuB,EAAc;IAC5D,8CAA8C;IAC9C,MAAMC,QAAQ,GAAGD,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,GAAKA,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,EAAE,CAAC,AAAC;IAE7D,4CAA4C;IAC5C,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,IAAIP,QAAQ,CAACC,GAAG,CAAC,CAACO,KAAK,GAAKA,KAAK,CAACC,MAAM,CAAC,CAAC,AAAC;IAEpE,2CAA2C;IAC3C,MAAMC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC;QAAEH,MAAM,EAAEJ,QAAQ;KAAE,EAAE,IAAM,IAAIQ,GAAG,EAAU,CAAC,AAAC;IAEzEb,QAAQ,CAACc,OAAO,CAAC,CAACN,KAAK,GAAK;QAC1BA,KAAK,CAACM,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,GAAK;YACzB,4DAA4D;YAC5DN,MAAM,CAACM,CAAC,CAAC,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,0DAA0D;IAC1DL,MAAM,CAACI,OAAO,CAAC,CAACI,KAAK,EAAEC,KAAK,GAAK;QAC/B,yCAAyC;QACzC,iEAAiE;QACjET,MAAM,CAACI,OAAO,CAAC,CAACM,UAAU,EAAEC,UAAU,GAAK;YACzC,IAAIF,KAAK,GAAGE,UAAU,EAAE;gBACtBD,UAAU,CAACN,OAAO,CAAC,CAACC,IAAI,GAAKG,KAAK,CAACI,MAAM,CAACP,IAAI,CAAC,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,kEAAkE;IAClE,OAAOL,MAAM,CAACT,GAAG,CAAC,CAACiB,KAAK,GAAKP,KAAK,CAACC,IAAI,CAACM,KAAK,CAAC,CAAC,CAACd,OAAO,EAAE,CAAC;AAC5D,CAAC;AAED,SAASmB,iCAAiC,CAACxB,IAAuB,EAAU;IAC1E,IAAIyB,SAAS,GAAG,EAAE,AAAC;IAEnB,IAAIzB,IAAI,CAACU,MAAM,EAAE;QACf,0BAA0B;QAC1B,MAAMC,MAAM,GAAGZ,eAAe,CAACC,IAAI,CAAC,AAAC;QAErCyB,SAAS,IAAId,MAAM,CAChBT,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,GAAK;YACrB,OAAOA,KAAK,GAAGT,MAAM,CAACD,MAAM,GAAG,CAAC,GAC5B,CAAC,CAAC,EAAES,KAAK,CAACjB,GAAG,CAAC,CAACC,GAAG,GAAK,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAACuB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAC/C,CAAC,EAAE,EAAEP,KAAK,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CACDA,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC;IAED,OAAOD,SAAS,CAAC;AACnB,CAAC;AAEM,SAAS3B,oBAAoB,CAClC6B,UAA0B,EAC1BC,UAAmB,GAAG,KAAK,EACnB;IACR,IAAID,UAAU,CAACE,IAAI,KAAK,OAAO,EAAE;QAC/B,OAAOF,UAAU,CAACG,IAAI,CAAC;IACzB,CAAC;IAED,IAAIL,SAAS,GAAGE,UAAU,CAACI,cAAc,AAAC;IAE1C,IAAIH,UAAU,EAAE;QACdH,SAAS,IAAI,CAAC,CAAC,EAAEO,KAAI,EAAA,QAAA,CAACC,GAAG,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,MAAMC,UAAU,GAAGP,UAAU,CAACQ,aAAa,AACzC,sFAAsF;KACrFjC,GAAG,CAAC,CAACC,GAAG,GAAKA,GAAG,CAACiC,OAAO,SAAS,GAAG,CAAC,CAACA,OAAO,SAAS,EAAE,CAAC,CAAC,CAC1DC,MAAM,CAACC,OAAO,CAAC,AAAC;IAEnBb,SAAS,IAAID,iCAAiC,CAACU,UAAU,CAAC,CAAC;IAE3D,OAAOT,SAAS,CAAC;AACnB,CAAC"}