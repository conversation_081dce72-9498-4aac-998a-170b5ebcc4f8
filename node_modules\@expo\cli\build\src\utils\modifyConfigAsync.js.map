{"version": 3, "sources": ["../../../src/utils/modifyConfigAsync.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SilentError } from './errors';\nimport * as Log from '../log';\n\n/** Wraps `[@expo/config] modifyConfigAsync()` and adds additional logging. */\nexport async function attemptModification(\n  projectRoot: string,\n  edits: Partial<ExpoConfig>,\n  exactEdits: Partial<ExpoConfig>\n): Promise<boolean> {\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n  });\n  if (modification.type !== 'success') {\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n  return modification.type === 'success';\n}\n\nexport function warnAboutConfigAndThrow(type: string, message: string, edits: Partial<ExpoConfig>) {\n  Log.log();\n  if (type === 'warn') {\n    // The project is using a dynamic config, give the user a helpful log and bail out.\n    Log.log(chalk.yellow(message));\n  }\n  notifyAboutManualConfigEdits(edits);\n  throw new SilentError();\n}\n\nfunction notifyAboutManualConfigEdits(edits: Partial<ExpoConfig>) {\n  Log.log(chalk.cyan(`Please add the following to your Expo config`));\n  Log.log();\n  Log.log(JSON.stringify(edits, null, 2));\n  Log.log();\n}\n"], "names": ["attemptModification", "warnAboutConfigAndThrow", "projectRoot", "edits", "exactEdits", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "type", "message", "Log", "log", "chalk", "yellow", "notifyAboutManualConfigEdits", "SilentError", "cyan", "JSON", "stringify"], "mappings": "AAAA;;;;;;;;;;;IAOsBA,mBAAmB,MAAnBA,mBAAmB;IAczBC,uBAAuB,MAAvBA,uBAAuB;;;yBArBO,cAAc;;;;;;;8DAC1C,OAAO;;;;;;wBAEG,UAAU;2DACjB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtB,eAAeD,mBAAmB,CACvCE,WAAmB,EACnBC,KAA0B,EAC1BC,UAA+B,EACb;IAClB,MAAMC,YAAY,GAAG,MAAMC,IAAAA,OAAiB,EAAA,kBAAA,EAACJ,WAAW,EAAEC,KAAK,EAAE;QAC/DI,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;IACH,IAAIF,YAAY,CAACG,IAAI,KAAK,SAAS,EAAE;QACnCP,uBAAuB,CAACI,YAAY,CAACG,IAAI,EAAEH,YAAY,CAACI,OAAO,EAAGL,UAAU,CAAC,CAAC;IAChF,CAAC;IACD,OAAOC,YAAY,CAACG,IAAI,KAAK,SAAS,CAAC;AACzC,CAAC;AAEM,SAASP,uBAAuB,CAACO,IAAY,EAAEC,OAAe,EAAEN,KAA0B,EAAE;IACjGO,IAAG,CAACC,GAAG,EAAE,CAAC;IACV,IAAIH,IAAI,KAAK,MAAM,EAAE;QACnB,mFAAmF;QACnFE,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,MAAM,CAACJ,OAAO,CAAC,CAAC,CAAC;IACjC,CAAC;IACDK,4BAA4B,CAACX,KAAK,CAAC,CAAC;IACpC,MAAM,IAAIY,OAAW,YAAA,EAAE,CAAC;AAC1B,CAAC;AAED,SAASD,4BAA4B,CAACX,KAA0B,EAAE;IAChEO,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACI,IAAI,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC,CAAC;IACpEN,IAAG,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,CAACC,GAAG,CAACM,IAAI,CAACC,SAAS,CAACf,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACxCO,IAAG,CAACC,GAAG,EAAE,CAAC;AACZ,CAAC"}