{"version": 3, "sources": ["../../../../src/start/interface/KeyPressHandler.ts"], "sourcesContent": ["import * as Log from '../../log';\nimport { logCmdError } from '../../utils/errors';\n\nconst CTRL_C = '\\u0003';\n\nconst debug = require('debug')('expo:start:interface:keyPressHandler') as typeof console.log;\n\n/** An abstract key stroke interceptor. */\nexport class KeyPressHandler {\n  private isInterceptingKeyStrokes = false;\n  private isHandlingKeyPress = false;\n\n  constructor(public onPress: (key: string) => Promise<any>) {}\n\n  /** Start observing interaction pause listeners. */\n  createInteractionListener() {\n    // Support observing prompts.\n    let wasIntercepting = false;\n\n    const listener = ({ pause }: { pause: boolean }) => {\n      if (pause) {\n        // Track if we were already intercepting key strokes before pausing, so we can\n        // resume after pausing.\n        wasIntercepting = this.isInterceptingKeyStrokes;\n        this.stopInterceptingKeyStrokes();\n      } else if (wasIntercepting) {\n        // Only start if we were previously intercepting.\n        this.startInterceptingKeyStrokes();\n      }\n    };\n\n    return listener;\n  }\n\n  private handleKeypress = async (key: string) => {\n    // Prevent sending another event until the previous event has finished.\n    if (this.isHandlingKeyPress && key !== CTRL_C) {\n      return;\n    }\n    this.isHandlingKeyPress = true;\n    try {\n      debug(`Key pressed: ${key}`);\n      await this.onPress(key);\n    } catch (error: any) {\n      await logCmdError(error);\n    } finally {\n      this.isHandlingKeyPress = false;\n    }\n  };\n\n  /** Start intercepting all key strokes and passing them to the input `onPress` method. */\n  startInterceptingKeyStrokes() {\n    if (this.isInterceptingKeyStrokes) {\n      return;\n    }\n    this.isInterceptingKeyStrokes = true;\n    const { stdin } = process;\n    // TODO: This might be here because of an old Node version.\n    if (!stdin.setRawMode) {\n      Log.warn('Using a non-interactive terminal, keyboard commands are disabled.');\n      return;\n    }\n    stdin.setRawMode(true);\n    stdin.resume();\n    stdin.setEncoding('utf8');\n    stdin.on('data', this.handleKeypress);\n  }\n\n  /** Stop intercepting all key strokes. */\n  stopInterceptingKeyStrokes() {\n    if (!this.isInterceptingKeyStrokes) {\n      return;\n    }\n    this.isInterceptingKeyStrokes = false;\n    const { stdin } = process;\n    stdin.removeListener('data', this.handleKeypress);\n    // TODO: This might be here because of an old Node version.\n    if (!stdin.setRawMode) {\n      Log.warn('Using a non-interactive terminal, keyboard commands are disabled.');\n      return;\n    }\n    stdin.setRawMode(false);\n    stdin.resume();\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CTRL_C", "debug", "require", "constructor", "onPress", "isInterceptingKeyStrokes", "isHandlingKeyPress", "handleKeypress", "key", "error", "logCmdError", "createInteractionListener", "wasIntercepting", "listener", "pause", "stopInterceptingKeyStrokes", "startInterceptingKeyStrokes", "stdin", "process", "setRawMode", "Log", "warn", "resume", "setEncoding", "on", "removeListener"], "mappings": "AAAA;;;;+BAQaA,iBAAe;;aAAfA,eAAe;;2DARP,WAAW;wBACJ,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhD,MAAMC,MAAM,GAAG,MAAQ,AAAC;AAExB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAGtF,MAAMH,eAAe;IAI1BI,YAAmBC,OAAsC,CAAE;QAAxCA,eAAAA,OAAsC,CAAA;aAHjDC,wBAAwB,GAAG,KAAK;aAChCC,kBAAkB,GAAG,KAAK;aAwB1BC,cAAc,GAAG,OAAOC,GAAW,GAAK;YAC9C,uEAAuE;YACvE,IAAI,IAAI,CAACF,kBAAkB,IAAIE,GAAG,KAAKR,MAAM,EAAE;gBAC7C,OAAO;YACT,CAAC;YACD,IAAI,CAACM,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI;gBACFL,KAAK,CAAC,CAAC,aAAa,EAAEO,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,IAAI,CAACJ,OAAO,CAACI,GAAG,CAAC,CAAC;YAC1B,EAAE,OAAOC,KAAK,EAAO;gBACnB,MAAMC,IAAAA,OAAW,YAAA,EAACD,KAAK,CAAC,CAAC;YAC3B,CAAC,QAAS;gBACR,IAAI,CAACH,kBAAkB,GAAG,KAAK,CAAC;YAClC,CAAC;QACH,CAAC;IApC2D;IAE5D,iDAAiD,GACjDK,yBAAyB,GAAG;QAC1B,6BAA6B;QAC7B,IAAIC,eAAe,GAAG,KAAK,AAAC;QAE5B,MAAMC,QAAQ,GAAG,CAAC,EAAEC,KAAK,CAAA,EAAsB,GAAK;YAClD,IAAIA,KAAK,EAAE;gBACT,8EAA8E;gBAC9E,wBAAwB;gBACxBF,eAAe,GAAG,IAAI,CAACP,wBAAwB,CAAC;gBAChD,IAAI,CAACU,0BAA0B,EAAE,CAAC;YACpC,OAAO,IAAIH,eAAe,EAAE;gBAC1B,iDAAiD;gBACjD,IAAI,CAACI,2BAA2B,EAAE,CAAC;YACrC,CAAC;QACH,CAAC,AAAC;QAEF,OAAOH,QAAQ,CAAC;IAClB;IAkBA,uFAAuF,GACvFG,2BAA2B,GAAG;QAC5B,IAAI,IAAI,CAACX,wBAAwB,EAAE;YACjC,OAAO;QACT,CAAC;QACD,IAAI,CAACA,wBAAwB,GAAG,IAAI,CAAC;QACrC,MAAM,EAAEY,KAAK,CAAA,EAAE,GAAGC,OAAO,AAAC;QAC1B,2DAA2D;QAC3D,IAAI,CAACD,KAAK,CAACE,UAAU,EAAE;YACrBC,IAAG,CAACC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QACDJ,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC,CAAC;QACvBF,KAAK,CAACK,MAAM,EAAE,CAAC;QACfL,KAAK,CAACM,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1BN,KAAK,CAACO,EAAE,CAAC,MAAM,EAAE,IAAI,CAACjB,cAAc,CAAC,CAAC;IACxC;IAEA,uCAAuC,GACvCQ,0BAA0B,GAAG;QAC3B,IAAI,CAAC,IAAI,CAACV,wBAAwB,EAAE;YAClC,OAAO;QACT,CAAC;QACD,IAAI,CAACA,wBAAwB,GAAG,KAAK,CAAC;QACtC,MAAM,EAAEY,KAAK,CAAA,EAAE,GAAGC,OAAO,AAAC;QAC1BD,KAAK,CAACQ,cAAc,CAAC,MAAM,EAAE,IAAI,CAAClB,cAAc,CAAC,CAAC;QAClD,2DAA2D;QAC3D,IAAI,CAACU,KAAK,CAACE,UAAU,EAAE;YACrBC,IAAG,CAACC,IAAI,CAAC,mEAAmE,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QACDJ,KAAK,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC;QACxBF,KAAK,CAACK,MAAM,EAAE,CAAC;IACjB;CACD"}