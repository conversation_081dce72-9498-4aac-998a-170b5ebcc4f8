{"version": 3, "file": "ExpoConfigLoader.js", "sourceRoot": "", "sources": ["../src/ExpoConfigLoader.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;AA0FH,0DAEC;AA1FD,2DAA6B;AAC7B,oDAA4B;AAC5B,8DAAiC;AACjC,gEAAmC;AACnC,gDAAwB;AACxB,gEAAuC;AAEvC,iDAAoD;AACpD,uCAAiD;AACjD,uCAA6C;AAE7C,KAAK,UAAU,QAAQ,CAAC,WAAmB,EAAE,OAAiB,EAAE;IAC9D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,UAAU,WAAW,8BAA8B,CAAC,CAAC;QACjE,OAAO;IACT,CAAC;IAED,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE3D,2CAA2C;IAC3C,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAEhE,MAAM,WAAW,GAAG,IAAA,iCAAkB,EAAC,WAAW,CAAC,CAAC;IACpD,IAAA,qBAAM,EAAC,WAAW,EAAE,2DAA2D,WAAW,GAAG,CAAC,CAAC;IAC/F,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACvC,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1B,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,IAAA,sBAAW,EAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;IACrF,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,CAAC,CAAC;IACjF,2CAA2C;IAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAM,CAAC,MAAM,CAAC;SAC7C,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;SAC5D,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,cAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IAE/D,MAAM,YAAY,GAAG;QACnB,GAAG,mCAAmC;QACtC,GAAG,CAAC,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAC;KAC9C,CAAC;IACF,MAAM,qBAAqB,GAAG,aAAa,CAAC,MAAM,CAChD,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAA,oBAAa,EAAC,UAAU,EAAE,YAAY,CAAC,CACzD,CAAC;IACF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAChF,IAAI,sBAAO,CAAC,IAAI,EAAE,CAAC;QACjB,sBAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;AACH,CAAC;AAED,mCAAmC;AACnC,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,KAAK,UAAU,EAAE,CAAC;IAC1C,CAAC,KAAK,IAAI,EAAE;QACV,MAAM,YAAY,GAAG,sBAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC;QACzE,IAAI,CAAC;YACH,MAAM,QAAQ,CAAC,sBAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,sBAAO,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;QACnF,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;YACnC,sBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;AACP,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,WAA0B;IAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,8BAAoB,CAAC;IAC9B,CAAC;IAED,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACjE,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7D,KAAK,MAAM,IAAI,IAAI,sBAAsB,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IAEV,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB;IACrC,OAAO,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACrD,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,IAAkC;IACpD,sBAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,sBAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,CAAC;IACpD,sBAAO,CAAC,GAAG,CAAC,SAAS,GAAG,sBAAO,CAAC,GAAG,CAAC,SAAS,IAAI,sBAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;IAEtE,+FAA+F;IAC/F,UAAU,CAAC,OAAO,GAAG,sBAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;AAC7D,CAAC;AAED,6DAA6D;AAC7D,MAAM,mCAAmC,GAAG;IAC1C,6BAA6B;IAC7B,4BAA4B;IAC5B,kCAAkC;IAClC,gCAAgC;IAChC,wCAAwC;IACxC,oBAAoB;QAClB,KAAK;QACL,aAAa;QACb,cAAc;QACd,aAAa;QACb,OAAO;QACP,OAAO;QACP,QAAQ;QACR,eAAe;QACf,sBAAsB;QACtB,QAAQ;QACR,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,UAAU;QACV,aAAa;QACb,WAAW;QACX,OAAO;QACP,sBAAsB;QACtB,IAAI;QACJ,YAAY;QACZ,mBAAmB;QACnB,qBAAqB;QACrB,cAAc;QACd,cAAc;QACd,aAAa;QACb,SAAS;QACT,gBAAgB;QAChB,sBAAsB;QACtB,mBAAmB;KACpB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ;CACpB,CAAC"}