{"version": 3, "sources": ["../../../../../src/start/platforms/ios/xcrun.ts"], "sourcesContent": ["import spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:ios:xcrun') as typeof console.log;\n\nexport function isSpawnResultError(obj: any): obj is Error & SpawnResult {\n  return (\n    obj &&\n    'message' in obj &&\n    obj.status !== undefined &&\n    obj.stdout !== undefined &&\n    obj.stderr !== undefined\n  );\n}\n\nexport async function xcrunAsync(args: (string | undefined)[], options?: SpawnOptions) {\n  debug('Running: xcrun ' + args.join(' '));\n  try {\n    return await spawnAsync('xcrun', args.filter(Boolean) as string[], options);\n  } catch (e) {\n    throwXcrunError(e);\n  }\n}\n\nfunction throwXcrunError(e: any): never {\n  if (isLicenseOutOfDate(e.stdout) || isLicenseOutOfDate(e.stderr)) {\n    throw new CommandError(\n      'XCODE_LICENSE_NOT_ACCEPTED',\n      'Xcode license is not accepted. Please run `sudo xcodebuild -license`.'\n    );\n  } else if (e.stderr?.includes('not a developer tool or in PATH')) {\n    throw new CommandError(\n      'SIMCTL_NOT_AVAILABLE',\n      `You may need to run ${chalk.bold(\n        'sudo xcode-select -s /Applications/Xcode.app'\n      )} and try again.`\n    );\n  }\n  // Attempt to craft a better error message...\n  if (Array.isArray(e.output)) {\n    e.message += '\\n' + e.output.join('\\n').trim();\n  } else if (e.stderr) {\n    e.message += '\\n' + e.stderr;\n  }\n  throw e;\n}\n\nfunction isLicenseOutOfDate(text: string) {\n  if (!text) {\n    return false;\n  }\n\n  const lower = text.toLowerCase();\n  return lower.includes('xcode') && lower.includes('license');\n}\n"], "names": ["isSpawnResultError", "xcrunAsync", "debug", "require", "obj", "status", "undefined", "stdout", "stderr", "args", "options", "join", "spawnAsync", "filter", "Boolean", "e", "throwXcrunError", "isLicenseOutOfDate", "CommandError", "includes", "chalk", "bold", "Array", "isArray", "output", "message", "trim", "text", "lower", "toLowerCase"], "mappings": "AAAA;;;;;;;;;;;IAOgBA,kBAAkB,MAAlBA,kBAAkB;IAUZC,UAAU,MAAVA,UAAU;;;8DAjBsB,mBAAmB;;;;;;;8DACvD,OAAO;;;;;;wBAEI,uBAAuB;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAEhF,SAASH,kBAAkB,CAACI,GAAQ,EAA8B;IACvE,OACEA,GAAG,IACH,SAAS,IAAIA,GAAG,IAChBA,GAAG,CAACC,MAAM,KAAKC,SAAS,IACxBF,GAAG,CAACG,MAAM,KAAKD,SAAS,IACxBF,GAAG,CAACI,MAAM,KAAKF,SAAS,CACxB;AACJ,CAAC;AAEM,eAAeL,UAAU,CAACQ,IAA4B,EAAEC,OAAsB,EAAE;IACrFR,KAAK,CAAC,iBAAiB,GAAGO,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI;QACF,OAAO,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,OAAO,EAAEH,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,EAAcJ,OAAO,CAAC,CAAC;IAC9E,EAAE,OAAOK,CAAC,EAAE;QACVC,eAAe,CAACD,CAAC,CAAC,CAAC;IACrB,CAAC;AACH,CAAC;AAED,SAASC,eAAe,CAACD,CAAM,EAAS;QAM3BA,GAAQ;IALnB,IAAIE,kBAAkB,CAACF,CAAC,CAACR,MAAM,CAAC,IAAIU,kBAAkB,CAACF,CAAC,CAACP,MAAM,CAAC,EAAE;QAChE,MAAM,IAAIU,OAAY,aAAA,CACpB,4BAA4B,EAC5B,uEAAuE,CACxE,CAAC;IACJ,OAAO,IAAIH,CAAAA,GAAQ,GAARA,CAAC,CAACP,MAAM,SAAU,GAAlBO,KAAAA,CAAkB,GAAlBA,GAAQ,CAAEI,QAAQ,CAAC,iCAAiC,CAAC,EAAE;QAChE,MAAM,IAAID,OAAY,aAAA,CACpB,sBAAsB,EACtB,CAAC,oBAAoB,EAAEE,MAAK,EAAA,QAAA,CAACC,IAAI,CAC/B,8CAA8C,CAC/C,CAAC,eAAe,CAAC,CACnB,CAAC;IACJ,CAAC;IACD,6CAA6C;IAC7C,IAAIC,KAAK,CAACC,OAAO,CAACR,CAAC,CAACS,MAAM,CAAC,EAAE;QAC3BT,CAAC,CAACU,OAAO,IAAI,IAAI,GAAGV,CAAC,CAACS,MAAM,CAACb,IAAI,CAAC,IAAI,CAAC,CAACe,IAAI,EAAE,CAAC;IACjD,OAAO,IAAIX,CAAC,CAACP,MAAM,EAAE;QACnBO,CAAC,CAACU,OAAO,IAAI,IAAI,GAAGV,CAAC,CAACP,MAAM,CAAC;IAC/B,CAAC;IACD,MAAMO,CAAC,CAAC;AACV,CAAC;AAED,SAASE,kBAAkB,CAACU,IAAY,EAAE;IACxC,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAMC,KAAK,GAAGD,IAAI,CAACE,WAAW,EAAE,AAAC;IACjC,OAAOD,KAAK,CAACT,QAAQ,CAAC,OAAO,CAAC,IAAIS,KAAK,CAACT,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC9D,CAAC"}