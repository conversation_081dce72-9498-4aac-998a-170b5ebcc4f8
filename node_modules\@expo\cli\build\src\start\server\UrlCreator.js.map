{"version": 3, "sources": ["../../../../src/start/server/UrlCreator.ts"], "sourcesContent": ["import assert from 'assert';\nimport { URL } from 'url';\n\nimport * as Log from '../../log';\nimport { getIpAddress } from '../../utils/ip';\n\nconst debug = require('debug')('expo:start:server:urlCreator') as typeof console.log;\n\nexport interface CreateURLOptions {\n  /** URL scheme to use when opening apps in custom runtimes. */\n  scheme?: string | null;\n  /** Type of dev server host to use. */\n  hostType?: 'localhost' | 'lan' | 'tunnel';\n  /** Requested hostname. */\n  hostname?: string | null;\n}\n\ninterface UrlComponents {\n  port: string;\n  hostname: string;\n  protocol: string;\n}\nexport class UrlCreator {\n  constructor(\n    public defaults: CreateURLOptions | undefined,\n    private bundlerInfo: { port: number; getTunnelUrl?: () => string | null }\n  ) {}\n\n  /**\n   * Return a URL for the \"loading\" interstitial page that is used to disambiguate which\n   * native runtime to open the dev server with.\n   *\n   * @param options options for creating the URL\n   * @param platform when opening the URL from the CLI to a connected device we can specify the platform as a query parameter, otherwise it will be inferred from the unsafe user agent sniffing.\n   *\n   * @returns URL like `http://localhost:8081/_expo/loading?platform=ios`\n   * @returns URL like `http://localhost:8081/_expo/loading` when no platform is provided.\n   */\n  public constructLoadingUrl(options: CreateURLOptions, platform: string | null): string {\n    const url = new URL('_expo/loading', this.constructUrl({ scheme: 'http', ...options }));\n    if (platform) {\n      url.search = new URLSearchParams({ platform }).toString();\n    }\n    const loadingUrl = url.toString();\n    debug(`Loading URL: ${loadingUrl}`);\n    return loadingUrl;\n  }\n\n  /** Create a URI for launching in a native dev client. Returns `null` when no `scheme` can be resolved. */\n  public constructDevClientUrl(options?: CreateURLOptions): null | string {\n    const protocol = options?.scheme || this.defaults?.scheme;\n\n    if (\n      !protocol ||\n      // Prohibit the use of http(s) in dev client URIs since they'll never be valid.\n      ['http', 'https'].includes(protocol.toLowerCase()) ||\n      // Prohibit the use of `_` characters in the protocol, Node will throw an error when parsing these URLs\n      protocol.includes('_')\n    ) {\n      debug(`Invalid protocol for dev client URL: ${protocol}`);\n      return null;\n    }\n\n    const manifestUrl = this.constructUrl({\n      ...options,\n      scheme: this.defaults?.hostType === 'tunnel' ? 'https' : 'http',\n    });\n    const devClientUrl = `${protocol}://expo-development-client/?url=${encodeURIComponent(\n      manifestUrl\n    )}`;\n    debug(`Dev client URL: ${devClientUrl} -- manifestUrl: ${manifestUrl} -- %O`, options);\n    return devClientUrl;\n  }\n\n  /** Create a generic URL. */\n  public constructUrl(options?: Partial<CreateURLOptions> | null): string {\n    const urlComponents = this.getUrlComponents({\n      ...this.defaults,\n      ...options,\n    });\n    const url = joinUrlComponents(urlComponents);\n    debug(`URL: ${url}`);\n    return url;\n  }\n\n  /** Get the URL components from the Ngrok server URL. */\n  private getTunnelUrlComponents(options: Pick<CreateURLOptions, 'scheme'>): UrlComponents | null {\n    const tunnelUrl = this.bundlerInfo.getTunnelUrl?.();\n    if (!tunnelUrl) {\n      return null;\n    }\n    const parsed = new URL(tunnelUrl);\n    return {\n      port: parsed.port,\n      hostname: parsed.hostname,\n      protocol: options.scheme ?? 'http',\n    };\n  }\n\n  private getUrlComponents(options: CreateURLOptions): UrlComponents {\n    // Proxy comes first.\n    const proxyURL = getProxyUrl();\n    if (proxyURL) {\n      return getUrlComponentsFromProxyUrl(options, proxyURL);\n    }\n\n    // Ngrok.\n    if (options.hostType === 'tunnel') {\n      const components = this.getTunnelUrlComponents(options);\n      if (components) {\n        return components;\n      }\n      Log.warn('Tunnel URL not found (it might not be ready yet), falling back to LAN URL.');\n    } else if (options.hostType === 'localhost' && !options.hostname) {\n      options.hostname = 'localhost';\n    }\n\n    return {\n      hostname: getDefaultHostname(options),\n      port: this.bundlerInfo.port.toString(),\n      protocol: options.scheme ?? 'http',\n    };\n  }\n}\n\nfunction getUrlComponentsFromProxyUrl(\n  options: Pick<CreateURLOptions, 'scheme'>,\n  url: string\n): UrlComponents {\n  const parsedProxyUrl = new URL(url);\n  let protocol = options.scheme ?? 'http';\n  if (parsedProxyUrl.protocol === 'https:') {\n    if (protocol === 'http') {\n      protocol = 'https';\n    }\n    if (!parsedProxyUrl.port) {\n      parsedProxyUrl.port = '443';\n    }\n  }\n  return {\n    port: parsedProxyUrl.port,\n    hostname: parsedProxyUrl.hostname,\n    protocol,\n  };\n}\n\nfunction getDefaultHostname(options: Pick<CreateURLOptions, 'hostname'>) {\n  // TODO: Drop REACT_NATIVE_PACKAGER_HOSTNAME\n  if (process.env.REACT_NATIVE_PACKAGER_HOSTNAME) {\n    return process.env.REACT_NATIVE_PACKAGER_HOSTNAME.trim();\n  } else if (options.hostname === 'localhost') {\n    // Restrict the use of `localhost`\n    // TODO: Note why we do this.\n    return '127.0.0.1';\n  }\n\n  return options.hostname || getIpAddress();\n}\n\nfunction joinUrlComponents({ protocol, hostname, port }: Partial<UrlComponents>): string {\n  assert(hostname, 'hostname cannot be inferred.');\n  const validProtocol = protocol ? `${protocol}://` : '';\n\n  const url = `${validProtocol}${hostname}`;\n\n  if (port) {\n    return url + `:${port}`;\n  }\n\n  return url;\n}\n\n/** @deprecated */\nfunction getProxyUrl(): string | undefined {\n  return process.env.EXPO_PACKAGER_PROXY_URL;\n}\n\n// TODO: Drop the undocumented env variables:\n// REACT_NATIVE_PACKAGER_HOSTNAME\n// EXPO_PACKAGER_PROXY_URL\n"], "names": ["UrlCreator", "debug", "require", "constructor", "defaults", "bundlerInfo", "constructLoadingUrl", "options", "platform", "url", "URL", "constructUrl", "scheme", "search", "URLSearchParams", "toString", "loadingUrl", "constructDevClientUrl", "protocol", "includes", "toLowerCase", "manifestUrl", "hostType", "devClientUrl", "encodeURIComponent", "urlComponents", "getUrlComponents", "joinUrlComponents", "getTunnelUrlComponents", "tunnelUrl", "getTunnelUrl", "parsed", "port", "hostname", "proxyURL", "getProxyUrl", "getUrlComponentsFromProxyUrl", "components", "Log", "warn", "getDefaultHostname", "parsedProxyUrl", "process", "env", "REACT_NATIVE_PACKAGER_HOSTNAME", "trim", "getIpAddress", "assert", "validProtocol", "EXPO_PACKAGER_PROXY_URL"], "mappings": "AAAA;;;;+BAsBaA,YAAU;;aAAVA,UAAU;;;8DAtBJ,QAAQ;;;;;;;yBACP,KAAK;;;;;;2DAEJ,WAAW;oBACH,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC,AAAsB,AAAC;AAgB9E,MAAMF,UAAU;IACrBG,YACSC,QAAsC,EACrCC,WAAiE,CACzE;QAFOD,gBAAAA,QAAsC,CAAA;QACrCC,mBAAAA,WAAiE,CAAA;IACxE;IAEH;;;;;;;;;GASC,GACMC,mBAAmB,CAACC,OAAyB,EAAEC,QAAuB,EAAU;QACrF,MAAMC,GAAG,GAAG,IAAIC,CAAAA,IAAG,EAAA,CAAA,IAAA,CAAC,eAAe,EAAE,IAAI,CAACC,YAAY,CAAC;YAAEC,MAAM,EAAE,MAAM;YAAE,GAAGL,OAAO;SAAE,CAAC,CAAC,AAAC;QACxF,IAAIC,QAAQ,EAAE;YACZC,GAAG,CAACI,MAAM,GAAG,IAAIC,eAAe,CAAC;gBAAEN,QAAQ;aAAE,CAAC,CAACO,QAAQ,EAAE,CAAC;QAC5D,CAAC;QACD,MAAMC,UAAU,GAAGP,GAAG,CAACM,QAAQ,EAAE,AAAC;QAClCd,KAAK,CAAC,CAAC,aAAa,EAAEe,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,OAAOA,UAAU,CAAC;IACpB;IAEA,wGAAwG,GACjGC,qBAAqB,CAACV,OAA0B,EAAiB;YAClC,GAAa,EAevC,IAAa;QAfvB,MAAMW,QAAQ,GAAGX,CAAAA,OAAO,QAAQ,GAAfA,KAAAA,CAAe,GAAfA,OAAO,CAAEK,MAAM,CAAA,IAAI,CAAA,CAAA,GAAa,GAAb,IAAI,CAACR,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEQ,MAAM,CAAA,AAAC;QAE1D,IACE,CAACM,QAAQ,IACT,+EAA+E;QAC/E;YAAC,MAAM;YAAE,OAAO;SAAC,CAACC,QAAQ,CAACD,QAAQ,CAACE,WAAW,EAAE,CAAC,IAClD,uGAAuG;QACvGF,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC,EACtB;YACAlB,KAAK,CAAC,CAAC,qCAAqC,EAAEiB,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAMG,WAAW,GAAG,IAAI,CAACV,YAAY,CAAC;YACpC,GAAGJ,OAAO;YACVK,MAAM,EAAE,CAAA,CAAA,IAAa,GAAb,IAAI,CAACR,QAAQ,SAAU,GAAvB,KAAA,CAAuB,GAAvB,IAAa,CAAEkB,QAAQ,CAAA,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;SAChE,CAAC,AAAC;QACH,MAAMC,YAAY,GAAG,CAAC,EAAEL,QAAQ,CAAC,gCAAgC,EAAEM,kBAAkB,CACnFH,WAAW,CACZ,CAAC,CAAC,AAAC;QACJpB,KAAK,CAAC,CAAC,gBAAgB,EAAEsB,YAAY,CAAC,iBAAiB,EAAEF,WAAW,CAAC,MAAM,CAAC,EAAEd,OAAO,CAAC,CAAC;QACvF,OAAOgB,YAAY,CAAC;IACtB;IAEA,0BAA0B,GACnBZ,YAAY,CAACJ,OAA0C,EAAU;QACtE,MAAMkB,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAC1C,GAAG,IAAI,CAACtB,QAAQ;YAChB,GAAGG,OAAO;SACX,CAAC,AAAC;QACH,MAAME,GAAG,GAAGkB,iBAAiB,CAACF,aAAa,CAAC,AAAC;QAC7CxB,KAAK,CAAC,CAAC,KAAK,EAAEQ,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAOA,GAAG,CAAC;IACb;IAEA,sDAAsD,GAC9CmB,sBAAsB,CAACrB,OAAyC,EAAwB;YAC5E,YAAgB,AAAa,EAA7B,GAA6B;QAA/C,MAAMsB,SAAS,GAAG,CAAA,GAA6B,GAA7B,CAAA,YAAgB,GAAhB,IAAI,CAACxB,WAAW,EAACyB,YAAY,SAAI,GAAjC,KAAA,CAAiC,GAAjC,GAA6B,CAA7B,IAAiC,CAAjC,YAAgB,CAAiB,AAAC;QACpD,IAAI,CAACD,SAAS,EAAE;YACd,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAME,MAAM,GAAG,IAAIrB,CAAAA,IAAG,EAAA,CAAA,IAAA,CAACmB,SAAS,CAAC,AAAC;QAClC,OAAO;YACLG,IAAI,EAAED,MAAM,CAACC,IAAI;YACjBC,QAAQ,EAAEF,MAAM,CAACE,QAAQ;YACzBf,QAAQ,EAAEX,OAAO,CAACK,MAAM,IAAI,MAAM;SACnC,CAAC;IACJ;IAEQc,gBAAgB,CAACnB,OAAyB,EAAiB;QACjE,qBAAqB;QACrB,MAAM2B,QAAQ,GAAGC,WAAW,EAAE,AAAC;QAC/B,IAAID,QAAQ,EAAE;YACZ,OAAOE,4BAA4B,CAAC7B,OAAO,EAAE2B,QAAQ,CAAC,CAAC;QACzD,CAAC;QAED,SAAS;QACT,IAAI3B,OAAO,CAACe,QAAQ,KAAK,QAAQ,EAAE;YACjC,MAAMe,UAAU,GAAG,IAAI,CAACT,sBAAsB,CAACrB,OAAO,CAAC,AAAC;YACxD,IAAI8B,UAAU,EAAE;gBACd,OAAOA,UAAU,CAAC;YACpB,CAAC;YACDC,IAAG,CAACC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QACzF,OAAO,IAAIhC,OAAO,CAACe,QAAQ,KAAK,WAAW,IAAI,CAACf,OAAO,CAAC0B,QAAQ,EAAE;YAChE1B,OAAO,CAAC0B,QAAQ,GAAG,WAAW,CAAC;QACjC,CAAC;QAED,OAAO;YACLA,QAAQ,EAAEO,kBAAkB,CAACjC,OAAO,CAAC;YACrCyB,IAAI,EAAE,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAACjB,QAAQ,EAAE;YACtCG,QAAQ,EAAEX,OAAO,CAACK,MAAM,IAAI,MAAM;SACnC,CAAC;IACJ;CACD;AAED,SAASwB,4BAA4B,CACnC7B,OAAyC,EACzCE,GAAW,EACI;IACf,MAAMgC,cAAc,GAAG,IAAI/B,CAAAA,IAAG,EAAA,CAAA,IAAA,CAACD,GAAG,CAAC,AAAC;IACpC,IAAIS,QAAQ,GAAGX,OAAO,CAACK,MAAM,IAAI,MAAM,AAAC;IACxC,IAAI6B,cAAc,CAACvB,QAAQ,KAAK,QAAQ,EAAE;QACxC,IAAIA,QAAQ,KAAK,MAAM,EAAE;YACvBA,QAAQ,GAAG,OAAO,CAAC;QACrB,CAAC;QACD,IAAI,CAACuB,cAAc,CAACT,IAAI,EAAE;YACxBS,cAAc,CAACT,IAAI,GAAG,KAAK,CAAC;QAC9B,CAAC;IACH,CAAC;IACD,OAAO;QACLA,IAAI,EAAES,cAAc,CAACT,IAAI;QACzBC,QAAQ,EAAEQ,cAAc,CAACR,QAAQ;QACjCf,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAASsB,kBAAkB,CAACjC,OAA2C,EAAE;IACvE,4CAA4C;IAC5C,IAAImC,OAAO,CAACC,GAAG,CAACC,8BAA8B,EAAE;QAC9C,OAAOF,OAAO,CAACC,GAAG,CAACC,8BAA8B,CAACC,IAAI,EAAE,CAAC;IAC3D,OAAO,IAAItC,OAAO,CAAC0B,QAAQ,KAAK,WAAW,EAAE;QAC3C,kCAAkC;QAClC,6BAA6B;QAC7B,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,OAAO1B,OAAO,CAAC0B,QAAQ,IAAIa,IAAAA,GAAY,aAAA,GAAE,CAAC;AAC5C,CAAC;AAED,SAASnB,iBAAiB,CAAC,EAAET,QAAQ,CAAA,EAAEe,QAAQ,CAAA,EAAED,IAAI,CAAA,EAA0B,EAAU;IACvFe,IAAAA,OAAM,EAAA,QAAA,EAACd,QAAQ,EAAE,8BAA8B,CAAC,CAAC;IACjD,MAAMe,aAAa,GAAG9B,QAAQ,GAAG,CAAC,EAAEA,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,AAAC;IAEvD,MAAMT,GAAG,GAAG,CAAC,EAAEuC,aAAa,CAAC,EAAEf,QAAQ,CAAC,CAAC,AAAC;IAE1C,IAAID,IAAI,EAAE;QACR,OAAOvB,GAAG,GAAG,CAAC,CAAC,EAAEuB,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC;IAED,OAAOvB,GAAG,CAAC;AACb,CAAC;AAED,gBAAgB,GAChB,SAAS0B,WAAW,GAAuB;IACzC,OAAOO,OAAO,CAACC,GAAG,CAACM,uBAAuB,CAAC;AAC7C,CAAC,CAED,6CAA6C;CAC7C,iCAAiC;CACjC,0BAA0B"}