{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/getVersionedPackages.ts"], "sourcesContent": ["import { PackageJSONConfig } from '@expo/config';\nimport npmPackageArg from 'npm-package-arg';\n\nimport { getVersionedNativeModulesAsync } from './bundledNativeModules';\nimport { hasExpoCanaryAsync } from './resolvePackages';\nimport { getVersionsAsync, SDKVersion } from '../../../api/getVersions';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:doctor:dependencies:getVersionedPackages'\n) as typeof console.log;\n\nexport type DependencyList = Record<string, string>;\n\n/** Adds `react-dom`, `react`, and `react-native` to the list of known package versions (`relatedPackages`) */\nfunction normalizeSdkVersionObject(version?: SDKVersion): Record<string, string> {\n  if (!version) {\n    return {};\n  }\n  const { relatedPackages, facebookReactVersion, facebookReactNativeVersion, expoVersion } =\n    version;\n\n  const reactVersion = facebookReactVersion\n    ? {\n        react: facebookReactVersion,\n        'react-dom': facebookReactVersion,\n      }\n    : undefined;\n\n  const expoVersionIfAvailable = expoVersion ? { expo: expoVersion } : undefined;\n\n  return {\n    ...relatedPackages,\n    ...reactVersion,\n    ...expoVersionIfAvailable,\n    'react-native': facebookReactNativeVersion,\n  };\n}\n\n/** Get the known versions for a given SDK, combines all sources. */\nexport async function getCombinedKnownVersionsAsync({\n  projectRoot,\n  sdkVersion,\n  skipCache,\n}: {\n  projectRoot: string;\n  sdkVersion?: string;\n  skipCache?: boolean;\n}) {\n  const skipRemoteVersions = await hasExpoCanaryAsync(projectRoot);\n  if (skipRemoteVersions) {\n    Log.warn('Dependency validation might be unreliable when using canary SDK versions');\n  }\n\n  if (env.EXPO_NO_DEPENDENCY_VALIDATION) {\n    debug('Dependency validation is disabled through EXPO_NO_DEPENDENCY_VALIDATION=1');\n    return {};\n  }\n\n  const bundledNativeModules = sdkVersion\n    ? await getVersionedNativeModulesAsync(projectRoot, sdkVersion, { skipRemoteVersions })\n    : {};\n  const versionsForSdk = !skipRemoteVersions\n    ? await getRemoteVersionsForSdkAsync({ sdkVersion, skipCache })\n    : {};\n  return {\n    ...bundledNativeModules,\n    // Prefer the remote versions over the bundled versions, this enables us to push\n    // emergency fixes that users can access without having to update the `expo` package.\n    ...versionsForSdk,\n  };\n}\n\n/** @returns a key/value list of known dependencies and their version (including range). */\nexport async function getRemoteVersionsForSdkAsync({\n  sdkVersion,\n  skipCache,\n}: { sdkVersion?: string; skipCache?: boolean } = {}): Promise<DependencyList> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Dependency validation is unreliable in offline-mode');\n    return {};\n  }\n\n  try {\n    const { sdkVersions } = await getVersionsAsync({ skipCache });\n\n    // We only want versioned dependencies so skip if they cannot be found.\n    if (!sdkVersion || !(sdkVersion in sdkVersions)) {\n      debug(\n        `Skipping versioned dependencies because the SDK version is not found. (sdkVersion: ${sdkVersion}, available: ${Object.keys(\n          sdkVersions\n        ).join(', ')})`\n      );\n      return {};\n    }\n\n    const version = sdkVersions[sdkVersion as keyof typeof sdkVersions] as unknown as SDKVersion;\n\n    return normalizeSdkVersionObject(version);\n  } catch (error: any) {\n    if (error instanceof CommandError && error.code === 'OFFLINE') {\n      return getRemoteVersionsForSdkAsync({ sdkVersion, skipCache });\n    }\n    throw error;\n  }\n}\n\ntype ExcludedNativeModules = {\n  name: string;\n  bundledNativeVersion: string;\n  isExcludedFromValidation: boolean;\n  specifiedVersion?: string; // e.g. 1.2.3, latest\n};\n\n/**\n * Versions a list of `packages` against a given `sdkVersion` based on local and remote versioning resources.\n *\n * @param projectRoot\n * @param param1\n * @returns\n */\nexport async function getVersionedPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    sdkVersion,\n    pkg,\n  }: {\n    /** List of npm packages to process. */\n    packages: string[];\n    /** Target SDK Version number to version the `packages` for. */\n    sdkVersion: string;\n    pkg: PackageJSONConfig;\n  }\n): Promise<{\n  packages: string[];\n  messages: string[];\n  excludedNativeModules: ExcludedNativeModules[];\n}> {\n  const versionsForSdk = await getCombinedKnownVersionsAsync({\n    projectRoot,\n    sdkVersion,\n    skipCache: true,\n  });\n\n  let nativeModulesCount = 0;\n  let othersCount = 0;\n  const excludedNativeModules: ExcludedNativeModules[] = [];\n\n  const versionedPackages = packages.map((arg) => {\n    const { name, type, raw, rawSpec } = npmPackageArg(arg);\n\n    if (['tag', 'version', 'range'].includes(type) && name && versionsForSdk[name]) {\n      // Unimodule packages from npm registry are modified to use the bundled version.\n      // Some packages have the recommended version listed in https://exp.host/--/api/v2/versions.\n      const isExcludedFromValidation = pkg?.expo?.install?.exclude?.includes(name);\n      const hasSpecifiedExactVersion = rawSpec !== '' && rawSpec !== '*';\n      if (isExcludedFromValidation || hasSpecifiedExactVersion) {\n        othersCount++;\n        excludedNativeModules.push({\n          name,\n          bundledNativeVersion: versionsForSdk[name],\n          isExcludedFromValidation,\n          specifiedVersion: hasSpecifiedExactVersion ? rawSpec : '',\n        });\n        return raw;\n      }\n      nativeModulesCount++;\n      return `${name}@${versionsForSdk[name]}`;\n    } else {\n      // Other packages are passed through unmodified.\n      othersCount++;\n      return raw;\n    }\n  });\n\n  const messages = getOperationLog({\n    othersCount,\n    nativeModulesCount,\n    sdkVersion,\n  });\n\n  return {\n    packages: versionedPackages,\n    messages,\n    excludedNativeModules,\n  };\n}\n\n/** Craft a set of messages regarding the install operations. */\nexport function getOperationLog({\n  nativeModulesCount,\n  sdkVersion,\n  othersCount,\n}: {\n  nativeModulesCount: number;\n  othersCount: number;\n  sdkVersion: string;\n}): string[] {\n  return [\n    nativeModulesCount > 0 &&\n      `${nativeModulesCount} SDK ${sdkVersion} compatible native ${\n        nativeModulesCount === 1 ? 'module' : 'modules'\n      }`,\n    othersCount > 0 && `${othersCount} other ${othersCount === 1 ? 'package' : 'packages'}`,\n  ].filter(Boolean) as string[];\n}\n"], "names": ["getCombinedKnownVersionsAsync", "getRemoteVersionsForSdkAsync", "getVersionedPackagesAsync", "getOperationLog", "debug", "require", "normalizeSdkVersionObject", "version", "relatedPackages", "facebookReactVersion", "facebookReactNativeVersion", "expoVersion", "reactVersion", "react", "undefined", "expoVersionIfAvailable", "expo", "projectRoot", "sdkVersion", "<PERSON><PERSON><PERSON>", "skipRemoteVersions", "hasExpoCanaryAsync", "Log", "warn", "env", "EXPO_NO_DEPENDENCY_VALIDATION", "bundledNativeModules", "getVersionedNativeModulesAsync", "versionsForSdk", "EXPO_OFFLINE", "sdkVersions", "getVersionsAsync", "Object", "keys", "join", "error", "CommandError", "code", "packages", "pkg", "nativeModulesCount", "othersCount", "excludedNativeModules", "versionedPackages", "map", "arg", "name", "type", "raw", "rawSpec", "npmPackageArg", "includes", "isExcludedFromValidation", "install", "exclude", "hasSpecifiedExactVersion", "push", "bundledNativeVersion", "specifiedVersion", "messages", "filter", "Boolean"], "mappings": "AAAA;;;;;;;;;;;IA0CsBA,6BAA6B,MAA7BA,6BAA6B;IAkC7BC,4BAA4B,MAA5BA,4BAA4B;IA+C5BC,yBAAyB,MAAzBA,yBAAyB;IAqE/BC,eAAe,MAAfA,eAAe;;;8DA/LL,iBAAiB;;;;;;sCAEI,wBAAwB;iCACpC,mBAAmB;6BACT,0BAA0B;qBACnD,cAAc;qBACd,oBAAoB;wBACX,uBAAuB;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,+CAA+C,CAChD,AAAsB,AAAC;AAIxB,4GAA4G,GAC5G,SAASC,yBAAyB,CAACC,OAAoB,EAA0B;IAC/E,IAAI,CAACA,OAAO,EAAE;QACZ,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,EAAEC,eAAe,CAAA,EAAEC,oBAAoB,CAAA,EAAEC,0BAA0B,CAAA,EAAEC,WAAW,CAAA,EAAE,GACtFJ,OAAO,AAAC;IAEV,MAAMK,YAAY,GAAGH,oBAAoB,GACrC;QACEI,KAAK,EAAEJ,oBAAoB;QAC3B,WAAW,EAAEA,oBAAoB;KAClC,GACDK,SAAS,AAAC;IAEd,MAAMC,sBAAsB,GAAGJ,WAAW,GAAG;QAAEK,IAAI,EAAEL,WAAW;KAAE,GAAGG,SAAS,AAAC;IAE/E,OAAO;QACL,GAAGN,eAAe;QAClB,GAAGI,YAAY;QACf,GAAGG,sBAAsB;QACzB,cAAc,EAAEL,0BAA0B;KAC3C,CAAC;AACJ,CAAC;AAGM,eAAeV,6BAA6B,CAAC,EAClDiB,WAAW,CAAA,EACXC,UAAU,CAAA,EACVC,SAAS,CAAA,EAKV,EAAE;IACD,MAAMC,kBAAkB,GAAG,MAAMC,IAAAA,gBAAkB,mBAAA,EAACJ,WAAW,CAAC,AAAC;IACjE,IAAIG,kBAAkB,EAAE;QACtBE,IAAG,IAAA,CAACC,IAAI,CAAC,0EAA0E,CAAC,CAAC;IACvF,CAAC;IAED,IAAIC,IAAG,IAAA,CAACC,6BAA6B,EAAE;QACrCrB,KAAK,CAAC,2EAA2E,CAAC,CAAC;QACnF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAMsB,oBAAoB,GAAGR,UAAU,GACnC,MAAMS,IAAAA,qBAA8B,+BAAA,EAACV,WAAW,EAAEC,UAAU,EAAE;QAAEE,kBAAkB;KAAE,CAAC,GACrF,EAAE,AAAC;IACP,MAAMQ,cAAc,GAAG,CAACR,kBAAkB,GACtC,MAAMnB,4BAA4B,CAAC;QAAEiB,UAAU;QAAEC,SAAS;KAAE,CAAC,GAC7D,EAAE,AAAC;IACP,OAAO;QACL,GAAGO,oBAAoB;QACvB,gFAAgF;QAChF,qFAAqF;QACrF,GAAGE,cAAc;KAClB,CAAC;AACJ,CAAC;AAGM,eAAe3B,4BAA4B,CAAC,EACjDiB,UAAU,CAAA,EACVC,SAAS,CAAA,EACoC,GAAG,EAAE,EAA2B;IAC7E,IAAIK,IAAG,IAAA,CAACK,YAAY,EAAE;QACpBP,IAAG,IAAA,CAACC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAChE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI;QACF,MAAM,EAAEO,WAAW,CAAA,EAAE,GAAG,MAAMC,IAAAA,YAAgB,iBAAA,EAAC;YAAEZ,SAAS;SAAE,CAAC,AAAC;QAE9D,uEAAuE;QACvE,IAAI,CAACD,UAAU,IAAI,CAAC,CAACA,UAAU,IAAIY,WAAW,CAAC,EAAE;YAC/C1B,KAAK,CACH,CAAC,mFAAmF,EAAEc,UAAU,CAAC,aAAa,EAAEc,MAAM,CAACC,IAAI,CACzHH,WAAW,CACZ,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAChB,CAAC;YACF,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM3B,OAAO,GAAGuB,WAAW,CAACZ,UAAU,CAA6B,AAAyB,AAAC;QAE7F,OAAOZ,yBAAyB,CAACC,OAAO,CAAC,CAAC;IAC5C,EAAE,OAAO4B,KAAK,EAAO;QACnB,IAAIA,KAAK,YAAYC,OAAY,aAAA,IAAID,KAAK,CAACE,IAAI,KAAK,SAAS,EAAE;YAC7D,OAAOpC,4BAA4B,CAAC;gBAAEiB,UAAU;gBAAEC,SAAS;aAAE,CAAC,CAAC;QACjE,CAAC;QACD,MAAMgB,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAgBM,eAAejC,yBAAyB,CAC7Ce,WAAmB,EACnB,EACEqB,QAAQ,CAAA,EACRpB,UAAU,CAAA,EACVqB,GAAG,CAAA,EAOJ,EAKA;IACD,MAAMX,cAAc,GAAG,MAAM5B,6BAA6B,CAAC;QACzDiB,WAAW;QACXC,UAAU;QACVC,SAAS,EAAE,IAAI;KAChB,CAAC,AAAC;IAEH,IAAIqB,kBAAkB,GAAG,CAAC,AAAC;IAC3B,IAAIC,WAAW,GAAG,CAAC,AAAC;IACpB,MAAMC,qBAAqB,GAA4B,EAAE,AAAC;IAE1D,MAAMC,iBAAiB,GAAGL,QAAQ,CAACM,GAAG,CAAC,CAACC,GAAG,GAAK;QAC9C,MAAM,EAAEC,IAAI,CAAA,EAAEC,IAAI,CAAA,EAAEC,GAAG,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGC,IAAAA,cAAa,EAAA,QAAA,EAACL,GAAG,CAAC,AAAC;QAExD,IAAI;YAAC,KAAK;YAAE,SAAS;YAAE,OAAO;SAAC,CAACM,QAAQ,CAACJ,IAAI,CAAC,IAAID,IAAI,IAAIlB,cAAc,CAACkB,IAAI,CAAC,EAAE;gBAG7CP,GAAS;YAF1C,gFAAgF;YAChF,4FAA4F;YAC5F,MAAMa,wBAAwB,GAAGb,GAAG,QAAM,GAATA,KAAAA,CAAS,GAATA,CAAAA,GAAS,GAATA,GAAG,CAAEvB,IAAI,SAAA,GAATuB,KAAAA,CAAS,GAATA,QAAAA,GAAS,CAAEc,OAAO,SAAT,GAATd,KAAAA,CAAS,GAATA,aAAoBe,OAAO,SAAlB,GAATf,KAAAA,CAAS,GAATA,KAA6BY,QAAQ,CAACL,IAAI,CAAC,AAAC;YAC7E,MAAMS,wBAAwB,GAAGN,OAAO,KAAK,EAAE,IAAIA,OAAO,KAAK,GAAG,AAAC;YACnE,IAAIG,wBAAwB,IAAIG,wBAAwB,EAAE;gBACxDd,WAAW,EAAE,CAAC;gBACdC,qBAAqB,CAACc,IAAI,CAAC;oBACzBV,IAAI;oBACJW,oBAAoB,EAAE7B,cAAc,CAACkB,IAAI,CAAC;oBAC1CM,wBAAwB;oBACxBM,gBAAgB,EAAEH,wBAAwB,GAAGN,OAAO,GAAG,EAAE;iBAC1D,CAAC,CAAC;gBACH,OAAOD,GAAG,CAAC;YACb,CAAC;YACDR,kBAAkB,EAAE,CAAC;YACrB,OAAO,CAAC,EAAEM,IAAI,CAAC,CAAC,EAAElB,cAAc,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,OAAO;YACL,gDAAgD;YAChDL,WAAW,EAAE,CAAC;YACd,OAAOO,GAAG,CAAC;QACb,CAAC;IACH,CAAC,CAAC,AAAC;IAEH,MAAMW,QAAQ,GAAGxD,eAAe,CAAC;QAC/BsC,WAAW;QACXD,kBAAkB;QAClBtB,UAAU;KACX,CAAC,AAAC;IAEH,OAAO;QACLoB,QAAQ,EAAEK,iBAAiB;QAC3BgB,QAAQ;QACRjB,qBAAqB;KACtB,CAAC;AACJ,CAAC;AAGM,SAASvC,eAAe,CAAC,EAC9BqC,kBAAkB,CAAA,EAClBtB,UAAU,CAAA,EACVuB,WAAW,CAAA,EAKZ,EAAY;IACX,OAAO;QACLD,kBAAkB,GAAG,CAAC,IACpB,CAAC,EAAEA,kBAAkB,CAAC,KAAK,EAAEtB,UAAU,CAAC,mBAAmB,EACzDsB,kBAAkB,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAChD,CAAC;QACJC,WAAW,GAAG,CAAC,IAAI,CAAC,EAAEA,WAAW,CAAC,OAAO,EAAEA,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC;KACxF,CAACmB,MAAM,CAACC,OAAO,CAAC,CAAa;AAChC,CAAC"}