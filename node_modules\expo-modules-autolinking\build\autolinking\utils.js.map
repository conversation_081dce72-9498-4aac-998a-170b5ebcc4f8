{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/autolinking/utils.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AAIxB,SAAgB,mCAAmC,CAAC,QAA2B;IAC7E,QAAQ,QAAQ,EAAE;QAChB,KAAK,KAAK,CAAC;QACX,KAAK,OAAO,CAAC;QACb,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,OAAO,CAAC,oBAAoB,CAAC,CAAC;QACvC,KAAK,SAAS;YACZ,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACzC,KAAK,UAAU;YACb,OAAO,OAAO,CAAC,uBAAuB,CAAC,CAAC;KAC3C;AACH,CAAC;AAZD,kFAYC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAAC,WAAmB,EAAE,WAAmB;IAC7E,8DAA8D;IAC9D,iEAAiE;IACjE,kEAAkE;IAClE,qDAAqD;IACrD,gDAAgD;IAChD,4DAA4D;IAC5D,+DAA+D;IAC/D,2CAA2C;IAC3C,MAAM,wBAAwB,GAAG,cAAI,CAAC,IAAI,CACxC,WAAW,EACX,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC;KAC/G,CAAC;IACF,MAAM,qBAAqB,GAAG,cAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,KAAK,cAAc,CAAC;IACzF,OAAO,qBAAqB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC;AACjE,CAAC;AAfD,wDAeC", "sourcesContent": ["import path from 'path';\n\nimport { SupportedPlatform } from '../types';\n\nexport function getLinkingImplementationForPlatform(platform: SupportedPlatform) {\n  switch (platform) {\n    case 'ios':\n    case 'macos':\n    case 'tvos':\n    case 'apple':\n      return require('../platforms/apple');\n    case 'android':\n      return require('../platforms/android');\n    case 'devtools':\n      return require('../platforms/devtools');\n  }\n}\n\n/**\n * Get the possible path to the pnpm isolated modules folder.\n */\nexport function getIsolatedModulesPath(packagePath: string, packageName: string): string | null {\n  // Check if the project is using isolated modules, by checking\n  // if the parent dir of `packagePath` is a `node_modules` folder.\n  // Isolated modules installs dependencies in small groups such as:\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/@expo/cli\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo\n  //   - /.pnpm/expo@50.x.x(...)/node_modules/expo-application\n  // When isolated modules are detected, expand the `searchPaths`\n  // to include possible nested dependencies.\n  const maybeIsolatedModulesPath = path.join(\n    packagePath,\n    packageName.startsWith('@') && packageName.includes('/') ? '../..' : '..' // scoped packages are nested deeper\n  );\n  const isIsolatedModulesPath = path.basename(maybeIsolatedModulesPath) === 'node_modules';\n  return isIsolatedModulesPath ? maybeIsolatedModulesPath : null;\n}\n"]}