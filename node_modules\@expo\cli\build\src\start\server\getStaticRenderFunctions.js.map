{"version": 3, "sources": ["../../../../src/start/server/getStaticRenderFunctions.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport fs from 'fs';\nimport path from 'path';\nimport requireString from 'require-from-string';\n\nimport { IS_METRO_BUNDLE_ERROR_SYMBOL, logMetroError } from './metro/metroErrorInterface';\nimport { createBundleUrlPath, ExpoMetroOptions } from './middleware/metroOptions';\nimport { augmentLogs } from './serverLogLikeMetro';\nimport { delayAsync } from '../../utils/delay';\nimport { SilentError } from '../../utils/errors';\nimport { profile } from '../../utils/profile';\n\n/** The list of input keys will become optional, everything else will remain the same. */\nexport type PickPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport const cachedSourceMaps: Map<string, { url: string; map: string }> = new Map();\n\n// Support unhandled rejections\n// Detect if running in Bun\n\n// @ts-expect-error: This is a global variable that is set by Bun.\nif (!process.isBun) {\n  require('source-map-support').install({\n    retrieveSourceMap(source: string) {\n      if (cachedSourceMaps.has(source)) {\n        return cachedSourceMaps.get(source);\n      }\n      return null;\n    },\n  });\n}\n\nasync function ensureFileInRootDirectory(projectRoot: string, otherFile: string) {\n  // Cannot be accessed using Metro's server API, we need to move the file\n  // into the project root and try again.\n  if (!path.relative(projectRoot, otherFile).startsWith('..' + path.sep)) {\n    return otherFile;\n  }\n\n  // Copy the file into the project to ensure it works in monorepos.\n  // This means the file cannot have any relative imports.\n  const tempDir = path.join(projectRoot, '.expo/static-tmp');\n  await fs.promises.mkdir(tempDir, { recursive: true });\n  const moduleId = path.join(tempDir, path.basename(otherFile));\n  await fs.promises.writeFile(moduleId, await fs.promises.readFile(otherFile, 'utf8'));\n  // Sleep to give watchman time to register the file.\n  await delayAsync(50);\n  return moduleId;\n}\n\nexport async function createMetroEndpointAsync(\n  projectRoot: string,\n  devServerUrl: string,\n  absoluteFilePath: string,\n  props: PickPartial<ExpoMetroOptions, 'mainModuleName' | 'bytecode'>\n): Promise<string> {\n  const root = getMetroServerRoot(projectRoot);\n  const safeOtherFile = await ensureFileInRootDirectory(projectRoot, absoluteFilePath);\n  const serverPath = path.relative(root, safeOtherFile).replace(/\\.[jt]sx?$/, '');\n\n  const urlFragment = createBundleUrlPath({\n    mainModuleName: serverPath,\n    lazy: false,\n    asyncRoutes: false,\n    inlineSourceMap: false,\n    engine: 'hermes',\n    minify: false,\n    bytecode: false,\n    ...props,\n  });\n\n  let url: string;\n  if (devServerUrl) {\n    url = new URL(urlFragment.replace(/^\\//, ''), devServerUrl).toString();\n  } else {\n    url = '/' + urlFragment.replace(/^\\/+/, '');\n  }\n  return url;\n}\n\nexport function evalMetroAndWrapFunctions<T = Record<string, any>>(\n  projectRoot: string,\n  script: string,\n  filename: string,\n  isExporting: boolean\n): T {\n  // TODO: Add back stack trace logic that hides traces from metro-runtime and other internal modules.\n  const contents = evalMetroNoHandling(projectRoot, script, filename);\n\n  if (!contents) {\n    // This can happen if ErrorUtils isn't working correctly on web and failing to throw an error when a module throws.\n    // This is unexpected behavior and should not be pretty formatted, therefore we're avoiding CommandError.\n    throw new Error(\n      '[Expo SSR] Module returned undefined, this could be due to a misconfiguration in Metro error handling'\n    );\n  }\n  // wrap each function with a try/catch that uses Metro's error formatter\n  return Object.keys(contents).reduce((acc, key) => {\n    const fn = contents[key];\n    if (typeof fn !== 'function') {\n      return { ...acc, [key]: fn };\n    }\n\n    acc[key] = async function (...props: any[]) {\n      try {\n        return await fn.apply(this, props);\n      } catch (error: any) {\n        await logMetroError(projectRoot, { error });\n\n        if (isExporting || error[IS_METRO_BUNDLE_ERROR_SYMBOL]) {\n          throw error;\n        } else {\n          // TODO: When does this happen?\n          throw new SilentError(error);\n        }\n      }\n    };\n    return acc;\n  }, {} as any);\n}\n\nexport function evalMetroNoHandling(projectRoot: string, src: string, filename: string) {\n  augmentLogs(projectRoot);\n\n  return profile(requireString, 'eval-metro-bundle')(src, filename);\n}\n"], "names": ["cachedSourceMaps", "createMetroEndpointAsync", "evalMetroAndWrapFunctions", "evalMetroNoHandling", "Map", "process", "isBun", "require", "install", "retrieveSourceMap", "source", "has", "get", "ensureFileInRootDirectory", "projectRoot", "otherFile", "path", "relative", "startsWith", "sep", "tempDir", "join", "fs", "promises", "mkdir", "recursive", "moduleId", "basename", "writeFile", "readFile", "delayAsync", "devServerUrl", "absoluteFilePath", "props", "root", "getMetroServerRoot", "safeOtherFile", "serverPath", "replace", "urlFragment", "createBundleUrlPath", "mainModuleName", "lazy", "asyncRoutes", "inlineSourceMap", "engine", "minify", "bytecode", "url", "URL", "toString", "script", "filename", "isExporting", "contents", "Error", "Object", "keys", "reduce", "acc", "key", "fn", "apply", "error", "logMetroError", "IS_METRO_BUNDLE_ERROR_SYMBOL", "SilentError", "src", "augmentLogs", "profile", "requireString"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAeaA,gBAAgB,MAAhBA,gBAAgB;IAmCPC,wBAAwB,MAAxBA,wBAAwB;IA8B9BC,yBAAyB,MAAzBA,yBAAyB;IAyCzBC,mBAAmB,MAAnBA,mBAAmB;;;yBAzHA,oBAAoB;;;;;;;8DACxC,IAAI;;;;;;;8DACF,MAAM;;;;;;;8DACG,qBAAqB;;;;;;qCAEa,6BAA6B;8BACnC,2BAA2B;oCACrD,sBAAsB;uBACvB,mBAAmB;wBAClB,oBAAoB;yBACxB,qBAAqB;;;;;;AAKtC,MAAMH,gBAAgB,GAA8C,IAAII,GAAG,EAAE,AAAC;AAErF,+BAA+B;AAC/B,2BAA2B;AAE3B,kEAAkE;AAClE,IAAI,CAACC,OAAO,CAACC,KAAK,EAAE;IAClBC,OAAO,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAAC;QACpCC,iBAAiB,EAACC,MAAc,EAAE;YAChC,IAAIV,gBAAgB,CAACW,GAAG,CAACD,MAAM,CAAC,EAAE;gBAChC,OAAOV,gBAAgB,CAACY,GAAG,CAACF,MAAM,CAAC,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED,eAAeG,yBAAyB,CAACC,WAAmB,EAAEC,SAAiB,EAAE;IAC/E,wEAAwE;IACxE,uCAAuC;IACvC,IAAI,CAACC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACH,WAAW,EAAEC,SAAS,CAAC,CAACG,UAAU,CAAC,IAAI,GAAGF,KAAI,EAAA,QAAA,CAACG,GAAG,CAAC,EAAE;QACtE,OAAOJ,SAAS,CAAC;IACnB,CAAC;IAED,kEAAkE;IAClE,wDAAwD;IACxD,MAAMK,OAAO,GAAGJ,KAAI,EAAA,QAAA,CAACK,IAAI,CAACP,WAAW,EAAE,kBAAkB,CAAC,AAAC;IAC3D,MAAMQ,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACJ,OAAO,EAAE;QAAEK,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IACtD,MAAMC,QAAQ,GAAGV,KAAI,EAAA,QAAA,CAACK,IAAI,CAACD,OAAO,EAAEJ,KAAI,EAAA,QAAA,CAACW,QAAQ,CAACZ,SAAS,CAAC,CAAC,AAAC;IAC9D,MAAMO,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACK,SAAS,CAACF,QAAQ,EAAE,MAAMJ,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACM,QAAQ,CAACd,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IACrF,oDAAoD;IACpD,MAAMe,IAAAA,MAAU,WAAA,EAAC,EAAE,CAAC,CAAC;IACrB,OAAOJ,QAAQ,CAAC;AAClB,CAAC;AAEM,eAAezB,wBAAwB,CAC5Ca,WAAmB,EACnBiB,YAAoB,EACpBC,gBAAwB,EACxBC,KAAmE,EAClD;IACjB,MAAMC,IAAI,GAAGC,IAAAA,MAAkB,EAAA,mBAAA,EAACrB,WAAW,CAAC,AAAC;IAC7C,MAAMsB,aAAa,GAAG,MAAMvB,yBAAyB,CAACC,WAAW,EAAEkB,gBAAgB,CAAC,AAAC;IACrF,MAAMK,UAAU,GAAGrB,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACiB,IAAI,EAAEE,aAAa,CAAC,CAACE,OAAO,eAAe,EAAE,CAAC,AAAC;IAEhF,MAAMC,WAAW,GAAGC,IAAAA,aAAmB,oBAAA,EAAC;QACtCC,cAAc,EAAEJ,UAAU;QAC1BK,IAAI,EAAE,KAAK;QACXC,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE,KAAK;QACtBC,MAAM,EAAE,QAAQ;QAChBC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE,KAAK;QACf,GAAGd,KAAK;KACT,CAAC,AAAC;IAEH,IAAIe,GAAG,AAAQ,AAAC;IAChB,IAAIjB,YAAY,EAAE;QAChBiB,GAAG,GAAG,IAAIC,GAAG,CAACV,WAAW,CAACD,OAAO,QAAQ,EAAE,CAAC,EAAEP,YAAY,CAAC,CAACmB,QAAQ,EAAE,CAAC;IACzE,OAAO;QACLF,GAAG,GAAG,GAAG,GAAGT,WAAW,CAACD,OAAO,SAAS,EAAE,CAAC,CAAC;IAC9C,CAAC;IACD,OAAOU,GAAG,CAAC;AACb,CAAC;AAEM,SAAS9C,yBAAyB,CACvCY,WAAmB,EACnBqC,MAAc,EACdC,QAAgB,EAChBC,WAAoB,EACjB;IACH,oGAAoG;IACpG,MAAMC,QAAQ,GAAGnD,mBAAmB,CAACW,WAAW,EAAEqC,MAAM,EAAEC,QAAQ,CAAC,AAAC;IAEpE,IAAI,CAACE,QAAQ,EAAE;QACb,mHAAmH;QACnH,yGAAyG;QACzG,MAAM,IAAIC,KAAK,CACb,uGAAuG,CACxG,CAAC;IACJ,CAAC;IACD,wEAAwE;IACxE,OAAOC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,GAAK;QAChD,MAAMC,EAAE,GAAGP,QAAQ,CAACM,GAAG,CAAC,AAAC;QACzB,IAAI,OAAOC,EAAE,KAAK,UAAU,EAAE;YAC5B,OAAO;gBAAE,GAAGF,GAAG;gBAAE,CAACC,GAAG,CAAC,EAAEC,EAAE;aAAE,CAAC;QAC/B,CAAC;QAEDF,GAAG,CAACC,GAAG,CAAC,GAAG,eAAgB,GAAG3B,KAAK,AAAO,EAAE;YAC1C,IAAI;gBACF,OAAO,MAAM4B,EAAE,CAACC,KAAK,CAAC,IAAI,EAAE7B,KAAK,CAAC,CAAC;YACrC,EAAE,OAAO8B,KAAK,EAAO;gBACnB,MAAMC,IAAAA,oBAAa,cAAA,EAAClD,WAAW,EAAE;oBAAEiD,KAAK;iBAAE,CAAC,CAAC;gBAE5C,IAAIV,WAAW,IAAIU,KAAK,CAACE,oBAA4B,6BAAA,CAAC,EAAE;oBACtD,MAAMF,KAAK,CAAC;gBACd,OAAO;oBACL,+BAA+B;oBAC/B,MAAM,IAAIG,OAAW,YAAA,CAACH,KAAK,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QACF,OAAOJ,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAQ,CAAC;AAChB,CAAC;AAEM,SAASxD,mBAAmB,CAACW,WAAmB,EAAEqD,GAAW,EAAEf,QAAgB,EAAE;IACtFgB,IAAAA,mBAAW,YAAA,EAACtD,WAAW,CAAC,CAAC;IAEzB,OAAOuD,IAAAA,QAAO,QAAA,EAACC,kBAAa,EAAA,QAAA,EAAE,mBAAmB,CAAC,CAACH,GAAG,EAAEf,QAAQ,CAAC,CAAC;AACpE,CAAC"}