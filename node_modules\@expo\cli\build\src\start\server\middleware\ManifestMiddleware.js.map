{"version": 3, "sources": ["../../../../../src/start/server/middleware/ManifestMiddleware.ts"], "sourcesContent": ["import {\n  ExpoConfig,\n  ExpoGoConfig,\n  getConfig,\n  PackageJSONConfig,\n  ProjectConfig,\n} from '@expo/config';\nimport { resolveEntryPoint, getMetroServerRoot } from '@expo/config/paths';\nimport path from 'path';\nimport { resolve } from 'url';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  shouldEnableAsyncImports,\n  createBundleUrlPath,\n  getBaseUrlFromExpoConfig,\n  getAsyncRoutesFromExpoConfig,\n  createBundleUrlPathFromExpoConfig,\n  convertPathToModuleSpecifier,\n} from './metroOptions';\nimport { resolveGoogleServicesFile, resolveManifestAssets } from './resolveAssets';\nimport { parsePlatformHeader, RuntimePlatform } from './resolvePlatform';\nimport { ServerHeaders, ServerNext, ServerRequest, ServerResponse } from './server.types';\nimport { isEnableHermesManaged } from '../../../export/exportHermes';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { stripExtension } from '../../../utils/url';\nimport * as ProjectDevices from '../../project/devices';\nimport { UrlCreator } from '../UrlCreator';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\nimport { getPlatformBundlers, PlatformBundlers } from '../platformBundlers';\nimport { createTemplateHtmlFromExpoConfigAsync } from '../webTemplate';\n\nconst debug = require('debug')('expo:start:server:middleware:manifest') as typeof console.log;\n\nconst supportedPlatforms = ['ios', 'android', 'web', 'none'];\n\nexport function getEntryWithServerRoot(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n) {\n  if (!supportedPlatforms.includes(props.platform)) {\n    throw new CommandError(\n      `Failed to resolve the project's entry file: The platform \"${props.platform}\" is not supported.`\n    );\n  }\n  return convertPathToModuleSpecifier(\n    path.relative(getMetroServerRoot(projectRoot), resolveEntryPoint(projectRoot, props))\n  );\n}\n\n/** Get the main entry module ID (file) relative to the project root. */\nexport function resolveMainModuleName(\n  projectRoot: string,\n  props: { platform: string; pkg?: PackageJSONConfig }\n): string {\n  const entryPoint = getEntryWithServerRoot(projectRoot, props);\n\n  debug(`Resolved entry point: ${entryPoint} (project root: ${projectRoot})`);\n\n  return convertPathToModuleSpecifier(stripExtension(entryPoint, 'js'));\n}\n\n/** Info about the computer hosting the dev server. */\nexport interface HostInfo {\n  host: string;\n  server: 'expo';\n  serverVersion: string;\n  serverDriver: string | null;\n  serverOS: NodeJS.Platform;\n  serverOSVersion: string;\n}\n\n/** Parsed values from the supported request headers. */\nexport interface ManifestRequestInfo {\n  /** Platform to serve. */\n  platform: RuntimePlatform;\n  /** Requested host name. */\n  hostname?: string | null;\n  /** The protocol used to request the manifest */\n  protocol?: 'http' | 'https';\n}\n\n/** Project related info. */\nexport type ResponseProjectSettings = {\n  expoGoConfig: ExpoGoConfig;\n  hostUri: string;\n  bundleUrl: string;\n  exp: ExpoConfig;\n};\n\nexport const DEVELOPER_TOOL = 'expo-cli';\n\nexport type ManifestMiddlewareOptions = {\n  /** Should start the dev servers in development mode (minify). */\n  mode?: 'development' | 'production';\n  /** Should instruct the bundler to create minified bundles. */\n  minify?: boolean;\n  constructUrl: UrlCreator['constructUrl'];\n  isNativeWebpack?: boolean;\n  privateKeyPath?: string;\n};\n\n/** Base middleware creator for serving the Expo manifest (like the index.html but for native runtimes). */\nexport abstract class ManifestMiddleware<\n  TManifestRequestInfo extends ManifestRequestInfo,\n> extends ExpoMiddleware {\n  private initialProjectConfig: ProjectConfig;\n  private platformBundlers: PlatformBundlers;\n\n  constructor(\n    protected projectRoot: string,\n    protected options: ManifestMiddlewareOptions\n  ) {\n    super(\n      projectRoot,\n      /**\n       * Only support `/`, `/manifest`, `/index.exp` for the manifest middleware.\n       */\n      ['/', '/manifest', '/index.exp']\n    );\n    this.initialProjectConfig = getConfig(projectRoot);\n    this.platformBundlers = getPlatformBundlers(projectRoot, this.initialProjectConfig.exp);\n  }\n\n  /** Exposed for testing. */\n  public async _resolveProjectSettingsAsync({\n    platform,\n    hostname,\n    protocol,\n  }: Pick<\n    TManifestRequestInfo,\n    'hostname' | 'platform' | 'protocol'\n  >): Promise<ResponseProjectSettings> {\n    // Read the config\n    const projectConfig = getConfig(this.projectRoot);\n\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: projectConfig.pkg,\n      platform,\n    });\n\n    const isHermesEnabled = isEnableHermesManaged(projectConfig.exp, platform);\n\n    // Create the manifest and set fields within it\n    const expoGoConfig = this.getExpoGoConfig({\n      mainModuleName,\n      hostname,\n    });\n\n    const hostUri = this.options.constructUrl({ scheme: '', hostname });\n\n    const bundleUrl = this._getBundleUrl({\n      platform,\n      mainModuleName,\n      hostname,\n      engine: isHermesEnabled ? 'hermes' : undefined,\n      baseUrl: getBaseUrlFromExpoConfig(projectConfig.exp),\n      asyncRoutes: getAsyncRoutesFromExpoConfig(\n        projectConfig.exp,\n        this.options.mode ?? 'development',\n        platform\n      ),\n      routerRoot: getRouterDirectoryModuleIdWithManifest(this.projectRoot, projectConfig.exp),\n      protocol,\n      reactCompiler: !!projectConfig.exp.experiments?.reactCompiler,\n    });\n\n    // Resolve all assets and set them on the manifest as URLs\n    await this.mutateManifestWithAssetsAsync(projectConfig.exp, bundleUrl);\n\n    return {\n      expoGoConfig,\n      hostUri,\n      bundleUrl,\n      exp: projectConfig.exp,\n    };\n  }\n\n  /** Get the main entry module ID (file) relative to the project root. */\n  private resolveMainModuleName(props: { pkg: PackageJSONConfig; platform: string }): string {\n    let entryPoint = getEntryWithServerRoot(this.projectRoot, props);\n\n    debug(`Resolved entry point: ${entryPoint} (project root: ${this.projectRoot})`);\n\n    // NOTE(Bacon): Webpack is currently hardcoded to index.bundle on native\n    // in the future (TODO) we should move this logic into a Webpack plugin and use\n    // a generated file name like we do on web.\n    // const server = getDefaultDevServer();\n    // // TODO: Move this into BundlerDevServer and read this info from self.\n    // const isNativeWebpack = server instanceof WebpackBundlerDevServer && server.isTargetingNative();\n    if (this.options.isNativeWebpack) {\n      entryPoint = 'index.js';\n    }\n\n    return stripExtension(entryPoint, 'js');\n  }\n\n  /** Parse request headers into options. */\n  public abstract getParsedHeaders(req: ServerRequest): TManifestRequestInfo;\n\n  /** Store device IDs that were sent in the request headers. */\n  private async saveDevicesAsync(req: ServerRequest) {\n    const deviceIds = req.headers?.['expo-dev-client-id'];\n    if (deviceIds) {\n      await ProjectDevices.saveDevicesAsync(this.projectRoot, deviceIds).catch((e) =>\n        Log.exception(e)\n      );\n    }\n  }\n\n  /** Create the bundle URL (points to the single JS entry file). Exposed for testing. */\n  public _getBundleUrl({\n    platform,\n    mainModuleName,\n    hostname,\n    engine,\n    baseUrl,\n    isExporting,\n    asyncRoutes,\n    routerRoot,\n    protocol,\n    reactCompiler,\n  }: {\n    platform: string;\n    hostname?: string | null;\n    mainModuleName: string;\n    engine?: 'hermes';\n    baseUrl?: string;\n    asyncRoutes: boolean;\n    isExporting?: boolean;\n    routerRoot: string;\n    protocol?: 'http' | 'https';\n    reactCompiler: boolean;\n  }): string {\n    const path = createBundleUrlPath({\n      mode: this.options.mode ?? 'development',\n      minify: this.options.minify,\n      platform,\n      mainModuleName,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      engine,\n      bytecode: engine === 'hermes',\n      baseUrl,\n      isExporting: !!isExporting,\n      asyncRoutes,\n      routerRoot,\n      reactCompiler,\n    });\n\n    return (\n      this.options.constructUrl({\n        scheme: protocol ?? 'http',\n        // hostType: this.options.location.hostType,\n        hostname,\n      }) + path\n    );\n  }\n\n  /** Get the manifest response to return to the runtime. This file contains info regarding where the assets can be loaded from. Exposed for testing. */\n  public abstract _getManifestResponseAsync(options: TManifestRequestInfo): Promise<{\n    body: string;\n    version: string;\n    headers: ServerHeaders;\n  }>;\n\n  private getExpoGoConfig({\n    mainModuleName,\n    hostname,\n  }: {\n    mainModuleName: string;\n    hostname?: string | null;\n  }): ExpoGoConfig {\n    return {\n      // localhost:8081\n      debuggerHost: this.options.constructUrl({ scheme: '', hostname }),\n      // Required for Expo Go to function.\n      developer: {\n        tool: DEVELOPER_TOOL,\n        projectRoot: this.projectRoot,\n      },\n      packagerOpts: {\n        // Required for dev client.\n        dev: this.options.mode !== 'production',\n      },\n      // Indicates the name of the main bundle.\n      mainModuleName,\n      // Add this string to make Flipper register React Native / Metro as \"running\".\n      // Can be tested by running:\n      // `METRO_SERVER_PORT=8081 open -a flipper.app`\n      // Where 8081 is the port where the Expo project is being hosted.\n      __flipperHack: 'React Native packager is running',\n    };\n  }\n\n  /** Resolve all assets and set them on the manifest as URLs */\n  private async mutateManifestWithAssetsAsync(manifest: ExpoConfig, bundleUrl: string) {\n    await resolveManifestAssets(this.projectRoot, {\n      manifest,\n      resolver: async (path) => {\n        if (this.options.isNativeWebpack) {\n          // When using our custom dev server, just do assets normally\n          // without the `assets/` subpath redirect.\n          return resolve(bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0], path);\n        }\n        return bundleUrl!.match(/^https?:\\/\\/.*?\\//)![0] + 'assets/' + path;\n      },\n    });\n    // The server normally inserts this but if we're offline we'll do it here\n    await resolveGoogleServicesFile(this.projectRoot, manifest);\n  }\n\n  public getWebBundleUrl() {\n    const platform = 'web';\n    // Read from headers\n    const mainModuleName = this.resolveMainModuleName({\n      pkg: this.initialProjectConfig.pkg,\n      platform,\n    });\n\n    return createBundleUrlPathFromExpoConfig(this.projectRoot, this.initialProjectConfig.exp, {\n      platform,\n      mainModuleName,\n      minify: this.options.minify,\n      lazy: shouldEnableAsyncImports(this.projectRoot),\n      mode: this.options.mode ?? 'development',\n      // Hermes doesn't support more modern JS features than most, if not all, modern browser.\n      engine: 'hermes',\n      isExporting: false,\n      bytecode: false,\n    });\n  }\n\n  /**\n   * Web platforms should create an index.html response using the same script resolution as native.\n   *\n   * Instead of adding a `bundleUrl` to a `manifest.json` (native) we'll add a `<script src=\"\">`\n   * to an `index.html`, this enables the web platform to load JavaScript from the server.\n   */\n  private async handleWebRequestAsync(req: ServerRequest, res: ServerResponse) {\n    res.setHeader('Content-Type', 'text/html');\n\n    res.end(await this.getSingleHtmlTemplateAsync());\n  }\n\n  getSingleHtmlTemplateAsync() {\n    // Read from headers\n    const bundleUrl = this.getWebBundleUrl();\n\n    return createTemplateHtmlFromExpoConfigAsync(this.projectRoot, {\n      exp: this.initialProjectConfig.exp,\n      scripts: [bundleUrl],\n    });\n  }\n\n  /** Exposed for testing. */\n  async checkBrowserRequestAsync(req: ServerRequest, res: ServerResponse, next: ServerNext) {\n    if (\n      this.platformBundlers.web === 'metro' &&\n      this.initialProjectConfig.exp.platforms?.includes('web')\n    ) {\n      // NOTE(EvanBacon): This effectively disables the safety check we do on custom runtimes to ensure\n      // the `expo-platform` header is included. When `web.bundler=web`, if the user has non-standard Expo\n      // code loading then they'll get a web bundle without a clear assertion of platform support.\n      const platform = parsePlatformHeader(req);\n      // On web, serve the public folder\n      if (!platform || platform === 'web') {\n        if (['static', 'server'].includes(this.initialProjectConfig.exp.web?.output ?? '')) {\n          // Skip the spa-styled index.html when static generation is enabled.\n          next();\n          return true;\n        } else {\n          await this.handleWebRequestAsync(req, res);\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  async handleRequestAsync(\n    req: ServerRequest,\n    res: ServerResponse,\n    next: ServerNext\n  ): Promise<void> {\n    // First check for standard JavaScript runtimes (aka legacy browsers like Chrome).\n    if (await this.checkBrowserRequestAsync(req, res, next)) {\n      return;\n    }\n\n    // Save device IDs for dev client.\n    await this.saveDevicesAsync(req);\n\n    // Read from headers\n    const options = this.getParsedHeaders(req);\n    const { body, headers } = await this._getManifestResponseAsync(options);\n    for (const [headerName, headerValue] of headers) {\n      res.setHeader(headerName, headerValue);\n    }\n    res.end(body);\n  }\n}\n"], "names": ["getEntryWithServerRoot", "resolveMainModuleName", "DEVELOPER_TOOL", "ManifestMiddleware", "debug", "require", "supportedPlatforms", "projectRoot", "props", "includes", "platform", "CommandError", "convertPathToModuleSpecifier", "path", "relative", "getMetroServerRoot", "resolveEntryPoint", "entryPoint", "stripExtension", "ExpoMiddleware", "constructor", "options", "initialProjectConfig", "getConfig", "platformBundlers", "getPlatformBundlers", "exp", "_resolveProjectSettingsAsync", "hostname", "protocol", "projectConfig", "mainModuleName", "pkg", "isHermesEnabled", "isEnableHermesManaged", "expoGoConfig", "getExpoGoConfig", "hostUri", "constructUrl", "scheme", "bundleUrl", "_getBundleUrl", "engine", "undefined", "baseUrl", "getBaseUrlFromExpoConfig", "asyncRoutes", "getAsyncRoutesFromExpoConfig", "mode", "routerRoot", "getRouterDirectoryModuleIdWithManifest", "reactCompiler", "experiments", "mutateManifestWithAssetsAsync", "isNativeWebpack", "saveDevicesAsync", "req", "deviceIds", "headers", "ProjectDevices", "catch", "e", "Log", "exception", "isExporting", "createBundleUrlPath", "minify", "lazy", "shouldEnableAsyncImports", "bytecode", "debuggerHost", "developer", "tool", "packagerOpts", "dev", "__flipperHack", "manifest", "resolveManifestAssets", "resolver", "resolve", "match", "resolveGoogleServicesFile", "getWebBundleUrl", "createBundleUrlPathFromExpoConfig", "handleWebRequestAsync", "res", "<PERSON><PERSON><PERSON><PERSON>", "end", "getSingleHtmlTemplateAsync", "createTemplateHtmlFromExpoConfigAsync", "scripts", "checkBrowserRequestAsync", "next", "web", "platforms", "parsePlatformHeader", "output", "handleRequestAsync", "getParsedHeaders", "body", "_getManifestResponseAsync", "headerName", "headerValue"], "mappings": "AAAA;;;;;;;;;;;IAqCgBA,sBAAsB,MAAtBA,sBAAsB;IAetBC,qBAAqB,MAArBA,qBAAqB;IAuCxBC,cAAc,MAAdA,cAAc;IAaLC,kBAAkB,MAAlBA,kBAAkB;;;yBAlGjC,cAAc;;;;;;;yBACiC,oBAAoB;;;;;;;8DACzD,MAAM;;;;;;;yBACC,KAAK;;;;;;gCAEE,kBAAkB;8BAQ1C,gBAAgB;+BAC0C,iBAAiB;iCAC7B,mBAAmB;8BAElC,8BAA8B;2DAC/C,cAAc;wBACN,uBAAuB;sBACrB,oBAAoB;+DACnB,uBAAuB;wBAEA,iBAAiB;kCAClB,qBAAqB;6BACrB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,uCAAuC,CAAC,AAAsB,AAAC;AAE9F,MAAMC,kBAAkB,GAAG;IAAC,KAAK;IAAE,SAAS;IAAE,KAAK;IAAE,MAAM;CAAC,AAAC;AAEtD,SAASN,sBAAsB,CACpCO,WAAmB,EACnBC,KAAoD,EACpD;IACA,IAAI,CAACF,kBAAkB,CAACG,QAAQ,CAACD,KAAK,CAACE,QAAQ,CAAC,EAAE;QAChD,MAAM,IAAIC,OAAY,aAAA,CACpB,CAAC,0DAA0D,EAAEH,KAAK,CAACE,QAAQ,CAAC,mBAAmB,CAAC,CACjG,CAAC;IACJ,CAAC;IACD,OAAOE,IAAAA,aAA4B,6BAAA,EACjCC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACC,IAAAA,MAAkB,EAAA,mBAAA,EAACR,WAAW,CAAC,EAAES,IAAAA,MAAiB,EAAA,kBAAA,EAACT,WAAW,EAAEC,KAAK,CAAC,CAAC,CACtF,CAAC;AACJ,CAAC;AAGM,SAASP,qBAAqB,CACnCM,WAAmB,EACnBC,KAAoD,EAC5C;IACR,MAAMS,UAAU,GAAGjB,sBAAsB,CAACO,WAAW,EAAEC,KAAK,CAAC,AAAC;IAE9DJ,KAAK,CAAC,CAAC,sBAAsB,EAAEa,UAAU,CAAC,gBAAgB,EAAEV,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5E,OAAOK,IAAAA,aAA4B,6BAAA,EAACM,IAAAA,KAAc,eAAA,EAACD,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;AACxE,CAAC;AA8BM,MAAMf,cAAc,GAAG,UAAU,AAAC;AAalC,MAAeC,kBAAkB,SAE9BgB,eAAc,eAAA;IAItBC,YACYb,WAAmB,EACnBc,OAAkC,CAC5C;QACA,KAAK,CACHd,WAAW,EACX;;OAEC,GACD;YAAC,GAAG;YAAE,WAAW;YAAE,YAAY;SAAC,CACjC,CAAC;QATQA,mBAAAA,WAAmB,CAAA;QACnBc,eAAAA,OAAkC,CAAA;QAS5C,IAAI,CAACC,oBAAoB,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAChB,WAAW,CAAC,CAAC;QACnD,IAAI,CAACiB,gBAAgB,GAAGC,IAAAA,iBAAmB,oBAAA,EAAClB,WAAW,EAAE,IAAI,CAACe,oBAAoB,CAACI,GAAG,CAAC,CAAC;IAC1F;IAEA,yBAAyB,SACZC,4BAA4B,CAAC,EACxCjB,QAAQ,CAAA,EACRkB,QAAQ,CAAA,EACRC,QAAQ,CAAA,EAIT,EAAoC;YAiChBC,GAA6B;QAhChD,kBAAkB;QAClB,MAAMA,aAAa,GAAGP,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAAChB,WAAW,CAAC,AAAC;QAElD,oBAAoB;QACpB,MAAMwB,cAAc,GAAG,IAAI,CAAC9B,qBAAqB,CAAC;YAChD+B,GAAG,EAAEF,aAAa,CAACE,GAAG;YACtBtB,QAAQ;SACT,CAAC,AAAC;QAEH,MAAMuB,eAAe,GAAGC,IAAAA,aAAqB,sBAAA,EAACJ,aAAa,CAACJ,GAAG,EAAEhB,QAAQ,CAAC,AAAC;QAE3E,+CAA+C;QAC/C,MAAMyB,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC;YACxCL,cAAc;YACdH,QAAQ;SACT,CAAC,AAAC;QAEH,MAAMS,OAAO,GAAG,IAAI,CAAChB,OAAO,CAACiB,YAAY,CAAC;YAAEC,MAAM,EAAE,EAAE;YAAEX,QAAQ;SAAE,CAAC,AAAC;QAEpE,MAAMY,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC;YACnC/B,QAAQ;YACRqB,cAAc;YACdH,QAAQ;YACRc,MAAM,EAAET,eAAe,GAAG,QAAQ,GAAGU,SAAS;YAC9CC,OAAO,EAAEC,IAAAA,aAAwB,yBAAA,EAACf,aAAa,CAACJ,GAAG,CAAC;YACpDoB,WAAW,EAAEC,IAAAA,aAA4B,6BAAA,EACvCjB,aAAa,CAACJ,GAAG,EACjB,IAAI,CAACL,OAAO,CAAC2B,IAAI,IAAI,aAAa,EAClCtC,QAAQ,CACT;YACDuC,UAAU,EAAEC,IAAAA,OAAsC,uCAAA,EAAC,IAAI,CAAC3C,WAAW,EAAEuB,aAAa,CAACJ,GAAG,CAAC;YACvFG,QAAQ;YACRsB,aAAa,EAAE,CAAC,CAACrB,CAAAA,CAAAA,GAA6B,GAA7BA,aAAa,CAACJ,GAAG,CAAC0B,WAAW,SAAe,GAA5CtB,KAAAA,CAA4C,GAA5CA,GAA6B,CAAEqB,aAAa,CAAA;SAC9D,CAAC,AAAC;QAEH,0DAA0D;QAC1D,MAAM,IAAI,CAACE,6BAA6B,CAACvB,aAAa,CAACJ,GAAG,EAAEc,SAAS,CAAC,CAAC;QAEvE,OAAO;YACLL,YAAY;YACZE,OAAO;YACPG,SAAS;YACTd,GAAG,EAAEI,aAAa,CAACJ,GAAG;SACvB,CAAC;IACJ;IAEA,sEAAsE,GAC9DzB,qBAAqB,CAACO,KAAmD,EAAU;QACzF,IAAIS,UAAU,GAAGjB,sBAAsB,CAAC,IAAI,CAACO,WAAW,EAAEC,KAAK,CAAC,AAAC;QAEjEJ,KAAK,CAAC,CAAC,sBAAsB,EAAEa,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,wEAAwE;QACxE,+EAA+E;QAC/E,2CAA2C;QAC3C,wCAAwC;QACxC,yEAAyE;QACzE,mGAAmG;QACnG,IAAI,IAAI,CAACc,OAAO,CAACiC,eAAe,EAAE;YAChCrC,UAAU,GAAG,UAAU,CAAC;QAC1B,CAAC;QAED,OAAOC,IAAAA,KAAc,eAAA,EAACD,UAAU,EAAE,IAAI,CAAC,CAAC;IAC1C;IAKA,4DAA4D,SAC9CsC,gBAAgB,CAACC,GAAkB,EAAE;YAC/BA,GAAW;QAA7B,MAAMC,SAAS,GAAGD,CAAAA,GAAW,GAAXA,GAAG,CAACE,OAAO,SAAwB,GAAnCF,KAAAA,CAAmC,GAAnCA,GAAW,AAAE,CAAC,oBAAoB,CAAC,AAAC;QACtD,IAAIC,SAAS,EAAE;YACb,MAAME,QAAc,CAACJ,gBAAgB,CAAC,IAAI,CAAChD,WAAW,EAAEkD,SAAS,CAAC,CAACG,KAAK,CAAC,CAACC,CAAC,GACzEC,IAAG,CAACC,SAAS,CAACF,CAAC,CAAC,CACjB,CAAC;QACJ,CAAC;IACH;IAEA,qFAAqF,GAC9EpB,aAAa,CAAC,EACnB/B,QAAQ,CAAA,EACRqB,cAAc,CAAA,EACdH,QAAQ,CAAA,EACRc,MAAM,CAAA,EACNE,OAAO,CAAA,EACPoB,WAAW,CAAA,EACXlB,WAAW,CAAA,EACXG,UAAU,CAAA,EACVpB,QAAQ,CAAA,EACRsB,aAAa,CAAA,EAYd,EAAU;QACT,MAAMtC,IAAI,GAAGoD,IAAAA,aAAmB,oBAAA,EAAC;YAC/BjB,IAAI,EAAE,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,IAAI,aAAa;YACxCkB,MAAM,EAAE,IAAI,CAAC7C,OAAO,CAAC6C,MAAM;YAC3BxD,QAAQ;YACRqB,cAAc;YACdoC,IAAI,EAAEC,IAAAA,aAAwB,yBAAA,EAAC,IAAI,CAAC7D,WAAW,CAAC;YAChDmC,MAAM;YACN2B,QAAQ,EAAE3B,MAAM,KAAK,QAAQ;YAC7BE,OAAO;YACPoB,WAAW,EAAE,CAAC,CAACA,WAAW;YAC1BlB,WAAW;YACXG,UAAU;YACVE,aAAa;SACd,CAAC,AAAC;QAEH,OACE,IAAI,CAAC9B,OAAO,CAACiB,YAAY,CAAC;YACxBC,MAAM,EAAEV,QAAQ,IAAI,MAAM;YAC1B,4CAA4C;YAC5CD,QAAQ;SACT,CAAC,GAAGf,IAAI,CACT;IACJ;IASQuB,eAAe,CAAC,EACtBL,cAAc,CAAA,EACdH,QAAQ,CAAA,EAIT,EAAgB;QACf,OAAO;YACL,iBAAiB;YACjB0C,YAAY,EAAE,IAAI,CAACjD,OAAO,CAACiB,YAAY,CAAC;gBAAEC,MAAM,EAAE,EAAE;gBAAEX,QAAQ;aAAE,CAAC;YACjE,oCAAoC;YACpC2C,SAAS,EAAE;gBACTC,IAAI,EAAEtE,cAAc;gBACpBK,WAAW,EAAE,IAAI,CAACA,WAAW;aAC9B;YACDkE,YAAY,EAAE;gBACZ,2BAA2B;gBAC3BC,GAAG,EAAE,IAAI,CAACrD,OAAO,CAAC2B,IAAI,KAAK,YAAY;aACxC;YACD,yCAAyC;YACzCjB,cAAc;YACd,8EAA8E;YAC9E,4BAA4B;YAC5B,+CAA+C;YAC/C,iEAAiE;YACjE4C,aAAa,EAAE,kCAAkC;SAClD,CAAC;IACJ;IAEA,4DAA4D,SAC9CtB,6BAA6B,CAACuB,QAAoB,EAAEpC,SAAiB,EAAE;QACnF,MAAMqC,IAAAA,cAAqB,sBAAA,EAAC,IAAI,CAACtE,WAAW,EAAE;YAC5CqE,QAAQ;YACRE,QAAQ,EAAE,OAAOjE,IAAI,GAAK;gBACxB,IAAI,IAAI,CAACQ,OAAO,CAACiC,eAAe,EAAE;oBAChC,4DAA4D;oBAC5D,0CAA0C;oBAC1C,OAAOyB,IAAAA,IAAO,EAAA,QAAA,EAACvC,SAAS,CAAEwC,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,EAAEnE,IAAI,CAAC,CAAC;gBAClE,CAAC;gBACD,OAAO2B,SAAS,CAAEwC,KAAK,qBAAqB,AAAC,CAAC,CAAC,CAAC,GAAG,SAAS,GAAGnE,IAAI,CAAC;YACtE,CAAC;SACF,CAAC,CAAC;QACH,yEAAyE;QACzE,MAAMoE,IAAAA,cAAyB,0BAAA,EAAC,IAAI,CAAC1E,WAAW,EAAEqE,QAAQ,CAAC,CAAC;IAC9D;IAEOM,eAAe,GAAG;QACvB,MAAMxE,QAAQ,GAAG,KAAK,AAAC;QACvB,oBAAoB;QACpB,MAAMqB,cAAc,GAAG,IAAI,CAAC9B,qBAAqB,CAAC;YAChD+B,GAAG,EAAE,IAAI,CAACV,oBAAoB,CAACU,GAAG;YAClCtB,QAAQ;SACT,CAAC,AAAC;QAEH,OAAOyE,IAAAA,aAAiC,kCAAA,EAAC,IAAI,CAAC5E,WAAW,EAAE,IAAI,CAACe,oBAAoB,CAACI,GAAG,EAAE;YACxFhB,QAAQ;YACRqB,cAAc;YACdmC,MAAM,EAAE,IAAI,CAAC7C,OAAO,CAAC6C,MAAM;YAC3BC,IAAI,EAAEC,IAAAA,aAAwB,yBAAA,EAAC,IAAI,CAAC7D,WAAW,CAAC;YAChDyC,IAAI,EAAE,IAAI,CAAC3B,OAAO,CAAC2B,IAAI,IAAI,aAAa;YACxC,wFAAwF;YACxFN,MAAM,EAAE,QAAQ;YAChBsB,WAAW,EAAE,KAAK;YAClBK,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL;IAEA;;;;;GAKC,SACae,qBAAqB,CAAC5B,GAAkB,EAAE6B,GAAmB,EAAE;QAC3EA,GAAG,CAACC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAE3CD,GAAG,CAACE,GAAG,CAAC,MAAM,IAAI,CAACC,0BAA0B,EAAE,CAAC,CAAC;IACnD;IAEAA,0BAA0B,GAAG;QAC3B,oBAAoB;QACpB,MAAMhD,SAAS,GAAG,IAAI,CAAC0C,eAAe,EAAE,AAAC;QAEzC,OAAOO,IAAAA,YAAqC,sCAAA,EAAC,IAAI,CAAClF,WAAW,EAAE;YAC7DmB,GAAG,EAAE,IAAI,CAACJ,oBAAoB,CAACI,GAAG;YAClCgE,OAAO,EAAE;gBAAClD,SAAS;aAAC;SACrB,CAAC,CAAC;IACL;IAEA,yBAAyB,SACnBmD,wBAAwB,CAACnC,GAAkB,EAAE6B,GAAmB,EAAEO,IAAgB,EAAE;YAGtF,GAAuC;QAFzC,IACE,IAAI,CAACpE,gBAAgB,CAACqE,GAAG,KAAK,OAAO,KACrC,CAAA,GAAuC,GAAvC,IAAI,CAACvE,oBAAoB,CAACI,GAAG,CAACoE,SAAS,SAAU,GAAjD,KAAA,CAAiD,GAAjD,GAAuC,CAAErF,QAAQ,CAAC,KAAK,CAAC,CAAA,EACxD;YACA,iGAAiG;YACjG,oGAAoG;YACpG,4FAA4F;YAC5F,MAAMC,QAAQ,GAAGqF,IAAAA,gBAAmB,oBAAA,EAACvC,GAAG,CAAC,AAAC;YAC1C,kCAAkC;YAClC,IAAI,CAAC9C,QAAQ,IAAIA,QAAQ,KAAK,KAAK,EAAE;oBACD,IAAiC;gBAAnE,IAAI;oBAAC,QAAQ;oBAAE,QAAQ;iBAAC,CAACD,QAAQ,CAAC,CAAA,CAAA,IAAiC,GAAjC,IAAI,CAACa,oBAAoB,CAACI,GAAG,CAACmE,GAAG,SAAQ,GAAzC,KAAA,CAAyC,GAAzC,IAAiC,CAAEG,MAAM,CAAA,IAAI,EAAE,CAAC,EAAE;oBAClF,oEAAoE;oBACpEJ,IAAI,EAAE,CAAC;oBACP,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,MAAM,IAAI,CAACR,qBAAqB,CAAC5B,GAAG,EAAE6B,GAAG,CAAC,CAAC;oBAC3C,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf;UAEMY,kBAAkB,CACtBzC,GAAkB,EAClB6B,GAAmB,EACnBO,IAAgB,EACD;QACf,kFAAkF;QAClF,IAAI,MAAM,IAAI,CAACD,wBAAwB,CAACnC,GAAG,EAAE6B,GAAG,EAAEO,IAAI,CAAC,EAAE;YACvD,OAAO;QACT,CAAC;QAED,kCAAkC;QAClC,MAAM,IAAI,CAACrC,gBAAgB,CAACC,GAAG,CAAC,CAAC;QAEjC,oBAAoB;QACpB,MAAMnC,OAAO,GAAG,IAAI,CAAC6E,gBAAgB,CAAC1C,GAAG,CAAC,AAAC;QAC3C,MAAM,EAAE2C,IAAI,CAAA,EAAEzC,OAAO,CAAA,EAAE,GAAG,MAAM,IAAI,CAAC0C,yBAAyB,CAAC/E,OAAO,CAAC,AAAC;QACxE,KAAK,MAAM,CAACgF,UAAU,EAAEC,WAAW,CAAC,IAAI5C,OAAO,CAAE;YAC/C2B,GAAG,CAACC,SAAS,CAACe,UAAU,EAAEC,WAAW,CAAC,CAAC;QACzC,CAAC;QACDjB,GAAG,CAACE,GAAG,CAACY,IAAI,CAAC,CAAC;IAChB;CACD"}