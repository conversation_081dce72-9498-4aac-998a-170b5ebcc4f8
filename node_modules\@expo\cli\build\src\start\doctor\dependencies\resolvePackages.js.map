{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/resolvePackages.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { CommandError } from '../../../utils/errors';\n\nexport async function resolvePackageVersionAsync(\n  projectRoot: string,\n  packageName: string\n): Promise<string> {\n  let packageJsonPath: string | undefined;\n  try {\n    packageJsonPath = resolveFrom(projectRoot, `${packageName}/package.json`);\n  } catch (error: any) {\n    // This is a workaround for packages using `exports`. If this doesn't\n    // include `package.json`, we have to use the error message to get the location.\n    if (error.code === 'ERR_PACKAGE_PATH_NOT_EXPORTED') {\n      packageJsonPath = error.message.match(/(\"exports\"|defined) in (.*)$/i)?.[2];\n    }\n  }\n  if (!packageJsonPath) {\n    throw new CommandError(\n      'PACKAGE_NOT_FOUND',\n      `\"${packageName}\" is added as a dependency in your project's package.json but it doesn't seem to be installed. Please run \"yarn\" or \"npm install\" to fix this issue.`\n    );\n  }\n  const packageJson = await JsonFile.readAsync<{ version: string }>(packageJsonPath);\n  return packageJson.version;\n}\n\nexport async function resolveAllPackageVersionsAsync(projectRoot: string, packages: string[]) {\n  const resolvedPackages = await Promise.all(\n    packages.map(async (packageName) => [\n      packageName,\n      await resolvePackageVersionAsync(projectRoot, packageName),\n    ])\n  );\n\n  return Object.fromEntries(resolvedPackages);\n}\n\n/**\n * Determine if the project has a `expo@canary` version installed.\n * This means that an unsable version is used, without the API knowing the exact packages.\n * Since this may be called during, or before, packages are installed, we also need to test on `package.json`.\n * Note, this returns `false` for beta releases.\n */\nexport async function hasExpoCanaryAsync(projectRoot: string) {\n  let expoVersion = '';\n\n  try {\n    // Resolve installed `expo` version first\n    expoVersion = await resolvePackageVersionAsync(projectRoot, 'expo');\n  } catch (error: any) {\n    if (error.code !== 'PACKAGE_NOT_FOUND') {\n      throw error;\n    }\n\n    // Resolve through project `package.json`\n    const packageJson = await JsonFile.readAsync<{ dependencies?: Record<string, string> }>(\n      resolveFrom(projectRoot, './package.json')\n    );\n    expoVersion = packageJson.dependencies?.expo ?? '';\n  }\n\n  if (expoVersion === 'canary') {\n    return true;\n  }\n\n  const prerelease = semver.prerelease(expoVersion) || [];\n  return !!prerelease.some((segment) => typeof segment === 'string' && segment.includes('canary'));\n}\n"], "names": ["resolvePackageVersionAsync", "resolveAllPackageVersionsAsync", "hasExpoCanaryAsync", "projectRoot", "packageName", "packageJsonPath", "resolveFrom", "error", "code", "message", "match", "CommandError", "packageJson", "JsonFile", "readAsync", "version", "packages", "resolvedPackages", "Promise", "all", "map", "Object", "fromEntries", "expoVersion", "dependencies", "expo", "prerelease", "semver", "some", "segment", "includes"], "mappings": "AAAA;;;;;;;;;;;IAMsBA,0BAA0B,MAA1BA,0BAA0B;IAwB1BC,8BAA8B,MAA9BA,8BAA8B;IAiB9BC,kBAAkB,MAAlBA,kBAAkB;;;8DA/CnB,iBAAiB;;;;;;;8DACd,cAAc;;;;;;;8DACnB,QAAQ;;;;;;wBAEE,uBAAuB;;;;;;AAE7C,eAAeF,0BAA0B,CAC9CG,WAAmB,EACnBC,WAAmB,EACF;IACjB,IAAIC,eAAe,AAAoB,AAAC;IACxC,IAAI;QACFA,eAAe,GAAGC,IAAAA,YAAW,EAAA,QAAA,EAACH,WAAW,EAAE,CAAC,EAAEC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;IAC5E,EAAE,OAAOG,KAAK,EAAO;QACnB,qEAAqE;QACrE,gFAAgF;QAChF,IAAIA,KAAK,CAACC,IAAI,KAAK,+BAA+B,EAAE;gBAChCD,GAAoD;YAAtEF,eAAe,GAAGE,CAAAA,GAAoD,GAApDA,KAAK,CAACE,OAAO,CAACC,KAAK,iCAAiC,SAAK,GAAzDH,KAAAA,CAAyD,GAAzDA,GAAoD,AAAE,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IACD,IAAI,CAACF,eAAe,EAAE;QACpB,MAAM,IAAIM,OAAY,aAAA,CACpB,mBAAmB,EACnB,CAAC,CAAC,EAAEP,WAAW,CAAC,oJAAoJ,CAAC,CACtK,CAAC;IACJ,CAAC;IACD,MAAMQ,WAAW,GAAG,MAAMC,SAAQ,EAAA,QAAA,CAACC,SAAS,CAAsBT,eAAe,CAAC,AAAC;IACnF,OAAOO,WAAW,CAACG,OAAO,CAAC;AAC7B,CAAC;AAEM,eAAed,8BAA8B,CAACE,WAAmB,EAAEa,QAAkB,EAAE;IAC5F,MAAMC,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCH,QAAQ,CAACI,GAAG,CAAC,OAAOhB,WAAW,GAAK;YAClCA,WAAW;YACX,MAAMJ,0BAA0B,CAACG,WAAW,EAAEC,WAAW,CAAC;SAC3D,CAAC,CACH,AAAC;IAEF,OAAOiB,MAAM,CAACC,WAAW,CAACL,gBAAgB,CAAC,CAAC;AAC9C,CAAC;AAQM,eAAef,kBAAkB,CAACC,WAAmB,EAAE;IAC5D,IAAIoB,WAAW,GAAG,EAAE,AAAC;IAErB,IAAI;QACF,yCAAyC;QACzCA,WAAW,GAAG,MAAMvB,0BAA0B,CAACG,WAAW,EAAE,MAAM,CAAC,CAAC;IACtE,EAAE,OAAOI,KAAK,EAAO;YASLK,GAAwB;QARtC,IAAIL,KAAK,CAACC,IAAI,KAAK,mBAAmB,EAAE;YACtC,MAAMD,KAAK,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,MAAMK,WAAW,GAAG,MAAMC,SAAQ,EAAA,QAAA,CAACC,SAAS,CAC1CR,IAAAA,YAAW,EAAA,QAAA,EAACH,WAAW,EAAE,gBAAgB,CAAC,CAC3C,AAAC;QACFoB,WAAW,GAAGX,CAAAA,CAAAA,GAAwB,GAAxBA,WAAW,CAACY,YAAY,SAAM,GAA9BZ,KAAAA,CAA8B,GAA9BA,GAAwB,CAAEa,IAAI,CAAA,IAAI,EAAE,CAAC;IACrD,CAAC;IAED,IAAIF,WAAW,KAAK,QAAQ,EAAE;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMG,UAAU,GAAGC,OAAM,EAAA,QAAA,CAACD,UAAU,CAACH,WAAW,CAAC,IAAI,EAAE,AAAC;IACxD,OAAO,CAAC,CAACG,UAAU,CAACE,IAAI,CAAC,CAACC,OAAO,GAAK,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnG,CAAC"}