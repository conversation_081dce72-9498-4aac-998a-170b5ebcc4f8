{"version": 3, "sources": ["../../../../../src/start/platforms/android/gradle.ts"], "sourcesContent": ["import spawnAsync, { SpawnResult } from '@expo/spawn-async';\nimport path from 'path';\n\nimport { env } from '../../../utils/env';\nimport { AbortCommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:android:gradle') as typeof console.log;\n\nfunction upperFirst(name: string) {\n  return name.charAt(0).toUpperCase() + name.slice(1);\n}\n\n/** Format gradle assemble arguments. Exposed for testing.  */\nexport function formatGradleArguments(\n  cmd: 'assemble' | 'install',\n  {\n    appName,\n    variant,\n    tasks = [cmd + upperFirst(variant)],\n  }: { tasks?: string[]; variant: string; appName: string }\n): string[] {\n  return appName ? tasks.map((task) => `${appName}:${task}`) : tasks;\n}\n\nfunction resolveGradleWPath(androidProjectPath: string): string {\n  return path.join(androidProjectPath, process.platform === 'win32' ? 'gradlew.bat' : 'gradlew');\n}\n\nfunction getPortArg(port: number): string {\n  return `-PreactNativeDevServerPort=${port}`;\n}\n\nfunction getActiveArchArg(architectures: string): string {\n  return `-PreactNativeArchitectures=${architectures}`;\n}\n\n/**\n * Build the Android project using Gradle.\n *\n * @param androidProjectPath - Path to the Android project like `projectRoot/android`.\n * @param props.variant - Variant to install.\n * @param props.appName - Name of the 'app' folder, this appears to always be `app`.\n * @param props.port - Dev server port to pass to the install command.\n * @param props.buildCache - Should use the `--build-cache` flag, enabling the [Gradle build cache](https://docs.gradle.org/current/userguide/build_cache.html).\n * @param props.architectures - Architectures to build for.\n * @returns - A promise resolving to spawn results.\n */\nexport async function assembleAsync(\n  androidProjectPath: string,\n  {\n    variant,\n    port,\n    appName,\n    buildCache,\n    architectures,\n    eagerBundleOptions,\n  }: {\n    variant: string;\n    port?: number;\n    appName: string;\n    buildCache?: boolean;\n    architectures?: string;\n    eagerBundleOptions?: string;\n  }\n): Promise<SpawnResult> {\n  const task = formatGradleArguments('assemble', { variant, appName });\n  const args = [\n    ...task,\n    // ignore linting errors\n    '-x',\n    'lint',\n    // ignore tests\n    '-x',\n    'test',\n    '--configure-on-demand',\n  ];\n\n  if (buildCache) args.push('--build-cache');\n\n  // Generate a profile under `/android/app/build/reports/profile`\n  if (env.EXPO_PROFILE) args.push('--profile');\n\n  return await spawnGradleAsync(androidProjectPath, {\n    port,\n    architectures,\n    args,\n    env: eagerBundleOptions\n      ? {\n          __EXPO_EAGER_BUNDLE_OPTIONS: eagerBundleOptions,\n        }\n      : {},\n  });\n}\n\n/**\n * Install an app on device or emulator using `gradlew install`.\n *\n * @param androidProjectPath - Path to the Android project like `projectRoot/android`.\n * @param props.variant - Variant to install.\n * @param props.appName - Name of the 'app' folder, this appears to always be `app`.\n * @param props.port - Dev server port to pass to the install command.\n * @returns - A promise resolving to spawn results.\n */\nexport async function installAsync(\n  androidProjectPath: string,\n  {\n    variant,\n    appName,\n    port,\n  }: {\n    variant: string;\n    appName: string;\n    port?: number;\n  }\n): Promise<SpawnResult> {\n  const args = formatGradleArguments('install', { variant, appName });\n  return await spawnGradleAsync(androidProjectPath, { port, args });\n}\n\nexport async function spawnGradleAsync(\n  projectRoot: string,\n  {\n    port,\n    architectures,\n    args,\n    env,\n  }: { port?: number; architectures?: string; args: string[]; env?: Record<string, string> }\n): Promise<SpawnResult> {\n  const gradlew = resolveGradleWPath(projectRoot);\n  if (port != null) args.push(getPortArg(port));\n  if (architectures) args.push(getActiveArchArg(architectures));\n  debug(`  ${gradlew} ${args.join(' ')}`);\n  try {\n    return await spawnAsync(gradlew, args, {\n      cwd: projectRoot,\n      stdio: 'inherit',\n      env: {\n        ...process.env,\n        ...(env ?? {}),\n      },\n    });\n  } catch (error: any) {\n    // User aborted the command with ctrl-c\n    if (error.status === 130) {\n      // Fail silently\n      throw new AbortCommandError();\n    }\n    throw error;\n  }\n}\n"], "names": ["formatGradleArguments", "assembleAsync", "installAsync", "spawnGradleAsync", "debug", "require", "upperFirst", "name", "char<PERSON>t", "toUpperCase", "slice", "cmd", "appName", "variant", "tasks", "map", "task", "resolveGradleWPath", "androidProjectPath", "path", "join", "process", "platform", "getPortArg", "port", "getActiveArchArg", "architectures", "buildCache", "eagerBundleOptions", "args", "push", "env", "EXPO_PROFILE", "__EXPO_EAGER_BUNDLE_OPTIONS", "projectRoot", "gradlew", "spawnAsync", "cwd", "stdio", "error", "status", "AbortCommandError"], "mappings": "AAAA;;;;;;;;;;;IAagBA,qBAAqB,MAArBA,qBAAqB;IAkCfC,aAAa,MAAbA,aAAa;IAwDbC,YAAY,MAAZA,YAAY;IAgBZC,gBAAgB,MAAhBA,gBAAgB;;;8DAvHE,mBAAmB;;;;;;;8DAC1C,MAAM;;;;;;qBAEH,oBAAoB;wBACN,uBAAuB;;;;;;AAEzD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,qCAAqC,CAAC,AAAsB,AAAC;AAE5F,SAASC,UAAU,CAACC,IAAY,EAAE;IAChC,OAAOA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,CAAC;AAGM,SAASV,qBAAqB,CACnCW,GAA2B,EAC3B,EACEC,OAAO,CAAA,EACPC,OAAO,CAAA,EACPC,KAAK,EAAG;IAACH,GAAG,GAAGL,UAAU,CAACO,OAAO,CAAC;CAAC,CAAA,EACoB,EAC/C;IACV,OAAOD,OAAO,GAAGE,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,GAAK,CAAC,EAAEJ,OAAO,CAAC,CAAC,EAAEI,IAAI,CAAC,CAAC,CAAC,GAAGF,KAAK,CAAC;AACrE,CAAC;AAED,SAASG,kBAAkB,CAACC,kBAA0B,EAAU;IAC9D,OAAOC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,kBAAkB,EAAEG,OAAO,CAACC,QAAQ,KAAK,OAAO,GAAG,aAAa,GAAG,SAAS,CAAC,CAAC;AACjG,CAAC;AAED,SAASC,UAAU,CAACC,IAAY,EAAU;IACxC,OAAO,CAAC,2BAA2B,EAAEA,IAAI,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,SAASC,gBAAgB,CAACC,aAAqB,EAAU;IACvD,OAAO,CAAC,2BAA2B,EAAEA,aAAa,CAAC,CAAC,CAAC;AACvD,CAAC;AAaM,eAAezB,aAAa,CACjCiB,kBAA0B,EAC1B,EACEL,OAAO,CAAA,EACPW,IAAI,CAAA,EACJZ,OAAO,CAAA,EACPe,UAAU,CAAA,EACVD,aAAa,CAAA,EACbE,kBAAkB,CAAA,EAQnB,EACqB;IACtB,MAAMZ,IAAI,GAAGhB,qBAAqB,CAAC,UAAU,EAAE;QAAEa,OAAO;QAAED,OAAO;KAAE,CAAC,AAAC;IACrE,MAAMiB,IAAI,GAAG;WACRb,IAAI;QACP,wBAAwB;QACxB,IAAI;QACJ,MAAM;QACN,eAAe;QACf,IAAI;QACJ,MAAM;QACN,uBAAuB;KACxB,AAAC;IAEF,IAAIW,UAAU,EAAEE,IAAI,CAACC,IAAI,CAAC,eAAe,CAAC,CAAC;IAE3C,gEAAgE;IAChE,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAEH,IAAI,CAACC,IAAI,CAAC,WAAW,CAAC,CAAC;IAE7C,OAAO,MAAM3B,gBAAgB,CAACe,kBAAkB,EAAE;QAChDM,IAAI;QACJE,aAAa;QACbG,IAAI;QACJE,GAAG,EAAEH,kBAAkB,GACnB;YACEK,2BAA2B,EAAEL,kBAAkB;SAChD,GACD,EAAE;KACP,CAAC,CAAC;AACL,CAAC;AAWM,eAAe1B,YAAY,CAChCgB,kBAA0B,EAC1B,EACEL,OAAO,CAAA,EACPD,OAAO,CAAA,EACPY,IAAI,CAAA,EAKL,EACqB;IACtB,MAAMK,IAAI,GAAG7B,qBAAqB,CAAC,SAAS,EAAE;QAAEa,OAAO;QAAED,OAAO;KAAE,CAAC,AAAC;IACpE,OAAO,MAAMT,gBAAgB,CAACe,kBAAkB,EAAE;QAAEM,IAAI;QAAEK,IAAI;KAAE,CAAC,CAAC;AACpE,CAAC;AAEM,eAAe1B,gBAAgB,CACpC+B,WAAmB,EACnB,EACEV,IAAI,CAAA,EACJE,aAAa,CAAA,EACbG,IAAI,CAAA,EACJE,GAAG,CAAA,EACqF,EACpE;IACtB,MAAMI,OAAO,GAAGlB,kBAAkB,CAACiB,WAAW,CAAC,AAAC;IAChD,IAAIV,IAAI,IAAI,IAAI,EAAEK,IAAI,CAACC,IAAI,CAACP,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAIE,aAAa,EAAEG,IAAI,CAACC,IAAI,CAACL,gBAAgB,CAACC,aAAa,CAAC,CAAC,CAAC;IAC9DtB,KAAK,CAAC,CAAC,EAAE,EAAE+B,OAAO,CAAC,CAAC,EAAEN,IAAI,CAACT,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI;QACF,OAAO,MAAMgB,IAAAA,WAAU,EAAA,QAAA,EAACD,OAAO,EAAEN,IAAI,EAAE;YACrCQ,GAAG,EAAEH,WAAW;YAChBI,KAAK,EAAE,SAAS;YAChBP,GAAG,EAAE;gBACH,GAAGV,OAAO,CAACU,GAAG;gBACd,GAAIA,GAAG,IAAI,EAAE;aACd;SACF,CAAC,CAAC;IACL,EAAE,OAAOQ,KAAK,EAAO;QACnB,uCAAuC;QACvC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;YACxB,gBAAgB;YAChB,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QACD,MAAMF,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}