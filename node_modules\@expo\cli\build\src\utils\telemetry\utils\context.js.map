{"version": 3, "sources": ["../../../../../src/utils/telemetry/utils/context.ts"], "sourcesContent": ["import * as ciInfo from 'ci-info';\nimport os from 'os';\n\nimport { groupBy } from '../../array';\n\nexport function createContext() {\n  return {\n    os: { name: os.platform(), version: os.release(), node: process.versions.node },\n    device: { arch: os.arch(), memory: summarizeMemory() },\n    cpu: summarizeCpuInfo(),\n    app: { name: 'expo/cli', version: process.env.__EXPO_VERSION },\n    ci: ciInfo.isCI ? { name: ciInfo.name, isPr: ciInfo.isPR } : undefined,\n  };\n}\n\nfunction summarizeMemory() {\n  const gb = os.totalmem() / 1024 / 1024 / 1024;\n  return Math.round(gb * 100) / 100;\n}\n\nfunction summarizeCpuInfo() {\n  const cpus = groupBy(os.cpus() ?? [], (item) => item.model);\n  const summary = { model: '', speed: 0, count: 0 };\n\n  for (const key in cpus) {\n    if (cpus[key].length > summary.count) {\n      summary.model = key;\n      summary.speed = cpus[key][0].speed;\n      summary.count = cpus[key].length;\n    }\n  }\n\n  return !summary.model || !summary.count ? undefined : summary;\n}\n"], "names": ["createContext", "os", "name", "platform", "version", "release", "node", "process", "versions", "device", "arch", "memory", "summarize<PERSON><PERSON><PERSON>", "cpu", "summarizeCpuInfo", "app", "env", "__EXPO_VERSION", "ci", "ciInfo", "isCI", "isPr", "isPR", "undefined", "gb", "totalmem", "Math", "round", "cpus", "groupBy", "item", "model", "summary", "speed", "count", "key", "length"], "mappings": "AAAA;;;;+BAKgBA,eAAa;;aAAbA,aAAa;;;+DAL<PERSON>,SAAS;;;;;;;8DAClB,IAAI;;;;;;uBAEK,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,SAASA,aAAa,GAAG;IAC9B,OAAO;QACLC,EAAE,EAAE;YAAEC,IAAI,EAAED,GAAE,EAAA,QAAA,CAACE,QAAQ,EAAE;YAAEC,OAAO,EAAEH,GAAE,EAAA,QAAA,CAACI,OAAO,EAAE;YAAEC,IAAI,EAAEC,OAAO,CAACC,QAAQ,CAACF,IAAI;SAAE;QAC/EG,MAAM,EAAE;YAAEC,IAAI,EAAET,GAAE,EAAA,QAAA,CAACS,IAAI,EAAE;YAAEC,MAAM,EAAEC,eAAe,EAAE;SAAE;QACtDC,GAAG,EAAEC,gBAAgB,EAAE;QACvBC,GAAG,EAAE;YAAEb,IAAI,EAAE,UAAU;YAAEE,OAAO,EAAEG,OAAO,CAACS,GAAG,CAACC,cAAc;SAAE;QAC9DC,EAAE,EAAEC,OAAM,EAAA,CAACC,IAAI,GAAG;YAAElB,IAAI,EAAEiB,OAAM,EAAA,CAACjB,IAAI;YAAEmB,IAAI,EAAEF,OAAM,EAAA,CAACG,IAAI;SAAE,GAAGC,SAAS;KACvE,CAAC;AACJ,CAAC;AAED,SAASX,eAAe,GAAG;IACzB,MAAMY,EAAE,GAAGvB,GAAE,EAAA,QAAA,CAACwB,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,AAAC;IAC9C,OAAOC,IAAI,CAACC,KAAK,CAACH,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;AACpC,CAAC;AAED,SAASV,gBAAgB,GAAG;IAC1B,MAAMc,IAAI,GAAGC,IAAAA,MAAO,QAAA,EAAC5B,GAAE,EAAA,QAAA,CAAC2B,IAAI,EAAE,IAAI,EAAE,EAAE,CAACE,IAAI,GAAKA,IAAI,CAACC,KAAK,CAAC,AAAC;IAC5D,MAAMC,OAAO,GAAG;QAAED,KAAK,EAAE,EAAE;QAAEE,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;KAAE,AAAC;IAElD,IAAK,MAAMC,GAAG,IAAIP,IAAI,CAAE;QACtB,IAAIA,IAAI,CAACO,GAAG,CAAC,CAACC,MAAM,GAAGJ,OAAO,CAACE,KAAK,EAAE;YACpCF,OAAO,CAACD,KAAK,GAAGI,GAAG,CAAC;YACpBH,OAAO,CAACC,KAAK,GAAGL,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CAACF,KAAK,CAAC;YACnCD,OAAO,CAACE,KAAK,GAAGN,IAAI,CAACO,GAAG,CAAC,CAACC,MAAM,CAAC;QACnC,CAAC;IACH,CAAC;IAED,OAAO,CAACJ,OAAO,CAACD,KAAK,IAAI,CAACC,OAAO,CAACE,KAAK,GAAGX,SAAS,GAAGS,OAAO,CAAC;AAChE,CAAC"}