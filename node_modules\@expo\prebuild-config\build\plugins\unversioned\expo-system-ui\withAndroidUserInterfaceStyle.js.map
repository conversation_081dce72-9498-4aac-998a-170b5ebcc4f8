{"version": 3, "file": "withAndroidUserInterfaceStyle.js", "names": ["_configPlugins", "data", "require", "withAndroidUserInterfaceStyle", "config", "withStringsXml", "userInterfaceStyle", "android", "WarningAggregator", "addWarningAndroid", "exports"], "sources": ["../../../../src/plugins/unversioned/expo-system-ui/withAndroidUserInterfaceStyle.ts"], "sourcesContent": ["import { ConfigPlugin, WarningAggregator, withStringsXml } from '@expo/config-plugins';\n\nexport const withAndroidUserInterfaceStyle: ConfigPlugin<void> = (config) => {\n  return withStringsXml(config, (config) => {\n    const userInterfaceStyle = config.android?.userInterfaceStyle ?? config.userInterfaceStyle;\n    if (userInterfaceStyle) {\n      WarningAggregator.addWarningAndroid(\n        'userInterfaceStyle',\n        // TODO: Maybe warn that they need a certain version of React Native as well?\n        'Install expo-system-ui in your project to enable this feature.'\n      );\n    }\n\n    return config;\n  });\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,6BAAiD,GAAIC,MAAM,IAAK;EAC3E,OAAO,IAAAC,+BAAc,EAACD,MAAM,EAAGA,MAAM,IAAK;IACxC,MAAME,kBAAkB,GAAGF,MAAM,CAACG,OAAO,EAAED,kBAAkB,IAAIF,MAAM,CAACE,kBAAkB;IAC1F,IAAIA,kBAAkB,EAAE;MACtBE,kCAAiB,CAACC,iBAAiB,CACjC,oBAAoB;MACpB;MACA,gEACF,CAAC;IACH;IAEA,OAAOL,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACM,OAAA,CAAAP,6BAAA,GAAAA,6BAAA", "ignoreList": []}