{"version": 3, "sources": ["../../../../../src/start/server/middleware/RuntimeRedirectMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:runtimeRedirect'\n) as typeof console.log;\n\n/** Runtime to target: expo = Expo Go, custom = Dev Client. */\ntype RuntimeTarget = 'expo' | 'custom';\n\nexport type DeepLinkHandler = (props: {\n  runtime: RuntimeTarget;\n  platform: RuntimePlatform;\n}) => void | Promise<void>;\n\nexport class RuntimeRedirectMiddleware extends ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected options: {\n      onDeepLink?: DeepLinkHandler;\n      getLocation: (props: { runtime: RuntimeTarget }) => string | null | undefined;\n    }\n  ) {\n    super(projectRoot, ['/_expo/link']);\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    const { query } = parse(req.url!, true);\n    const isDevClient = query['choice'] === 'expo-dev-client';\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n    const runtime = isDevClient ? 'custom' : 'expo';\n\n    debug(`props:`, { platform, runtime });\n\n    this.options.onDeepLink?.({ runtime, platform });\n\n    const redirect = this.options.getLocation({ runtime });\n    if (!redirect) {\n      Log.warn(\n        `[redirect middleware]: Unable to determine redirect location for runtime '${runtime}' and platform '${platform}'`\n      );\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n    debug('Redirect ->', redirect);\n    res.setHeader('Location', redirect);\n\n    // Disable caching\n    disableResponseCache(res);\n\n    // 'Temporary Redirect'\n    res.statusCode = 307;\n    res.end();\n  }\n}\n"], "names": ["RuntimeRedirectMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "options", "handleRequestAsync", "req", "res", "query", "parse", "url", "isDevClient", "platform", "parsePlatformHeader", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "assertRuntimePlatform", "runtime", "onDeepLink", "redirect", "getLocation", "Log", "warn", "statusCode", "end", "<PERSON><PERSON><PERSON><PERSON>", "disableResponseCache"], "mappings": "AAAA;;;;+BAyBaA,2BAAyB;;aAAzBA,yBAAyB;;;yBAzBhB,KAAK;;;;;;gCAE0B,kBAAkB;iCAOhE,mBAAmB;2DAEL,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,8CAA8C,CAC/C,AAAsB,AAAC;AAUjB,MAAMF,yBAAyB,SAASG,eAAc,eAAA;IAC3DC,YACYC,WAAmB,EACnBC,OAGT,CACD;QACA,KAAK,CAACD,WAAW,EAAE;YAAC,aAAa;SAAC,CAAC,CAAC;QAN1BA,mBAAAA,WAAmB,CAAA;QACnBC,eAAAA,OAGT,CAAA;IAGH;UAEMC,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;YAU/E,QAAY,AAAW,EAAvB,GAAuB;QATvB,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAGC,IAAAA,IAAK,EAAA,MAAA,EAACH,GAAG,CAACI,GAAG,EAAG,IAAI,CAAC,AAAC;QACxC,MAAMC,WAAW,GAAGH,KAAK,CAAC,QAAQ,CAAC,KAAK,iBAAiB,AAAC;QAC1D,MAAMI,QAAQ,GAAGC,IAAAA,gBAAmB,oBAAA,EAACP,GAAG,CAAC,IAAIQ,IAAAA,gBAAkC,mCAAA,EAACR,GAAG,CAAC,AAAC;QACrFS,IAAAA,gBAA4B,6BAAA,EAACH,QAAQ,CAAC,CAAC;QACvCI,IAAAA,gBAAqB,sBAAA,EAACJ,QAAQ,CAAC,CAAC;QAChC,MAAMK,OAAO,GAAGN,WAAW,GAAG,QAAQ,GAAG,MAAM,AAAC;QAEhDZ,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;YAAEa,QAAQ;YAAEK,OAAO;SAAE,CAAC,CAAC;QAEvC,CAAA,GAAuB,GAAvB,CAAA,QAAY,GAAZ,IAAI,CAACb,OAAO,EAACc,UAAU,SAAyB,GAAhD,KAAA,CAAgD,GAAhD,GAAuB,CAAvB,IAAgD,CAAhD,QAAY,EAAc;YAAED,OAAO;YAAEL,QAAQ;SAAE,CAAC,CAAC;QAEjD,MAAMO,QAAQ,GAAG,IAAI,CAACf,OAAO,CAACgB,WAAW,CAAC;YAAEH,OAAO;SAAE,CAAC,AAAC;QACvD,IAAI,CAACE,QAAQ,EAAE;YACbE,IAAG,CAACC,IAAI,CACN,CAAC,0EAA0E,EAAEL,OAAO,CAAC,gBAAgB,EAAEL,QAAQ,CAAC,CAAC,CAAC,CACnH,CAAC;YACFL,GAAG,CAACgB,UAAU,GAAG,GAAG,CAAC;YACrBhB,GAAG,CAACiB,GAAG,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QACDzB,KAAK,CAAC,aAAa,EAAEoB,QAAQ,CAAC,CAAC;QAC/BZ,GAAG,CAACkB,SAAS,CAAC,UAAU,EAAEN,QAAQ,CAAC,CAAC;QAEpC,kBAAkB;QAClBO,IAAAA,eAAoB,qBAAA,EAACnB,GAAG,CAAC,CAAC;QAE1B,uBAAuB;QACvBA,GAAG,CAACgB,UAAU,GAAG,GAAG,CAAC;QACrBhB,GAAG,CAACiB,GAAG,EAAE,CAAC;IACZ;CACD"}