{"version": 3, "sources": ["../../../../../src/start/platforms/android/adbReverse.ts"], "sourcesContent": ["import { assertSdkRoot } from './AndroidSdk';\nimport { adbArgs, Device, getAttachedDevicesAsync, getServer, logUnauthorized } from './adb';\nimport * as Log from '../../../log';\nimport { installExitHooks } from '../../../utils/exit';\n\nconst debug = require('debug')('expo:start:platforms:android:adbReverse') as typeof console.log;\n\nlet removeExitHook: (() => void) | null = null;\n\nexport function hasAdbReverseAsync(): boolean {\n  try {\n    return !!assertSdkRoot();\n  } catch (error: any) {\n    debug('Failed to resolve the Android SDK path, skipping ADB: %s', error.message);\n    return false;\n  }\n}\n\nexport async function startAdbReverseAsync(ports: number[]): Promise<boolean> {\n  // Install cleanup automatically...\n  removeExitHook = installExitHooks(() => {\n    stopAdbReverseAsync(ports);\n  });\n\n  const devices = await getAttachedDevicesAsync();\n  for (const device of devices) {\n    for (const port of ports) {\n      if (!(await adbReverseAsync(device, port))) {\n        debug(`Failed to start reverse port ${port} on device \"${device.name}\"`);\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nexport async function stopAdbReverseAsync(ports: number[]): Promise<void> {\n  removeExitHook?.();\n\n  const devices = await getAttachedDevicesAsync();\n  for (const device of devices) {\n    for (const port of ports) {\n      await adbReverseRemoveAsync(device, port);\n    }\n  }\n}\n\nasync function adbReverseAsync(device: Device, port: number): Promise<boolean> {\n  if (!device.isAuthorized) {\n    logUnauthorized(device);\n    return false;\n  }\n\n  try {\n    await getServer().runAsync(adbArgs(device.pid, 'reverse', `tcp:${port}`, `tcp:${port}`));\n    return true;\n  } catch (error: any) {\n    Log.warn(`[ADB] Couldn't reverse port ${port}: ${error.message}`);\n    return false;\n  }\n}\n\nasync function adbReverseRemoveAsync(device: Device, port: number): Promise<boolean> {\n  if (!device.isAuthorized) {\n    return false;\n  }\n\n  try {\n    await getServer().runAsync(adbArgs(device.pid, 'reverse', '--remove', `tcp:${port}`));\n    return true;\n  } catch (error: any) {\n    // Don't send this to warn because we call this preemptively sometimes\n    debug(`Could not unforward port ${port}: ${error.message}`);\n    return false;\n  }\n}\n"], "names": ["hasAdbReverseAsync", "startAdbReverseAsync", "stopAdbReverseAsync", "debug", "require", "removeExitHook", "assertSdkRoot", "error", "message", "ports", "installExitHooks", "devices", "getAttachedDevicesAsync", "device", "port", "adbReverseAsync", "name", "adbReverseRemoveAsync", "isAuthorized", "logUnauthorized", "getServer", "runAsync", "adbArgs", "pid", "Log", "warn"], "mappings": "AAAA;;;;;;;;;;;IASgBA,kBAAkB,MAAlBA,kBAAkB;IASZC,oBAAoB,MAApBA,oBAAoB;IAkBpBC,mBAAmB,MAAnBA,mBAAmB;;4BApCX,cAAc;qBACyC,OAAO;2DACvE,cAAc;sBACF,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yCAAyC,CAAC,AAAsB,AAAC;AAEhG,IAAIC,cAAc,GAAwB,IAAI,AAAC;AAExC,SAASL,kBAAkB,GAAY;IAC5C,IAAI;QACF,OAAO,CAAC,CAACM,IAAAA,WAAa,cAAA,GAAE,CAAC;IAC3B,EAAE,OAAOC,KAAK,EAAO;QACnBJ,KAAK,CAAC,0DAA0D,EAAEI,KAAK,CAACC,OAAO,CAAC,CAAC;QACjF,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAEM,eAAeP,oBAAoB,CAACQ,KAAe,EAAoB;IAC5E,mCAAmC;IACnCJ,cAAc,GAAGK,IAAAA,KAAgB,iBAAA,EAAC,IAAM;QACtCR,mBAAmB,CAACO,KAAK,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,MAAME,OAAO,GAAG,MAAMC,IAAAA,IAAuB,wBAAA,GAAE,AAAC;IAChD,KAAK,MAAMC,MAAM,IAAIF,OAAO,CAAE;QAC5B,KAAK,MAAMG,IAAI,IAAIL,KAAK,CAAE;YACxB,IAAI,CAAE,MAAMM,eAAe,CAACF,MAAM,EAAEC,IAAI,CAAC,AAAC,EAAE;gBAC1CX,KAAK,CAAC,CAAC,6BAA6B,EAAEW,IAAI,CAAC,YAAY,EAAED,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,eAAed,mBAAmB,CAACO,KAAe,EAAiB;IACxEJ,cAAc,QAAI,GAAlBA,KAAAA,CAAkB,GAAlBA,cAAc,EAAI,CAAC;IAEnB,MAAMM,OAAO,GAAG,MAAMC,IAAAA,IAAuB,wBAAA,GAAE,AAAC;IAChD,KAAK,MAAMC,MAAM,IAAIF,OAAO,CAAE;QAC5B,KAAK,MAAMG,IAAI,IAAIL,KAAK,CAAE;YACxB,MAAMQ,qBAAqB,CAACJ,MAAM,EAAEC,IAAI,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;AACH,CAAC;AAED,eAAeC,eAAe,CAACF,MAAc,EAAEC,IAAY,EAAoB;IAC7E,IAAI,CAACD,MAAM,CAACK,YAAY,EAAE;QACxBC,IAAAA,IAAe,gBAAA,EAACN,MAAM,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI;QACF,MAAMO,IAAAA,IAAS,UAAA,GAAE,CAACC,QAAQ,CAACC,IAAAA,IAAO,QAAA,EAACT,MAAM,CAACU,GAAG,EAAE,SAAS,EAAE,CAAC,IAAI,EAAET,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC;IACd,EAAE,OAAOP,KAAK,EAAO;QACnBiB,IAAG,CAACC,IAAI,CAAC,CAAC,4BAA4B,EAAEX,IAAI,CAAC,EAAE,EAAEP,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,eAAeS,qBAAqB,CAACJ,MAAc,EAAEC,IAAY,EAAoB;IACnF,IAAI,CAACD,MAAM,CAACK,YAAY,EAAE;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI;QACF,MAAME,IAAAA,IAAS,UAAA,GAAE,CAACC,QAAQ,CAACC,IAAAA,IAAO,QAAA,EAACT,MAAM,CAACU,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,IAAI,EAAET,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACtF,OAAO,IAAI,CAAC;IACd,EAAE,OAAOP,KAAK,EAAO;QACnB,sEAAsE;QACtEJ,KAAK,CAAC,CAAC,yBAAyB,EAAEW,IAAI,CAAC,EAAE,EAAEP,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}