{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/getMissingPackages.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport resolveFrom from 'resolve-from';\nimport semver from 'semver';\n\nimport { getCombinedKnownVersionsAsync } from './getVersionedPackages';\n\nconst debug = require('debug')('expo:doctor:dependencies:getMissingPackages') as typeof console.log;\n\nexport type ResolvedPackage = {\n  /** Module ID pointing to the library `package.json`. */\n  file: string;\n  /** NPM package name. */\n  pkg: string;\n  /** Required version range. */\n  version?: string;\n  /** If the dependency should be installed as a `devDependency` */\n  dev?: boolean;\n};\n\n/** Given a set of required packages, this method returns a list of missing packages. */\nexport function collectMissingPackages(\n  projectRoot: string,\n  requiredPackages: ResolvedPackage[]\n): {\n  missing: ResolvedPackage[];\n  resolutions: Record<string, string>;\n} {\n  const resolutions: Record<string, string> = {};\n\n  const missingPackages = requiredPackages.filter((p) => {\n    const resolved = resolveFrom.silent(projectRoot, p.file);\n    if (!resolved || !versionSatisfiesRequiredPackage(resolved, p)) {\n      return true;\n    }\n    resolutions[p.pkg] = resolved;\n    return false;\n  });\n\n  return { missing: missingPackages, resolutions };\n}\n\nexport function versionSatisfiesRequiredPackage(\n  packageJsonFilePath: string,\n  resolvedPackage: Pick<ResolvedPackage, 'version' | 'pkg'>\n): boolean {\n  // If the version is specified, check that it satisfies the installed version.\n  if (!resolvedPackage.version) {\n    debug(`Required package \"${resolvedPackage.pkg}\" found (no version constraint specified).`);\n    return true;\n  }\n\n  const pkgJson = JsonFile.read(packageJsonFilePath);\n  if (\n    // package.json has version.\n    typeof pkgJson.version === 'string' &&\n    // semver satisfaction.\n    semver.satisfies(pkgJson.version, resolvedPackage.version)\n  ) {\n    return true;\n  }\n  debug(\n    `Installed package \"${resolvedPackage.pkg}\" does not satisfy version constraint \"${resolvedPackage.version}\" (version: \"${pkgJson.version}\")`\n  );\n  return false;\n}\n\n/**\n * Collect missing packages given a list of required packages.\n * Any missing packages will be versioned to the known versions for the current SDK.\n *\n * @param projectRoot\n * @param props.requiredPackages list of required packages to check for\n * @returns list of missing packages and resolutions to existing packages.\n */\nexport async function getMissingPackagesAsync(\n  projectRoot: string,\n  {\n    sdkVersion,\n    requiredPackages,\n  }: {\n    sdkVersion?: string;\n    requiredPackages: ResolvedPackage[];\n  }\n): Promise<{\n  missing: ResolvedPackage[];\n  resolutions: Record<string, string>;\n}> {\n  const results = collectMissingPackages(projectRoot, requiredPackages);\n  if (!results.missing.length) {\n    return results;\n  }\n\n  // Ensure the versions are right for the SDK that the project is currently using.\n  await mutatePackagesWithKnownVersionsAsync(projectRoot, sdkVersion, results.missing);\n\n  return results;\n}\n\nexport async function mutatePackagesWithKnownVersionsAsync(\n  projectRoot: string,\n  sdkVersion: string | undefined,\n  packages: ResolvedPackage[]\n) {\n  // Ensure the versions are right for the SDK that the project is currently using.\n  const relatedPackages = await getCombinedKnownVersionsAsync({ projectRoot, sdkVersion });\n  for (const pkg of packages) {\n    if (\n      // Only use the SDK versions if the package does not already have a hardcoded version.\n      // We do this because some packages have API coded into the CLI which expects an exact version.\n      !pkg.version &&\n      pkg.pkg in relatedPackages\n    ) {\n      pkg.version = relatedPackages[pkg.pkg];\n    }\n  }\n  return packages;\n}\n"], "names": ["collectMissingPackages", "versionSatisfiesRequiredPackage", "getMissingPackagesAsync", "mutatePackagesWithKnownVersionsAsync", "debug", "require", "projectRoot", "requiredPackages", "resolutions", "missingPackages", "filter", "p", "resolved", "resolveFrom", "silent", "file", "pkg", "missing", "packageJsonFilePath", "resolvedPackage", "version", "pkgJson", "JsonFile", "read", "semver", "satisfies", "sdkVersion", "results", "length", "packages", "relatedPackages", "getCombinedKnownVersionsAsync"], "mappings": "AAAA;;;;;;;;;;;IAoBgBA,sBAAsB,MAAtBA,sBAAsB;IAqBtBC,+BAA+B,MAA/BA,+BAA+B;IAiCzBC,uBAAuB,MAAvBA,uBAAuB;IAwBvBC,oCAAoC,MAApCA,oCAAoC;;;8DAlGrC,iBAAiB;;;;;;;8DACd,cAAc;;;;;;;8DACnB,QAAQ;;;;;;sCAEmB,wBAAwB;;;;;;AAEtE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6CAA6C,CAAC,AAAsB,AAAC;AAc7F,SAASL,sBAAsB,CACpCM,WAAmB,EACnBC,gBAAmC,EAInC;IACA,MAAMC,WAAW,GAA2B,EAAE,AAAC;IAE/C,MAAMC,eAAe,GAAGF,gBAAgB,CAACG,MAAM,CAAC,CAACC,CAAC,GAAK;QACrD,MAAMC,QAAQ,GAAGC,YAAW,EAAA,QAAA,CAACC,MAAM,CAACR,WAAW,EAAEK,CAAC,CAACI,IAAI,CAAC,AAAC;QACzD,IAAI,CAACH,QAAQ,IAAI,CAACX,+BAA+B,CAACW,QAAQ,EAAED,CAAC,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QACDH,WAAW,CAACG,CAAC,CAACK,GAAG,CAAC,GAAGJ,QAAQ,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,AAAC;IAEH,OAAO;QAAEK,OAAO,EAAER,eAAe;QAAED,WAAW;KAAE,CAAC;AACnD,CAAC;AAEM,SAASP,+BAA+B,CAC7CiB,mBAA2B,EAC3BC,eAAyD,EAChD;IACT,8EAA8E;IAC9E,IAAI,CAACA,eAAe,CAACC,OAAO,EAAE;QAC5BhB,KAAK,CAAC,CAAC,kBAAkB,EAAEe,eAAe,CAACH,GAAG,CAAC,0CAA0C,CAAC,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMK,OAAO,GAAGC,SAAQ,EAAA,QAAA,CAACC,IAAI,CAACL,mBAAmB,CAAC,AAAC;IACnD,IACE,4BAA4B;IAC5B,OAAOG,OAAO,CAACD,OAAO,KAAK,QAAQ,IACnC,uBAAuB;IACvBI,OAAM,EAAA,QAAA,CAACC,SAAS,CAACJ,OAAO,CAACD,OAAO,EAAED,eAAe,CAACC,OAAO,CAAC,EAC1D;QACA,OAAO,IAAI,CAAC;IACd,CAAC;IACDhB,KAAK,CACH,CAAC,mBAAmB,EAAEe,eAAe,CAACH,GAAG,CAAC,uCAAuC,EAAEG,eAAe,CAACC,OAAO,CAAC,aAAa,EAAEC,OAAO,CAACD,OAAO,CAAC,EAAE,CAAC,CAC9I,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAAC;AAUM,eAAelB,uBAAuB,CAC3CI,WAAmB,EACnB,EACEoB,UAAU,CAAA,EACVnB,gBAAgB,CAAA,EAIjB,EAIA;IACD,MAAMoB,OAAO,GAAG3B,sBAAsB,CAACM,WAAW,EAAEC,gBAAgB,CAAC,AAAC;IACtE,IAAI,CAACoB,OAAO,CAACV,OAAO,CAACW,MAAM,EAAE;QAC3B,OAAOD,OAAO,CAAC;IACjB,CAAC;IAED,iFAAiF;IACjF,MAAMxB,oCAAoC,CAACG,WAAW,EAAEoB,UAAU,EAAEC,OAAO,CAACV,OAAO,CAAC,CAAC;IAErF,OAAOU,OAAO,CAAC;AACjB,CAAC;AAEM,eAAexB,oCAAoC,CACxDG,WAAmB,EACnBoB,UAA8B,EAC9BG,QAA2B,EAC3B;IACA,iFAAiF;IACjF,MAAMC,eAAe,GAAG,MAAMC,IAAAA,qBAA6B,8BAAA,EAAC;QAAEzB,WAAW;QAAEoB,UAAU;KAAE,CAAC,AAAC;IACzF,KAAK,MAAMV,GAAG,IAAIa,QAAQ,CAAE;QAC1B,IACE,sFAAsF;QACtF,+FAA+F;QAC/F,CAACb,GAAG,CAACI,OAAO,IACZJ,GAAG,CAACA,GAAG,IAAIc,eAAe,EAC1B;YACAd,GAAG,CAACI,OAAO,GAAGU,eAAe,CAACd,GAAG,CAACA,GAAG,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IACD,OAAOa,QAAQ,CAAC;AAClB,CAAC"}