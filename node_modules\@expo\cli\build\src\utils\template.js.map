{"version": 3, "sources": ["../../../src/utils/template.ts"], "sourcesContent": ["/**\n * Simple unsafe interpolation for template strings. Does NOT escape values.\n *\n * Arguments can be named or numeric.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#tagged_templates\n *\n * @example\n * const t1Closure = unsafeTemplate`${0}${1}${0}!`;\n * // const t1Closure = unsafeTemplate([\"\",\"\",\"\",\"!\"],0,1,0);\n * t1Closure(\"Y\", \"A\"); // \"YAY!\"\n *\n * @example\n * const t2Closure = unsafeTemplate`${0} ${\"foo\"}!`;\n * // const t2Closure = unsafeTemplate([\"\",\" \",\"!\"],0,\"foo\");\n * t2Closure(\"Hello\", { foo: \"World\" }); // \"Hello World!\"\n *\n * @example\n * const t3Closure = unsafeTemplate`I'm ${\"name\"}. I'm almost ${\"age\"} years old.`;\n * // const t3Closure = unsafeTemplate([\"I'm \", \". I'm almost \", \" years old.\"], \"name\", \"age\");\n * t3Closure(\"foo\", { name: \"MDN\", age: 30 }); // \"I'm MDN. I'm almost 30 years old.\"\n * t3Closure({ name: \"MDN\", age: 30 }); // \"I'm MDN. I'm almost 30 years old.\"\n */\nexport function unsafeTemplate(strings: TemplateStringsArray, ...keys: (string | number)[]) {\n  return (\n    ...values: (string | number)[] | [...(string | number)[], Record<string | number, string>]\n  ) => {\n    const lastValue = values[values.length - 1];\n    const dict = typeof lastValue === 'object' ? lastValue : {};\n    const result = [strings[0]];\n    keys.forEach((key, i) => {\n      const value = typeof key === 'number' && Number.isInteger(key) ? values[key] : dict[key];\n      result.push(value as string, strings[i + 1]);\n    });\n    return result.join('');\n  };\n}\n"], "names": ["unsafeTemplate", "strings", "keys", "values", "lastValue", "length", "dict", "result", "for<PERSON>ach", "key", "i", "value", "Number", "isInteger", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD;;;;+BAAgBA,gBAAc;;aAAdA,cAAc;;AAAvB,SAASA,cAAc,CAACC,OAA6B,EAAE,GAAGC,IAAI,AAAqB,EAAE;IAC1F,OAAO,CACL,GAAGC,MAAM,AAAiF,GACvF;QACH,MAAMC,SAAS,GAAGD,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,AAAC;QAC5C,MAAMC,IAAI,GAAG,OAAOF,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,EAAE,AAAC;QAC5D,MAAMG,MAAM,GAAG;YAACN,OAAO,CAAC,CAAC,CAAC;SAAC,AAAC;QAC5BC,IAAI,CAACM,OAAO,CAAC,CAACC,GAAG,EAAEC,CAAC,GAAK;YACvB,MAAMC,KAAK,GAAG,OAAOF,GAAG,KAAK,QAAQ,IAAIG,MAAM,CAACC,SAAS,CAACJ,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC,GAAGH,IAAI,CAACG,GAAG,CAAC,AAAC;YACzFF,MAAM,CAACO,IAAI,CAACH,KAAK,EAAYV,OAAO,CAACS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,OAAOH,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC"}