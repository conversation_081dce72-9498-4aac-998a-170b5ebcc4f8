{"version": 3, "sources": ["../../../../src/start/server/openPlatforms.ts"], "sourcesContent": ["import { DevServerManager } from './DevServerManager';\nimport { AbortCommandError } from '../../utils/errors';\nimport { Options } from '../resolveOptions';\n\n/** Launch the app on various platforms in parallel. */\nexport async function openPlatformsAsync(\n  devServerManager: DevServerManager,\n  options: Pick<Options, 'ios' | 'android' | 'web'>\n) {\n  const results = await Promise.allSettled([\n    options.android ? devServerManager.getDefaultDevServer().openPlatformAsync('emulator') : null,\n    options.ios ? devServerManager.getDefaultDevServer().openPlatformAsync('simulator') : null,\n    options.web\n      ? devServerManager\n          .ensureWebDevServerRunningAsync()\n          .then(() => devServerManager.getWebDevServer()?.openPlatformAsync('desktop'))\n      : null,\n  ]);\n\n  const errors = results\n    .map((result) => (result.status === 'rejected' ? result.reason : null))\n    .filter(Boolean);\n\n  if (errors.length) {\n    // ctrl+c\n    const isEscapedError = errors.some((error: any) => error.code === 'ABORTED');\n    if (isEscapedError) {\n      throw new AbortCommandError();\n    }\n    throw errors[0];\n  }\n\n  return !!options.android || !!options.ios;\n}\n"], "names": ["openPlatformsAsync", "devServerManager", "options", "results", "Promise", "allSettled", "android", "getDefaultDevServer", "openPlatformAsync", "ios", "web", "ensureWebDevServerRunningAsync", "then", "getWebDevServer", "errors", "map", "result", "status", "reason", "filter", "Boolean", "length", "isEscapedError", "some", "error", "code", "AbortCommandError"], "mappings": "AAAA;;;;+<PERSON><PERSON>s<PERSON>,oBAAkB;;aAAlBA,kBAAkB;;wBAJN,oBAAoB;AAI/C,eAAeA,kBAAkB,CACtCC,gBAAkC,EAClCC,OAAiD,EACjD;IACA,MAAMC,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC;QACvCH,OAAO,CAACI,OAAO,GAAGL,gBAAgB,CAACM,mBAAmB,EAAE,CAACC,iBAAiB,CAAC,UAAU,CAAC,GAAG,IAAI;QAC7FN,OAAO,CAACO,GAAG,GAAGR,gBAAgB,CAACM,mBAAmB,EAAE,CAACC,iBAAiB,CAAC,WAAW,CAAC,GAAG,IAAI;QAC1FN,OAAO,CAACQ,GAAG,GACPT,gBAAgB,CACbU,8BAA8B,EAAE,CAChCC,IAAI,CAAC;gBAAMX,GAAkC;YAAlCA,OAAAA,CAAAA,GAAkC,GAAlCA,gBAAgB,CAACY,eAAe,EAAE,SAAmB,GAArDZ,KAAAA,CAAqD,GAArDA,GAAkC,CAAEO,iBAAiB,CAAC,SAAS,CAAC,CAAA;SAAA,CAAC,GAC/E,IAAI;KACT,CAAC,AAAC;IAEH,MAAMM,MAAM,GAAGX,OAAO,CACnBY,GAAG,CAAC,CAACC,MAAM,GAAMA,MAAM,CAACC,MAAM,KAAK,UAAU,GAAGD,MAAM,CAACE,MAAM,GAAG,IAAI,AAAC,CAAC,CACtEC,MAAM,CAACC,OAAO,CAAC,AAAC;IAEnB,IAAIN,MAAM,CAACO,MAAM,EAAE;QACjB,SAAS;QACT,MAAMC,cAAc,GAAGR,MAAM,CAACS,IAAI,CAAC,CAACC,KAAU,GAAKA,KAAK,CAACC,IAAI,KAAK,SAAS,CAAC,AAAC;QAC7E,IAAIH,cAAc,EAAE;YAClB,MAAM,IAAII,OAAiB,kBAAA,EAAE,CAAC;QAChC,CAAC;QACD,MAAMZ,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,CAACZ,OAAO,CAACI,OAAO,IAAI,CAAC,CAACJ,OAAO,CAACO,GAAG,CAAC;AAC5C,CAAC"}