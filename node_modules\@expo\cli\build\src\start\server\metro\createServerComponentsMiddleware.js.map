{"version": 3, "sources": ["../../../../../src/start/server/metro/createServerComponentsMiddleware.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport { getRscMiddleware } from '@expo/server/build/middleware/rsc';\nimport assert from 'assert';\nimport path from 'path';\nimport url from 'url';\n\nimport { IS_METRO_BUNDLE_ERROR_SYMBOL, logMetroError } from './metroErrorInterface';\nimport { isPossiblyUnableToResolveError } from '../../../export/embed/xcodeCompilerLogger';\nimport { ExportAssetMap } from '../../../export/saveAssets';\nimport { stripAnsi } from '../../../utils/ansi';\nimport { toPosixPath } from '../../../utils/filePath';\nimport { memoize } from '../../../utils/fn';\nimport { getIpAddress } from '../../../utils/ip';\nimport { streamToStringAsync } from '../../../utils/stream';\nimport { createBuiltinAPIRequestHandler } from '../middleware/createBuiltinAPIRequestHandler';\nimport {\n  createBundleUrlSearchParams,\n  ExpoMetroOptions,\n  getMetroOptionsFromUrl,\n} from '../middleware/metroOptions';\n\nconst debug = require('debug')('expo:rsc') as typeof console.log;\n\ntype SSRLoadModuleArtifactsFunc = (\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>\n) => Promise<{ artifacts: SerialAsset[]; src: string }>;\n\ntype SSRLoadModuleFunc = <T extends Record<string, any>>(\n  filePath: string,\n  specificOptions?: Partial<ExpoMetroOptions>,\n  extras?: { hot?: boolean }\n) => Promise<T>;\n\nconst getMetroServerRootMemo = memoize(getMetroServerRoot);\n\nexport function createServerComponentsMiddleware(\n  projectRoot: string,\n  {\n    rscPath,\n    instanceMetroOptions,\n    ssrLoadModule,\n    ssrLoadModuleArtifacts,\n    useClientRouter,\n    createModuleId,\n  }: {\n    rscPath: string;\n    instanceMetroOptions: Partial<ExpoMetroOptions>;\n    ssrLoadModule: SSRLoadModuleFunc;\n    ssrLoadModuleArtifacts: SSRLoadModuleArtifactsFunc;\n    useClientRouter: boolean;\n    createModuleId: (\n      filePath: string,\n      context: { platform: string; environment: string }\n    ) => string | number;\n  }\n) {\n  const routerModule = useClientRouter\n    ? 'expo-router/build/rsc/router/noopRouter'\n    : 'expo-router/build/rsc/router/expo-definedRouter';\n\n  const rscMiddleware = getRscMiddleware({\n    config: {},\n    // Disabled in development\n    baseUrl: '',\n    rscPath,\n    onError: console.error,\n    renderRsc: async (args) => {\n      // In development we should add simulated versions of common production headers.\n      if (args.headers['x-real-ip'] == null) {\n        args.headers['x-real-ip'] = getIpAddress();\n      }\n      if (args.headers['x-forwarded-for'] == null) {\n        args.headers['x-forwarded-for'] = args.headers['x-real-ip'];\n      }\n      if (args.headers['x-forwarded-proto'] == null) {\n        args.headers['x-forwarded-proto'] = 'http';\n      }\n\n      // Dev server-only implementation.\n      try {\n        return await renderRscToReadableStream({\n          ...args,\n          headers: new Headers(args.headers),\n          body: args.body!,\n        });\n      } catch (error: any) {\n        // If you get a codeFrame error during SSR like when using a Class component in React Server Components, then this\n        // will throw with:\n        // {\n        //   rawObject: {\n        //     type: 'TransformError',\n        //     lineNumber: 0,\n        //     errors: [ [Object] ],\n        //     name: 'SyntaxError',\n        //     message: '...',\n        //   }\n        // }\n\n        // TODO: Revisit all error handling now that we do direct metro bundling...\n        await logMetroError(projectRoot, { error });\n\n        if (error[IS_METRO_BUNDLE_ERROR_SYMBOL]) {\n          throw new Response(JSON.stringify(error), {\n            status: isPossiblyUnableToResolveError(error) ? 404 : 500,\n            headers: {\n              'Content-Type': 'application/json',\n            },\n          });\n        }\n\n        const sanitizedServerMessage = stripAnsi(error.message) ?? error.message;\n        throw new Response(sanitizedServerMessage, {\n          status: 500,\n          headers: {\n            'Content-Type': 'text/plain',\n          },\n        });\n      }\n    },\n  });\n\n  let rscPathPrefix = rscPath;\n  if (rscPathPrefix !== '/') {\n    rscPathPrefix += '/';\n  }\n\n  async function exportServerActionsAsync(\n    {\n      platform,\n      entryPoints,\n      domRoot,\n    }: { platform: string; entryPoints: string[]; domRoot?: string },\n    files: ExportAssetMap\n  ): Promise<{\n    clientBoundaries: string[];\n    manifest: Record<string, [string, string]>;\n  }> {\n    const uniqueEntryPoints = [...new Set(entryPoints)];\n    // TODO: Support multiple entry points in a single split server bundle...\n    const manifest: Record<string, [string, string]> = {};\n    const nestedClientBoundaries: string[] = [];\n    const nestedServerBoundaries: string[] = [];\n    const processedEntryPoints = new Set<string>();\n    async function processEntryPoint(entryPoint: string) {\n      processedEntryPoints.add(entryPoint);\n\n      const contents = await ssrLoadModuleArtifacts(entryPoint, {\n        environment: 'react-server',\n        platform,\n        // Ignore the metro runtime to avoid overwriting the original in the API route.\n        modulesOnly: true,\n        // Required\n        runModule: true,\n        // Required to ensure assets load as client boundaries.\n        domRoot,\n      });\n\n      const reactClientReferences = contents.artifacts\n        .filter((a) => a.type === 'js')[0]\n        .metadata.reactClientReferences?.map((ref) => fileURLToFilePath(ref));\n\n      if (reactClientReferences) {\n        nestedClientBoundaries.push(...reactClientReferences!);\n      }\n      const reactServerReferences = contents.artifacts\n        .filter((a) => a.type === 'js')[0]\n        .metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref));\n\n      if (reactServerReferences) {\n        nestedServerBoundaries.push(...reactServerReferences!);\n      }\n\n      // Naive check to ensure the module runtime is not included in the server action bundle.\n      if (contents.src.includes('The experimental Metro feature')) {\n        throw new Error(\n          'Internal error: module runtime should not be included in server action bundles: ' +\n            entryPoint\n        );\n      }\n\n      const relativeName = createModuleId(entryPoint, {\n        platform,\n        environment: 'react-server',\n      });\n      const safeName = path.basename(contents.artifacts.find((a) => a.type === 'js')!.filename!);\n\n      const outputName = `_expo/rsc/${platform}/${safeName}`;\n      // While we're here, export the router for the server to dynamically render RSC.\n      files.set(outputName, {\n        targetDomain: 'server',\n        contents: wrapBundle(contents.src),\n      });\n\n      // Import relative to `dist/server/_expo/rsc/web/router.js`\n      manifest[entryPoint] = [String(relativeName), outputName];\n    }\n\n    async function processEntryPoints(entryPoints: string[], recursions = 0) {\n      // Arbitrary recursion limit to prevent infinite loops.\n      if (recursions > 10) {\n        throw new Error('Recursion limit exceeded while processing server boundaries');\n      }\n\n      for (const entryPoint of entryPoints) {\n        await processEntryPoint(entryPoint);\n      }\n\n      // When a server action has other server actions inside of it, we need to process those as well to ensure all entry points are in the manifest and accounted for.\n      let uniqueNestedServerBoundaries = [...new Set(nestedServerBoundaries)];\n      // Filter out values that have already been processed.\n      uniqueNestedServerBoundaries = uniqueNestedServerBoundaries.filter(\n        (value) => !processedEntryPoints.has(value)\n      );\n      if (uniqueNestedServerBoundaries.length) {\n        debug('bundling nested server action boundaries', uniqueNestedServerBoundaries);\n        return processEntryPoints(uniqueNestedServerBoundaries, recursions + 1);\n      }\n    }\n\n    await processEntryPoints(uniqueEntryPoints);\n\n    // Save the SSR manifest so we can perform more replacements in the server renderer and with server actions.\n    files.set(`_expo/rsc/${platform}/action-manifest.js`, {\n      targetDomain: 'server',\n      contents: 'module.exports = ' + JSON.stringify(manifest),\n    });\n\n    return { manifest, clientBoundaries: nestedClientBoundaries };\n  }\n\n  async function getExpoRouterClientReferencesAsync(\n    { platform, domRoot }: { platform: string; domRoot?: string },\n    files: ExportAssetMap\n  ): Promise<{\n    reactClientReferences: string[];\n    reactServerReferences: string[];\n    cssModules: SerialAsset[];\n  }> {\n    const contents = await ssrLoadModuleArtifacts(routerModule, {\n      environment: 'react-server',\n      platform,\n      modulesOnly: true,\n      domRoot,\n    });\n\n    // Extract the global CSS modules that are imported from the router.\n    // These will be injected in the head of the HTML document for the website.\n    const cssModules = contents.artifacts.filter((a) => a.type.startsWith('css'));\n\n    const reactServerReferences = contents.artifacts\n      .filter((a) => a.type === 'js')[0]\n      .metadata.reactServerReferences?.map((ref) => fileURLToFilePath(ref));\n\n    if (!reactServerReferences) {\n      throw new Error(\n        'Static server action references were not returned from the Metro SSR bundle for definedRouter'\n      );\n    }\n    debug('React client boundaries:', reactServerReferences);\n\n    const reactClientReferences = contents.artifacts\n      .filter((a) => a.type === 'js')[0]\n      .metadata.reactClientReferences?.map((ref) => fileURLToFilePath(ref));\n\n    if (!reactClientReferences) {\n      throw new Error(\n        'Static client references were not returned from the Metro SSR bundle for definedRouter'\n      );\n    }\n    debug('React client boundaries:', reactClientReferences);\n\n    // While we're here, export the router for the server to dynamically render RSC.\n    files.set(`_expo/rsc/${platform}/router.js`, {\n      targetDomain: 'server',\n      contents: wrapBundle(contents.src),\n    });\n\n    return { reactClientReferences, reactServerReferences, cssModules };\n  }\n\n  const routerCache = new Map<\n    string,\n    typeof import('expo-router/build/rsc/router/expo-definedRouter')\n  >();\n\n  async function getExpoRouterRscEntriesGetterAsync({ platform }: { platform: string }) {\n    await ensureMemo();\n    // We can only cache this if we're using the client router since it doesn't change or use HMR\n    if (routerCache.has(platform) && useClientRouter) {\n      return routerCache.get(platform)!;\n    }\n\n    const router = await ssrLoadModule<\n      typeof import('expo-router/build/rsc/router/expo-definedRouter')\n    >(\n      routerModule,\n      {\n        environment: 'react-server',\n        modulesOnly: true,\n        platform,\n      },\n      {\n        hot: !useClientRouter,\n      }\n    );\n\n    routerCache.set(platform, router);\n    return router;\n  }\n\n  function getResolveClientEntry(context: {\n    platform: string;\n    engine?: 'hermes' | null;\n    ssrManifest?: Map<string, string>;\n  }): (\n    file: string,\n    isServer: boolean\n  ) => {\n    id: string;\n    chunks: string[];\n  } {\n    const serverRoot = getMetroServerRootMemo(projectRoot);\n\n    const {\n      mode,\n      minify = false,\n      isExporting,\n      baseUrl,\n      routerRoot,\n      asyncRoutes,\n      preserveEnvVars,\n      reactCompiler,\n      lazy,\n    } = instanceMetroOptions;\n\n    assert(\n      isExporting != null &&\n        baseUrl != null &&\n        mode != null &&\n        routerRoot != null &&\n        asyncRoutes != null,\n      `The server must be started. (isExporting: ${isExporting}, baseUrl: ${baseUrl}, mode: ${mode}, routerRoot: ${routerRoot}, asyncRoutes: ${asyncRoutes})`\n    );\n\n    return (file: string, isServer: boolean) => {\n      if (isExporting) {\n        assert(context.ssrManifest, 'SSR manifest must exist when exporting');\n        const relativeFilePath = toPosixPath(path.relative(serverRoot, file));\n\n        assert(\n          context.ssrManifest.has(relativeFilePath),\n          `SSR manifest is missing client boundary \"${relativeFilePath}\"`\n        );\n\n        const chunk = context.ssrManifest.get(relativeFilePath);\n\n        return {\n          id: String(createModuleId(file, { platform: context.platform, environment: 'client' })),\n          chunks: chunk != null ? [chunk] : [],\n        };\n      }\n\n      const environment = isServer ? 'react-server' : 'client';\n      const searchParams = createBundleUrlSearchParams({\n        mainModuleName: '',\n        platform: context.platform,\n        mode,\n        minify,\n        lazy,\n        preserveEnvVars,\n        asyncRoutes,\n        baseUrl,\n        routerRoot,\n        isExporting,\n        reactCompiler: !!reactCompiler,\n        engine: context.engine ?? undefined,\n        bytecode: false,\n        clientBoundaries: [],\n        inlineSourceMap: false,\n        environment,\n        modulesOnly: true,\n        runModule: false,\n      });\n\n      searchParams.set('resolver.clientboundary', String(true));\n\n      const clientReferenceUrl = new URL('http://a');\n\n      // TICKLE: Handshake 1\n      searchParams.set('xRSC', '1');\n\n      clientReferenceUrl.search = searchParams.toString();\n\n      const filePath = file.startsWith('file://') ? fileURLToFilePath(file) : file;\n\n      const relativeFilePath = path.relative(serverRoot, filePath);\n\n      clientReferenceUrl.pathname = relativeFilePath;\n\n      // Ensure url.pathname ends with '.bundle'\n      if (!clientReferenceUrl.pathname.endsWith('.bundle')) {\n        clientReferenceUrl.pathname += '.bundle';\n      }\n\n      // Return relative URLs to help Android fetch from wherever it was loaded from since it doesn't support localhost.\n      const chunkName = clientReferenceUrl.pathname + clientReferenceUrl.search;\n\n      return {\n        id: String(createModuleId(filePath, { platform: context.platform, environment })),\n        chunks: [chunkName],\n      };\n    };\n  }\n\n  const rscRendererCache = new Map<string, typeof import('expo-router/build/rsc/rsc-renderer')>();\n\n  let ensurePromise: Promise<any> | null = null;\n  async function ensureSSRReady() {\n    // TODO: Extract CSS Modules / Assets from the bundler process\n    const runtime = await ssrLoadModule<typeof import('expo-router/build/rsc/rsc-renderer')>(\n      'metro-runtime/src/modules/empty-module.js',\n      {\n        environment: 'react-server',\n        platform: 'web',\n      }\n    );\n    return runtime;\n  }\n  const ensureMemo = () => {\n    ensurePromise ??= ensureSSRReady();\n    return ensurePromise;\n  };\n\n  async function getRscRendererAsync(platform: string) {\n    await ensureMemo();\n    // NOTE(EvanBacon): We memoize this now that there's a persistent server storage cache for Server Actions.\n    if (rscRendererCache.has(platform)) {\n      return rscRendererCache.get(platform)!;\n    }\n\n    // TODO: Extract CSS Modules / Assets from the bundler process\n    const renderer = await ssrLoadModule<typeof import('expo-router/build/rsc/rsc-renderer')>(\n      'expo-router/build/rsc/rsc-renderer',\n      {\n        environment: 'react-server',\n        platform,\n      }\n    );\n\n    rscRendererCache.set(platform, renderer);\n    return renderer;\n  }\n\n  const rscRenderContext = new Map<string, any>();\n\n  function getRscRenderContext(platform: string) {\n    // NOTE(EvanBacon): We memoize this now that there's a persistent server storage cache for Server Actions.\n    if (rscRenderContext.has(platform)) {\n      return rscRenderContext.get(platform)!;\n    }\n\n    const context = {};\n\n    rscRenderContext.set(platform, context);\n    return context;\n  }\n\n  async function renderRscToReadableStream(\n    {\n      input,\n      headers,\n      method,\n      platform,\n      body,\n      engine,\n      contentType,\n      ssrManifest,\n      decodedBody,\n    }: {\n      input: string;\n      headers: Headers;\n      method: 'POST' | 'GET';\n      platform: string;\n      body?: ReadableStream<Uint8Array>;\n      engine?: 'hermes' | null;\n      contentType?: string;\n      ssrManifest?: Map<string, string>;\n      decodedBody?: unknown;\n    },\n    isExporting: boolean | undefined = instanceMetroOptions.isExporting\n  ) {\n    assert(\n      isExporting != null,\n      'The server must be started before calling renderRscToReadableStream.'\n    );\n\n    if (method === 'POST') {\n      assert(body, 'Server request must be provided when method is POST (server actions)');\n    }\n\n    const context = getRscRenderContext(platform);\n\n    context['__expo_requestHeaders'] = headers;\n\n    const { renderRsc } = await getRscRendererAsync(platform);\n\n    return renderRsc(\n      {\n        body,\n        decodedBody,\n        context,\n        config: {},\n        input,\n        contentType,\n      },\n      {\n        isExporting,\n        entries: await getExpoRouterRscEntriesGetterAsync({ platform }),\n        resolveClientEntry: getResolveClientEntry({ platform, engine, ssrManifest }),\n        async loadServerModuleRsc(urlFragment) {\n          const serverRoot = getMetroServerRootMemo(projectRoot);\n\n          debug('[SSR] loadServerModuleRsc:', urlFragment);\n\n          const options = getMetroOptionsFromUrl(urlFragment);\n\n          return ssrLoadModule(path.join(serverRoot, options.mainModuleName), options, {\n            hot: true,\n          });\n        },\n      }\n    );\n  }\n\n  return {\n    // Get the static client boundaries (no dead code elimination allowed) for the production export.\n    getExpoRouterClientReferencesAsync,\n    exportServerActionsAsync,\n\n    async exportRoutesAsync(\n      {\n        platform,\n        ssrManifest,\n      }: {\n        platform: string;\n        ssrManifest: Map<string, string>;\n      },\n      files: ExportAssetMap\n    ) {\n      // TODO: When we add web SSR support, we need to extract CSS Modules / Assets from the bundler process to prevent FLOUC.\n      const { getBuildConfig } = (await getExpoRouterRscEntriesGetterAsync({ platform })).default;\n\n      // Get all the routes to render.\n      const buildConfig = await getBuildConfig!(async () =>\n        // TODO: Rework prefetching code to use Metro runtime.\n        []\n      );\n\n      await Promise.all(\n        Array.from(buildConfig).map(async ({ entries }) => {\n          for (const { input, isStatic } of entries || []) {\n            if (!isStatic) {\n              debug('Skipping static export for route', { input });\n              continue;\n            }\n            const destRscFile = path.join('_flight', platform, encodeInput(input));\n\n            const pipe = await renderRscToReadableStream(\n              {\n                input,\n                method: 'GET',\n                platform,\n                headers: new Headers(),\n                ssrManifest,\n              },\n              true\n            );\n\n            const rsc = await streamToStringAsync(pipe);\n            debug('RSC Payload', { platform, input, rsc });\n\n            files.set(destRscFile, {\n              contents: rsc,\n              targetDomain: 'client',\n              rscId: input,\n            });\n          }\n        })\n      );\n    },\n\n    middleware: createBuiltinAPIRequestHandler(\n      // Match `/_flight/[platform]/[...path]`\n      (req) => {\n        return getFullUrl(req.url).pathname.startsWith(rscPathPrefix);\n      },\n      rscMiddleware\n    ),\n    onReloadRscEvent: (platform: string) => {\n      // NOTE: We cannot clear the renderer context because it would break the mounted context state.\n\n      rscRendererCache.delete(platform);\n      routerCache.delete(platform);\n    },\n  };\n}\n\nconst getFullUrl = (url: string) => {\n  try {\n    return new URL(url);\n  } catch {\n    return new URL(url, 'http://localhost:0');\n  }\n};\n\nexport const fileURLToFilePath = (fileURL: string) => {\n  return url.fileURLToPath(fileURL);\n};\n\nconst encodeInput = (input: string) => {\n  if (input === '') {\n    return 'index.txt';\n  }\n  if (input === 'index') {\n    throw new Error('Input should not be `index`');\n  }\n  if (input.startsWith('/')) {\n    throw new Error('Input should not start with `/`');\n  }\n  if (input.endsWith('/')) {\n    throw new Error('Input should not end with `/`');\n  }\n  return input + '.txt';\n};\n\nfunction wrapBundle(str: string) {\n  // Skip the metro runtime so debugging is a bit easier.\n  // Replace the __r() call with an export statement.\n  // Use gm to apply to the last require line. This is needed when the bundle has side-effects.\n  return str.replace(/^(__r\\(.*\\);)$/gm, 'module.exports = $1');\n}\n"], "names": ["createServerComponentsMiddleware", "fileURLToFilePath", "debug", "require", "getMetroServerRootMemo", "memoize", "getMetroServerRoot", "projectRoot", "rscPath", "instanceMetroOptions", "ssrLoadModule", "ssrLoadModuleArtifacts", "useClientRouter", "createModuleId", "routerModule", "rscMiddleware", "getRscMiddleware", "config", "baseUrl", "onError", "console", "error", "renderRsc", "args", "headers", "getIpAddress", "renderRscToReadableStream", "Headers", "body", "logMetroError", "IS_METRO_BUNDLE_ERROR_SYMBOL", "Response", "JSON", "stringify", "status", "isPossiblyUnableToResolveError", "sanitizedServerMessage", "stripAnsi", "message", "rscPathPrefix", "exportServerActionsAsync", "platform", "entryPoints", "domRoot", "files", "uniqueEntryPoints", "Set", "manifest", "nestedClientBoundaries", "nestedServerBoundaries", "processedEntryPoints", "processEntryPoint", "entryPoint", "contents", "add", "environment", "modulesOnly", "runModule", "reactClientReferences", "artifacts", "filter", "a", "type", "metadata", "map", "ref", "push", "reactServerReferences", "src", "includes", "Error", "relativeName", "safeName", "path", "basename", "find", "filename", "outputName", "set", "targetDomain", "wrapBundle", "String", "processEntryPoints", "recursions", "uniqueNestedServerBoundaries", "value", "has", "length", "clientBoundaries", "getExpoRouterClientReferencesAsync", "cssModules", "startsWith", "routerCache", "Map", "getExpoRouterRscEntriesGetterAsync", "ensureMemo", "get", "router", "hot", "getResolveClientEntry", "context", "serverRoot", "mode", "minify", "isExporting", "routerRoot", "asyncRoutes", "preserveEnvVars", "reactCompiler", "lazy", "assert", "file", "isServer", "ssrManifest", "relativeFilePath", "toPosixPath", "relative", "chunk", "id", "chunks", "searchParams", "createBundleUrlSearchParams", "mainModuleName", "engine", "undefined", "bytecode", "inlineSourceMap", "clientReferenceUrl", "URL", "search", "toString", "filePath", "pathname", "endsWith", "chunkName", "rsc<PERSON><PERSON><PERSON><PERSON><PERSON>", "ensurePromise", "ensureSSRReady", "runtime", "getRscRendererAsync", "renderer", "rscRenderContext", "getRscRenderContext", "input", "method", "contentType", "decodedBody", "entries", "resolveClientEntry", "loadServerModuleRsc", "urlFragment", "options", "getMetroOptionsFromUrl", "join", "exportRoutesAsync", "getBuildConfig", "default", "buildConfig", "Promise", "all", "Array", "from", "isStatic", "destRscFile", "encodeInput", "pipe", "rsc", "streamToStringAsync", "rscId", "middleware", "createBuiltinAPIRequestHandler", "req", "getFullUrl", "url", "onReloadRscEvent", "delete", "fileURL", "fileURLToPath", "str", "replace"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAqCgBA,gCAAgC,MAAhCA,gCAAgC;IAokBnCC,iBAAiB,MAAjBA,iBAAiB;;;yBAzmBK,oBAAoB;;;;;;;yBAEtB,mCAAmC;;;;;;;8DACjD,QAAQ;;;;;;;8DACV,MAAM;;;;;;;8DACP,KAAK;;;;;;qCAEuC,uBAAuB;qCACpC,2CAA2C;sBAEhE,qBAAqB;0BACnB,yBAAyB;oBAC7B,mBAAmB;oBACd,mBAAmB;wBACZ,uBAAuB;gDACZ,8CAA8C;8BAKtF,4BAA4B;;;;;;AAEnC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,AAAsB,AAAC;AAajE,MAAMC,sBAAsB,GAAGC,IAAAA,GAAO,QAAA,EAACC,MAAkB,EAAA,mBAAA,CAAC,AAAC;AAEpD,SAASN,gCAAgC,CAC9CO,WAAmB,EACnB,EACEC,OAAO,CAAA,EACPC,oBAAoB,CAAA,EACpBC,aAAa,CAAA,EACbC,sBAAsB,CAAA,EACtBC,eAAe,CAAA,EACfC,cAAc,CAAA,EAWf,EACD;IACA,MAAMC,YAAY,GAAGF,eAAe,GAChC,yCAAyC,GACzC,iDAAiD,AAAC;IAEtD,MAAMG,aAAa,GAAGC,IAAAA,IAAgB,EAAA,iBAAA,EAAC;QACrCC,MAAM,EAAE,EAAE;QACV,0BAA0B;QAC1BC,OAAO,EAAE,EAAE;QACXV,OAAO;QACPW,OAAO,EAAEC,OAAO,CAACC,KAAK;QACtBC,SAAS,EAAE,OAAOC,IAAI,GAAK;YACzB,gFAAgF;YAChF,IAAIA,IAAI,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;gBACrCD,IAAI,CAACC,OAAO,CAAC,WAAW,CAAC,GAAGC,IAAAA,GAAY,aAAA,GAAE,CAAC;YAC7C,CAAC;YACD,IAAIF,IAAI,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,IAAI,EAAE;gBAC3CD,IAAI,CAACC,OAAO,CAAC,iBAAiB,CAAC,GAAGD,IAAI,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC9D,CAAC;YACD,IAAID,IAAI,CAACC,OAAO,CAAC,mBAAmB,CAAC,IAAI,IAAI,EAAE;gBAC7CD,IAAI,CAACC,OAAO,CAAC,mBAAmB,CAAC,GAAG,MAAM,CAAC;YAC7C,CAAC;YAED,kCAAkC;YAClC,IAAI;gBACF,OAAO,MAAME,yBAAyB,CAAC;oBACrC,GAAGH,IAAI;oBACPC,OAAO,EAAE,IAAIG,OAAO,CAACJ,IAAI,CAACC,OAAO,CAAC;oBAClCI,IAAI,EAAEL,IAAI,CAACK,IAAI;iBAChB,CAAC,CAAC;YACL,EAAE,OAAOP,KAAK,EAAO;gBACnB,kHAAkH;gBAClH,mBAAmB;gBACnB,IAAI;gBACJ,iBAAiB;gBACjB,8BAA8B;gBAC9B,qBAAqB;gBACrB,4BAA4B;gBAC5B,2BAA2B;gBAC3B,sBAAsB;gBACtB,MAAM;gBACN,IAAI;gBAEJ,2EAA2E;gBAC3E,MAAMQ,IAAAA,oBAAa,cAAA,EAACtB,WAAW,EAAE;oBAAEc,KAAK;iBAAE,CAAC,CAAC;gBAE5C,IAAIA,KAAK,CAACS,oBAA4B,6BAAA,CAAC,EAAE;oBACvC,MAAM,IAAIC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACZ,KAAK,CAAC,EAAE;wBACxCa,MAAM,EAAEC,IAAAA,oBAA8B,+BAAA,EAACd,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;wBACzDG,OAAO,EAAE;4BACP,cAAc,EAAE,kBAAkB;yBACnC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,MAAMY,sBAAsB,GAAGC,IAAAA,KAAS,UAAA,EAAChB,KAAK,CAACiB,OAAO,CAAC,IAAIjB,KAAK,CAACiB,OAAO,AAAC;gBACzE,MAAM,IAAIP,QAAQ,CAACK,sBAAsB,EAAE;oBACzCF,MAAM,EAAE,GAAG;oBACXV,OAAO,EAAE;wBACP,cAAc,EAAE,YAAY;qBAC7B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;KACF,CAAC,AAAC;IAEH,IAAIe,aAAa,GAAG/B,OAAO,AAAC;IAC5B,IAAI+B,aAAa,KAAK,GAAG,EAAE;QACzBA,aAAa,IAAI,GAAG,CAAC;IACvB,CAAC;IAED,eAAeC,wBAAwB,CACrC,EACEC,QAAQ,CAAA,EACRC,WAAW,CAAA,EACXC,OAAO,CAAA,EACuD,EAChEC,KAAqB,EAIpB;QACD,MAAMC,iBAAiB,GAAG;eAAI,IAAIC,GAAG,CAACJ,WAAW,CAAC;SAAC,AAAC;QACpD,yEAAyE;QACzE,MAAMK,QAAQ,GAAqC,EAAE,AAAC;QACtD,MAAMC,sBAAsB,GAAa,EAAE,AAAC;QAC5C,MAAMC,sBAAsB,GAAa,EAAE,AAAC;QAC5C,MAAMC,oBAAoB,GAAG,IAAIJ,GAAG,EAAU,AAAC;QAC/C,eAAeK,iBAAiB,CAACC,UAAkB,EAAE;gBAcrBC,GAEG,EAKHA,IAEG;YAtBjCH,oBAAoB,CAACI,GAAG,CAACF,UAAU,CAAC,CAAC;YAErC,MAAMC,QAAQ,GAAG,MAAM1C,sBAAsB,CAACyC,UAAU,EAAE;gBACxDG,WAAW,EAAE,cAAc;gBAC3Bd,QAAQ;gBACR,+EAA+E;gBAC/Ee,WAAW,EAAE,IAAI;gBACjB,WAAW;gBACXC,SAAS,EAAE,IAAI;gBACf,uDAAuD;gBACvDd,OAAO;aACR,CAAC,AAAC;YAEH,MAAMe,qBAAqB,GAAGL,CAAAA,GAEG,GAFHA,QAAQ,CAACM,SAAS,CAC7CC,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CACjCC,QAAQ,CAACL,qBAAqB,SAAK,GAFRL,KAAAA,CAEQ,GAFRA,GAEG,CAAEW,GAAG,CAAC,CAACC,GAAG,GAAKhE,iBAAiB,CAACgE,GAAG,CAAC,CAAC,AAAC;YAExE,IAAIP,qBAAqB,EAAE;gBACzBV,sBAAsB,CAACkB,IAAI,IAAIR,qBAAqB,CAAE,CAAC;YACzD,CAAC;YACD,MAAMS,qBAAqB,GAAGd,CAAAA,IAEG,GAFHA,QAAQ,CAACM,SAAS,CAC7CC,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CACjCC,QAAQ,CAACI,qBAAqB,SAAK,GAFRd,KAAAA,CAEQ,GAFRA,IAEG,CAAEW,GAAG,CAAC,CAACC,GAAG,GAAKhE,iBAAiB,CAACgE,GAAG,CAAC,CAAC,AAAC;YAExE,IAAIE,qBAAqB,EAAE;gBACzBlB,sBAAsB,CAACiB,IAAI,IAAIC,qBAAqB,CAAE,CAAC;YACzD,CAAC;YAED,wFAAwF;YACxF,IAAId,QAAQ,CAACe,GAAG,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAAE;gBAC3D,MAAM,IAAIC,KAAK,CACb,kFAAkF,GAChFlB,UAAU,CACb,CAAC;YACJ,CAAC;YAED,MAAMmB,YAAY,GAAG1D,cAAc,CAACuC,UAAU,EAAE;gBAC9CX,QAAQ;gBACRc,WAAW,EAAE,cAAc;aAC5B,CAAC,AAAC;YACH,MAAMiB,QAAQ,GAAGC,KAAI,EAAA,QAAA,CAACC,QAAQ,CAACrB,QAAQ,CAACM,SAAS,CAACgB,IAAI,CAAC,CAACd,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAK,IAAI,CAAC,CAAEc,QAAQ,CAAE,AAAC;YAE3F,MAAMC,UAAU,GAAG,CAAC,UAAU,EAAEpC,QAAQ,CAAC,CAAC,EAAE+B,QAAQ,CAAC,CAAC,AAAC;YACvD,gFAAgF;YAChF5B,KAAK,CAACkC,GAAG,CAACD,UAAU,EAAE;gBACpBE,YAAY,EAAE,QAAQ;gBACtB1B,QAAQ,EAAE2B,UAAU,CAAC3B,QAAQ,CAACe,GAAG,CAAC;aACnC,CAAC,CAAC;YAEH,2DAA2D;YAC3DrB,QAAQ,CAACK,UAAU,CAAC,GAAG;gBAAC6B,MAAM,CAACV,YAAY,CAAC;gBAAEM,UAAU;aAAC,CAAC;QAC5D,CAAC;QAED,eAAeK,kBAAkB,CAACxC,WAAqB,EAAEyC,UAAU,GAAG,CAAC,EAAE;YACvE,uDAAuD;YACvD,IAAIA,UAAU,GAAG,EAAE,EAAE;gBACnB,MAAM,IAAIb,KAAK,CAAC,6DAA6D,CAAC,CAAC;YACjF,CAAC;YAED,KAAK,MAAMlB,UAAU,IAAIV,WAAW,CAAE;gBACpC,MAAMS,iBAAiB,CAACC,UAAU,CAAC,CAAC;YACtC,CAAC;YAED,iKAAiK;YACjK,IAAIgC,4BAA4B,GAAG;mBAAI,IAAItC,GAAG,CAACG,sBAAsB,CAAC;aAAC,AAAC;YACxE,sDAAsD;YACtDmC,4BAA4B,GAAGA,4BAA4B,CAACxB,MAAM,CAChE,CAACyB,KAAK,GAAK,CAACnC,oBAAoB,CAACoC,GAAG,CAACD,KAAK,CAAC,CAC5C,CAAC;YACF,IAAID,4BAA4B,CAACG,MAAM,EAAE;gBACvCrF,KAAK,CAAC,0CAA0C,EAAEkF,4BAA4B,CAAC,CAAC;gBAChF,OAAOF,kBAAkB,CAACE,4BAA4B,EAAED,UAAU,GAAG,CAAC,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,MAAMD,kBAAkB,CAACrC,iBAAiB,CAAC,CAAC;QAE5C,4GAA4G;QAC5GD,KAAK,CAACkC,GAAG,CAAC,CAAC,UAAU,EAAErC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YACpDsC,YAAY,EAAE,QAAQ;YACtB1B,QAAQ,EAAE,mBAAmB,GAAGrB,IAAI,CAACC,SAAS,CAACc,QAAQ,CAAC;SACzD,CAAC,CAAC;QAEH,OAAO;YAAEA,QAAQ;YAAEyC,gBAAgB,EAAExC,sBAAsB;SAAE,CAAC;IAChE,CAAC;IAED,eAAeyC,kCAAkC,CAC/C,EAAEhD,QAAQ,CAAA,EAAEE,OAAO,CAAA,EAA0C,EAC7DC,KAAqB,EAKpB;YAY6BS,GAEG,EASHA,IAEG;QAxBjC,MAAMA,QAAQ,GAAG,MAAM1C,sBAAsB,CAACG,YAAY,EAAE;YAC1DyC,WAAW,EAAE,cAAc;YAC3Bd,QAAQ;YACRe,WAAW,EAAE,IAAI;YACjBb,OAAO;SACR,CAAC,AAAC;QAEH,oEAAoE;QACpE,2EAA2E;QAC3E,MAAM+C,UAAU,GAAGrC,QAAQ,CAACM,SAAS,CAACC,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,CAAC6B,UAAU,CAAC,KAAK,CAAC,CAAC,AAAC;QAE9E,MAAMxB,qBAAqB,GAAGd,CAAAA,GAEG,GAFHA,QAAQ,CAACM,SAAS,CAC7CC,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CACjCC,QAAQ,CAACI,qBAAqB,SAAK,GAFRd,KAAAA,CAEQ,GAFRA,GAEG,CAAEW,GAAG,CAAC,CAACC,GAAG,GAAKhE,iBAAiB,CAACgE,GAAG,CAAC,CAAC,AAAC;QAExE,IAAI,CAACE,qBAAqB,EAAE;YAC1B,MAAM,IAAIG,KAAK,CACb,+FAA+F,CAChG,CAAC;QACJ,CAAC;QACDpE,KAAK,CAAC,0BAA0B,EAAEiE,qBAAqB,CAAC,CAAC;QAEzD,MAAMT,qBAAqB,GAAGL,CAAAA,IAEG,GAFHA,QAAQ,CAACM,SAAS,CAC7CC,MAAM,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CACjCC,QAAQ,CAACL,qBAAqB,SAAK,GAFRL,KAAAA,CAEQ,GAFRA,IAEG,CAAEW,GAAG,CAAC,CAACC,GAAG,GAAKhE,iBAAiB,CAACgE,GAAG,CAAC,CAAC,AAAC;QAExE,IAAI,CAACP,qBAAqB,EAAE;YAC1B,MAAM,IAAIY,KAAK,CACb,wFAAwF,CACzF,CAAC;QACJ,CAAC;QACDpE,KAAK,CAAC,0BAA0B,EAAEwD,qBAAqB,CAAC,CAAC;QAEzD,gFAAgF;QAChFd,KAAK,CAACkC,GAAG,CAAC,CAAC,UAAU,EAAErC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC3CsC,YAAY,EAAE,QAAQ;YACtB1B,QAAQ,EAAE2B,UAAU,CAAC3B,QAAQ,CAACe,GAAG,CAAC;SACnC,CAAC,CAAC;QAEH,OAAO;YAAEV,qBAAqB;YAAES,qBAAqB;YAAEuB,UAAU;SAAE,CAAC;IACtE,CAAC;IAED,MAAME,WAAW,GAAG,IAAIC,GAAG,EAGxB,AAAC;IAEJ,eAAeC,kCAAkC,CAAC,EAAErD,QAAQ,CAAA,EAAwB,EAAE;QACpF,MAAMsD,UAAU,EAAE,CAAC;QACnB,6FAA6F;QAC7F,IAAIH,WAAW,CAACN,GAAG,CAAC7C,QAAQ,CAAC,IAAI7B,eAAe,EAAE;YAChD,OAAOgF,WAAW,CAACI,GAAG,CAACvD,QAAQ,CAAC,CAAE;QACpC,CAAC;QAED,MAAMwD,MAAM,GAAG,MAAMvF,aAAa,CAGhCI,YAAY,EACZ;YACEyC,WAAW,EAAE,cAAc;YAC3BC,WAAW,EAAE,IAAI;YACjBf,QAAQ;SACT,EACD;YACEyD,GAAG,EAAE,CAACtF,eAAe;SACtB,CACF,AAAC;QAEFgF,WAAW,CAACd,GAAG,CAACrC,QAAQ,EAAEwD,MAAM,CAAC,CAAC;QAClC,OAAOA,MAAM,CAAC;IAChB,CAAC;IAED,SAASE,qBAAqB,CAACC,OAI9B,EAMC;QACA,MAAMC,UAAU,GAAGjG,sBAAsB,CAACG,WAAW,CAAC,AAAC;QAEvD,MAAM,EACJ+F,IAAI,CAAA,EACJC,MAAM,EAAG,KAAK,CAAA,EACdC,WAAW,CAAA,EACXtF,OAAO,CAAA,EACPuF,UAAU,CAAA,EACVC,WAAW,CAAA,EACXC,eAAe,CAAA,EACfC,aAAa,CAAA,EACbC,IAAI,CAAA,IACL,GAAGpG,oBAAoB,AAAC;QAEzBqG,IAAAA,OAAM,EAAA,QAAA,EACJN,WAAW,IAAI,IAAI,IACjBtF,OAAO,IAAI,IAAI,IACfoF,IAAI,IAAI,IAAI,IACZG,UAAU,IAAI,IAAI,IAClBC,WAAW,IAAI,IAAI,EACrB,CAAC,0CAA0C,EAAEF,WAAW,CAAC,WAAW,EAAEtF,OAAO,CAAC,QAAQ,EAAEoF,IAAI,CAAC,cAAc,EAAEG,UAAU,CAAC,eAAe,EAAEC,WAAW,CAAC,CAAC,CAAC,CACxJ,CAAC;QAEF,OAAO,CAACK,IAAY,EAAEC,QAAiB,GAAK;YAC1C,IAAIR,WAAW,EAAE;gBACfM,IAAAA,OAAM,EAAA,QAAA,EAACV,OAAO,CAACa,WAAW,EAAE,wCAAwC,CAAC,CAAC;gBACtE,MAAMC,gBAAgB,GAAGC,IAAAA,SAAW,YAAA,EAAC1C,KAAI,EAAA,QAAA,CAAC2C,QAAQ,CAACf,UAAU,EAAEU,IAAI,CAAC,CAAC,AAAC;gBAEtED,IAAAA,OAAM,EAAA,QAAA,EACJV,OAAO,CAACa,WAAW,CAAC3B,GAAG,CAAC4B,gBAAgB,CAAC,EACzC,CAAC,yCAAyC,EAAEA,gBAAgB,CAAC,CAAC,CAAC,CAChE,CAAC;gBAEF,MAAMG,KAAK,GAAGjB,OAAO,CAACa,WAAW,CAACjB,GAAG,CAACkB,gBAAgB,CAAC,AAAC;gBAExD,OAAO;oBACLI,EAAE,EAAErC,MAAM,CAACpE,cAAc,CAACkG,IAAI,EAAE;wBAAEtE,QAAQ,EAAE2D,OAAO,CAAC3D,QAAQ;wBAAEc,WAAW,EAAE,QAAQ;qBAAE,CAAC,CAAC;oBACvFgE,MAAM,EAAEF,KAAK,IAAI,IAAI,GAAG;wBAACA,KAAK;qBAAC,GAAG,EAAE;iBACrC,CAAC;YACJ,CAAC;YAED,MAAM9D,WAAW,GAAGyD,QAAQ,GAAG,cAAc,GAAG,QAAQ,AAAC;YACzD,MAAMQ,YAAY,GAAGC,IAAAA,aAA2B,4BAAA,EAAC;gBAC/CC,cAAc,EAAE,EAAE;gBAClBjF,QAAQ,EAAE2D,OAAO,CAAC3D,QAAQ;gBAC1B6D,IAAI;gBACJC,MAAM;gBACNM,IAAI;gBACJF,eAAe;gBACfD,WAAW;gBACXxF,OAAO;gBACPuF,UAAU;gBACVD,WAAW;gBACXI,aAAa,EAAE,CAAC,CAACA,aAAa;gBAC9Be,MAAM,EAAEvB,OAAO,CAACuB,MAAM,IAAIC,SAAS;gBACnCC,QAAQ,EAAE,KAAK;gBACfrC,gBAAgB,EAAE,EAAE;gBACpBsC,eAAe,EAAE,KAAK;gBACtBvE,WAAW;gBACXC,WAAW,EAAE,IAAI;gBACjBC,SAAS,EAAE,KAAK;aACjB,CAAC,AAAC;YAEH+D,YAAY,CAAC1C,GAAG,CAAC,yBAAyB,EAAEG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YAE1D,MAAM8C,kBAAkB,GAAG,IAAIC,GAAG,CAAC,UAAU,CAAC,AAAC;YAE/C,sBAAsB;YACtBR,YAAY,CAAC1C,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAE9BiD,kBAAkB,CAACE,MAAM,GAAGT,YAAY,CAACU,QAAQ,EAAE,CAAC;YAEpD,MAAMC,QAAQ,GAAGpB,IAAI,CAACpB,UAAU,CAAC,SAAS,CAAC,GAAG1F,iBAAiB,CAAC8G,IAAI,CAAC,GAAGA,IAAI,AAAC;YAE7E,MAAMG,iBAAgB,GAAGzC,KAAI,EAAA,QAAA,CAAC2C,QAAQ,CAACf,UAAU,EAAE8B,QAAQ,CAAC,AAAC;YAE7DJ,kBAAkB,CAACK,QAAQ,GAAGlB,iBAAgB,CAAC;YAE/C,0CAA0C;YAC1C,IAAI,CAACa,kBAAkB,CAACK,QAAQ,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBACpDN,kBAAkB,CAACK,QAAQ,IAAI,SAAS,CAAC;YAC3C,CAAC;YAED,kHAAkH;YAClH,MAAME,SAAS,GAAGP,kBAAkB,CAACK,QAAQ,GAAGL,kBAAkB,CAACE,MAAM,AAAC;YAE1E,OAAO;gBACLX,EAAE,EAAErC,MAAM,CAACpE,cAAc,CAACsH,QAAQ,EAAE;oBAAE1F,QAAQ,EAAE2D,OAAO,CAAC3D,QAAQ;oBAAEc,WAAW;iBAAE,CAAC,CAAC;gBACjFgE,MAAM,EAAE;oBAACe,SAAS;iBAAC;aACpB,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,MAAMC,gBAAgB,GAAG,IAAI1C,GAAG,EAA+D,AAAC;IAEhG,IAAI2C,aAAa,GAAwB,IAAI,AAAC;IAC9C,eAAeC,cAAc,GAAG;QAC9B,8DAA8D;QAC9D,MAAMC,OAAO,GAAG,MAAMhI,aAAa,CACjC,2CAA2C,EAC3C;YACE6C,WAAW,EAAE,cAAc;YAC3Bd,QAAQ,EAAE,KAAK;SAChB,CACF,AAAC;QACF,OAAOiG,OAAO,CAAC;IACjB,CAAC;IACD,MAAM3C,UAAU,GAAG,IAAM;QACvByC,aAAa,KAAKC,cAAc,EAAE,CAAC;QACnC,OAAOD,aAAa,CAAC;IACvB,CAAC,AAAC;IAEF,eAAeG,mBAAmB,CAAClG,QAAgB,EAAE;QACnD,MAAMsD,UAAU,EAAE,CAAC;QACnB,0GAA0G;QAC1G,IAAIwC,gBAAgB,CAACjD,GAAG,CAAC7C,QAAQ,CAAC,EAAE;YAClC,OAAO8F,gBAAgB,CAACvC,GAAG,CAACvD,QAAQ,CAAC,CAAE;QACzC,CAAC;QAED,8DAA8D;QAC9D,MAAMmG,QAAQ,GAAG,MAAMlI,aAAa,CAClC,oCAAoC,EACpC;YACE6C,WAAW,EAAE,cAAc;YAC3Bd,QAAQ;SACT,CACF,AAAC;QAEF8F,gBAAgB,CAACzD,GAAG,CAACrC,QAAQ,EAAEmG,QAAQ,CAAC,CAAC;QACzC,OAAOA,QAAQ,CAAC;IAClB,CAAC;IAED,MAAMC,gBAAgB,GAAG,IAAIhD,GAAG,EAAe,AAAC;IAEhD,SAASiD,mBAAmB,CAACrG,QAAgB,EAAE;QAC7C,0GAA0G;QAC1G,IAAIoG,gBAAgB,CAACvD,GAAG,CAAC7C,QAAQ,CAAC,EAAE;YAClC,OAAOoG,gBAAgB,CAAC7C,GAAG,CAACvD,QAAQ,CAAC,CAAE;QACzC,CAAC;QAED,MAAM2D,OAAO,GAAG,EAAE,AAAC;QAEnByC,gBAAgB,CAAC/D,GAAG,CAACrC,QAAQ,EAAE2D,OAAO,CAAC,CAAC;QACxC,OAAOA,OAAO,CAAC;IACjB,CAAC;IAED,eAAe1E,yBAAyB,CACtC,EACEqH,KAAK,CAAA,EACLvH,OAAO,CAAA,EACPwH,MAAM,CAAA,EACNvG,QAAQ,CAAA,EACRb,IAAI,CAAA,EACJ+F,MAAM,CAAA,EACNsB,WAAW,CAAA,EACXhC,WAAW,CAAA,EACXiC,WAAW,CAAA,EAWZ,EACD1C,WAAgC,GAAG/F,oBAAoB,CAAC+F,WAAW,EACnE;QACAM,IAAAA,OAAM,EAAA,QAAA,EACJN,WAAW,IAAI,IAAI,EACnB,sEAAsE,CACvE,CAAC;QAEF,IAAIwC,MAAM,KAAK,MAAM,EAAE;YACrBlC,IAAAA,OAAM,EAAA,QAAA,EAAClF,IAAI,EAAE,sEAAsE,CAAC,CAAC;QACvF,CAAC;QAED,MAAMwE,OAAO,GAAG0C,mBAAmB,CAACrG,QAAQ,CAAC,AAAC;QAE9C2D,OAAO,CAAC,uBAAuB,CAAC,GAAG5E,OAAO,CAAC;QAE3C,MAAM,EAAEF,SAAS,CAAA,EAAE,GAAG,MAAMqH,mBAAmB,CAAClG,QAAQ,CAAC,AAAC;QAE1D,OAAOnB,SAAS,CACd;YACEM,IAAI;YACJsH,WAAW;YACX9C,OAAO;YACPnF,MAAM,EAAE,EAAE;YACV8H,KAAK;YACLE,WAAW;SACZ,EACD;YACEzC,WAAW;YACX2C,OAAO,EAAE,MAAMrD,kCAAkC,CAAC;gBAAErD,QAAQ;aAAE,CAAC;YAC/D2G,kBAAkB,EAAEjD,qBAAqB,CAAC;gBAAE1D,QAAQ;gBAAEkF,MAAM;gBAAEV,WAAW;aAAE,CAAC;YAC5E,MAAMoC,mBAAmB,EAACC,WAAW,EAAE;gBACrC,MAAMjD,UAAU,GAAGjG,sBAAsB,CAACG,WAAW,CAAC,AAAC;gBAEvDL,KAAK,CAAC,4BAA4B,EAAEoJ,WAAW,CAAC,CAAC;gBAEjD,MAAMC,OAAO,GAAGC,IAAAA,aAAsB,uBAAA,EAACF,WAAW,CAAC,AAAC;gBAEpD,OAAO5I,aAAa,CAAC+D,KAAI,EAAA,QAAA,CAACgF,IAAI,CAACpD,UAAU,EAAEkD,OAAO,CAAC7B,cAAc,CAAC,EAAE6B,OAAO,EAAE;oBAC3ErD,GAAG,EAAE,IAAI;iBACV,CAAC,CAAC;YACL,CAAC;SACF,CACF,CAAC;IACJ,CAAC;IAED,OAAO;QACL,iGAAiG;QACjGT,kCAAkC;QAClCjD,wBAAwB;QAExB,MAAMkH,iBAAiB,EACrB,EACEjH,QAAQ,CAAA,EACRwE,WAAW,CAAA,EAIZ,EACDrE,KAAqB,EACrB;YACA,wHAAwH;YACxH,MAAM,EAAE+G,cAAc,CAAA,EAAE,GAAG,CAAC,MAAM7D,kCAAkC,CAAC;gBAAErD,QAAQ;aAAE,CAAC,CAAC,CAACmH,OAAO,AAAC;YAE5F,gCAAgC;YAChC,MAAMC,WAAW,GAAG,MAAMF,cAAc,CAAE,UACxC,sDAAsD;gBACtD,EAAE,CACH,AAAC;YAEF,MAAMG,OAAO,CAACC,GAAG,CACfC,KAAK,CAACC,IAAI,CAACJ,WAAW,CAAC,CAAC7F,GAAG,CAAC,OAAO,EAAEmF,OAAO,CAAA,EAAE,GAAK;gBACjD,KAAK,MAAM,EAAEJ,KAAK,CAAA,EAAEmB,QAAQ,CAAA,EAAE,IAAIf,OAAO,IAAI,EAAE,CAAE;oBAC/C,IAAI,CAACe,QAAQ,EAAE;wBACbhK,KAAK,CAAC,kCAAkC,EAAE;4BAAE6I,KAAK;yBAAE,CAAC,CAAC;wBACrD,SAAS;oBACX,CAAC;oBACD,MAAMoB,WAAW,GAAG1F,KAAI,EAAA,QAAA,CAACgF,IAAI,CAAC,SAAS,EAAEhH,QAAQ,EAAE2H,WAAW,CAACrB,KAAK,CAAC,CAAC,AAAC;oBAEvE,MAAMsB,IAAI,GAAG,MAAM3I,yBAAyB,CAC1C;wBACEqH,KAAK;wBACLC,MAAM,EAAE,KAAK;wBACbvG,QAAQ;wBACRjB,OAAO,EAAE,IAAIG,OAAO,EAAE;wBACtBsF,WAAW;qBACZ,EACD,IAAI,CACL,AAAC;oBAEF,MAAMqD,GAAG,GAAG,MAAMC,IAAAA,OAAmB,oBAAA,EAACF,IAAI,CAAC,AAAC;oBAC5CnK,KAAK,CAAC,aAAa,EAAE;wBAAEuC,QAAQ;wBAAEsG,KAAK;wBAAEuB,GAAG;qBAAE,CAAC,CAAC;oBAE/C1H,KAAK,CAACkC,GAAG,CAACqF,WAAW,EAAE;wBACrB9G,QAAQ,EAAEiH,GAAG;wBACbvF,YAAY,EAAE,QAAQ;wBACtByF,KAAK,EAAEzB,KAAK;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;QAED0B,UAAU,EAAEC,IAAAA,+BAA8B,+BAAA,EACxC,wCAAwC;QACxC,CAACC,GAAG,GAAK;YACP,OAAOC,UAAU,CAACD,GAAG,CAACE,GAAG,CAAC,CAACzC,QAAQ,CAACzC,UAAU,CAACpD,aAAa,CAAC,CAAC;QAChE,CAAC,EACDxB,aAAa,CACd;QACD+J,gBAAgB,EAAE,CAACrI,QAAgB,GAAK;YACtC,+FAA+F;YAE/F8F,gBAAgB,CAACwC,MAAM,CAACtI,QAAQ,CAAC,CAAC;YAClCmD,WAAW,CAACmF,MAAM,CAACtI,QAAQ,CAAC,CAAC;QAC/B,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAMmI,UAAU,GAAG,CAACC,GAAW,GAAK;IAClC,IAAI;QACF,OAAO,IAAI7C,GAAG,CAAC6C,GAAG,CAAC,CAAC;IACtB,EAAE,OAAM;QACN,OAAO,IAAI7C,GAAG,CAAC6C,GAAG,EAAE,oBAAoB,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC,AAAC;AAEK,MAAM5K,iBAAiB,GAAG,CAAC+K,OAAe,GAAK;IACpD,OAAOH,IAAG,EAAA,QAAA,CAACI,aAAa,CAACD,OAAO,CAAC,CAAC;AACpC,CAAC,AAAC;AAEF,MAAMZ,WAAW,GAAG,CAACrB,KAAa,GAAK;IACrC,IAAIA,KAAK,KAAK,EAAE,EAAE;QAChB,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,IAAIA,KAAK,KAAK,OAAO,EAAE;QACrB,MAAM,IAAIzE,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IACD,IAAIyE,KAAK,CAACpD,UAAU,CAAC,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACrD,CAAC;IACD,IAAIyE,KAAK,CAACV,QAAQ,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,IAAI/D,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACnD,CAAC;IACD,OAAOyE,KAAK,GAAG,MAAM,CAAC;AACxB,CAAC,AAAC;AAEF,SAAS/D,UAAU,CAACkG,GAAW,EAAE;IAC/B,uDAAuD;IACvD,mDAAmD;IACnD,6FAA6F;IAC7F,OAAOA,GAAG,CAACC,OAAO,qBAAqB,qBAAqB,CAAC,CAAC;AAChE,CAAC"}