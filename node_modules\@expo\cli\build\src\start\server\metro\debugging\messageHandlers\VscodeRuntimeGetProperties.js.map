{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeGetProperties.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { <PERSON><PERSON>and<PERSON> } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode doesn't seem to work nicely with missing `description` fields on `RemoteObject` instances.\n * It also tries to invoke `Runtime.callFunctionOn` on `Symbol` types, which crashes Hermes.\n * This handler tries to compensate for these two separate issues.\n *\n * @see https://github.com/facebook/hermes/issues/114\n * @see https://github.com/microsoft/vscode-js-debug/issues/1583\n */\nexport class VscodeRuntimeGetPropertiesHandler extends MessageHandler {\n  /** Keep track of `Runtime.getProperties` responses to intercept, by request id */\n  interceptGetProperties = new Set<number>();\n\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeGetProperties>) {\n    if (message.method === 'Runtime.getProperties') {\n      this.interceptGetProperties.add(message.id);\n    }\n\n    // Do not block propagation of this message\n    return false;\n  }\n\n  handleDeviceMessage(message: DeviceResponse<RuntimeGetProperties>) {\n    if ('id' in message && this.interceptGetProperties.has(message.id)) {\n      this.interceptGetProperties.delete(message.id);\n\n      for (const item of message.result.result ?? []) {\n        // Force-fully format the properties description to be an empty string\n        if (item.value) {\n          item.value.description = item.value.description ?? '';\n        }\n\n        // Avoid passing the `objectId` for symbol types.\n        // When collapsing in vscode, it will fetch information about the symbol using the `objectId`.\n        // The `Runtime.getProperties` request of the symbol hard-crashes Hermes.\n        if (item.value?.type === 'symbol' && item.value.objectId) {\n          delete item.value.objectId;\n        }\n      }\n    }\n\n    // Do not block propagation of this message\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-getProperties */\nexport type RuntimeGetProperties = CdpMessage<\n  'Runtime.getProperties',\n  Protocol.Runtime.GetPropertiesRequest,\n  Protocol.Runtime.GetPropertiesResponse\n>;\n"], "names": ["VscodeRuntimeGetPropertiesHandler", "MessageHandler", "interceptGetProperties", "Set", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "add", "id", "handleDeviceMessage", "has", "delete", "item", "result", "value", "description", "type", "objectId"], "mappings": "AAAA;;;;+<PERSON>ca<PERSON>,mCAAiC;;aAAjCA,iCAAiC;;gCAZf,mBAAmB;iCAClB,oBAAoB;AAW7C,MAAMA,iCAAiC,SAASC,eAAc,eAAA;IACnE,gFAAgF,GAChFC,sBAAsB,GAAG,IAAIC,GAAG,EAAU,CAAC;IAE3CC,SAAS,GAAG;QACV,OAAOC,IAAAA,gBAAe,gBAAA,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,CAAC,KAAK,QAAQ,CAAC;IAC/D;IAEAC,qBAAqB,CAACC,OAA8C,EAAE;QACpE,IAAIA,OAAO,CAACC,MAAM,KAAK,uBAAuB,EAAE;YAC9C,IAAI,CAACR,sBAAsB,CAACS,GAAG,CAACF,OAAO,CAACG,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,2CAA2C;QAC3C,OAAO,KAAK,CAAC;IACf;IAEAC,mBAAmB,CAACJ,OAA6C,EAAE;QACjE,IAAI,IAAI,IAAIA,OAAO,IAAI,IAAI,CAACP,sBAAsB,CAACY,GAAG,CAACL,OAAO,CAACG,EAAE,CAAC,EAAE;YAClE,IAAI,CAACV,sBAAsB,CAACa,MAAM,CAACN,OAAO,CAACG,EAAE,CAAC,CAAC;YAE/C,KAAK,MAAMI,IAAI,IAAIP,OAAO,CAACQ,MAAM,CAACA,MAAM,IAAI,EAAE,CAAE;oBAS1CD,GAAU;gBARd,sEAAsE;gBACtE,IAAIA,IAAI,CAACE,KAAK,EAAE;oBACdF,IAAI,CAACE,KAAK,CAACC,WAAW,GAAGH,IAAI,CAACE,KAAK,CAACC,WAAW,IAAI,EAAE,CAAC;gBACxD,CAAC;gBAED,iDAAiD;gBACjD,8FAA8F;gBAC9F,yEAAyE;gBACzE,IAAIH,CAAAA,CAAAA,GAAU,GAAVA,IAAI,CAACE,KAAK,SAAM,GAAhBF,KAAAA,CAAgB,GAAhBA,GAAU,CAAEI,IAAI,CAAA,KAAK,QAAQ,IAAIJ,IAAI,CAACE,KAAK,CAACG,QAAQ,EAAE;oBACxD,OAAOL,IAAI,CAACE,KAAK,CAACG,QAAQ,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,OAAO,KAAK,CAAC;IACf;CACD"}