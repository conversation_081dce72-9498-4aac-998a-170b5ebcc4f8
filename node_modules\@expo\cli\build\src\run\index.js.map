{"version": 3, "sources": ["../../../src/run/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { logPlatformRunCommand } from './hints';\nimport { Command } from '../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../utils/args';\nimport { CommandError, logCmdError } from '../utils/errors';\n\nexport const expoRun: Command = async (argv) => {\n  const args = assertWithOptionsArgs(\n    {\n      // Types\n      '--help': Boolean,\n      // Aliases\n      '-h': '--help',\n    },\n    {\n      argv,\n      // Allow additional flags for both android and ios commands\n      permissive: true,\n    }\n  );\n\n  try {\n    let [platform] = args._ ?? [];\n\n    // Workaround, filter `--flag` as platform\n    if (platform?.startsWith('-')) {\n      platform = '';\n    }\n\n    // Remove the platform from raw arguments, when provided\n    const argsWithoutPlatform = !platform ? argv : argv?.splice(1);\n\n    // Do not capture `--help` when platform is provided\n    if (!platform && args['--help']) {\n      printHelp(\n        'Run the native app locally',\n        `npx expo run <android|ios>`,\n        chalk`{dim $} npx expo run <android|ios> --help  Output usage information`\n      );\n    }\n\n    if (!platform) {\n      const { selectAsync } = await import('../utils/prompts.js');\n      platform = await selectAsync('Select the platform to run', [\n        { title: 'Android', value: 'android' },\n        { title: 'iOS', value: 'ios' },\n      ]);\n    }\n\n    logPlatformRunCommand(platform, argsWithoutPlatform);\n\n    switch (platform) {\n      case 'android': {\n        const { expoRunAndroid } = await import('./android/index.js');\n        return expoRunAndroid(argsWithoutPlatform);\n      }\n\n      case 'ios': {\n        const { expoRunIos } = await import('./ios/index.js');\n        return expoRunIos(argsWithoutPlatform);\n      }\n\n      default:\n        throw new CommandError('UNSUPPORTED_PLATFORM', `Unsupported platform: ${platform}`);\n    }\n  } catch (error: any) {\n    logCmdError(error);\n  }\n};\n"], "names": ["expoRun", "argv", "args", "assertWithOptionsArgs", "Boolean", "permissive", "platform", "_", "startsWith", "argsWithoutPlatform", "splice", "printHelp", "chalk", "selectAsync", "title", "value", "logPlatformRunCommand", "expoRunAndroid", "expoRunIos", "CommandError", "error", "logCmdError"], "mappings": "AAAA;;;;;+BAQa<PERSON>,SAAO;;aAAPA,OAAO;;;8DAPF,OAAO;;;;;;uBAEa,SAAS;sBAEE,eAAe;wBACtB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMA,OAAO,GAAY,OAAOC,IAAI,GAAK;IAC9C,MAAMC,IAAI,GAAGC,IAAAA,KAAqB,sBAAA,EAChC;QACE,QAAQ;QACR,QAAQ,EAAEC,OAAO;QACjB,UAAU;QACV,IAAI,EAAE,QAAQ;KACf,EACD;QACEH,IAAI;QACJ,2DAA2D;QAC3DI,UAAU,EAAE,IAAI;KACjB,CACF,AAAC;IAEF,IAAI;QACF,IAAI,CAACC,QAAQ,CAAC,GAAGJ,IAAI,CAACK,CAAC,IAAI,EAAE,AAAC;QAE9B,0CAA0C;QAC1C,IAAID,QAAQ,QAAY,GAApBA,KAAAA,CAAoB,GAApBA,QAAQ,CAAEE,UAAU,CAAC,GAAG,CAAC,EAAE;YAC7BF,QAAQ,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,wDAAwD;QACxD,MAAMG,mBAAmB,GAAG,CAACH,QAAQ,GAAGL,IAAI,GAAGA,IAAI,QAAQ,GAAZA,KAAAA,CAAY,GAAZA,IAAI,CAAES,MAAM,CAAC,CAAC,CAAC,AAAC;QAE/D,oDAAoD;QACpD,IAAI,CAACJ,QAAQ,IAAIJ,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/BS,IAAAA,KAAS,UAAA,EACP,4BAA4B,EAC5B,CAAC,0BAA0B,CAAC,EAC5BC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,mEAAmE,CAAC,CAC3E,CAAC;QACJ,CAAC;QAED,IAAI,CAACN,QAAQ,EAAE;YACb,MAAM,EAAEO,WAAW,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,qBAAqB,GAAC,AAAC;YAC5DP,QAAQ,GAAG,MAAMO,WAAW,CAAC,4BAA4B,EAAE;gBACzD;oBAAEC,KAAK,EAAE,SAAS;oBAAEC,KAAK,EAAE,SAAS;iBAAE;gBACtC;oBAAED,KAAK,EAAE,KAAK;oBAAEC,KAAK,EAAE,KAAK;iBAAE;aAC/B,CAAC,CAAC;QACL,CAAC;QAEDC,IAAAA,MAAqB,sBAAA,EAACV,QAAQ,EAAEG,mBAAmB,CAAC,CAAC;QAErD,OAAQH,QAAQ;YACd,KAAK,SAAS;gBAAE;oBACd,MAAM,EAAEW,cAAc,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,oBAAoB,GAAC,AAAC;oBAC9D,OAAOA,cAAc,CAACR,mBAAmB,CAAC,CAAC;gBAC7C,CAAC;YAED,KAAK,KAAK;gBAAE;oBACV,MAAM,EAAES,UAAU,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,gBAAgB,GAAC,AAAC;oBACtD,OAAOA,UAAU,CAACT,mBAAmB,CAAC,CAAC;gBACzC,CAAC;YAED;gBACE,MAAM,IAAIU,OAAY,aAAA,CAAC,sBAAsB,EAAE,CAAC,sBAAsB,EAAEb,QAAQ,CAAC,CAAC,CAAC,CAAC;SACvF;IACH,EAAE,OAAOc,KAAK,EAAO;QACnBC,IAAAA,OAAW,YAAA,EAACD,KAAK,CAAC,CAAC;IACrB,CAAC;AACH,CAAC,AAAC"}