import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  RefreshControl,
  SafeAreaView
} from 'react-native';
import { storeService } from '../../services/store';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface DashboardStats {
  totalSales: number;
  pendingOrders: number;
  completedOrders: number;
  revenue: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

const DashboardScreen = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await storeService.getDashboardStats();
      setStats(data);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={fetchDashboardData} />
        }
      >
        <Text style={styles.title}>Dashboard</Text>
        
        {error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
          </Card>
        ) : stats ? (
          <>
            <View style={styles.statsRow}>
              <StatCard 
                title="Total Sales" 
                value={stats.totalSales.toString()} 
                icon="shopping-cart" 
                color="#4A6572"
              />
              <StatCard 
                title="Pending Orders" 
                value={stats.pendingOrders.toString()} 
                icon="hourglass-empty" 
                color="#FF9800"
              />
            </View>
            
            <View style={styles.statsRow}>
              <StatCard 
                title="Completed Orders" 
                value={stats.completedOrders.toString()} 
                icon="check-circle" 
                color="#4CAF50"
              />
              <StatCard 
                title="Daily Revenue" 
                value={`$${stats.revenue.daily.toFixed(2)}`} 
                icon="attach-money" 
                color="#FF6B35"
              />
            </View>
            
            <Card style={styles.revenueCard}>
              <Text style={styles.cardTitle}>Revenue Overview</Text>
              <View style={styles.revenueRow}>
                <View style={styles.revenueItem}>
                  <Text style={styles.revenueLabel}>Daily</Text>
                  <Text style={styles.revenueValue}>${stats.revenue.daily.toFixed(2)}</Text>
                </View>
                <View style={styles.revenueItem}>
                  <Text style={styles.revenueLabel}>Weekly</Text>
                  <Text style={styles.revenueValue}>${stats.revenue.weekly.toFixed(2)}</Text>
                </View>
                <View style={styles.revenueItem}>
                  <Text style={styles.revenueLabel}>Monthly</Text>
                  <Text style={styles.revenueValue}>${stats.revenue.monthly.toFixed(2)}</Text>
                </View>
              </View>
            </Card>
          </>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};

interface StatCardProps {
  title: string;
  value: string;
  icon: string;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color }) => (
  <Card style={styles.statCard}>
    <View style={styles.statIconContainer}>
      <Icon name={icon} size={24} color={color} />
    </View>
    <Text style={styles.statValue}>{value}</Text>
    <Text style={styles.statTitle}>{title}</Text>
  </Card>
);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: SPACING.md,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.xl,
    color: COLORS.secondary,
    marginBottom: SPACING.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: SPACING.xs,
    alignItems: 'center',
    padding: SPACING.md,
  },
  statIconContainer: {
    marginBottom: SPACING.sm,
  },
  statValue: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
    marginBottom: SPACING.xs,
  },
  statTitle: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary + '80',
  },
  revenueCard: {
    marginTop: SPACING.sm,
  },
  cardTitle: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.secondary,
    marginBottom: SPACING.md,
  },
  revenueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  revenueItem: {
    alignItems: 'center',
  },
  revenueLabel: {
    ...FONTS.regular,
    fontSize: FONTS.sizes.sm,
    color: COLORS.secondary + '80',
    marginBottom: SPACING.xs,
  },
  revenueValue: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.md,
    color: COLORS.accent,
  },
  errorCard: {
    backgroundColor: COLORS.error + '20',
  },
  errorText: {
    ...FONTS.medium,
    fontSize: FONTS.sizes.md,
    color: COLORS.error,
    textAlign: 'center',
  },
});

export default DashboardScreen;