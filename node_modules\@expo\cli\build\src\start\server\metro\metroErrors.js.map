{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrors.ts"], "sourcesContent": ["// Used to cast a type to metro errors without depending on specific versions of metro.\n\nexport type FileAndDirCandidates = {\n  dir: FileCandidates;\n  file: FileCandidates;\n};\n\n/**\n * This is a way to describe what files we tried to look for when resolving\n * a module name as file. This is mainly used for error reporting, so that\n * we can explain why we cannot resolve a module.\n */\nexport type FileCandidates =\n  // We only tried to resolve a specific asset.\n  | { type: 'asset'; name: string }\n  // We attempted to resolve a name as being a source file (ex. JavaScript,\n  // JSON...), in which case there can be several extensions we tried, for\n  // example `/js/foo.ios.js`, `/js/foo.js`, etc. for a single prefix '/js/foo'.\n  | {\n      type: 'sourceFile';\n      filePathPrefix: string;\n      candidateExts: readonly string[];\n    };\n\ntype FailedToResolveNameError = Error & {\n  dirPaths: string[];\n  extraPaths: string[];\n};\n\ntype FailedToResolvePathError = Error & {\n  candidates: FileAndDirCandidates;\n};\n\nexport function isFailedToResolveNameError(error: any): error is FailedToResolveNameError {\n  return !!error && 'extraPaths' in error && error.constructor.name === 'FailedToResolveNameError';\n}\n\nexport function isFailedToResolvePathError(error: any): error is FailedToResolvePathError {\n  return (\n    !!error &&\n    'candidates' in error &&\n    error.constructor.name === 'FailedToResolvePathError' &&\n    !error.message.includes('Importing native-only module')\n  );\n}\n"], "names": ["isFailedToResolveNameError", "isFailedToResolvePathError", "error", "constructor", "name", "message", "includes"], "mappings": "AAAA,uFAAuF;AAEvF;;;;;;;;;;;IA+BgBA,0BAA0B,MAA1BA,0BAA0B;IAI1BC,0BAA0B,MAA1BA,0BAA0B;;AAJnC,SAASD,0BAA0B,CAACE,KAAU,EAAqC;IACxF,OAAO,CAAC,CAACA,KAAK,IAAI,YAAY,IAAIA,KAAK,IAAIA,KAAK,CAACC,WAAW,CAACC,IAAI,KAAK,0BAA0B,CAAC;AACnG,CAAC;AAEM,SAASH,0BAA0B,CAACC,KAAU,EAAqC;IACxF,OACE,CAAC,CAACA,KAAK,IACP,YAAY,IAAIA,KAAK,IACrBA,KAAK,CAACC,WAAW,CAACC,IAAI,KAAK,0BAA0B,IACrD,CAACF,KAAK,CAACG,OAAO,CAACC,QAAQ,CAAC,8BAA8B,CAAC,CACvD;AACJ,CAAC"}