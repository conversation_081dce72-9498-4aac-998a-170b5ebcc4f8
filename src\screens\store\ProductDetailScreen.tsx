import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Image,
  SafeAreaView,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { storeService } from '../../services/store';
import { Product } from '../../types';
import { COLORS, FONTS, SPACING } from '../../theme';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Input from '../../components/Input';
import Icon from 'react-native-vector-icons/MaterialIcons';

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.string().min(1, 'Price is required'),
  stock: z.string().min(1, 'Stock is required'),
  category: z.string().min(1, 'Category is required'),
  image: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
});

type ProductFormData = z.infer<typeof productSchema>;

const ProductDetailScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const { product, isNew } = route.params as { product?: Product; isNew: boolean };
  
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, formState: { errors }, reset } = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price?.toString() || '',
      stock: product?.stock?.toString() || '',
      category: product?.category || '',
      image: product?.image || '',
    },
  });

  const onSubmit = async (data: ProductFormData) => {
    setIsLoading(true);
    try {
      const productData = {
        ...data,
        price: parseFloat(data.price),
        stock: parseInt(data.stock),
      };

      if (isNew) {
        await storeService.createProduct(productData);
        Alert.alert('Success', 'Product created successfully');
      } else {
        await storeService.updateProduct(product!.id, productData);
        Alert.alert('Success', 'Product updated successfully');
      }
      
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save product');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.header}>
          <Button
            title=""
            onPress={() => navigation.goBack()}
            variant="outline"
            size="small"
            style={styles.backButton}
          />
          <Text style={styles.title}>
            {isNew ? 'Add Product' : 'Edit Product'}
          </Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.formCard}>
            <Controller
              control={control}
              name="name"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Product Name"
                  placeholder="Enter product name"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.name?.message}
                  containerStyle={styles.inputContainer}
                />
              )}
            />

            <Controller
              control={control}
              name="description"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Description"
                  placeholder="Enter product description"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.description?.message}
                  multiline
                  numberOfLines={3}
                  containerStyle={styles.inputContainer}
                />
              )}
            />

            <View style={styles.row}>
              <Controller
                control={control}
                name="price"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Price ($)"
                    placeholder="0.00"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    error={errors.price?.message}
                    keyboardType="decimal-pad"
                    containerStyle={[styles.inputContainer, styles.halfWidth]}
                  />
                )}
              />

              <Controller
                control={control}
                name="stock"
                render={({ field: { onChange, onBlur, value } }) => (
                  <Input
                    label="Stock"
                    placeholder="0"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    error={errors.stock?.message}
                    keyboardType="number-pad"
                    containerStyle={[styles.inputContainer, styles.halfWidth]}
                  />
                )}
              />
            </View>

            <Controller
              control={control}
              name="category"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Category"
                  placeholder="Enter category"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.category?.message}
                  containerStyle={styles.inputContainer}
                />
              )}
            />

            <Controller
              control={control}
              name="image"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Image URL (Optional)"
                  placeholder="https://example.com/image.jpg"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  error={errors.image?.message}
                  containerStyle={styles.inputContainer}
                />
              )}
            />
          </Card>

          <Button
            title={isNew ? 'Create Product' : 'Update Product'}
            onPress={handleSubmit(onSubmit)}
            isLoading={isLoading}
            style={styles.submitButton}
          />
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  title: {
    ...FONTS.bold,
    fontSize: FONTS.sizes.lg,
    color: COLORS.secondary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  formCard: {
    marginBottom: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.md,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  submitButton: {
    marginBottom: SPACING.xl,
  },
});

export default ProductDetailScreen;
