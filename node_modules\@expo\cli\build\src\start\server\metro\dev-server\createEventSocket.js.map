{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createEventSocket.ts"], "sourcesContent": ["import { format as prettyFormat, plugins as prettyPlugins } from 'pretty-format';\nimport { WebSocketServer } from 'ws';\n\nimport type { createMessagesSocket } from './createMessageSocket';\nimport { createBroadcaster } from './utils/createSocketBroadcaster';\nimport { createSocketMap } from './utils/createSocketMap';\nimport { parseRawMessage, serializeMessage } from './utils/socketMessages';\n\nconst debug = require('debug')('expo:metro:devserver:eventsSocket') as typeof console.log;\n\ntype EventsSocketOptions = {\n  /** The message endpoint broadcaster, used to relay commands from Metro */\n  broadcast: ReturnType<typeof createMessagesSocket>['broadcast'];\n};\n\n/**\n * Metro events server that dispatches all Metro events to connected clients.\n * This includes logs, errors, bundling progression, etc.\n */\nexport function createEventsSocket(options: EventsSocketOptions) {\n  const clients = createSocketMap();\n  const broadcast = createBroadcaster(clients.map);\n\n  const server = new WebSocketServer({\n    noServer: true,\n    verifyClient({ origin }: { origin: string }) {\n      // This exposes the full JS logs and enables issuing commands like reload\n      // so let's make sure only locally running stuff can connect to it\n      // origin is only checked if it is set, e.g. when the request is made from a (CORS) browser\n      // any 'back-end' connection isn't CORS at all, and has full control over the origin header,\n      // so there is no point in checking it security wise\n      return !origin || origin.startsWith('http://localhost:') || origin.startsWith('file:');\n    },\n  });\n\n  server.on('connection', (socket) => {\n    const client = clients.registerSocket(socket);\n\n    // Register disconnect handlers\n    socket.on('close', client.terminate);\n    socket.on('error', client.terminate);\n    // Register message handler\n    socket.on('message', (data, isBinary) => {\n      const message = parseRawMessage<Command>(data, isBinary);\n      if (!message) return;\n\n      if (message.type === 'command') {\n        options.broadcast(message.command, message.params);\n      } else {\n        debug(`Received unknown message type: ${message.type}`);\n      }\n    });\n  });\n\n  return {\n    endpoint: '/events' as const,\n    server: new WebSocketServer({ noServer: true }),\n    reportMetroEvent: (event: any) => {\n      // Avoid serializing data if there are no clients\n      if (!clients.map.size) {\n        return;\n      }\n\n      return broadcast(null, serializeMetroEvent(event));\n    },\n  };\n}\n\ntype Command = {\n  type: 'command';\n  command: string;\n  params?: any;\n};\n\nfunction serializeMetroEvent(message: any): string {\n  // Some types reported by Metro are not serializable\n  if (message && message.error && message.error instanceof Error) {\n    return serializeMessage({\n      ...message,\n      error: prettyFormat(message.error, {\n        escapeString: true,\n        highlight: true,\n        maxDepth: 3,\n        min: true,\n      }),\n    });\n  }\n\n  if (message && message.type === 'client_log') {\n    return serializeMessage({\n      ...message,\n      data: message.data.map((item: any) =>\n        typeof item === 'string'\n          ? item\n          : prettyFormat(item, {\n              escapeString: true,\n              highlight: true,\n              maxDepth: 3,\n              min: true,\n              plugins: [prettyPlugins.ReactElement],\n            })\n      ),\n    });\n  }\n\n  return serializeMessage(message);\n}\n"], "names": ["createEventsSocket", "debug", "require", "options", "clients", "createSocketMap", "broadcast", "createBroadcaster", "map", "server", "WebSocketServer", "noServer", "verifyClient", "origin", "startsWith", "on", "socket", "client", "registerSocket", "terminate", "data", "isBinary", "message", "parseRawMessage", "type", "command", "params", "endpoint", "reportMetroEvent", "event", "size", "serializeMetroEvent", "error", "Error", "serializeMessage", "prettyFormat", "escapeString", "highlight", "max<PERSON><PERSON><PERSON>", "min", "item", "plugins", "prettyPlugins", "ReactElement"], "mappings": "AAAA;;;;+BAmBgBA,oBAAkB;;aAAlBA,kBAAkB;;;yBAnB+B,eAAe;;;;;;;yBAChD,IAAI;;;;;;yCAGF,iCAAiC;iCACnC,yBAAyB;gCACP,wBAAwB;AAE1E,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mCAAmC,CAAC,AAAsB,AAAC;AAWnF,SAASF,kBAAkB,CAACG,OAA4B,EAAE;IAC/D,MAAMC,OAAO,GAAGC,IAAAA,gBAAe,gBAAA,GAAE,AAAC;IAClC,MAAMC,SAAS,GAAGC,IAAAA,wBAAiB,kBAAA,EAACH,OAAO,CAACI,GAAG,CAAC,AAAC;IAEjD,MAAMC,MAAM,GAAG,IAAIC,CAAAA,GAAe,EAAA,CAAA,gBAAA,CAAC;QACjCC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAC,EAAEC,MAAM,CAAA,EAAsB,EAAE;YAC3C,yEAAyE;YACzE,kEAAkE;YAClE,2FAA2F;YAC3F,4FAA4F;YAC5F,oDAAoD;YACpD,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACC,UAAU,CAAC,mBAAmB,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzF,CAAC;KACF,CAAC,AAAC;IAEHL,MAAM,CAACM,EAAE,CAAC,YAAY,EAAE,CAACC,MAAM,GAAK;QAClC,MAAMC,MAAM,GAAGb,OAAO,CAACc,cAAc,CAACF,MAAM,CAAC,AAAC;QAE9C,+BAA+B;QAC/BA,MAAM,CAACD,EAAE,CAAC,OAAO,EAAEE,MAAM,CAACE,SAAS,CAAC,CAAC;QACrCH,MAAM,CAACD,EAAE,CAAC,OAAO,EAAEE,MAAM,CAACE,SAAS,CAAC,CAAC;QACrC,2BAA2B;QAC3BH,MAAM,CAACD,EAAE,CAAC,SAAS,EAAE,CAACK,IAAI,EAAEC,QAAQ,GAAK;YACvC,MAAMC,OAAO,GAAGC,IAAAA,eAAe,gBAAA,EAAUH,IAAI,EAAEC,QAAQ,CAAC,AAAC;YACzD,IAAI,CAACC,OAAO,EAAE,OAAO;YAErB,IAAIA,OAAO,CAACE,IAAI,KAAK,SAAS,EAAE;gBAC9BrB,OAAO,CAACG,SAAS,CAACgB,OAAO,CAACG,OAAO,EAAEH,OAAO,CAACI,MAAM,CAAC,CAAC;YACrD,OAAO;gBACLzB,KAAK,CAAC,CAAC,+BAA+B,EAAEqB,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO;QACLG,QAAQ,EAAE,SAAS;QACnBlB,MAAM,EAAE,IAAIC,CAAAA,GAAe,EAAA,CAAA,gBAAA,CAAC;YAAEC,QAAQ,EAAE,IAAI;SAAE,CAAC;QAC/CiB,gBAAgB,EAAE,CAACC,KAAU,GAAK;YAChC,iDAAiD;YACjD,IAAI,CAACzB,OAAO,CAACI,GAAG,CAACsB,IAAI,EAAE;gBACrB,OAAO;YACT,CAAC;YAED,OAAOxB,SAAS,CAAC,IAAI,EAAEyB,mBAAmB,CAACF,KAAK,CAAC,CAAC,CAAC;QACrD,CAAC;KACF,CAAC;AACJ,CAAC;AAQD,SAASE,mBAAmB,CAACT,OAAY,EAAU;IACjD,oDAAoD;IACpD,IAAIA,OAAO,IAAIA,OAAO,CAACU,KAAK,IAAIV,OAAO,CAACU,KAAK,YAAYC,KAAK,EAAE;QAC9D,OAAOC,IAAAA,eAAgB,iBAAA,EAAC;YACtB,GAAGZ,OAAO;YACVU,KAAK,EAAEG,IAAAA,aAAY,EAAA,OAAA,EAACb,OAAO,CAACU,KAAK,EAAE;gBACjCI,YAAY,EAAE,IAAI;gBAClBC,SAAS,EAAE,IAAI;gBACfC,QAAQ,EAAE,CAAC;gBACXC,GAAG,EAAE,IAAI;aACV,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED,IAAIjB,OAAO,IAAIA,OAAO,CAACE,IAAI,KAAK,YAAY,EAAE;QAC5C,OAAOU,IAAAA,eAAgB,iBAAA,EAAC;YACtB,GAAGZ,OAAO;YACVF,IAAI,EAAEE,OAAO,CAACF,IAAI,CAACZ,GAAG,CAAC,CAACgC,IAAS,GAC/B,OAAOA,IAAI,KAAK,QAAQ,GACpBA,IAAI,GACJL,IAAAA,aAAY,EAAA,OAAA,EAACK,IAAI,EAAE;oBACjBJ,YAAY,EAAE,IAAI;oBAClBC,SAAS,EAAE,IAAI;oBACfC,QAAQ,EAAE,CAAC;oBACXC,GAAG,EAAE,IAAI;oBACTE,OAAO,EAAE;wBAACC,aAAa,EAAA,QAAA,CAACC,YAAY;qBAAC;iBACtC,CAAC,CACP;SACF,CAAC,CAAC;IACL,CAAC;IAED,OAAOT,IAAAA,eAAgB,iBAAA,EAACZ,OAAO,CAAC,CAAC;AACnC,CAAC"}