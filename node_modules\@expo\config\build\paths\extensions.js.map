{"version": 3, "file": "extensions.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "e", "__esModule", "default", "getExtensions", "platforms", "extensions", "workflows", "assert", "Array", "isArray", "fileExtensions", "workflow", "platform", "extension", "push", "filter", "Boolean", "join", "getLanguageExtensionsInOrder", "isTS", "isModern", "isReact", "addLanguage", "lang", "unshift", "getBareExtensions", "languageOptions", "_addMiscellaneousExtensions", "includes"], "sources": ["../../src/paths/extensions.ts"], "sourcesContent": ["import assert from 'assert';\n\nexport type LanguageOptions = {\n  isTS: boolean;\n  isModern: boolean;\n  isReact: boolean;\n};\n\nexport function getExtensions(\n  platforms: string[],\n  extensions: string[],\n  workflows: string[]\n): string[] {\n  // In the past we used spread operators to collect the values so now we enforce type safety on them.\n  assert(Array.isArray(platforms), 'Expected: `platforms: string[]`');\n  assert(Array.isArray(extensions), 'Expected: `extensions: string[]`');\n  assert(Array.isArray(workflows), 'Expected: `workflows: string[]`');\n\n  const fileExtensions = [];\n  // support .expo files\n  for (const workflow of [...workflows, '']) {\n    // Ensure order is correct: [platformA.js, platformB.js, js]\n    for (const platform of [...platforms, '']) {\n      // Support both TypeScript and JavaScript\n      for (const extension of extensions) {\n        fileExtensions.push([platform, workflow, extension].filter(Boolean).join('.'));\n      }\n    }\n  }\n  return fileExtensions;\n}\n\nexport function getLanguageExtensionsInOrder({\n  isTS,\n  isModern,\n  isReact,\n}: LanguageOptions): string[] {\n  // @ts-ignore: filter removes false type\n  const addLanguage = (lang: string): string[] => [lang, isReact && `${lang}x`].filter(Boolean);\n\n  // Support JavaScript\n  let extensions = addLanguage('js');\n\n  if (isModern) {\n    extensions.unshift('mjs');\n  }\n  if (isTS) {\n    extensions = [...addLanguage('ts'), ...extensions];\n  }\n\n  return extensions;\n}\n\nexport function getBareExtensions(\n  platforms: string[],\n  languageOptions: LanguageOptions = { isTS: true, isModern: true, isReact: true }\n): string[] {\n  const fileExtensions = getExtensions(\n    platforms,\n    getLanguageExtensionsInOrder(languageOptions),\n    []\n  );\n  // Always add these last\n  _addMiscellaneousExtensions(platforms, fileExtensions);\n  return fileExtensions;\n}\n\nfunction _addMiscellaneousExtensions(platforms: string[], fileExtensions: string[]): string[] {\n  // Always add these with no platform extension\n  // In the future we may want to add platform and workspace extensions to json.\n  fileExtensions.push('json');\n  // Native doesn't currently support web assembly.\n  if (platforms.includes('web')) {\n    fileExtensions.push('wasm');\n  }\n  return fileExtensions;\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA4B,SAAAC,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAQrB,SAASG,aAAaA,CAC3BC,SAAmB,EACnBC,UAAoB,EACpBC,SAAmB,EACT;EACV;EACA,IAAAC,iBAAM,EAACC,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,EAAE,iCAAiC,CAAC;EACnE,IAAAG,iBAAM,EAACC,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,EAAE,kCAAkC,CAAC;EACrE,IAAAE,iBAAM,EAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE,iCAAiC,CAAC;EAEnE,MAAMI,cAAc,GAAG,EAAE;EACzB;EACA,KAAK,MAAMC,QAAQ,IAAI,CAAC,GAAGL,SAAS,EAAE,EAAE,CAAC,EAAE;IACzC;IACA,KAAK,MAAMM,QAAQ,IAAI,CAAC,GAAGR,SAAS,EAAE,EAAE,CAAC,EAAE;MACzC;MACA,KAAK,MAAMS,SAAS,IAAIR,UAAU,EAAE;QAClCK,cAAc,CAACI,IAAI,CAAC,CAACF,QAAQ,EAAED,QAAQ,EAAEE,SAAS,CAAC,CAACE,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;MAChF;IACF;EACF;EACA,OAAOP,cAAc;AACvB;AAEO,SAASQ,4BAA4BA,CAAC;EAC3CC,IAAI;EACJC,QAAQ;EACRC;AACe,CAAC,EAAY;EAC5B;EACA,MAAMC,WAAW,GAAIC,IAAY,IAAe,CAACA,IAAI,EAAEF,OAAO,IAAI,GAAGE,IAAI,GAAG,CAAC,CAACR,MAAM,CAACC,OAAO,CAAC;;EAE7F;EACA,IAAIX,UAAU,GAAGiB,WAAW,CAAC,IAAI,CAAC;EAElC,IAAIF,QAAQ,EAAE;IACZf,UAAU,CAACmB,OAAO,CAAC,KAAK,CAAC;EAC3B;EACA,IAAIL,IAAI,EAAE;IACRd,UAAU,GAAG,CAAC,GAAGiB,WAAW,CAAC,IAAI,CAAC,EAAE,GAAGjB,UAAU,CAAC;EACpD;EAEA,OAAOA,UAAU;AACnB;AAEO,SAASoB,iBAAiBA,CAC/BrB,SAAmB,EACnBsB,eAAgC,GAAG;EAAEP,IAAI,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CAAC,EACtE;EACV,MAAMX,cAAc,GAAGP,aAAa,CAClCC,SAAS,EACTc,4BAA4B,CAACQ,eAAe,CAAC,EAC7C,EACF,CAAC;EACD;EACAC,2BAA2B,CAACvB,SAAS,EAAEM,cAAc,CAAC;EACtD,OAAOA,cAAc;AACvB;AAEA,SAASiB,2BAA2BA,CAACvB,SAAmB,EAAEM,cAAwB,EAAY;EAC5F;EACA;EACAA,cAAc,CAACI,IAAI,CAAC,MAAM,CAAC;EAC3B;EACA,IAAIV,SAAS,CAACwB,QAAQ,CAAC,KAAK,CAAC,EAAE;IAC7BlB,cAAc,CAACI,IAAI,CAAC,MAAM,CAAC;EAC7B;EACA,OAAOJ,cAAc;AACvB", "ignoreList": []}