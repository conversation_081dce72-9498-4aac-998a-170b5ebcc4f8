{"version": 3, "sources": ["../../../../../src/utils/telemetry/clients/RudderDetachedClient.ts"], "sourcesContent": ["import { spawn } from 'node:child_process';\nimport fs from 'node:fs';\nimport path from 'node:path';\n\nimport { createTempFilePath } from '../../createTempPath';\nimport type { TelemetryClient, TelemetryClientStrategy, TelemetryRecordInternal } from '../types';\n\nconst debug = require('debug')('expo:telemetry:client:detached') as typeof console.log;\n\nexport class RudderDetachedClient implements TelemetryClient {\n  /** This client should be used for short-lived commands */\n  readonly strategy: TelemetryClientStrategy = 'detached';\n  /** All recorded telemetry events */\n  private records: TelemetryRecordInternal[] = [];\n\n  abort() {\n    return this.records;\n  }\n\n  record(record: TelemetryRecordInternal[]) {\n    this.records.push(\n      ...record.map((record) => ({\n        ...record,\n        originalTimestamp: record.sentAt,\n      }))\n    );\n  }\n\n  async flush() {\n    try {\n      if (!this.records.length) {\n        return debug('No records to flush, skipping...');\n      }\n\n      const file = createTempFilePath('expo-telemetry.json');\n      const data = JSON.stringify({ records: this.records });\n\n      this.records = [];\n\n      await fs.promises.mkdir(path.dirname(file), { recursive: true });\n      await fs.promises.writeFile(file, data);\n\n      const child = spawn(process.execPath, [require.resolve('./flushRudderDetached'), file], {\n        detached: true,\n        windowsHide: true,\n        shell: false,\n        stdio: 'ignore',\n      });\n\n      child.unref();\n    } catch (error) {\n      // This could fail if any direct or indirect imports change during an upgrade to the `expo` dependency via `npx expo install --fix`,\n      // since this file may no longer be present after the upgrade, but before the process under the old Expo CLI version is terminated.\n      debug('Exception while initiating detached flush:', error);\n    }\n\n    debug('Detached flush started');\n  }\n}\n"], "names": ["RudderDetachedClient", "debug", "require", "strategy", "records", "abort", "record", "push", "map", "originalTimestamp", "sentAt", "flush", "length", "file", "createTempFilePath", "data", "JSON", "stringify", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "child", "spawn", "process", "execPath", "resolve", "detached", "windowsHide", "shell", "stdio", "unref", "error"], "mappings": "AAAA;;;;+BASaA,sBAAoB;;aAApBA,oBAAoB;;;yBATX,oBAAoB;;;;;;;8DAC3B,SAAS;;;;;;;8DACP,WAAW;;;;;;gCAEO,sBAAsB;;;;;;AAGzD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAEhF,MAAMF,oBAAoB;IAC/B,wDAAwD,GACxD,AAASG,QAAQ,GAA4B,UAAU,CAAC;IACxD,kCAAkC,GAClC,AAAQC,OAAO,GAA8B,EAAE,CAAC;IAEhDC,KAAK,GAAG;QACN,OAAO,IAAI,CAACD,OAAO,CAAC;IACtB;IAEAE,MAAM,CAACA,MAAiC,EAAE;QACxC,IAAI,CAACF,OAAO,CAACG,IAAI,IACZD,MAAM,CAACE,GAAG,CAAC,CAACF,MAAM,GAAK,CAAC;gBACzB,GAAGA,MAAM;gBACTG,iBAAiB,EAAEH,MAAM,CAACI,MAAM;aACjC,CAAC,CAAC,CACJ,CAAC;IACJ;UAEMC,KAAK,GAAG;QACZ,IAAI;YACF,IAAI,CAAC,IAAI,CAACP,OAAO,CAACQ,MAAM,EAAE;gBACxB,OAAOX,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACnD,CAAC;YAED,MAAMY,IAAI,GAAGC,IAAAA,eAAkB,mBAAA,EAAC,qBAAqB,CAAC,AAAC;YACvD,MAAMC,IAAI,GAAGC,IAAI,CAACC,SAAS,CAAC;gBAAEb,OAAO,EAAE,IAAI,CAACA,OAAO;aAAE,CAAC,AAAC;YAEvD,IAAI,CAACA,OAAO,GAAG,EAAE,CAAC;YAElB,MAAMc,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,KAAK,CAACC,SAAI,EAAA,QAAA,CAACC,OAAO,CAACT,IAAI,CAAC,EAAE;gBAAEU,SAAS,EAAE,IAAI;aAAE,CAAC,CAAC;YACjE,MAAML,OAAE,EAAA,QAAA,CAACC,QAAQ,CAACK,SAAS,CAACX,IAAI,EAAEE,IAAI,CAAC,CAAC;YAExC,MAAMU,KAAK,GAAGC,IAAAA,iBAAK,EAAA,MAAA,EAACC,OAAO,CAACC,QAAQ,EAAE;gBAAC1B,OAAO,CAAC2B,OAAO,CAAC,uBAAuB,CAAC;gBAAEhB,IAAI;aAAC,EAAE;gBACtFiB,QAAQ,EAAE,IAAI;gBACdC,WAAW,EAAE,IAAI;gBACjBC,KAAK,EAAE,KAAK;gBACZC,KAAK,EAAE,QAAQ;aAChB,CAAC,AAAC;YAEHR,KAAK,CAACS,KAAK,EAAE,CAAC;QAChB,EAAE,OAAOC,KAAK,EAAE;YACd,oIAAoI;YACpI,mIAAmI;YACnIlC,KAAK,CAAC,4CAA4C,EAAEkC,KAAK,CAAC,CAAC;QAC7D,CAAC;QAEDlC,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAClC;CACD"}