{"version": 3, "sources": ["../../../../../src/start/platforms/ios/ensureSimulatorAppRunning.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport spawnAsync from '@expo/spawn-async';\n\nimport { Device } from './simctl';\nimport * as Log from '../../../log';\nimport { waitForActionAsync } from '../../../utils/delay';\nimport { CommandError } from '../../../utils/errors';\n\n/** Open the Simulator.app and return when the system registers it as 'open'. */\nexport async function ensureSimulatorAppRunningAsync(\n  device: Partial<Pick<Device, 'udid'>>,\n  {\n    maxWaitTime,\n  }: {\n    maxWaitTime?: number;\n  } = {}\n): Promise<void> {\n  if (await isSimulatorAppRunningAsync()) {\n    return;\n  }\n\n  Log.log(`\\u203A Opening the iOS simulator, this might take a moment.`);\n\n  // In theory this would ensure the correct simulator is booted as well.\n  // This isn't theory though, this is Xcode.\n  await openSimulatorAppAsync(device);\n\n  if (!(await waitForSimulatorAppToStart({ maxWaitTime }))) {\n    throw new CommandError(\n      'SIMULATOR_TIMEOUT',\n      `Simulator app did not open fast enough. Try opening Simulator first, then running your app.`\n    );\n  }\n}\n\nasync function waitForSimulatorAppToStart({\n  maxWaitTime,\n}: { maxWaitTime?: number } = {}): Promise<boolean> {\n  return waitForActionAsync<boolean>({\n    interval: 50,\n    maxWaitTime,\n    action: isSimulatorAppRunningAsync,\n  });\n}\n\n// I think the app can be open while no simulators are booted.\nasync function isSimulatorAppRunningAsync(): Promise<boolean> {\n  try {\n    const zeroMeansNo = (\n      await osascript.execAsync(\n        'tell app \"System Events\" to count processes whose name is \"Simulator\"'\n      )\n    ).trim();\n    if (zeroMeansNo === '0') {\n      return false;\n    }\n  } catch (error: any) {\n    if (error.message.includes('Application isn’t running')) {\n      return false;\n    }\n    throw error;\n  }\n\n  return true;\n}\n\nasync function openSimulatorAppAsync(device: { udid?: string }) {\n  const args = ['-a', 'Simulator'];\n  if (device.udid) {\n    // This has no effect if the app is already running.\n    args.push('--args', '-CurrentDeviceUDID', device.udid);\n  }\n  await spawnAsync('open', args);\n}\n"], "names": ["ensureSimulatorAppRunningAsync", "device", "maxWaitTime", "isSimulatorAppRunningAsync", "Log", "log", "openSimulatorAppAsync", "waitForSimulatorAppToStart", "CommandError", "waitForActionAsync", "interval", "action", "zeroMeansNo", "osascript", "execAsync", "trim", "error", "message", "includes", "args", "udid", "push", "spawnAsync"], "mappings": "AAAA;;;;+BASs<PERSON>,gCAA8B;;aAA9BA,8BAA8B;;;+DATzB,iBAAiB;;;;;;;8DACrB,mBAAmB;;;;;;2DAGrB,cAAc;uBACA,sBAAsB;wBAC5B,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7C,eAAeA,8BAA8B,CAClDC,MAAqC,EACrC,EACEC,WAAW,CAAA,EAGZ,GAAG,EAAE,EACS;IACf,IAAI,MAAMC,0BAA0B,EAAE,EAAE;QACtC,OAAO;IACT,CAAC;IAEDC,IAAG,CAACC,GAAG,CAAC,CAAC,2DAA2D,CAAC,CAAC,CAAC;IAEvE,uEAAuE;IACvE,2CAA2C;IAC3C,MAAMC,qBAAqB,CAACL,MAAM,CAAC,CAAC;IAEpC,IAAI,CAAE,MAAMM,0BAA0B,CAAC;QAAEL,WAAW;KAAE,CAAC,AAAC,EAAE;QACxD,MAAM,IAAIM,OAAY,aAAA,CACpB,mBAAmB,EACnB,CAAC,2FAA2F,CAAC,CAC9F,CAAC;IACJ,CAAC;AACH,CAAC;AAED,eAAeD,0BAA0B,CAAC,EACxCL,WAAW,CAAA,EACc,GAAG,EAAE,EAAoB;IAClD,OAAOO,IAAAA,MAAkB,mBAAA,EAAU;QACjCC,QAAQ,EAAE,EAAE;QACZR,WAAW;QACXS,MAAM,EAAER,0BAA0B;KACnC,CAAC,CAAC;AACL,CAAC;AAED,8DAA8D;AAC9D,eAAeA,0BAA0B,GAAqB;IAC5D,IAAI;QACF,MAAMS,WAAW,GAAG,CAClB,MAAMC,UAAS,EAAA,CAACC,SAAS,CACvB,uEAAuE,CACxE,CACF,CAACC,IAAI,EAAE,AAAC;QACT,IAAIH,WAAW,KAAK,GAAG,EAAE;YACvB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,EAAE,OAAOI,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAMF,KAAK,CAAC;IACd,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,eAAeV,qBAAqB,CAACL,MAAyB,EAAE;IAC9D,MAAMkB,IAAI,GAAG;QAAC,IAAI;QAAE,WAAW;KAAC,AAAC;IACjC,IAAIlB,MAAM,CAACmB,IAAI,EAAE;QACf,oDAAoD;QACpDD,IAAI,CAACE,IAAI,CAAC,QAAQ,EAAE,oBAAoB,EAAEpB,MAAM,CAACmB,IAAI,CAAC,CAAC;IACzD,CAAC;IACD,MAAME,IAAAA,WAAU,EAAA,QAAA,EAAC,MAAM,EAAEH,IAAI,CAAC,CAAC;AACjC,CAAC"}