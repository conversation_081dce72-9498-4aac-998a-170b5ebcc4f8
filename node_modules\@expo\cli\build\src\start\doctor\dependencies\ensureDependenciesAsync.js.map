{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/ensureDependenciesAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport wrapAnsi from 'wrap-ansi';\n\nimport { getMissingPackagesAsync, ResolvedPackage } from './getMissingPackages';\nimport { installAsync } from '../../../install/installAsync';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { isInteractive } from '../../../utils/interactive';\nimport { logNewSection } from '../../../utils/ora';\nimport { confirmAsync } from '../../../utils/prompts';\n\nexport type EnsureDependenciesOptions = {\n  /** The packages and/or version ranges that should be enforced in the project */\n  requiredPackages: ResolvedPackage[];\n  /** The user-facing message when the required packages are missing or incorrect */\n  installMessage: string;\n  /** The user-facing message when users aborted the installation */\n  warningMessage: string;\n  /** A previously loaded Expo configuration (loads when omitted) */\n  exp?: ExpoConfig;\n  /** If the prompts asking users to install should be skipped (defaults to false, in CI defaults to true) */\n  skipPrompt?: boolean;\n  /** Project can be mutated in the current environment (defaults to true, in CI defaults to false) */\n  isProjectMutable?: boolean;\n};\n\nexport async function ensureDependenciesAsync(\n  projectRoot: string,\n  {\n    exp = getConfig(projectRoot).exp,\n    requiredPackages,\n    warningMessage,\n    installMessage,\n    // Don't prompt in CI\n    skipPrompt = !isInteractive(),\n    isProjectMutable = isInteractive(),\n  }: EnsureDependenciesOptions\n): Promise<boolean> {\n  const { missing } = await getMissingPackagesAsync(projectRoot, {\n    sdkVersion: exp.sdkVersion,\n    requiredPackages,\n  });\n  if (!missing.length) {\n    return true;\n  }\n\n  // Prompt to install or bail out...\n  const readableMissingPackages = missing\n    .map(({ pkg, version }) => (version ? [pkg, version].join('@') : pkg))\n    .join(', ');\n\n  let title = installMessage;\n\n  if (skipPrompt && !isProjectMutable) {\n    title += '\\n\\n';\n  } else {\n    let confirm = skipPrompt;\n    if (skipPrompt) {\n      // Automatically install packages without prompting.\n      Log.log(wrapForTerminal(title + ` Installing ${chalk.cyan(readableMissingPackages)}`));\n    } else {\n      confirm = await confirmAsync({\n        message: wrapForTerminal(\n          title + ` Would you like to install ${chalk.cyan(readableMissingPackages)}?`\n        ),\n        initial: true,\n      });\n    }\n\n    if (confirm) {\n      // Format with version if available.\n      const [packages, devPackages] = missing.reduce(\n        ([deps, devDeps], p) => {\n          const pkg = p.version ? [p.pkg, p.version].join('@') : p.pkg;\n          if (p.dev) {\n            return [deps, devDeps.concat(pkg)];\n          }\n          return [deps.concat(pkg), devDeps];\n        },\n        [[], []] as [string[], string[]]\n      );\n\n      if (packages.length) {\n        await installPackagesAsync(projectRoot, {\n          packages,\n        });\n      }\n\n      if (devPackages.length) {\n        await installPackagesAsync(projectRoot, {\n          packages: devPackages,\n          dev: true,\n        });\n      }\n\n      // Try again but skip prompting twice, simply fail if the packages didn't install correctly.\n      return await ensureDependenciesAsync(projectRoot, {\n        skipPrompt: true,\n        installMessage,\n        warningMessage,\n        requiredPackages,\n      });\n    }\n\n    // Reset the title so it doesn't print twice in interactive mode.\n    title = '';\n  }\n\n  const installCommand = 'npx expo install ' + missing.map(({ pkg }) => pkg).join(' ');\n\n  const disableMessage = warningMessage;\n\n  const solution = `Please install ${chalk.bold(\n    readableMissingPackages\n  )} by running:\\n\\n  ${chalk.reset.bold(installCommand)}\\n\\n`;\n\n  // This prevents users from starting a misconfigured JS or TS project by default.\n  throw new CommandError(wrapForTerminal(title + solution + disableMessage + '\\n'));\n}\n\n/**  Wrap long messages to fit smaller terminals. */\nfunction wrapForTerminal(message: string): string {\n  return wrapAnsi(message, process.stdout.columns || 80);\n}\n\n/** Create the bash install command from a given set of packages and settings. */\nexport function createInstallCommand({\n  packages,\n}: {\n  packages: {\n    file: string;\n    pkg: string;\n    version?: string | undefined;\n  }[];\n}) {\n  return 'npx expo install ' + packages.map(({ pkg }) => pkg).join(' ');\n}\n\n/** Install packages in the project. */\nasync function installPackagesAsync(\n  projectRoot: string,\n  { packages, dev }: { packages: string[]; dev?: boolean }\n) {\n  const packagesStr = chalk.bold(packages.join(', '));\n  Log.log();\n  const installingPackageStep = logNewSection(`Installing ${packagesStr}`);\n  try {\n    await installAsync(packages, { projectRoot, dev });\n  } catch (e: any) {\n    installingPackageStep.fail(`Failed to install ${packagesStr} with error: ${e.message}`);\n    throw e;\n  }\n  installingPackageStep.succeed(`Installed ${packagesStr}`);\n}\n"], "names": ["ensureDependenciesAsync", "createInstallCommand", "projectRoot", "exp", "getConfig", "requiredPackages", "warningMessage", "installMessage", "skip<PERSON>rompt", "isInteractive", "isProjectMutable", "missing", "getMissingPackagesAsync", "sdkVersion", "length", "readableMissingPackages", "map", "pkg", "version", "join", "title", "confirm", "Log", "log", "wrapForTerminal", "chalk", "cyan", "<PERSON><PERSON><PERSON>", "message", "initial", "packages", "devPackages", "reduce", "deps", "devDeps", "p", "dev", "concat", "installPackagesAsync", "installCommand", "disableMessage", "solution", "bold", "reset", "CommandError", "wrapAnsi", "process", "stdout", "columns", "packagesStr", "installingPackageStep", "logNewSection", "installAsync", "e", "fail", "succeed"], "mappings": "AAAA;;;;;;;;;;;IA2BsBA,uBAAuB,MAAvBA,uBAAuB;IAoG7BC,oBAAoB,MAApBA,oBAAoB;;;yBA/HE,cAAc;;;;;;;8DAClC,OAAO;;;;;;;8DACJ,WAAW;;;;;;oCAEyB,sBAAsB;8BAClD,+BAA+B;2DACvC,cAAc;wBACN,uBAAuB;6BACtB,4BAA4B;qBAC5B,oBAAoB;yBACrB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB9C,eAAeD,uBAAuB,CAC3CE,WAAmB,EACnB,EACEC,GAAG,EAAGC,IAAAA,OAAS,EAAA,UAAA,EAACF,WAAW,CAAC,CAACC,GAAG,CAAA,EAChCE,gBAAgB,CAAA,EAChBC,cAAc,CAAA,EACdC,cAAc,CAAA,EACd,qBAAqB;AACrBC,UAAU,EAAG,CAACC,IAAAA,YAAa,cAAA,GAAE,CAAA,EAC7BC,gBAAgB,EAAGD,IAAAA,YAAa,cAAA,GAAE,CAAA,EACR,EACV;IAClB,MAAM,EAAEE,OAAO,CAAA,EAAE,GAAG,MAAMC,IAAAA,mBAAuB,wBAAA,EAACV,WAAW,EAAE;QAC7DW,UAAU,EAAEV,GAAG,CAACU,UAAU;QAC1BR,gBAAgB;KACjB,CAAC,AAAC;IACH,IAAI,CAACM,OAAO,CAACG,MAAM,EAAE;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mCAAmC;IACnC,MAAMC,uBAAuB,GAAGJ,OAAO,CACpCK,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAMA,OAAO,GAAG;YAACD,GAAG;YAAEC,OAAO;SAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGF,GAAG,AAAC,CAAC,CACrEE,IAAI,CAAC,IAAI,CAAC,AAAC;IAEd,IAAIC,KAAK,GAAGb,cAAc,AAAC;IAE3B,IAAIC,UAAU,IAAI,CAACE,gBAAgB,EAAE;QACnCU,KAAK,IAAI,MAAM,CAAC;IAClB,OAAO;QACL,IAAIC,OAAO,GAAGb,UAAU,AAAC;QACzB,IAAIA,UAAU,EAAE;YACd,oDAAoD;YACpDc,IAAG,CAACC,GAAG,CAACC,eAAe,CAACJ,KAAK,GAAG,CAAC,YAAY,EAAEK,MAAK,EAAA,QAAA,CAACC,IAAI,CAACX,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,OAAO;YACLM,OAAO,GAAG,MAAMM,IAAAA,QAAY,aAAA,EAAC;gBAC3BC,OAAO,EAAEJ,eAAe,CACtBJ,KAAK,GAAG,CAAC,2BAA2B,EAAEK,MAAK,EAAA,QAAA,CAACC,IAAI,CAACX,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAC7E;gBACDc,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QACL,CAAC;QAED,IAAIR,OAAO,EAAE;YACX,oCAAoC;YACpC,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,OAAO,CAACqB,MAAM,CAC5C,CAAC,CAACC,IAAI,EAAEC,OAAO,CAAC,EAAEC,CAAC,GAAK;gBACtB,MAAMlB,GAAG,GAAGkB,CAAC,CAACjB,OAAO,GAAG;oBAACiB,CAAC,CAAClB,GAAG;oBAAEkB,CAAC,CAACjB,OAAO;iBAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGgB,CAAC,CAAClB,GAAG,AAAC;gBAC7D,IAAIkB,CAAC,CAACC,GAAG,EAAE;oBACT,OAAO;wBAACH,IAAI;wBAAEC,OAAO,CAACG,MAAM,CAACpB,GAAG,CAAC;qBAAC,CAAC;gBACrC,CAAC;gBACD,OAAO;oBAACgB,IAAI,CAACI,MAAM,CAACpB,GAAG,CAAC;oBAAEiB,OAAO;iBAAC,CAAC;YACrC,CAAC,EACD;gBAAC,EAAE;gBAAE,EAAE;aAAC,CACT,AAAC;YAEF,IAAIJ,QAAQ,CAAChB,MAAM,EAAE;gBACnB,MAAMwB,oBAAoB,CAACpC,WAAW,EAAE;oBACtC4B,QAAQ;iBACT,CAAC,CAAC;YACL,CAAC;YAED,IAAIC,WAAW,CAACjB,MAAM,EAAE;gBACtB,MAAMwB,oBAAoB,CAACpC,WAAW,EAAE;oBACtC4B,QAAQ,EAAEC,WAAW;oBACrBK,GAAG,EAAE,IAAI;iBACV,CAAC,CAAC;YACL,CAAC;YAED,4FAA4F;YAC5F,OAAO,MAAMpC,uBAAuB,CAACE,WAAW,EAAE;gBAChDM,UAAU,EAAE,IAAI;gBAChBD,cAAc;gBACdD,cAAc;gBACdD,gBAAgB;aACjB,CAAC,CAAC;QACL,CAAC;QAED,iEAAiE;QACjEe,KAAK,GAAG,EAAE,CAAC;IACb,CAAC;IAED,MAAMmB,cAAc,GAAG,mBAAmB,GAAG5B,OAAO,CAACK,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAA,EAAE,GAAKA,GAAG,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,AAAC;IAErF,MAAMqB,cAAc,GAAGlC,cAAc,AAAC;IAEtC,MAAMmC,QAAQ,GAAG,CAAC,eAAe,EAAEhB,MAAK,EAAA,QAAA,CAACiB,IAAI,CAC3C3B,uBAAuB,CACxB,CAAC,kBAAkB,EAAEU,MAAK,EAAA,QAAA,CAACkB,KAAK,CAACD,IAAI,CAACH,cAAc,CAAC,CAAC,IAAI,CAAC,AAAC;IAE7D,iFAAiF;IACjF,MAAM,IAAIK,OAAY,aAAA,CAACpB,eAAe,CAACJ,KAAK,GAAGqB,QAAQ,GAAGD,cAAc,GAAG,IAAI,CAAC,CAAC,CAAC;AACpF,CAAC;AAED,kDAAkD,GAClD,SAAShB,eAAe,CAACI,OAAe,EAAU;IAChD,OAAOiB,IAAAA,SAAQ,EAAA,QAAA,EAACjB,OAAO,EAAEkB,OAAO,CAACC,MAAM,CAACC,OAAO,IAAI,EAAE,CAAC,CAAC;AACzD,CAAC;AAGM,SAAS/C,oBAAoB,CAAC,EACnC6B,QAAQ,CAAA,EAOT,EAAE;IACD,OAAO,mBAAmB,GAAGA,QAAQ,CAACd,GAAG,CAAC,CAAC,EAAEC,GAAG,CAAA,EAAE,GAAKA,GAAG,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC;AACxE,CAAC;AAED,qCAAqC,GACrC,eAAemB,oBAAoB,CACjCpC,WAAmB,EACnB,EAAE4B,QAAQ,CAAA,EAAEM,GAAG,CAAA,EAAyC,EACxD;IACA,MAAMa,WAAW,GAAGxB,MAAK,EAAA,QAAA,CAACiB,IAAI,CAACZ,QAAQ,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC,AAAC;IACpDG,IAAG,CAACC,GAAG,EAAE,CAAC;IACV,MAAM2B,qBAAqB,GAAGC,IAAAA,IAAa,cAAA,EAAC,CAAC,WAAW,EAAEF,WAAW,CAAC,CAAC,CAAC,AAAC;IACzE,IAAI;QACF,MAAMG,IAAAA,aAAY,aAAA,EAACtB,QAAQ,EAAE;YAAE5B,WAAW;YAAEkC,GAAG;SAAE,CAAC,CAAC;IACrD,EAAE,OAAOiB,CAAC,EAAO;QACfH,qBAAqB,CAACI,IAAI,CAAC,CAAC,kBAAkB,EAAEL,WAAW,CAAC,aAAa,EAAEI,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC,CAAC;QACxF,MAAMyB,CAAC,CAAC;IACV,CAAC;IACDH,qBAAqB,CAACK,OAAO,CAAC,CAAC,UAAU,EAAEN,WAAW,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC"}