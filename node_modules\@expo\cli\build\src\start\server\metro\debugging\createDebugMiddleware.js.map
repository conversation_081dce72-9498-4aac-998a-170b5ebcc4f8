{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/createDebugMiddleware.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { WebSocketServer } from 'ws';\n\nimport { createHandlersFactory } from './createHandlersFactory';\nimport { Log } from '../../../../log';\nimport { env } from '../../../../utils/env';\nimport { type MetroBundlerDevServer } from '../MetroBundlerDevServer';\nimport { NETWORK_RESPONSE_STORAGE } from './messageHandlers/NetworkResponse';\n\nconst debug = require('debug')('expo:metro:debugging:middleware') as typeof console.log;\n\nexport function createDebugMiddleware(metroBundler: MetroBundlerDevServer) {\n  // Load the React Native debugging tools from project\n  // TODO: check if this works with isolated modules\n  const { createDevMiddleware } =\n    require('@react-native/dev-middleware') as typeof import('@react-native/dev-middleware');\n\n  const { middleware, websocketEndpoints } = createDevMiddleware({\n    projectRoot: metroBundler.projectRoot,\n    serverBaseUrl: metroBundler\n      .getUrlCreator()\n      .constructUrl({ scheme: 'http', hostType: 'localhost' }),\n    logger: createLogger(chalk.bold('Debug:')),\n    unstable_customInspectorMessageHandler: createHandlersFactory(),\n    unstable_experiments: {\n      // Enable the Network tab in React Native DevTools\n      enableNetworkInspector: true,\n      // Only enable opening the browser version of React Native DevTools when debugging.\n      // This is useful when debugging the React Native DevTools by going to `/open-debugger` in the browser.\n      enableOpenDebuggerRedirect: env.EXPO_DEBUG,\n    },\n  });\n\n  // NOTE(cedric): add a temporary websocket to handle Network-related CDP events\n  websocketEndpoints['/inspector/network'] = createNetworkWebsocket(\n    websocketEndpoints['/inspector/debug']\n  );\n\n  return {\n    debugMiddleware: middleware,\n    debugWebsocketEndpoints: websocketEndpoints,\n  };\n}\n\nfunction createLogger(\n  logPrefix: string\n): Parameters<typeof import('@react-native/dev-middleware').createDevMiddleware>[0]['logger'] {\n  return {\n    info: (...args) => Log.log(logPrefix, ...args),\n    warn: (...args) => Log.warn(logPrefix, ...args),\n    error: (...args) => Log.error(logPrefix, ...args),\n  };\n}\n\n/**\n * This adds a dedicated websocket connection that handles Network-related CDP events.\n * It's a temporary solution until Fusebox either implements the Network CDP domain,\n * or allows external domain agents that can send messages over the CDP socket to the debugger.\n * The Network websocket rebroadcasts events on the debugger CDP connections.\n */\nfunction createNetworkWebsocket(debuggerWebsocket: WebSocketServer) {\n  const wss = new WebSocketServer({\n    noServer: true,\n    perMessageDeflate: true,\n    // Don't crash on exceptionally large messages - assume the device is\n    // well-behaved and the debugger is prepared to handle large messages.\n    maxPayload: 0,\n  });\n\n  wss.on('connection', (networkSocket) => {\n    networkSocket.on('message', (data) => {\n      try {\n        // Parse the network message, to determine how the message should be handled\n        const message = JSON.parse(data.toString());\n\n        if (message.method === 'Expo(Network.receivedResponseBody)' && message.params) {\n          // If its a response body, write it to the global storage\n          const { requestId, ...requestInfo } = message.params;\n          NETWORK_RESPONSE_STORAGE.set(requestId, requestInfo);\n        } else {\n          // Otherwise, directly re-broadcast the Network events to all connected debuggers\n          debuggerWebsocket.clients.forEach((debuggerSocket) => {\n            if (debuggerSocket.readyState === debuggerSocket.OPEN) {\n              debuggerSocket.send(data.toString());\n            }\n          });\n        }\n      } catch (error) {\n        debug('Failed to handle Network CDP event', error);\n      }\n    });\n  });\n\n  return wss;\n}\n"], "names": ["createDebugMiddleware", "debug", "require", "metroBundler", "createDevMiddleware", "middleware", "websocketEndpoints", "projectRoot", "serverBaseUrl", "getUrlCreator", "constructUrl", "scheme", "hostType", "logger", "createLogger", "chalk", "bold", "unstable_customInspectorMessageHandler", "createHandlersFactory", "unstable_experiments", "enableNetworkInspector", "enableOpenDebuggerRedirect", "env", "EXPO_DEBUG", "createNetworkWebsocket", "debugMiddleware", "debugWebsocketEndpoints", "logPrefix", "info", "args", "Log", "log", "warn", "error", "debuggerWebsocket", "wss", "WebSocketServer", "noServer", "perMessageDeflate", "maxPayload", "on", "networkSocket", "data", "message", "JSON", "parse", "toString", "method", "params", "requestId", "requestInfo", "NETWORK_RESPONSE_STORAGE", "set", "clients", "for<PERSON>ach", "debuggerSocket", "readyState", "OPEN", "send"], "mappings": "AAAA;;;;+BAWg<PERSON>,uBAAqB;;aAArBA,qBAAqB;;;8DAXnB,OAAO;;;;;;;yBACO,IAAI;;;;;;uCAEE,yBAAyB;qBAC3C,iBAAiB;qBACjB,uBAAuB;iCAEF,mCAAmC;;;;;;AAE5E,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,AAAsB,AAAC;AAEjF,SAASF,qBAAqB,CAACG,YAAmC,EAAE;IACzE,qDAAqD;IACrD,kDAAkD;IAClD,MAAM,EAAEC,mBAAmB,CAAA,EAAE,GAC3BF,OAAO,CAAC,8BAA8B,CAAC,AAAiD,AAAC;IAE3F,MAAM,EAAEG,UAAU,CAAA,EAAEC,kBAAkB,CAAA,EAAE,GAAGF,mBAAmB,CAAC;QAC7DG,WAAW,EAAEJ,YAAY,CAACI,WAAW;QACrCC,aAAa,EAAEL,YAAY,CACxBM,aAAa,EAAE,CACfC,YAAY,CAAC;YAAEC,MAAM,EAAE,MAAM;YAAEC,QAAQ,EAAE,WAAW;SAAE,CAAC;QAC1DC,MAAM,EAAEC,YAAY,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1CC,sCAAsC,EAAEC,IAAAA,sBAAqB,sBAAA,GAAE;QAC/DC,oBAAoB,EAAE;YACpB,kDAAkD;YAClDC,sBAAsB,EAAE,IAAI;YAC5B,mFAAmF;YACnF,uGAAuG;YACvGC,0BAA0B,EAAEC,IAAG,IAAA,CAACC,UAAU;SAC3C;KACF,CAAC,AAAC;IAEH,+EAA+E;IAC/EjB,kBAAkB,CAAC,oBAAoB,CAAC,GAAGkB,sBAAsB,CAC/DlB,kBAAkB,CAAC,kBAAkB,CAAC,CACvC,CAAC;IAEF,OAAO;QACLmB,eAAe,EAAEpB,UAAU;QAC3BqB,uBAAuB,EAAEpB,kBAAkB;KAC5C,CAAC;AACJ,CAAC;AAED,SAASQ,YAAY,CACnBa,SAAiB,EAC2E;IAC5F,OAAO;QACLC,IAAI,EAAE,CAAIC,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACC,GAAG,CAACJ,SAAS,KAAKE,IAAI,CAAC;QAC9CG,IAAI,EAAE,CAAIH,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACE,IAAI,CAACL,SAAS,KAAKE,IAAI,CAAC;QAC/CI,KAAK,EAAE,CAAIJ,GAAAA,IAAI,GAAKC,IAAG,IAAA,CAACG,KAAK,CAACN,SAAS,KAAKE,IAAI,CAAC;KAClD,CAAC;AACJ,CAAC;AAED;;;;;CAKC,GACD,SAASL,sBAAsB,CAACU,iBAAkC,EAAE;IAClE,MAAMC,GAAG,GAAG,IAAIC,CAAAA,GAAe,EAAA,CAAA,gBAAA,CAAC;QAC9BC,QAAQ,EAAE,IAAI;QACdC,iBAAiB,EAAE,IAAI;QACvB,qEAAqE;QACrE,sEAAsE;QACtEC,UAAU,EAAE,CAAC;KACd,CAAC,AAAC;IAEHJ,GAAG,CAACK,EAAE,CAAC,YAAY,EAAE,CAACC,aAAa,GAAK;QACtCA,aAAa,CAACD,EAAE,CAAC,SAAS,EAAE,CAACE,IAAI,GAAK;YACpC,IAAI;gBACF,4EAA4E;gBAC5E,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,QAAQ,EAAE,CAAC,AAAC;gBAE5C,IAAIH,OAAO,CAACI,MAAM,KAAK,oCAAoC,IAAIJ,OAAO,CAACK,MAAM,EAAE;oBAC7E,yDAAyD;oBACzD,MAAM,EAAEC,SAAS,CAAA,EAAE,GAAGC,WAAW,EAAE,GAAGP,OAAO,CAACK,MAAM,AAAC;oBACrDG,gBAAwB,yBAAA,CAACC,GAAG,CAACH,SAAS,EAAEC,WAAW,CAAC,CAAC;gBACvD,OAAO;oBACL,iFAAiF;oBACjFhB,iBAAiB,CAACmB,OAAO,CAACC,OAAO,CAAC,CAACC,cAAc,GAAK;wBACpD,IAAIA,cAAc,CAACC,UAAU,KAAKD,cAAc,CAACE,IAAI,EAAE;4BACrDF,cAAc,CAACG,IAAI,CAAChB,IAAI,CAACI,QAAQ,EAAE,CAAC,CAAC;wBACvC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,EAAE,OAAOb,KAAK,EAAE;gBACdhC,KAAK,CAAC,oCAAoC,EAAEgC,KAAK,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAOE,GAAG,CAAC;AACb,CAAC"}