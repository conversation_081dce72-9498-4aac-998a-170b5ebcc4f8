{"version": 3, "sources": ["../../../../src/start/server/DevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\n\nimport {\n  closeDevelopmentSessionAsync,\n  updateDevelopmentSessionAsync,\n} from '../../api/updateDevelopmentSession';\nimport { hasCredentials } from '../../api/user/UserSettings';\nimport { env } from '../../utils/env';\nimport * as ProjectDevices from '../project/devices';\n\nconst debug = require('debug')('expo:start:server:developmentSession') as typeof console.log;\n\nexport class DevelopmentSession {\n  /** If the `startAsync` was successfully called */\n  private hasActiveSession = false;\n\n  constructor(\n    /** Project root directory. */\n    private projectRoot: string,\n    /** Development Server URL. */\n    public url: string | null\n  ) {}\n\n  /**\n   * Notify the Expo servers that a project is running, this enables the Expo Go app\n   * and Dev Clients to offer a \"recently in development\" section for quick access.\n   *\n   * @param projectRoot Project root folder, used for retrieving device installation IDs.\n   * @param props.exp Partial Expo config with values that will be used in the Expo Go app.\n   * @param props.runtime which runtime the app should be opened in. `native` for dev clients, `web` for web browsers.\n   */\n  public async startAsync({\n    exp = getConfig(this.projectRoot).exp,\n    runtime,\n  }: {\n    exp?: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n    runtime: 'native' | 'web';\n  }): Promise<void> {\n    try {\n      if (env.CI || env.EXPO_OFFLINE) {\n        debug(\n          env.CI\n            ? 'This project will not be suggested in Expo Go or Dev Clients because Expo CLI is running in CI.'\n            : 'This project will not be suggested in Expo Go or Dev Clients because Expo CLI is running in offline-mode.'\n        );\n        return;\n      }\n\n      const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n      if (!hasCredentials() && !deviceIds?.length) {\n        debug(\n          'Development session will not ping because the user is not authenticated and there are no devices.'\n        );\n        return;\n      }\n\n      if (this.url) {\n        debug(`Development session ping (runtime: ${runtime}, url: ${this.url})`);\n        await updateDevelopmentSessionAsync({\n          url: this.url,\n          runtime,\n          exp,\n          deviceIds,\n        });\n        this.hasActiveSession = true;\n      }\n    } catch (error: any) {\n      debug(`Error updating development session API: ${error}`);\n    }\n  }\n\n  /** Get all recent devices for the project. */\n  private async getDeviceInstallationIdsAsync(): Promise<string[]> {\n    const { devices } = await ProjectDevices.getDevicesInfoAsync(this.projectRoot);\n    return devices.map(({ installationId }) => installationId);\n  }\n\n  /** Try to close any pending development sessions, but always resolve */\n  public async closeAsync(): Promise<boolean> {\n    if (env.CI || env.EXPO_OFFLINE || !this.hasActiveSession) {\n      return false;\n    }\n\n    // Clear out the development session, even if the call fails.\n    // This blocks subsequent calls to `stopAsync`\n    this.hasActiveSession = false;\n\n    try {\n      const deviceIds = await this.getDeviceInstallationIdsAsync();\n\n      if (!hasCredentials() && !deviceIds?.length) {\n        return false;\n      }\n\n      if (this.url) {\n        await closeDevelopmentSessionAsync({\n          url: this.url,\n          deviceIds,\n        });\n      }\n\n      return true;\n    } catch (error: any) {\n      debug(`Error closing development session API: ${error}`);\n      return false;\n    }\n  }\n}\n"], "names": ["DevelopmentSession", "debug", "require", "constructor", "projectRoot", "url", "hasActiveSession", "startAsync", "exp", "getConfig", "runtime", "env", "CI", "EXPO_OFFLINE", "deviceIds", "getDeviceInstallationIdsAsync", "hasCredentials", "length", "updateDevelopmentSessionAsync", "error", "devices", "ProjectDevices", "getDevicesInfoAsync", "map", "installationId", "closeAsync", "closeDevelopmentSessionAsync"], "mappings": "AAAA;;;;+BAYa<PERSON>,oBAAkB;;aAAlBA,kBAAkB;;;yBAZO,cAAc;;;;;;0CAK7C,oCAAoC;8BACZ,6BAA6B;qBACxC,iBAAiB;+<PERSON><PERSON><PERSON>,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAEtF,MAAMF,kBAAkB;IAI7BG,YAEUC,WAAmB,EAEpBC,GAAkB,CACzB;QAHQD,mBAAAA,WAAmB,CAAA;QAEpBC,WAAAA,GAAkB,CAAA;aANnBC,gBAAgB,GAAG,KAAK;IAO7B;IAEH;;;;;;;GAOC,SACYC,UAAU,CAAC,EACtBC,GAAG,EAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAACL,WAAW,CAAC,CAACI,GAAG,CAAA,EACrCE,OAAO,CAAA,EAIR,EAAiB;QAChB,IAAI;YACF,IAAIC,IAAG,IAAA,CAACC,EAAE,IAAID,IAAG,IAAA,CAACE,YAAY,EAAE;gBAC9BZ,KAAK,CACHU,IAAG,IAAA,CAACC,EAAE,GACF,iGAAiG,GACjG,2GAA2G,CAChH,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAME,SAAS,GAAG,MAAM,IAAI,CAACC,6BAA6B,EAAE,AAAC;YAE7D,IAAI,CAACC,IAAAA,aAAc,eAAA,GAAE,IAAI,CAACF,CAAAA,SAAS,QAAQ,GAAjBA,KAAAA,CAAiB,GAAjBA,SAAS,CAAEG,MAAM,CAAA,EAAE;gBAC3ChB,KAAK,CACH,mGAAmG,CACpG,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAACI,GAAG,EAAE;gBACZJ,KAAK,CAAC,CAAC,mCAAmC,EAAES,OAAO,CAAC,OAAO,EAAE,IAAI,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,MAAMa,IAAAA,yBAA6B,8BAAA,EAAC;oBAClCb,GAAG,EAAE,IAAI,CAACA,GAAG;oBACbK,OAAO;oBACPF,GAAG;oBACHM,SAAS;iBACV,CAAC,CAAC;gBACH,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAAC;YAC/B,CAAC;QACH,EAAE,OAAOa,KAAK,EAAO;YACnBlB,KAAK,CAAC,CAAC,wCAAwC,EAAEkB,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH;IAEA,4CAA4C,SAC9BJ,6BAA6B,GAAsB;QAC/D,MAAM,EAAEK,OAAO,CAAA,EAAE,GAAG,MAAMC,QAAc,CAACC,mBAAmB,CAAC,IAAI,CAAClB,WAAW,CAAC,AAAC;QAC/E,OAAOgB,OAAO,CAACG,GAAG,CAAC,CAAC,EAAEC,cAAc,CAAA,EAAE,GAAKA,cAAc,CAAC,CAAC;IAC7D;IAEA,sEAAsE,SACzDC,UAAU,GAAqB;QAC1C,IAAId,IAAG,IAAA,CAACC,EAAE,IAAID,IAAG,IAAA,CAACE,YAAY,IAAI,CAAC,IAAI,CAACP,gBAAgB,EAAE;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6DAA6D;QAC7D,8CAA8C;QAC9C,IAAI,CAACA,gBAAgB,GAAG,KAAK,CAAC;QAE9B,IAAI;YACF,MAAMQ,SAAS,GAAG,MAAM,IAAI,CAACC,6BAA6B,EAAE,AAAC;YAE7D,IAAI,CAACC,IAAAA,aAAc,eAAA,GAAE,IAAI,CAACF,CAAAA,SAAS,QAAQ,GAAjBA,KAAAA,CAAiB,GAAjBA,SAAS,CAAEG,MAAM,CAAA,EAAE;gBAC3C,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IAAI,IAAI,CAACZ,GAAG,EAAE;gBACZ,MAAMqB,IAAAA,yBAA4B,6BAAA,EAAC;oBACjCrB,GAAG,EAAE,IAAI,CAACA,GAAG;oBACbS,SAAS;iBACV,CAAC,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC;QACd,EAAE,OAAOK,KAAK,EAAO;YACnBlB,KAAK,CAAC,CAAC,uCAAuC,EAAEkB,KAAK,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH;CACD"}