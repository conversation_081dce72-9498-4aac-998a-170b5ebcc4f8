{"version": 3, "sources": ["../../../../../src/start/platforms/ios/promptAppleDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { getBestSimulatorAsync } from './getBestSimulator';\nimport { Device } from './simctl';\nimport { createSelectionFilter, promptAsync } from '../../../utils/prompts';\n\n/**\n * Sort the devices so the last simulator that was opened (user's default) is the first suggested.\n *\n * @param devices list of devices to sort.\n * @param osType optional sort by operating system.\n */\nexport async function sortDefaultDeviceToBeginningAsync<T extends { udid: string }>(\n  devices: T[],\n  osType?: Device['osType']\n): Promise<T[]> {\n  const defaultId = await getBestSimulatorAsync({ osType });\n  if (defaultId) {\n    let iterations = 0;\n    while (devices[0].udid !== defaultId && iterations < devices.length) {\n      devices.push(devices.shift()!);\n      iterations++;\n    }\n  }\n  return devices;\n}\n\n/** Prompt the user to select an Apple device, sorting the most likely option to the beginning. */\nexport async function promptAppleDeviceAsync(\n  devices: Device[],\n  osType?: Device['osType']\n): Promise<Device> {\n  devices = await sortDefaultDeviceToBeginningAsync(devices, osType);\n  const results = await promptAppleDeviceInternalAsync(devices);\n  return devices.find(({ udid }) => results === udid)!;\n}\n\nasync function promptAppleDeviceInternalAsync(devices: Device[]): Promise<string> {\n  // TODO: provide an option to add or download more simulators\n  // TODO: Add support for physical devices too.\n\n  const { value } = await promptAsync({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a simulator',\n    choices: devices.map((item) => {\n      const isActive = item.state === 'Booted';\n      const format = isActive ? chalk.bold : (text: string) => text;\n      return {\n        title: `${format(item.name)} ${chalk.dim(`(${item.osVersion})`)}`,\n        value: item.udid,\n      };\n    }),\n    suggest: createSelectionFilter(),\n  });\n\n  return value;\n}\n"], "names": ["sortDefaultDeviceToBeginningAsync", "promptAppleDeviceAsync", "devices", "osType", "defaultId", "getBestSimulatorAsync", "iterations", "udid", "length", "push", "shift", "results", "promptAppleDeviceInternalAsync", "find", "value", "promptAsync", "type", "name", "limit", "message", "choices", "map", "item", "isActive", "state", "format", "chalk", "bold", "text", "title", "dim", "osVersion", "suggest", "createSelectionFilter"], "mappings": "AAAA;;;;;;;;;;;IAYsBA,iCAAiC,MAAjCA,iCAAiC;IAgBjCC,sBAAsB,MAAtBA,sBAAsB;;;8DA5B1B,OAAO;;;;;;kCAEa,oBAAoB;yBAEP,wBAAwB;;;;;;AAQpE,eAAeD,iCAAiC,CACrDE,OAAY,EACZC,MAAyB,EACX;IACd,MAAMC,SAAS,GAAG,MAAMC,IAAAA,iBAAqB,sBAAA,EAAC;QAAEF,MAAM;KAAE,CAAC,AAAC;IAC1D,IAAIC,SAAS,EAAE;QACb,IAAIE,UAAU,GAAG,CAAC,AAAC;QACnB,MAAOJ,OAAO,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKH,SAAS,IAAIE,UAAU,GAAGJ,OAAO,CAACM,MAAM,CAAE;YACnEN,OAAO,CAACO,IAAI,CAACP,OAAO,CAACQ,KAAK,EAAE,CAAE,CAAC;YAC/BJ,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAOJ,OAAO,CAAC;AACjB,CAAC;AAGM,eAAeD,sBAAsB,CAC1CC,OAAiB,EACjBC,MAAyB,EACR;IACjBD,OAAO,GAAG,MAAMF,iCAAiC,CAACE,OAAO,EAAEC,MAAM,CAAC,CAAC;IACnE,MAAMQ,OAAO,GAAG,MAAMC,8BAA8B,CAACV,OAAO,CAAC,AAAC;IAC9D,OAAOA,OAAO,CAACW,IAAI,CAAC,CAAC,EAAEN,IAAI,CAAA,EAAE,GAAKI,OAAO,KAAKJ,IAAI,CAAC,CAAE;AACvD,CAAC;AAED,eAAeK,8BAA8B,CAACV,OAAiB,EAAmB;IAChF,6DAA6D;IAC7D,8CAA8C;IAE9C,MAAM,EAAEY,KAAK,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAW,YAAA,EAAC;QAClCC,IAAI,EAAE,cAAc;QACpBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,oBAAoB;QAC7BC,OAAO,EAAElB,OAAO,CAACmB,GAAG,CAAC,CAACC,IAAI,GAAK;YAC7B,MAAMC,QAAQ,GAAGD,IAAI,CAACE,KAAK,KAAK,QAAQ,AAAC;YACzC,MAAMC,MAAM,GAAGF,QAAQ,GAAGG,MAAK,EAAA,QAAA,CAACC,IAAI,GAAG,CAACC,IAAY,GAAKA,IAAI,AAAC;YAC9D,OAAO;gBACLC,KAAK,EAAE,CAAC,EAAEJ,MAAM,CAACH,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,EAAES,MAAK,EAAA,QAAA,CAACI,GAAG,CAAC,CAAC,CAAC,EAAER,IAAI,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjEjB,KAAK,EAAEQ,IAAI,CAACf,IAAI;aACjB,CAAC;QACJ,CAAC,CAAC;QACFyB,OAAO,EAAEC,IAAAA,QAAqB,sBAAA,GAAE;KACjC,CAAC,AAAC;IAEH,OAAOnB,KAAK,CAAC;AACf,CAAC"}