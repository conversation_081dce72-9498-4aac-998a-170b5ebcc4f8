{"version": 3, "sources": ["../../../src/start/startAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SimulatorAppPrerequisite } from './doctor/apple/SimulatorAppPrerequisite';\nimport { getXcodeVersionAsync } from './doctor/apple/XcodePrerequisite';\nimport { validateDependenciesVersionsAsync } from './doctor/dependencies/validateDependenciesVersions';\nimport { WebSupportProjectPrerequisite } from './doctor/web/WebSupportProjectPrerequisite';\nimport { startInterfaceAsync } from './interface/startInterface';\nimport { Options, resolvePortsAsync } from './resolveOptions';\nimport * as Log from '../log';\nimport { BundlerStartOptions } from './server/BundlerDevServer';\nimport { DevServerManager, MultiBundlerStartOptions } from './server/DevServerManager';\nimport { openPlatformsAsync } from './server/openPlatforms';\nimport { getPlatformBundlers, PlatformBundlers } from './server/platformBundlers';\nimport { env } from '../utils/env';\nimport { isInteractive } from '../utils/interactive';\nimport { profile } from '../utils/profile';\n\nasync function getMultiBundlerStartOptions(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean },\n  platformBundlers: PlatformBundlers\n): Promise<[BundlerStartOptions, MultiBundlerStartOptions]> {\n  const commonOptions: BundlerStartOptions = {\n    mode: options.dev ? 'development' : 'production',\n    devClient: options.devClient,\n    privateKeyPath: options.privateKeyPath ?? undefined,\n    https: options.https,\n    maxWorkers: options.maxWorkers,\n    resetDevServer: options.clear,\n    minify: options.minify,\n    location: {\n      hostType: options.host,\n      scheme: options.scheme,\n    },\n  };\n  const multiBundlerSettings = await resolvePortsAsync(projectRoot, options, settings);\n\n  const optionalBundlers: Partial<PlatformBundlers> = { ...platformBundlers };\n  // In the default case, we don't want to start multiple bundlers since this is\n  // a bit slower. Our priority (for legacy) is native platforms.\n  if (!options.web) {\n    delete optionalBundlers['web'];\n  }\n\n  const bundlers = [...new Set(Object.values(optionalBundlers))];\n  const multiBundlerStartOptions = bundlers.map((bundler) => {\n    const port =\n      bundler === 'webpack' ? multiBundlerSettings.webpackPort : multiBundlerSettings.metroPort;\n    return {\n      type: bundler,\n      options: {\n        ...commonOptions,\n        port,\n      },\n    };\n  });\n\n  return [commonOptions, multiBundlerStartOptions];\n}\n\nexport async function startAsync(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean }\n) {\n  Log.log(chalk.gray(`Starting project at ${projectRoot}`));\n\n  const { exp, pkg } = profile(getConfig)(projectRoot);\n\n  if (exp.platforms?.includes('ios') && process.platform !== 'win32') {\n    // If Xcode could potentially be used, then we should eagerly perform the\n    // assertions since they can take a while on cold boots.\n    getXcodeVersionAsync({ silent: true });\n    SimulatorAppPrerequisite.instance.assertAsync().catch(() => {\n      // noop -- this will be thrown again when the user attempts to open the project.\n    });\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  const [defaultOptions, startOptions] = await getMultiBundlerStartOptions(\n    projectRoot,\n    options,\n    settings,\n    platformBundlers\n  );\n\n  const devServerManager = new DevServerManager(projectRoot, defaultOptions);\n\n  // Validations\n\n  if (options.web || settings.webOnly) {\n    await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n  }\n\n  // Start the server as soon as possible.\n  await profile(devServerManager.startAsync.bind(devServerManager))(startOptions);\n\n  if (!settings.webOnly) {\n    await devServerManager.watchEnvironmentVariables();\n\n    // After the server starts, we can start attempting to bootstrap TypeScript.\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  if (!env.EXPO_NO_DEPENDENCY_VALIDATION && !settings.webOnly && !options.devClient) {\n    await profile(validateDependenciesVersionsAsync)(projectRoot, exp, pkg);\n  }\n\n  // Open project on devices.\n  await profile(openPlatformsAsync)(devServerManager, options);\n\n  // Present the Terminal UI.\n  if (isInteractive()) {\n    await profile(startInterfaceAsync)(devServerManager, {\n      platforms: exp.platforms ?? ['ios', 'android', 'web'],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n    if (url) {\n      if (env.__EXPO_E2E_TEST) {\n        // Print the URL to stdout for tests\n        console.info(`[__EXPO_E2E_TEST:server] ${JSON.stringify({ url })}`);\n      }\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  // Final note about closing the server.\n  const logLocation = settings.webOnly ? 'in the browser console' : 'below';\n  Log.log(\n    chalk`Logs for your project will appear ${logLocation}.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n"], "names": ["startAsync", "getMultiBundlerStartOptions", "projectRoot", "options", "settings", "platformBundlers", "commonOptions", "mode", "dev", "devClient", "privateKeyPath", "undefined", "https", "maxWorkers", "resetDevServer", "clear", "minify", "location", "hostType", "host", "scheme", "multiBundlerSettings", "resolvePortsAsync", "optionalBundlers", "web", "bundlers", "Set", "Object", "values", "multiBundlerStartOptions", "map", "bundler", "port", "webpackPort", "metroPort", "type", "exp", "Log", "log", "chalk", "gray", "pkg", "profile", "getConfig", "platforms", "includes", "process", "platform", "getXcodeVersionAsync", "silent", "SimulatorAppPrerequisite", "instance", "assertAsync", "catch", "getPlatformBundlers", "defaultOptions", "startOptions", "devServerManager", "DevServerManager", "webOnly", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "bind", "watchEnvironmentVariables", "bootstrapTypeScriptAsync", "env", "EXPO_NO_DEPENDENCY_VALIDATION", "validateDependenciesVersionsAsync", "openPlatformsAsync", "isInteractive", "startInterfaceAsync", "url", "getDefaultDevServer", "getDevServerUrl", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "logLocation", "dim"], "mappings": "AAAA;;;;+BA8<PERSON><PERSON>,YAAU;;aAAVA,UAAU;;;yBA9DN,cAAc;;;;;;;8DACtB,OAAO;;;;;;0CAEgB,yCAAyC;mCAC7C,kCAAkC;8CACrB,oDAAoD;+CACxD,4CAA4C;gCACtD,4BAA4B;gCACrB,kBAAkB;2DACxC,QAAQ;kCAE8B,2BAA2B;+BACnD,wBAAwB;kCACL,2BAA2B;qBAC7D,cAAc;6BACJ,sBAAsB;yBAC5B,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,eAAeC,2BAA2B,CACxCC,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B,EAC/BC,gBAAkC,EACwB;IAC1D,MAAMC,aAAa,GAAwB;QACzCC,IAAI,EAAEJ,OAAO,CAACK,GAAG,GAAG,aAAa,GAAG,YAAY;QAChDC,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5BC,cAAc,EAAEP,OAAO,CAACO,cAAc,IAAIC,SAAS;QACnDC,KAAK,EAAET,OAAO,CAACS,KAAK;QACpBC,UAAU,EAAEV,OAAO,CAACU,UAAU;QAC9BC,cAAc,EAAEX,OAAO,CAACY,KAAK;QAC7BC,MAAM,EAAEb,OAAO,CAACa,MAAM;QACtBC,QAAQ,EAAE;YACRC,QAAQ,EAAEf,OAAO,CAACgB,IAAI;YACtBC,MAAM,EAAEjB,OAAO,CAACiB,MAAM;SACvB;KACF,AAAC;IACF,MAAMC,oBAAoB,GAAG,MAAMC,IAAAA,eAAiB,kBAAA,EAACpB,WAAW,EAAEC,OAAO,EAAEC,QAAQ,CAAC,AAAC;IAErF,MAAMmB,gBAAgB,GAA8B;QAAE,GAAGlB,gBAAgB;KAAE,AAAC;IAC5E,8EAA8E;IAC9E,+DAA+D;IAC/D,IAAI,CAACF,OAAO,CAACqB,GAAG,EAAE;QAChB,OAAOD,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,MAAME,QAAQ,GAAG;WAAI,IAAIC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACL,gBAAgB,CAAC,CAAC;KAAC,AAAC;IAC/D,MAAMM,wBAAwB,GAAGJ,QAAQ,CAACK,GAAG,CAAC,CAACC,OAAO,GAAK;QACzD,MAAMC,IAAI,GACRD,OAAO,KAAK,SAAS,GAAGV,oBAAoB,CAACY,WAAW,GAAGZ,oBAAoB,CAACa,SAAS,AAAC;QAC5F,OAAO;YACLC,IAAI,EAAEJ,OAAO;YACb5B,OAAO,EAAE;gBACP,GAAGG,aAAa;gBAChB0B,IAAI;aACL;SACF,CAAC;IACJ,CAAC,CAAC,AAAC;IAEH,OAAO;QAAC1B,aAAa;QAAEuB,wBAAwB;KAAC,CAAC;AACnD,CAAC;AAEM,eAAe7B,UAAU,CAC9BE,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B,EAC/B;QAKIgC,GAAa;IAJjBC,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,CAAC,oBAAoB,EAAEtC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1D,MAAM,EAAEkC,GAAG,CAAA,EAAEK,GAAG,CAAA,EAAE,GAAGC,IAAAA,QAAO,QAAA,EAACC,OAAS,EAAA,UAAA,CAAC,CAACzC,WAAW,CAAC,AAAC;IAErD,IAAIkC,CAAAA,CAAAA,GAAa,GAAbA,GAAG,CAACQ,SAAS,SAAU,GAAvBR,KAAAA,CAAuB,GAAvBA,GAAa,CAAES,QAAQ,CAAC,KAAK,CAAC,KAAIC,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;QAClE,yEAAyE;QACzE,wDAAwD;QACxDC,IAAAA,kBAAoB,qBAAA,EAAC;YAAEC,MAAM,EAAE,IAAI;SAAE,CAAC,CAAC;QACvCC,yBAAwB,yBAAA,CAACC,QAAQ,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,IAAM;QAC1D,gFAAgF;QAClF,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAMhD,gBAAgB,GAAGiD,IAAAA,iBAAmB,oBAAA,EAACpD,WAAW,EAAEkC,GAAG,CAAC,AAAC;IAE/D,MAAM,CAACmB,cAAc,EAAEC,YAAY,CAAC,GAAG,MAAMvD,2BAA2B,CACtEC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,CACjB,AAAC;IAEF,MAAMoD,gBAAgB,GAAG,IAAIC,iBAAgB,iBAAA,CAACxD,WAAW,EAAEqD,cAAc,CAAC,AAAC;IAE3E,cAAc;IAEd,IAAIpD,OAAO,CAACqB,GAAG,IAAIpB,QAAQ,CAACuD,OAAO,EAAE;QACnC,MAAMF,gBAAgB,CAACG,8BAA8B,CAACC,8BAA6B,8BAAA,CAAC,CAAC;IACvF,CAAC;IAED,wCAAwC;IACxC,MAAMnB,IAAAA,QAAO,QAAA,EAACe,gBAAgB,CAACzD,UAAU,CAAC8D,IAAI,CAACL,gBAAgB,CAAC,CAAC,CAACD,YAAY,CAAC,CAAC;IAEhF,IAAI,CAACpD,QAAQ,CAACuD,OAAO,EAAE;QACrB,MAAMF,gBAAgB,CAACM,yBAAyB,EAAE,CAAC;QAEnD,4EAA4E;QAC5E,MAAMN,gBAAgB,CAACO,wBAAwB,EAAE,CAAC;IACpD,CAAC;IAED,IAAI,CAACC,IAAG,IAAA,CAACC,6BAA6B,IAAI,CAAC9D,QAAQ,CAACuD,OAAO,IAAI,CAACxD,OAAO,CAACM,SAAS,EAAE;QACjF,MAAMiC,IAAAA,QAAO,QAAA,EAACyB,6BAAiC,kCAAA,CAAC,CAACjE,WAAW,EAAEkC,GAAG,EAAEK,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED,2BAA2B;IAC3B,MAAMC,IAAAA,QAAO,QAAA,EAAC0B,cAAkB,mBAAA,CAAC,CAACX,gBAAgB,EAAEtD,OAAO,CAAC,CAAC;IAE7D,2BAA2B;IAC3B,IAAIkE,IAAAA,YAAa,cAAA,GAAE,EAAE;QACnB,MAAM3B,IAAAA,QAAO,QAAA,EAAC4B,eAAmB,oBAAA,CAAC,CAACb,gBAAgB,EAAE;YACnDb,SAAS,EAAER,GAAG,CAACQ,SAAS,IAAI;gBAAC,KAAK;gBAAE,SAAS;gBAAE,KAAK;aAAC;SACtD,CAAC,CAAC;IACL,OAAO;YAEOa,IAAsC;QADlD,uCAAuC;QACvC,MAAMc,GAAG,GAAGd,CAAAA,IAAsC,GAAtCA,gBAAgB,CAACe,mBAAmB,EAAE,SAAiB,GAAvDf,KAAAA,CAAuD,GAAvDA,IAAsC,CAAEgB,eAAe,EAAE,AAAC;QACtE,IAAIF,GAAG,EAAE;YACP,IAAIN,IAAG,IAAA,CAACS,eAAe,EAAE;gBACvB,oCAAoC;gBACpCC,OAAO,CAACC,IAAI,CAAC,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAAC;oBAAEP,GAAG;iBAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,CAAC;YACDlC,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sBAAsB,EAAEgC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,uCAAuC;IACvC,MAAMQ,WAAW,GAAG3E,QAAQ,CAACuD,OAAO,GAAG,wBAAwB,GAAG,OAAO,AAAC;IAC1EtB,IAAG,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,kCAAkC,EAAEwC,WAAW,CAAC,CAAC,EACrDV,IAAAA,YAAa,cAAA,GAAE,GAAG9B,MAAK,EAAA,QAAA,CAACyC,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAG,EAAE,CAC3D,CAAC,CACH,CAAC;AACJ,CAAC"}